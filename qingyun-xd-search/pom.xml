<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-parent</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xd-search-parent</artifactId>
    <name>qingyun-xd-search-parent</name>
    <description>qingyun-xd-search-parent</description>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--<qingyun.shop.version>3.5.9-UP-SNAPSHOT</qingyun.shop.version>-->
<!--        <qingyun.box.version>2.0.1-SNAPSHOT</qingyun.box.version>-->
        <!--<qingyun.base.version>3.5.8-SNAPSHOT</qingyun.base.version>-->
        <!--<qingyun.mq.version>2.3.3-SNAPSHOT</qingyun.mq.version>-->
<!--        <qingyun.cache.version>2.0.2-SNAPSHOT</qingyun.cache.version>-->
        <!--<qingyun.shop.version>3.5.9-UP-SNAPSHOT</qingyun.shop.version>-->
        <!--<qingyun.storage.version>2.3.1-SNAPSHOT</qingyun.storage.version>-->
        <!--<qingyun.xd.product.version>1.0.8-SNAPSHOT</qingyun.xd.product.version>-->
        <!--<qingyun.xd.promotion.version>1.0.8-SNAPSHOT</qingyun.xd.promotion.version>-->
        <!--<qingyun.xd.cms.version>1.0.8-SNAPSHOT</qingyun.xd.cms.version>-->
       <!-- <qingyun-gee-bom.version>0.2-SNAPSHOT</qingyun-gee-bom.version>-->
        <!--<qingyun.xs.user.version>3.1.0-SNAPSHOT</qingyun.xs.user.version>-->
        <!--<qingyun.xd.recommend.version>1.0.1-SNAPSHOT</qingyun.xd.recommend.version>-->
        <apache.poi.version>5.2.2</apache.poi.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <pinyin.version>1.1.8</pinyin.version>
    </properties>
    <modules>
<!--        <module>qingyun-xd-search-base</module>-->
        <module>qingyun-xd-search-client</module>
<!--        <module>qingyun-xd-search-service</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-search-client</artifactId>
                <version>${qingyun.base.search.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-cache</artifactId>
                <version>${qingyun.cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-promotion-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-storage-client</artifactId>
                <version>${qingyun.storage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-product-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-cms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xs-user-client</artifactId>
                <version>${qingyun.xs.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-recommend-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.3</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-upload-client</artifactId>
                <version>${qingyun.upload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.16.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-mock</artifactId>
                <version>2.0.8</version>
            </dependency>
            <!--<dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-gee-bom</artifactId>
                <version>${qingyun-gee-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>-->

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.stuxuhai</groupId>
                <artifactId>jpinyin</artifactId>
                <version>${pinyin.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

</project>