<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-parent</artifactId>
        <version>5.0.0-UP-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>qingyun-xd-parent</artifactId>
    <version>3.7.3-UP-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>../qingyun-xd-cms</module>
        <module>../qingyun-xd-elm-order</module>
        <module>../qingyun-xd-order</module>
        <module>../qingyun-xd-price</module>
        <module>../qingyun-xd-product</module>
        <module>../qingyun-xd-promotion</module>
        <module>../qingyun-xd-recommend</module>
        <module>../qingyun-xd-report</module>
        <module>../qingyun-xd-search</module>
        <module>../qingyun-xd-tms</module>
        <module>../qingyun-xd-wms</module>

    </modules>
    <properties>
        <revision>${project.version}</revision>
        <!-- 解决文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 解决编译时中文乱码-->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--        <kotlin.compiler.incremental>true</kotlin.compiler.incremental>-->
        <java.version>1.8</java.version>
        <org.spring.version>5.3.18</org.spring.version>
        <spring-boot.version>2.6.6</spring-boot.version>
        <swagger-models.version>1.6.6</swagger-models.version>

        <druid.version>1.1.24</druid.version>
        <redisson.version>3.13.6</redisson.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <poi.version>4.1.1</poi.version>
        <qingyun.box.version>3.0.4-UP-SNAPSHOT</qingyun.box.version>
        <qingyun.base.mvc.version>3.5.3-SNAPSHOT</qingyun.base.mvc.version>
        <qingyun.base.db.version>1.0.3-SNAPSHOT</qingyun.base.db.version>
        <qingyun.basic.version>1.7.3-SNAPSHOT</qingyun.basic.version>
        <qingyun.cache.version>3.1.2-UP-SNAPSHOT</qingyun.cache.version>
        <qingyun.mq.version>3.9.7-UP-SNAPSHOT</qingyun.mq.version>
        <qingyun.xs.user.version>3.4.4-UP-SNAPSHOT</qingyun.xs.user.version>
        <qingyun.common.version>3.6.9-UP-SNAPSHOT</qingyun.common.version>
        <qingyun.price.version>1.4.9-UP-SNAPSHOT</qingyun.price.version>
        <qingyun.product.version>3.7.0-UP-SNAPSHOT</qingyun.product.version>
        <!--        <qingyun.xs.pos.version>2.0.0-SNAPSHOT</qingyun.xs.pos.version>-->
        <qingyun.purchase.version>3.0.7-UP-SNAPSHOT</qingyun.purchase.version>
        <qingyun.supplier.version>3.0.7-UP-SNAPSHOT</qingyun.supplier.version>
        <qingyun.shop.version>4.1.9-UP-SNAPSHOT</qingyun.shop.version>
        <qingyun.invoice.version>2.1.7-UP-SNAPSHOT</qingyun.invoice.version>
        <qingyun.sync.version>3.0.9-UP-SNAPSHOT</qingyun.sync.version>
        <kotlin.version>1.7.0</kotlin.version>
        <qingyun.storage.version>3.7.3-UP-SNAPSHOT</qingyun.storage.version>
        <qingyun.msg.version>3.0.4-UP-SNAPSHOT</qingyun.msg.version>
        <qingyun.order.version>3.6.9-UP-SNAPSHOT</qingyun.order.version>
        <qingyun.smm.version>3.2.8-UP-SNAPSHOT</qingyun.smm.version>
        <qingyun.upload.version>3.1.8-UP-SNAPSHOT</qingyun.upload.version>
        <qingyun.import.plugin.version>1.1.1-UP-SNAPSHOT</qingyun.import.plugin.version>
        <qingyun.bigdata.version>3.0.11-UP-SNAPSHOT</qingyun.bigdata.version>
        <qingyun.renderer.version>2.2.4-UP-SNAPSHOT</qingyun.renderer.version>
        <qingyun.integration.version>1.1.2-SNAPSHOT</qingyun.integration.version>
        <qingyun.integration.client.version>1.1.0-UP-SNAPSHOT</qingyun.integration.client.version>
        <qingyun.settlement-interface.version>3.1.4-UP-SNAPSHOT</qingyun.settlement-interface.version>
        <qingyun.infrastructure.version>2.2.8-SNAPSHOT</qingyun.infrastructure.version>
        <qingyun.map.version>1.0.5-UP-SNAPSHOT</qingyun.map.version>
        <qingyun.weixin.version>1.2.1-UP-SNAPSHOT</qingyun.weixin.version>
        <shangou_platform_open_sdk.version>1.0.30.1-SNAPSHOT</shangou_platform_open_sdk.version>
        <squareup.okhttp3.version>4.9.1</squareup.okhttp3.version>
        <qingyun.settlement.version>3.3.3-UP-SNAPSHOT</qingyun.settlement.version>
        <qingyun.open.platform.version>1.0.0-SNAPSHOT</qingyun.open.platform.version>
        <qingyun.marketing.version>1.2.3-UP-SNAPSHOT</qingyun.marketing.version>
        <qingyun.gate.sign.version>3.1.1-SNAPSHOT</qingyun.gate.sign.version>
        <qingyun.print.version>2.0.9-UP-SNAPSHOT</qingyun.print.version>
        <qingyun.pay.version>3.1.6-UP-SNAPSHOT</qingyun.pay.version>
        <qingyun.tiot.platform.version>3.0.9-UP-SNAPSHOT</qingyun.tiot.platform.version>
        <qingyun.bom.order.version>1.0.4-UP-SNAPSHOT</qingyun.bom.order.version>
        <qingyun.report.version>4.0.5-UP-SNAPSHOT</qingyun.report.version>
        <qingyun.base.search.version>1.0.4-UP-SNAPSHOT</qingyun.base.search.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-basic</artifactId>
        </dependency>
        <!--使用 lombok 简化 Java 代码-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <!--            <version>1.16.16</version>-->
            <scope>provided</scope>
        </dependency>

        <!--kotlin-->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk7</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-common</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <version>2.9.4</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-basic</artifactId>
                <version>${qingyun.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-settlementTb-client</artifactId>
                <version>${qingyun.settlement.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-switch</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-metrics-client</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-mq</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-components-inventory</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-intermediate-business</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-test</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-jddj</artifactId>
                <version>${qingyun.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-elm</artifactId>
                <version>${qingyun.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-xiaoe</artifactId>
                <version>${qingyun.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-renderer</artifactId>
                <version>${qingyun.renderer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-bigdata-client</artifactId>
                <version>${qingyun.bigdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-import-plugin</artifactId>
                <version>${qingyun.import.plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-msg-client</artifactId>
                <version>${qingyun.msg.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-product-client</artifactId>
                <version>${qingyun.product.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-price-client</artifactId>
                <version>${qingyun.price.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-storage-client</artifactId>
                <version>${qingyun.storage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xs-user-client</artifactId>
                <version>${qingyun.xs.user.version}</version>
            </dependency>
            <!--  project version start   -->
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-cache</artifactId>
                <version>${qingyun.cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-common-client</artifactId>
                <version>${qingyun.common.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.pinshang.qingyun</groupId>-->
            <!--                <artifactId>qingyun-xs-pos-client</artifactId>-->
            <!--                <version>${qingyun.xs.pos.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-purchase-client</artifactId>
                <version>${qingyun.purchase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-purchase-service</artifactId>
                <version>${qingyun.purchase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-supplier-client</artifactId>
                <version>${qingyun.supplier.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-supplier-service</artifactId>
                <version>${qingyun.supplier.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-tiot-platform-client</artifactId>
                <version>${qingyun.tiot.platform.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-invoice-client</artifactId>
                <version>${qingyun.invoice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-sync</artifactId>
                <version>${qingyun.sync.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-client</artifactId>
                <version>${qingyun.order.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-search-client</artifactId>
                <version>${qingyun.base.search.version}</version>
            </dependency>

            <!--  project version end   -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-config</artifactId>
                <version>2.0.2.RELEASE</version>
            </dependency>

            <!-- core -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${org.spring.version}</version>
            </dependency>

            <!--Swagger2 - RESTful API文档-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>

            <!--spring web相关包-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <!--fastjson-->
            <!-- 使用qingyun-parent中定义的，不要在项目中定义 -->
            <!--<dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.6</version>
            </dependency>-->

            <!--mybatis依赖包-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.4.0</version>
            </dependency>
            <!--tk.mybatis-->
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>4.0.1</version>
            </dependency>
            <!-- pagehelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>

            <!--开发相关-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
                <version>${spring-boot.version}</version>
            </dependency>

            <!--数据库-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.41</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-smm-client</artifactId>
                <version>${qingyun.smm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-upload-client</artifactId>
                <version>${qingyun.upload.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign.form</groupId>
                        <artifactId>feign-form-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign.form</groupId>
                        <artifactId>feign-form</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-map-client</artifactId>
                <version>${qingyun.map.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-weixin-client</artifactId>
                <version>${qingyun.weixin.version}</version>
            </dependency>


            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-gate-sign</artifactId>
                <version>${qingyun.gate.sign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk7</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-common</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-print-client</artifactId>
                <version>${qingyun.print.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-pay-client</artifactId>
                <version>${qingyun.pay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-bom-order-client</artifactId>
                <version>${qingyun.bom.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-report-client</artifactId>
                <version>${qingyun.report.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <compilerPlugins>
                        <!-- Or "spring" for the Spring support -->
                        <plugin>spring</plugin>
                        <plugin>no-arg</plugin>
                    </compilerPlugins>

                    <pluginOptions>
                        <!-- Each annotation is placed on its own line -->
                        <!--<option>all-open:annotation=cn.apier.yim.common.com.yanjunhua.base.common.annotation.NeedNoArgConstructor</option>-->
                        <option>no-arg:annotation=com.yanjunhua.base.common.annotation.NeedNoArgConstructor</option>
                    </pluginOptions>
                    <jvmTarget>1.8</jvmTarget>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <!--                <executions>-->
                <!--                    &lt;!&ndash; 替换会被 maven 特别处理的 default-compile &ndash;&gt;-->
                <!--                    <execution>-->
                <!--                        <id>default-compile</id>-->
                <!--                        <phase>none</phase>-->
                <!--                    </execution>-->
                <!--                    &lt;!&ndash; 替换会被 maven 特别处理的 default-testCompile &ndash;&gt;-->
                <!--                    <execution>-->
                <!--                        <id>default-testCompile</id>-->
                <!--                        <phase>none</phase>-->
                <!--                    </execution>-->

                <!--                    <execution>-->
                <!--                        <id>java-compile</id>-->
                <!--                        <phase>compile</phase>-->
                <!--                        <goals> <goal>compile</goal> </goals>-->
                <!--                    </execution>-->
                <!--                    <execution>-->
                <!--                        <id>java-test-compile</id>-->
                <!--                        <phase>test-compile</phase>-->
                <!--                        <goals> <goal>testCompile</goal> </goals>-->
                <!--                    </execution>-->
                <!--                </executions>-->
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>

    </build>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>