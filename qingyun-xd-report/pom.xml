<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-parent</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xd-report</artifactId>
	<packaging>pom</packaging>
    <modules>
        <module>qingyun-xd-report-client</module>
<!--        <module>qingyun-xd-report-service</module>-->
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <wxpay.version>0.0.3</wxpay.version>
        <poi.version>4.1.1</poi.version>
        <poi-ooxml.version>4.1.1</poi-ooxml.version>
        <alipay.version>20170725114550</alipay.version>
        <px-dify-sdk.version>1.2.2-SNAPSHOT</px-dify-sdk.version>
<!--        <qingyun.product.version>3.3.5-SNAPSHOT</qingyun.product.version>-->
    </properties>

    <dependencies>
       
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-basic</artifactId>
                <version>${qingyun.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-switch</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-test</artifactId>
                <version>${qingyun.infrastructure.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
                <!-- <version>4.1.1</version>-->
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
                <!--<version>4.1.1</version>-->
            </dependency>
            <dependency>
                <groupId>com.github.wxpay</groupId>
                <artifactId>wxpay-sdk</artifactId>
                <version>${wxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-promotion-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-tms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-wms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-price-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-product-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-cms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-product-client</artifactId>
                <version>${qingyun.product.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-renderer</artifactId>
                <version>${qingyun.renderer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-print-client</artifactId>
                <version>${qingyun.print.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.11</version>
            </dependency>-->
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-recommend-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- AI 侧实体识别SDK  -->
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>px-dify-sdk</artifactId>
                <version>${px-dify-sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>