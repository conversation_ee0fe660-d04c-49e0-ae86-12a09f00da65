<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-xd-report</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
	</parent>
    <artifactId>qingyun-xd-report-service</artifactId>
    <properties>
        <java.version>1.8</java.version>
        <feign.form.version>3.8.0</feign.form.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-components-inventory</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-health-check</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
<!--            <version>${qingyun.infrastructure.version}</version>-->
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-switch</artifactId>
<!--            <version>${qingyun.infrastructure.version}</version>-->
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-metrics-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-bigdata-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-data-query</artifactId>
            <version>${qingyun.infrastructure.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-test</artifactId>
            <version>${qingyun.infrastructure.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-box</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>org.springframework.cloud</groupId>-->
            <!--<artifactId>spring-cloud-starter-config</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-common-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${springfox-swagger.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${swagger-models.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
            <version>${feign.form.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jolokia</groupId>
            <artifactId>jolokia-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-basic</artifactId>
            <version>${qingyun.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-shop-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xs-user-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-cms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-product-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-promotion-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-storage-client</artifactId>
            <version>${qingyun.storage.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-smm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-renderer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-upload-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-import-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-marketing-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-print-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-recommend-client</artifactId>
        </dependency>
        <!-- AI 侧实体识别SDK  -->
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>px-dify-sdk</artifactId>
            <version>1.2.2-SNAPSHOT</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>