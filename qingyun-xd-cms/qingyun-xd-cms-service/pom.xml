<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-cms</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
    </parent>
    <artifactId>qingyun-xd-cms-service</artifactId>
    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-product-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-health-check</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-components-inventory</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-switch</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-metrics-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-renderer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-box</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-common-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${springfox-swagger.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${swagger-models.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jolokia</groupId>
            <artifactId>jolokia-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-base-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-promotion-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-report-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-product-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-price-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-product-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-shop-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-wms-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-gee-remote-api</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-recommend-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-mq</artifactId>
            <version>${qingyun.mq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-storage-client</artifactId>
            <version>${qingyun.storage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-storage-admin-client</artifactId>
            <version>${qingyun.storage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xd-search-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-smm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <!-- 使用qingyun-parent中定义的，不要在项目中定义 -->
<!--            <version>1.2.62</version>-->
        </dependency>
        <!--  hutool工具包  -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.3.7</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-order-client</artifactId>
<!--            <version>2.1.5-SAPSHOT</version>-->
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-import-plugin</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-price-client</artifactId>
            <version>${qingyun.price.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-pay-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.pinshang.qingyun</groupId>
                    <artifactId>qingyun-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-xs-user-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-upload-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-pos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-marketing-client</artifactId>
        </dependency>

        <!-- AI 侧实体识别SDK  -->
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>px-dify-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>qingyun-infrastructure</groupId>
            <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-code-gen-maven-plugin</artifactId>
                <version>1.0.3-SNAPSHOT</version>
                <configuration>
                    <baseDir>${basedir}</baseDir>
                    <projectName>${project.name}</projectName>
                    <dataSource>
                        <driverName>com.mysql.jdbc.Driver</driverName>
                        <url>*******************************************</url>
                        <username>root</username>
                        <password>123456</password>
                    </dataSource>
                    <packageInfo>
                        <modelName>xd.cms</modelName>
                    </packageInfo>
                    <template>
                        <persistenceType>tk</persistenceType>
                    </template>
                    <strategy>
                        <!--
                          表生成策略：
                            1. 如果填写 property，则只生成填写的表
                            2. 如果不填写 property，则生成所有表
                          建议每次填写需要生成的表，避免生成不必要的表，造成以外
                        -->
                        <include>
                            <property>t_xd_zfb_coupon_commodity</property>
                        </include>
                    </strategy>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.11</version>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.velocity</groupId>
                        <artifactId>velocity</artifactId>
                        <version>1.7</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

        </plugins>
    </build>
</project>