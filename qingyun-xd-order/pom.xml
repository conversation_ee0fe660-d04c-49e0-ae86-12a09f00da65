<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-parent</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xd-order</artifactId>
	<packaging>pom</packaging>
    <modules>
        <module>qingyun-xd-order-client</module>
<!--        <module>qingyun-xd-order-service</module>-->
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <poi.version>4.1.1</poi.version>
        <poi-ooxml.version>4.1.1</poi-ooxml.version>
        <qingyun.xd.elm.order>3.2.7-UP-SNAPSHOT</qingyun.xd.elm.order>
        <apollo-client.version>1.8.0</apollo-client.version>
    </properties>

    <dependencies>
        <!--使用 lombok 简化 Java 代码-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>

            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-mq</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-metrics-client</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-switch</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-components-inventory</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-weixin-client</artifactId>
                <version>${qingyun.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-settlement-interface-client</artifactId>
                <version>${qingyun.settlement-interface.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo-client.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
                <!-- <version>4.1.1</version>-->
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
                <!--<version>4.1.1</version>-->
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-basic</artifactId>
                <version>${qingyun.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-promotion-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-tms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-wms-client</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-price-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-product-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-cms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-elm</artifactId>
                <version>${qingyun.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-jddj</artifactId>
                <version>${qingyun.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-smm-client</artifactId>
                <version>${qingyun.smm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-storage-client</artifactId>
                <version>${qingyun.storage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-map-client</artifactId>
                <version>${qingyun.map.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-renderer</artifactId>
                <version>${qingyun.renderer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xs-user-client</artifactId>
                <version>${qingyun.xs.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-elm-order-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-settlementTb-client</artifactId>
                <version>${qingyun.settlement.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-pay-client</artifactId>
                <version>${qingyun.pay.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>