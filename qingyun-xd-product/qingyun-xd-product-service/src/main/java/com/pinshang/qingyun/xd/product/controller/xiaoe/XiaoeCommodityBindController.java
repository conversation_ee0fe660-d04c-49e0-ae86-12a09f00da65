package com.pinshang.qingyun.xd.product.controller.xiaoe;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.xd.product.dto.xiaoe.*;
import com.pinshang.qingyun.xd.product.service.xiaoe.XiaoeCommodityBindService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 小鹅商品与清美商品绑定  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Api(tags = "小鹅商品与清美商品绑定", description = "小鹅商品与清美商品绑定")
@RestController
@RequestMapping("/xiaoeCommodityBind")
public class XiaoeCommodityBindController {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XiaoeCommodityBindService xiaoeCommodityBindService;

    /**
     * 小鹅商品与清美商品绑定表 列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "小鹅商品与清美商品绑定表 列表")
    public PageInfo<XiaoeCommodityBindODTO> page(@RequestBody XiaoeCommodityBindPageIDTO req) {
        return xiaoeCommodityBindService.page(req);
    }

    /**
     * 保存 小鹅商品与清美商品绑定表
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存 小鹅商品与清美商品绑定表")
    public Boolean save(@RequestBody XiaoeCommodityBindSaveIDTO req) {
        String lockKey = QYApplicationContext.redisKeyProfile + "xiaoeSave:" + req.getCommodityId() + "_" + req.getSkuId();
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                return xiaoeCommodityBindService.save(req);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.TRUE;
    }

    /**
     * 更新 截单时间
     */
    @PostMapping("/updateCutoffTime")
    @ApiOperation(value = "更新 截单时间")
    public Boolean updateCutoffTime(@RequestBody XiaoeCommodityBindUpdateIDTO req) {
        return xiaoeCommodityBindService.updateCutoffTime(req);
    }

    /**
     * 推送大仓发货
     */
    @PostMapping("/pushWarehouse")
    @ApiOperation(value = "推送大仓发货")
    public XiaoeCommodityPushResultODTO pushWarehouse(@RequestBody XiaoeCommodityPushIDTO req) {
        String lockKey = QYApplicationContext.redisKeyProfile + "xiaoePushWarehouse";
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                return xiaoeCommodityBindService.pushWarehouse(req);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }

    /**
     * 处理推送大仓结果
     */
    @PostMapping("/xiaoe/processPushResult")
    @ApiOperation(value = "处理推送大仓结果")
    public Boolean processPushResult(@RequestBody List<XiaoeTongPushOrderIDTO> pushResults) {
        return xiaoeCommodityBindService.processPushResult(pushResults);
    }

    /**
     * 查询小鹅通商品列表（分页）
     */
    @PostMapping("/xiaoe/queryGoodsList")
    @ApiOperation(value = "查询小鹅通商品列表（分页）")
    public PageInfo<XiaoeGoodsListODTO> queryGoodsList(@RequestBody XiaoeGoodsListPageIDTO req) {
        return xiaoeCommodityBindService.queryGoodsList(req);
    }

    /**
     * 查询小鹅通商品详情
     */
    @PostMapping("/xiaoe/queryGoodsDetail")
    @ApiOperation(value = "查询小鹅通商品详情")
    public List<XiaoeGoodsDetailODTO> queryGoodsDetail(@RequestBody XiaoeGoodsDetailIDTO req) {
        return xiaoeCommodityBindService.queryGoodsDetail(req);
    }

    /**
     * 刷新小鹅通商品详情
     */
    @PostMapping("/xiaoe/batchRefreshGoods")
    @ApiOperation(value = "刷新小鹅通商品详情")
    public Boolean batchRefreshGoods() {
        String lockKey = QYApplicationContext.redisKeyProfile + "xiaoeBatchRefreshGoods:" + FastThreadLocalUtil.getQY().getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                return xiaoeCommodityBindService.batchRefreshGoods();
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return Boolean.TRUE;
    }

}
