package com.pinshang.qingyun.xd.product.service.xiaoe;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopCommodityBusinessTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.elm.dto.XiaoeUpdatePushStatusIDTO;
import com.pinshang.qingyun.elm.serivice.XiaoeOrderClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.product.dto.CommodityODto;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.SaveShopCommodityIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommoditySaveDTO;
import com.pinshang.qingyun.shop.service.shopCommodity.SaveShopCommodityClientClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.dto.tob.TobCommodityWarehouseODTO;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.xd.product.dto.xiaoe.*;
import com.pinshang.qingyun.xd.product.enums.XiaoeCommodityBindOperateEnum;
import com.pinshang.qingyun.xd.product.mapper.XiaoeOrderItemMapper;
import com.pinshang.qingyun.xd.product.mapper.xiaoe.XiaoeCommodityBindMapper;
import com.pinshang.qingyun.xd.product.model.XiaoeOrderItem;
import com.pinshang.qingyun.xd.product.model.xiaoe.LogXiaoeCommodityBind;
import com.pinshang.qingyun.xd.product.model.xiaoe.XiaoeCommodityBind;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 小鹅商品与清美商品绑定表  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
@Service
public class XiaoeCommodityBindService {

    @Resource
    private ToBClient toBClient;

    @Resource
    private OrderClient orderClient;

    @Resource
    private XiaoeOrderClient xiaoeOrderClient;

    @Resource
    private CommodityClient commodityClient;

    @Resource
    private DictionaryClient dictionaryClient;

    @Resource
    private XiaoeGoodsService xiaoeGoodsService;

    @Resource
    private IMqSenderComponent mqSenderComponent;

    @Resource
    private XiaoeOrderItemMapper xiaoeOrderItemMapper;
    @Resource
    private SaveShopCommodityClientClient saveShopCommodityClientClient;

    @Resource
    private XiaoeCommodityBindMapper xiaoeCommodityBindMapper;

    private static final int BATCH_SIZE = 500;

    /**
     * 小鹅商品与清美商品绑定表 列表
     */
    public PageInfo<XiaoeCommodityBindODTO> page(XiaoeCommodityBindPageIDTO req) {
        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
            // 分页查询绑定关系数据
            List<XiaoeCommodityBindODTO> resultList = xiaoeCommodityBindMapper.list(req);

            // 填充B端库存依据
            buildInventory(resultList);
        });
    }

    /**
     * 保存 小鹅商品与清美商品绑定表
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(XiaoeCommodityBindSaveIDTO req) {
        // 校验业务逻辑
        checkSaveParam(req);

        if (!Objects.equals(req.getForceStatus(), YesOrNoEnums.YES.getCode())) {
            return Boolean.TRUE;
        }

        // 保存绑定信息
        XiaoeCommodityBind xiaoeCommodityBind = buildXiaoeCommodityBindDO(req);
        xiaoeCommodityBindMapper.insert(xiaoeCommodityBind);

        this.sendXiaoeCommodityBindLogMessage(xiaoeCommodityBind, XiaoeCommodityBindOperateEnum.BIND.getCode());
        return Boolean.TRUE;
    }

    /**
     * 更新 截单时间
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCutoffTime(XiaoeCommodityBindUpdateIDTO req) {
        // 更新业务逻辑 校验，数据组装，更新
        XiaoeCommodityBind xiaoeCommodityBind = xiaoeCommodityBindMapper.selectByPrimaryKey(req.getBindId());

        QYAssert.isTrue(Objects.nonNull(xiaoeCommodityBind), "未找到对应绑定关系");
        QYAssert.isTrue(Objects.isNull(xiaoeCommodityBind.getCutoffTime()), "已截单，不可重复截单");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        xiaoeCommodityBind.setCutoffTime(new Date());
        xiaoeCommodityBind.setUpdateId(tokenInfo.getUserId());
        xiaoeCommodityBindMapper.updateByPrimaryKeySelective(xiaoeCommodityBind);

        this.sendXiaoeCommodityBindLogMessage(xiaoeCommodityBind, XiaoeCommodityBindOperateEnum.CUTOFF.getCode());
        return Boolean.TRUE;
    }

    /**
     * 推送大仓发货
     */
    @Transactional(rollbackFor = Exception.class)
    public XiaoeCommodityPushResultODTO pushWarehouse(XiaoeCommodityPushIDTO req) {
        XiaoeCommodityBind xiaoeCommodityBind = xiaoeCommodityBindMapper.selectByPrimaryKey(req.getBindId());
        QYAssert.isTrue(Objects.nonNull(xiaoeCommodityBind), "未找到对应绑定关系");
        // 根据查询商品关联订单（品鲜订单状态≠已取消，订单是否已推送大仓=否，下单时间＜订单截单时间或订单截单时间=空”条件的行数是否＞0）
        XiaoeCommodityPushVO xiaoeOrderItemVO = BeanCloneUtils.copyTo(xiaoeCommodityBind, XiaoeCommodityPushVO.class);
        if (Objects.nonNull(xiaoeCommodityBind.getCutoffTime())) {
            xiaoeOrderItemVO.setCutoffTime(DateUtil.getDateFormate(xiaoeCommodityBind.getCutoffTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        // 查询可推送的数量
        BigDecimal unPushedQuantity = xiaoeOrderItemMapper.countUnPushedQuantity(xiaoeOrderItemVO);
        QYAssert.isTrue(Objects.nonNull(unPushedQuantity)
                && unPushedQuantity.compareTo(BigDecimal.ZERO) > 0, "当前选择商品没有可推送大仓的订单");

        // 查询order 是否可推送
        QYAssert.isTrue(BooleanUtils.isTrue(orderClient.enablePushXiaoetongOrder()), "暂时无法推送，请稍后重试");

        // 页面展示：获取推送汇总信息
        XiaoePushCommodityItemODTO commodityItem = xiaoeOrderItemMapper.countPushStatistics(xiaoeOrderItemVO);

        // 获取截单时间配置并验证送货日期
        if (StringUtils.isNotBlank(req.getDeliveryDate())) {
            validateDeliveryDateWithCutoffTime(req.getDeliveryDate());
        }

        // 返回结果
        XiaoeCommodityPushResultODTO odto = new XiaoeCommodityPushResultODTO();

        int pushMode = req.getForceStatus();
        if (Objects.equals(pushMode, 0)) {
            // 预览模式
            XiaoeCommodityPushODTO result = new XiaoeCommodityPushODTO();
            // 获取送货日期
            String deliveryDate = calculateDeliveryDate(getCutoffTimeStr());
            result.setDeliveryDate(deliveryDate);
            result.setCommodityItem(commodityItem);
            odto.setData(result);
            odto.setResultCode(XiaoeCommodityPushResultODTO.FIRST_PREVIEW);
            return odto;
        } else if (Objects.equals(pushMode, 1)) {
            // 库存验证推送
            return handleInventoryCheckPush(xiaoeCommodityBind, xiaoeOrderItemVO, commodityItem, req.getDeliveryDate(), odto);
        } else if (pushMode == 2) {
            // 直接推送
            executePush(xiaoeOrderItemVO, xiaoeCommodityBind, req.getDeliveryDate());
        }
        odto.setResultCode(XiaoeCommodityPushResultODTO.PUSH_COMPLETED);
        return odto;
    }

    /**
     * 获取小鹅通商品列表
     */
    public PageInfo<XiaoeGoodsListODTO> queryGoodsList(XiaoeGoodsListPageIDTO req) {
        return xiaoeGoodsService.page(req);
    }

    /**
     * 获取小鹅通商品详情（不展示已删除的）
     */
    public List<XiaoeGoodsDetailODTO> queryGoodsDetail(XiaoeGoodsDetailIDTO req) {
        return Optional.ofNullable(xiaoeGoodsService.queryGoodsDetail(req))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .filter(sku -> !YesOrNoEnums.YES.getCode().equals(sku.getIsDeleted()))
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }


    /**
     * 刷新小鹅通商品列表
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchRefreshGoods() {
        // 产品要求刷所有
        List<XiaoeCommodityBind> commodityBinds = xiaoeCommodityBindMapper.selectAll();
        if (CollectionUtils.isEmpty(commodityBinds)) {
            return Boolean.FALSE;
        }

        List<String> resourceIdList = commodityBinds.stream().map(XiaoeCommodityBind::getResourceId).collect(Collectors.toList());
        XiaoeGoodsDetailIDTO idto = new XiaoeGoodsDetailIDTO();
        idto.setResourceType(21);
        idto.setResourceIdList(resourceIdList);
        // 获取小鹅商品详情
        List<XiaoeGoodsDetailODTO> resultList = xiaoeGoodsService.queryGoodsDetail(idto);
        List<XiaoeGoodsDetailODTO> updateList = new ArrayList<>();

        if (CollectionUtils.isEmpty(resultList)) {
            return Boolean.FALSE;
        }
        // 将商品详情按资源ID、SPU ID和SKU ID映射为键值对
        Map<String, XiaoeGoodsDetailODTO> goodsDetailMap = new HashMap<>();
        for (XiaoeGoodsDetailODTO goodsDetail : resultList) {
            String key = goodsDetail.getResourceId() + ":" + goodsDetail.getSpuId() + ":" + goodsDetail.getSkuId();
            goodsDetailMap.put(key, goodsDetail);
        }

        // 遍历商品绑定列表，匹配并更新商品详情
        for (XiaoeCommodityBind commodityBind : commodityBinds) {
            String resourceId = commodityBind.getResourceId();
            String skuId = commodityBind.getSkuId();
            String spuId = commodityBind.getSpuId();
            String key = resourceId + ":" + spuId + ":" + skuId;

            // 如果找到匹配的需要更新的商品详情
            XiaoeGoodsDetailODTO matchedGoodsDetail = goodsDetailMap.get(key);
            if (matchedGoodsDetail != null && Objects.equals(matchedGoodsDetail.getIsDeleted(), YesOrNoEnums.NO.getCode())) {
                matchedGoodsDetail.setId(commodityBind.getId());
                updateList.add(matchedGoodsDetail);
            } else {
                // 不存在说明被删除了
                XiaoeGoodsDetailODTO goodsDetail = new XiaoeGoodsDetailODTO();
                goodsDetail.setId(commodityBind.getId());
                goodsDetail.setIsDeleted(YesOrNoEnums.YES.getCode());
                updateList.add(goodsDetail);
            }
        }

        if (CollectionUtils.isEmpty(updateList)) {
            return Boolean.FALSE;
        }

        // 批量也要分批次更新（产品说刷新所有，后续数据多了）
        int totalSize = updateList.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            // 获取当前批次
            int end = Math.min(i + BATCH_SIZE, totalSize);
            List<XiaoeGoodsDetailODTO> batchList = updateList.subList(i, end);

            // 批量更新
            xiaoeCommodityBindMapper.updateBindXiaoeGoods(batchList);
        }

        return Boolean.TRUE;
    }

    /**
     * 处理库存验证推送
     */
    private XiaoeCommodityPushResultODTO handleInventoryCheckPush(XiaoeCommodityBind binding,
                                                                  XiaoeCommodityPushVO pushVO,
                                                                  XiaoePushCommodityItemODTO commodityItem,
                                                                  String deliveryDate,
                                                                  XiaoeCommodityPushResultODTO odto) {
        // 查询库存信息
        Map<Long, BigDecimal> commodityMap = Collections.singletonMap(binding.getCommodityId(), commodityItem.getUnPushedQuantity());
        List<CommodityInventoryODTO> inventoryList = queryInventory(commodityMap, deliveryDate);
        CommodityInventoryODTO inventory = inventoryList.get(0);
        List<XiaoeOrderItem> orderItems = xiaoeOrderItemMapper.findPendingPushOrders(pushVO);

        BigDecimal availableQuantity = inventory.getInventoryQuantity();
        BigDecimal totalFrozenQuantity = BigDecimal.ZERO;
        int availableCount = 0;

        // 分析库存与订单匹配情况
        for (XiaoeOrderItem orderItem : orderItems) {
            BigDecimal requiredQuantity = orderItem.getRealQuantity();
            if (requiredQuantity.compareTo(availableQuantity) <= 0) {
                // 库存充足，更新数量
                availableQuantity = availableQuantity.subtract(requiredQuantity);
                totalFrozenQuantity = totalFrozenQuantity.add(requiredQuantity);
                availableCount++;
            } else {
                break;
            }
        }

        QYAssert.isTrue(availableCount > 0, "商品没有可用库存，请与大仓部门沟通确认清美优选商品入库情况");

        if (availableCount < orderItems.size()) {
            // 部分推送 - 返回预览信息
            XiaoeCommodityPushODTO result = new XiaoeCommodityPushODTO();
            result.setDeliveryDate(deliveryDate);
            result.setCommodityItem(commodityItem);
            result.setExpectedPushOrderCount(availableCount);
            result.setFrozenItemQuantity(totalFrozenQuantity);
            odto.setData(result);
            odto.setResultCode(XiaoeCommodityPushResultODTO.PARTIAL_PUSH);
            return odto;
        } else {
            // 库存充足 - 执行推送
            executePush(pushVO, binding, deliveryDate);
            odto.setResultCode(XiaoeCommodityPushResultODTO.PUSH_COMPLETED);
            return odto;
        }
    }

    /**
     * 执行订单推送到大仓
     */
    private void executePush(XiaoeCommodityPushVO pushVO, XiaoeCommodityBind binding, String deliveryDate) {
        List<XiaoeOrderItem> orderItems = xiaoeOrderItemMapper.findPendingPushOrders(pushVO);

        if (CollectionUtils.isEmpty(orderItems)) {
            log.warn("订单列表为空，无法推送订单到大仓！");
            return;
        }

        // 期初门店商品
        initializeShopCommodity(orderItems, binding);

        // 推送订单到大仓
        pushOrdersToWarehouse(orderItems, binding, deliveryDate);
    }

    /**
     * 处理推送结果
     */
    public Boolean processPushResult(List<XiaoeTongPushOrderIDTO> pushResults) {
        // 处理推送结果，更新成功推送的订单状态
        List<Long> pushSuccessOrderList = pushResults.stream()
                .filter(result -> Objects.equals(result.getOrderStauts(), YesOrNoEnums.YES.getCode()))
                .map(XiaoeTongPushOrderIDTO::getOrderId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(pushSuccessOrderList)) {
            String orderTime = pushResults.get(0).getOrderTime();
            Long bindId = pushResults.get(0).getBindId();
            XiaoeCommodityBind xiaoeCommodityBind = xiaoeCommodityBindMapper.selectByPrimaryKey(bindId);
            QYAssert.isTrue(Objects.nonNull(xiaoeCommodityBind), "未找到对应绑定关系");

            XiaoeUpdatePushStatusIDTO req = new XiaoeUpdatePushStatusIDTO();
            req.setCommodityId(xiaoeCommodityBind.getCommodityId());
            req.setSkuId(xiaoeCommodityBind.getSkuId());
            req.setSpuId(xiaoeCommodityBind.getSpuId());
            req.setDeliveryDate(orderTime);
            req.setXdOrderIds(pushSuccessOrderList);
            // 更新t_xd_order的预约时段 和订单关联表的推送状态
            xiaoeOrderClient.updatePushStatus(req);

            log.info("成功推送订单数量: {}", pushSuccessOrderList.size());

            // 发送操作日志
            this.sendXiaoeCommodityBindLogMessage(xiaoeCommodityBind, XiaoeCommodityBindOperateEnum.PUSH.getCode());

            return true;
        }
        return false;
    }

    /**
     * 期初门店商品
     */
    private void initializeShopCommodity(List<XiaoeOrderItem> orderItems, XiaoeCommodityBind binding) {
        List<ShopCommoditySaveDTO> shopCommodityList = new ArrayList<>();
        for (XiaoeOrderItem orderItem : orderItems) {
            ShopCommoditySaveDTO shopCommoditySaveDTO = ShopCommoditySaveDTO.initCommodityPrice(orderItem.getShopId(), orderItem.getCommodityId(), binding.getRetailPrice());
            shopCommoditySaveDTO.setWeightPrice(binding.getOrderPrice());
            shopCommodityList.add(shopCommoditySaveDTO);
        }
        SaveShopCommodityIDTO saveShopCommodityIDTO = new SaveShopCommodityIDTO();
        saveShopCommodityIDTO.setBusinessType(ShopCommodityBusinessTypeEnum.DEFAULT.getCode());
        saveShopCommodityIDTO.setBusinessBillCode(String.valueOf(orderItems.get(0).getXdOrderId()));
        saveShopCommodityIDTO.setShopCommodityList(shopCommodityList);
        saveShopCommodityIDTO.setOperatorId(FastThreadLocalUtil.getQY().getUserId());
        saveShopCommodityClientClient.asyncSaveShopCommodity(saveShopCommodityIDTO);
    }

    /**
     * 推送订单到大仓系统
     */
    private List<XiaoeTongPushOrderIDTO> pushOrdersToWarehouse(List<XiaoeOrderItem> orderItems, XiaoeCommodityBind binding, String deliveryDate) {
        List<XiaoeTongPushOrderIDTO> requestList = orderItems.stream()
                .map(orderItem -> buildPushOrderRequest(orderItem, binding, deliveryDate))
                .collect(Collectors.toList());
        return orderClient.createXiaoeTongOrder(requestList);
    }

    public List<XiaoeCommodityBind> listBindByXiaoeSkuId(String spuId, String skuId) {
        Example example = new Example(XiaoeCommodityBind.class);
        example.createCriteria()
                .andEqualTo("spuId", spuId)
                .andEqualTo("skuId", skuId);
        return xiaoeCommodityBindMapper.selectByExample(example);
    }

    private List<CommodityInventoryODTO> queryInventory(Map<Long, BigDecimal> commodityList, String deliveryDate) {
        // 查询库存接口
        CommodityInventoryIDTO idto = new CommodityInventoryIDTO();
        idto.setOrderTime(DateUtil.parseDate(deliveryDate, "yyyy-MM-dd"));
        List<CommodityInventoryDetailIDTO> inventoryDetailList = new ArrayList<>();
        for (Map.Entry<Long, BigDecimal> entry : commodityList.entrySet()) {
            CommodityInventoryDetailIDTO detail = new CommodityInventoryDetailIDTO();
            detail.setCommodityId(entry.getKey());
            detail.setQuantity(entry.getValue());
            detail.setLevel(ProductTypeEnum.NORMAL.getCode());
            inventoryDetailList.add(detail);
        }
        idto.setOrderCommodityList(inventoryDetailList);
        List<CommodityInventoryODTO> commodityInventoryList = toBClient.queryCommodityWithBomInventory(idto);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityInventoryList), "查询库存失败");
        return commodityInventoryList;
    }

    private Map<Long, TobCommodityWarehouseODTO> queryStockType(List<Long> commodityIdList) {
        // 查询库存依据
        Map<Long, TobCommodityWarehouseODTO> resultMap = toBClient.queryTobCommodityWarehouse(commodityIdList);
        QYAssert.isTrue(MapUtils.isNotEmpty(resultMap), "查询库存依据失败");
        return resultMap;
    }

    /**
     * 发送 小鹅商品与清美商品绑定表 日志
     */
    private void sendXiaoeCommodityBindLogMessage(XiaoeCommodityBind xiaoeCommodityBind, Integer operateType) {
        LogXiaoeCommodityBind logDTO = new LogXiaoeCommodityBind();

        CommodityDetailODTO commodity = commodityClient.findCommodityDetailByCommodityId(xiaoeCommodityBind.getCommodityId());
        logDTO.setCommodityId(xiaoeCommodityBind.getCommodityId());
        logDTO.setCommodityCode(commodity.getCommodityCode());
        logDTO.setCommodityName(commodity.getCommodityName());
        logDTO.setCommoditySpec(commodity.getCommoditySpec());

        logDTO.setResourceId(xiaoeCommodityBind.getResourceId());
        logDTO.setSpuId(xiaoeCommodityBind.getSpuId());
        logDTO.setSkuId(xiaoeCommodityBind.getSkuId());
        logDTO.setGoodsName(xiaoeCommodityBind.getGoodsName());
        logDTO.setSkuName(xiaoeCommodityBind.getSkuName());
        logDTO.setSkuSpecCode(xiaoeCommodityBind.getSkuSpecCode());

        logDTO.setOperationCode(operateType);
        logDTO.setOperationName(XiaoeCommodityBindOperateEnum.getName(operateType));

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        String employeeNumber = tokenInfo.getEmployeeNumber();
        String realName = tokenInfo.getRealName();
        logDTO.setCreateId(tokenInfo.getUserId());
        logDTO.setCreateName(employeeNumber + realName);
        logDTO.setCreateTime(new Date());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName", "t_log_xiaoe_commodity_bind");
        jsonObject.put("data", Collections.singletonList(logDTO));
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.LOG_CREATE_TOPIC, jsonObject.toJSONString(),
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.LOG_CREATE.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }

    // ==================== 辅助方法 ====================

    private XiaoeCommodityBind buildXiaoeCommodityBindDO(XiaoeCommodityBindSaveIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        XiaoeCommodityBind xiaoeCommodityBind = new XiaoeCommodityBind();
        xiaoeCommodityBind.setCommodityId(req.getCommodityId());
        xiaoeCommodityBind.setConversionFactor(req.getConversionFactor());
        xiaoeCommodityBind.setOrderPrice(req.getOrderPrice());
        xiaoeCommodityBind.setRetailPrice(req.getRetailPrice());
        xiaoeCommodityBind.setResourceId(req.getResourceId());
        xiaoeCommodityBind.setResourceType(req.getResourceType());
        xiaoeCommodityBind.setSpuId(req.getSpuId());
        xiaoeCommodityBind.setSkuId(req.getSkuId());

        XiaoeGoodsDetailIDTO idto = new XiaoeGoodsDetailIDTO();
        idto.setResourceType(req.getResourceType());
        idto.setResourceIdList(Collections.singletonList(req.getResourceId()));
        List<XiaoeGoodsDetailODTO> goodsDetailList = queryGoodsDetail(idto);
        XiaoeGoodsDetailODTO goodsDetail = goodsDetailList.stream()
                .filter(item -> item.getSkuId().equals(req.getSkuId()) && item.getSpuId().equals(req.getSpuId()))
                .findFirst()
                .orElse(null);
        QYAssert.isTrue(Objects.nonNull(goodsDetail) && Objects.equals(goodsDetail.getIsDeleted(), YesOrNoEnums.NO.getCode()), "小鹅通商品已删除");

        xiaoeCommodityBind.setGoodsName(goodsDetail.getGoodsName());
        xiaoeCommodityBind.setSkuName(goodsDetail.getSkuName());
        xiaoeCommodityBind.setSkuSpecCode(goodsDetail.getSkuSpecCode());
        xiaoeCommodityBind.setIsDeleted(goodsDetail.getIsDeleted());
        xiaoeCommodityBind.setSaleStatus(goodsDetail.getSaleStatus());
        xiaoeCommodityBind.setCreatedAt(DateUtil.parseDate(goodsDetail.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
        xiaoeCommodityBind.setCreateId(tokenInfo.getUserId());
        xiaoeCommodityBind.setUpdateId(tokenInfo.getUserId());
        xiaoeCommodityBind.setCreateTime(new Date());
        return xiaoeCommodityBind;
    }

    /**
     * 构建推送订单请求
     */
    private XiaoeTongPushOrderIDTO buildPushOrderRequest(XiaoeOrderItem orderItem, XiaoeCommodityBind binding, String deliveryDate) {
        XiaoeTongPushOrderIDTO request = new XiaoeTongPushOrderIDTO();
        request.setShopId(orderItem.getShopId());
        request.setOrderId(orderItem.getXdOrderId());
        request.setBindId(binding.getId());
        request.setCommodityId(orderItem.getCommodityId());
        request.setOrderQuantity(orderItem.getRealQuantity());
        request.setPrice(binding.getOrderPrice());
        request.setOrderTime(deliveryDate);
        request.setOrderCreateTime(DateUtil.getDateFormate(orderItem.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        return request;
    }

    private void checkSaveParam(XiaoeCommodityBindSaveIDTO req) {
        QYAssert.isTrue(Objects.nonNull(req.getCommodityId()), "请选择1个清美商品");
        QYAssert.isTrue(Objects.nonNull(req.getSkuId()), "请选择1个小鹅通商品规格");
        QYAssert.isTrue(Objects.nonNull(req.getConversionFactor()), "请填写转换系数");
        QYAssert.isTrue(Objects.nonNull(req.getOrderPrice()), "请填写门店向总部的订货价");
        QYAssert.isTrue(req.getOrderPrice().compareTo(new BigDecimal("0.01")) >= 0 &&
                        req.getOrderPrice().compareTo(new BigDecimal("999999.99")) <= 0 &&
                        req.getOrderPrice().scale() <= 2,
                "门店向总部的订货价请输入0.01-999999.99之间数字，最多保留2位小数");

        QYAssert.isTrue(Objects.nonNull(req.getRetailPrice()), "请填写门店期初零售价");
        QYAssert.isTrue(req.getRetailPrice().compareTo(new BigDecimal("0.01")) >= 0 &&
                        req.getRetailPrice().compareTo(new BigDecimal("999999.99")) <= 0 &&
                        req.getRetailPrice().scale() <= 2,
                "门店期初零售价请输入0.01-999999.99之间数字，最多保留2位小数");

        CommodityODto commodity = commodityClient.getCommodityDetailById(req.getCommodityId(), FastThreadLocalUtil.getQY().getEnterpriseId());
        QYAssert.isTrue(Objects.nonNull(commodity) && commodity.getIsWeight() != 1, "清美优选商品不可添加称重商品");
        QYAssert.isTrue(commodity.getLogisticsModel() != 0, "清美优选商品不可添加直送商品");

        Map<Long, TobCommodityWarehouseODTO> resultMap = queryStockType(Collections.singletonList(req.getCommodityId()));
        QYAssert.isTrue(MapUtils.isNotEmpty(resultMap) && Objects.nonNull(resultMap.get(req.getCommodityId()).getStockType()), "清美优选商品不可添加未设置库存依据商品");

        List<XiaoeCommodityBind> bindList = this.listBindByXiaoeSkuId(req.getSpuId(), req.getSkuId());
        QYAssert.isTrue(CollectionUtils.isEmpty(bindList), "当前选择小鹅通商品规格与品鲜商品绑定过了，不可再次绑定");
    }

    private void buildInventory(List<XiaoeCommodityBindODTO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<Long> commodityIdList = resultList.stream()
                .map(XiaoeCommodityBindODTO::getCommodityId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, TobCommodityWarehouseODTO> resultMap = queryStockType(commodityIdList);

        // 遍历结果列表，设置库存信息
        for (XiaoeCommodityBindODTO bind : resultList) {
            TobCommodityWarehouseODTO inventory = resultMap.get(bind.getCommodityId());
            if (inventory != null) {
                bind.setStockType(inventory.getStockType());
            }
        }
    }

    // ==================== 截单时间验证相关方法 ====================

    /**
     * 验证送货日期是否符合截单时间要求
     *
     * @param expectedDeliveryDate 前端传入的期望送货日期，格式为 yyyy-MM-dd
     */
    private void validateDeliveryDateWithCutoffTime(String expectedDeliveryDate) {

        String cutoffTimeStr = getCutoffTimeStr();

        // 验证日期格式
        validateDateFormat(expectedDeliveryDate);

        // 计算应该的送货日期
        String calculatedDateStr = calculateDeliveryDate(cutoffTimeStr);

        // 验证前端传入的送货日期是否与计算出的送货日期一致
        QYAssert.isTrue(expectedDeliveryDate.equals(calculatedDateStr), "当前操作已过大仓截单时间，送货日期有变化，请重新操作");
        log.info("送货日期验证通过，截单时间: {}, 送货日期: {}", cutoffTimeStr, expectedDeliveryDate);
    }

    private String getCutoffTimeStr() {
        // 获取截单时间配置
        DictionaryODTO dictionary = dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time");
        QYAssert.isTrue(Objects.nonNull(dictionary) && StringUtils.isNotBlank(dictionary.getOptionValue()), "小鹅通推送大仓截单时间字典未配置");
        return dictionary.getOptionValue();
    }

    /**
     * 验证日期格式是否正确
     *
     * @param dateStr 日期字符串
     */
    private void validateDateFormat(String dateStr) {
        QYAssert.isTrue(StringUtils.isNotBlank(dateStr), "送货日期不能为空");

        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("送货日期格式错误，请使用 yyyy-MM-dd 格式");
        }
    }

    /**
     * 根据截单时间计算实际送货日期
     *
     * @param cutoffTimeStr 截单时间，格式如 "19:00"
     * @return 计算出的送货日期
     */
    private String calculateDeliveryDate(String cutoffTimeStr) {
        try {
            LocalTime cutoffTime = LocalTime.parse(cutoffTimeStr, DateTimeFormatter.ofPattern("HH:mm"));
            LocalDateTime now = LocalDateTime.now();
            LocalTime currentTime = now.toLocalTime();
            LocalDate today = now.toLocalDate();
            LocalDate deliveryDate;
            // 截单时间逻辑：
            // 1. 如果当前时间在截单时间之前，送货日期为明天
            // 2. 如果当前时间在截单时间之后，送货日期为后天
            if (currentTime.isBefore(cutoffTime)) {
                deliveryDate = today.plusDays(1);
            } else {
                deliveryDate = today.plusDays(2);
            }
            return deliveryDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("截单时间格式错误，请使用 HH:mm 格式，如 19:00");
        }
    }
}
