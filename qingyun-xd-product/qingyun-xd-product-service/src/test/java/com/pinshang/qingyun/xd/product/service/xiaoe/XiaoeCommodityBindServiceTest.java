package com.pinshang.qingyun.xd.product.service.xiaoe;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.elm.serivice.XiaoeOrderClient;
import com.pinshang.qingyun.infrastructure.mq.MqSenderComponent;
import com.pinshang.qingyun.infrastructure.test.BaseDbUnitTest;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.shop.service.shopCommodity.SaveShopCommodityClientClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.xd.product.dto.xiaoe.*;
import com.pinshang.qingyun.xd.product.mapper.XiaoeOrderItemMapper;
import com.pinshang.qingyun.xd.product.mapper.xiaoe.XiaoeCommodityBindMapper;
import com.pinshang.qingyun.xd.product.model.XiaoeOrderItem;
import com.pinshang.qingyun.xd.product.model.xiaoe.XiaoeCommodityBind;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * XiaoeCommodityBindService 单元测试
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class XiaoeCommodityBindServiceTest extends BaseDbUnitTest {

    @InjectMocks
    private XiaoeCommodityBindService xiaoeCommodityBindService;

    @Mock
    private XiaoeCommodityBindMapper xiaoeCommodityBindMapper;

    @Mock
    private XiaoeOrderItemMapper xiaoeOrderItemMapper;

    @Autowired
    private XiaoeCommodityBindMapper xiaoeCommodityBindMapper2;

    @Autowired
    private XiaoeOrderItemMapper xiaoeOrderItemMapper2;

    @Mock
    private ToBClient toBClient;

    @Mock
    private OrderClient orderClient;

    @Mock
    private DictionaryClient dictionaryClient;

    @Mock
    private SaveShopCommodityClientClient saveShopCommodityClientClient;

    @Mock
    private CommodityClient commodityClient;

    @Mock
    private XiaoeOrderClient xiaoeOrderClient;

    @Mock
    private MqSenderComponent mqSenderComponent;

    private XiaoeCommodityPushIDTO pushRequest;
    private XiaoeCommodityBind mockBinding;
    private XiaoePushCommodityItemODTO mockCommodityItem;

//    @Override
//    protected List<String> getInitSqlScripts() {
//        // 指定自定义初始化脚本
//        return Arrays.asList(
//                "sql/clean.sql",
//                "sql/create_tables.sql"
//        );
//    }

    @BeforeEach
    void setUp() {
        // 准备测试数据
        pushRequest = new XiaoeCommodityPushIDTO();
        pushRequest.setBindId(randomLong());
        pushRequest.setForceStatus(0); // 预览模式
        LocalDate currentDate = LocalDate.now();
        LocalDate t1Date = currentDate.plusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        pushRequest.setDeliveryDate(t1Date.format(formatter));

        // 模拟绑定关系
        mockBinding = new XiaoeCommodityBind();
        mockBinding.setId(randomLong());
        mockBinding.setCommodityId(randomLong());
        mockBinding.setResourceId(randomString());
        mockBinding.setSpuId(randomString());
        mockBinding.setSkuId(randomString());
        mockBinding.setCutoffTime(new Date());

        // 模拟商品推送统计信息
        mockCommodityItem = new XiaoePushCommodityItemODTO();
        mockCommodityItem.setCommodityCode(randomString());
        mockCommodityItem.setCommodityName(randomString());
        mockCommodityItem.setTotalQuantity(new BigDecimal("10"));
        mockCommodityItem.setUnPushedQuantity(new BigDecimal("5"));
        mockCommodityItem.setPushedQuantity(new BigDecimal("5"));
        mockCommodityItem.setPushStatus(1); // 部分推送

    }

    @Test
    void testPushWarehouse_BindingNotFound_ShouldThrowException() {
        // 准备参数
        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(null);

        // 调用
        BizLogicException exception = assertThrows(BizLogicException.class,
                () -> xiaoeCommodityBindService.pushWarehouse(pushRequest));

        // 断言
        assertEquals("未找到对应绑定关系", exception.getMessage());
        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushRequest.getBindId());
    }

    @Test
    void testPushWarehouse_NoUnpushedQuantity_ShouldThrowException() {
        // 准备参数
        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        when(xiaoeOrderItemMapper.countUnPushedQuantity(any(XiaoeCommodityPushVO.class)))
                .thenReturn(BigDecimal.ZERO);

        // 调用
        BizLogicException exception = assertThrows(BizLogicException.class,
                () -> xiaoeCommodityBindService.pushWarehouse(pushRequest));

        // 断言
        assertEquals("当前选择商品没有可推送大仓的订单", exception.getMessage());
        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushRequest.getBindId());
        verify(xiaoeOrderItemMapper, times(1)).countUnPushedQuantity(any(XiaoeCommodityPushVO.class));
    }

    @Test
    void testPushWarehouse_PreviewMode_ShouldReturnPreviewResult() {
        // 准备参数
        pushRequest.setForceStatus(0); // 预览模式

        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        doReturn(BigDecimal.valueOf(5)).when(xiaoeOrderItemMapper).countUnPushedQuantity(any(XiaoeCommodityPushVO.class));
        when(xiaoeOrderItemMapper.countPushStatistics(any(XiaoeCommodityPushVO.class))).thenReturn(mockCommodityItem);
        // Mock 字典服务
        DictionaryODTO mockDictionary = new DictionaryODTO();
        mockDictionary.setOptionValue("19:00");
        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
                .thenReturn(mockDictionary);

        // 调用
        XiaoeCommodityPushResultODTO result = xiaoeCommodityBindService.pushWarehouse(pushRequest);

        // 断言
        assertNotNull(result);
        assertEquals(XiaoeCommodityPushResultODTO.FIRST_PREVIEW, result.getResultCode());
        assertNotNull(result.getData());
        XiaoeCommodityPushODTO data = (XiaoeCommodityPushODTO) result.getData();
        assertNotNull(data.getDeliveryDate());
        assertEquals(mockCommodityItem, data.getCommodityItem());

        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushRequest.getBindId());
        verify(xiaoeOrderItemMapper, times(1)).countUnPushedQuantity(any(XiaoeCommodityPushVO.class));
        verify(xiaoeOrderItemMapper, times(1)).countPushStatistics(any(XiaoeCommodityPushVO.class));
    }

    @Test
    void testPushWarehouse_InventoryCheckMode_InsufficientInventory_ShouldThrowException() {
        // 准备参数
        pushRequest.setForceStatus(1); // 库存验证推送

        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        when(xiaoeOrderItemMapper.countUnPushedQuantity(any(XiaoeCommodityPushVO.class)))
                .thenReturn(new BigDecimal("5"));
        when(xiaoeOrderItemMapper.countPushStatistics(any(XiaoeCommodityPushVO.class)))
                .thenReturn(mockCommodityItem);

        // Mock 库存不足
        CommodityInventoryODTO inventory = new CommodityInventoryODTO();
        inventory.setInventoryQuantity(BigDecimal.ZERO);
        when(toBClient.queryCommodityWithBomInventory(any())).thenReturn(Collections.singletonList(inventory));

        List<XiaoeOrderItem> orderItems = createMockOrderItems(2);
        when(xiaoeOrderItemMapper.findPendingPushOrders(any(XiaoeCommodityPushVO.class)))
                .thenReturn(orderItems);

        // 调用
        // Mock 字典服务
        DictionaryODTO mockDictionary = new DictionaryODTO();
        mockDictionary.setOptionValue("19:00");
        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
                .thenReturn(mockDictionary);

        BizLogicException exception = assertThrows(BizLogicException.class,
                () -> xiaoeCommodityBindService.pushWarehouse(pushRequest));

        // 断言
        assertEquals("商品没有可用库存，请与大仓部门沟通确认清美优选商品入库情况", exception.getMessage());
        verify(toBClient, times(1)).queryCommodityWithBomInventory(any());
    }

    @Test
    void testPushWarehouse_InventoryCheckMode_PartialPush_ShouldReturnPartialResult() {
        // 准备参数
        pushRequest.setForceStatus(1); // 库存验证推送

        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        when(xiaoeOrderItemMapper.countUnPushedQuantity(any(XiaoeCommodityPushVO.class)))
                .thenReturn(new BigDecimal("5"));
        when(xiaoeOrderItemMapper.countPushStatistics(any(XiaoeCommodityPushVO.class)))
                .thenReturn(mockCommodityItem);

        // Mock 库存部分满足 - 库存只有3个，但需要5个订单
        CommodityInventoryODTO inventory = new CommodityInventoryODTO();
        inventory.setInventoryQuantity(new BigDecimal("3")); // 库存只有3个
        when(toBClient.queryCommodityWithBomInventory(any())).thenReturn(Collections.singletonList(inventory));

        // Mock 5个订单，每个需要1个商品
        List<XiaoeOrderItem> orderItems = createMockOrderItems(5);
        when(xiaoeOrderItemMapper.findPendingPushOrders(any(XiaoeCommodityPushVO.class)))
                .thenReturn(orderItems);

        // Mock 字典服务
        DictionaryODTO mockDictionary = new DictionaryODTO();
        mockDictionary.setOptionValue("19:00");
        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
                .thenReturn(mockDictionary);

        // 调用
        XiaoeCommodityPushResultODTO result = xiaoeCommodityBindService.pushWarehouse(pushRequest);

        // 断言
        assertNotNull(result);
        assertEquals(XiaoeCommodityPushResultODTO.PARTIAL_PUSH, result.getResultCode());
        assertNotNull(result.getData());

        XiaoeCommodityPushODTO data = (XiaoeCommodityPushODTO) result.getData();
        assertEquals(3, data.getExpectedPushOrderCount()); // 只能推送3个订单
        assertEquals(new BigDecimal("3"), data.getFrozenItemQuantity()); // 冻结3个商品
        assertEquals(pushRequest.getDeliveryDate(), data.getDeliveryDate());
        assertEquals(mockCommodityItem, data.getCommodityItem());

        verify(toBClient, times(1)).queryCommodityWithBomInventory(any());
        verify(xiaoeOrderItemMapper, times(1)).findPendingPushOrders(any(XiaoeCommodityPushVO.class));
    }

    @Test
    void testPushWarehouse_InventoryCheckMode_FullPush_ShouldExecutePush() {
        // 准备参数
        pushRequest.setForceStatus(1); // 库存验证推送

        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        when(xiaoeOrderItemMapper.countUnPushedQuantity(any(XiaoeCommodityPushVO.class)))
                .thenReturn(new BigDecimal("3"));
        when(xiaoeOrderItemMapper.countPushStatistics(any(XiaoeCommodityPushVO.class)))
                .thenReturn(mockCommodityItem);

        // Mock 库存充足 - 库存有10个，需要3个订单
        CommodityInventoryODTO inventory = new CommodityInventoryODTO();
        inventory.setInventoryQuantity(new BigDecimal("10")); // 库存充足
        when(toBClient.queryCommodityWithBomInventory(any())).thenReturn(Collections.singletonList(inventory));

        // Mock 3个订单，每个需要1个商品
        List<XiaoeOrderItem> orderItems = createMockOrderItems(3);
        when(xiaoeOrderItemMapper.findPendingPushOrders(any(XiaoeCommodityPushVO.class)))
                .thenReturn(orderItems);

        // Mock 字典服务
        DictionaryODTO mockDictionary = new DictionaryODTO();
        mockDictionary.setOptionValue("19:00");
        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
                .thenReturn(mockDictionary);

        // Mock 推送相关服务
        List<XiaoeTongPushOrderIDTO> mockOrderList = new ArrayList<>();
        XiaoeTongPushOrderIDTO pushOrderDTO = new XiaoeTongPushOrderIDTO();
        pushOrderDTO.setShopId(randomLong());
        pushOrderDTO.setOrderId(randomLong());
        pushOrderDTO.setOrderTime(randomString());
        pushOrderDTO.setCommodityId(randomLong());
        pushOrderDTO.setOrderQuantity(randomBigDecimal());
        pushOrderDTO.setPrice(randomBigDecimal());
        pushOrderDTO.setOrderCreateTime(randomString());
        pushOrderDTO.setOrderStauts(1); // 推送成功
        mockOrderList.add(pushOrderDTO);

        when(orderClient.createXiaoeTongOrder(any())).thenReturn(mockOrderList);
        when(commodityClient.findCommodityDetailByCommodityId(any())).thenReturn(new CommodityDetailODTO());

        // 调用
        XiaoeCommodityPushResultODTO result = xiaoeCommodityBindService.pushWarehouse(pushRequest);

        // 断言
        assertNotNull(result);
        assertEquals(XiaoeCommodityPushResultODTO.PUSH_COMPLETED, result.getResultCode());

        verify(toBClient, times(1)).queryCommodityWithBomInventory(any());
        verify(xiaoeOrderItemMapper, times(2)).findPendingPushOrders(any(XiaoeCommodityPushVO.class)); // 一次验证库存，一次执行推送
        verify(orderClient, times(1)).createXiaoeTongOrder(any());
        verify(commodityClient, times(1)).findCommodityDetailByCommodityId(any());
    }

    @Test
    void testPushWarehouse_DirectPushMode_ShouldExecutePush() {
        // 准备参数
        pushRequest.setForceStatus(2); // 直接推送

        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushRequest.getBindId())).thenReturn(mockBinding);
        when(xiaoeOrderItemMapper.countUnPushedQuantity(any(XiaoeCommodityPushVO.class)))
                .thenReturn(new BigDecimal("5"));
        when(xiaoeOrderItemMapper.countPushStatistics(any(XiaoeCommodityPushVO.class)))
                .thenReturn(mockCommodityItem);

        // Mock 订单列表
        when(xiaoeOrderItemMapper.findPendingPushOrders(any(XiaoeCommodityPushVO.class)))
                .thenReturn(createMockOrderItems(2));

        // Mock 字典服务
        DictionaryODTO mockDictionary = new DictionaryODTO();
        mockDictionary.setOptionValue("19:00");
        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
                .thenReturn(mockDictionary);

        List<XiaoeTongPushOrderIDTO> mockOrderList = new ArrayList<>();
        XiaoeTongPushOrderIDTO idto = new XiaoeTongPushOrderIDTO();
        idto.setShopId(randomLong());
        idto.setOrderId(randomLong());
        idto.setOrderTime(randomString());
        idto.setCommodityId(randomLong());
        idto.setOrderQuantity(randomBigDecimal());
        idto.setPrice(randomBigDecimal());
        idto.setOrderCreateTime(randomString());
        idto.setOrderStauts(1);
        mockOrderList.add(idto);

        when(orderClient.createXiaoeTongOrder(any())).thenReturn(mockOrderList);

        when(commodityClient.findCommodityDetailByCommodityId(any())).thenReturn(new CommodityDetailODTO());

        XiaoeCommodityPushResultODTO result = xiaoeCommodityBindService.pushWarehouse(pushRequest);

        // 断言
        assertNotNull(result);
        assertEquals(XiaoeCommodityPushResultODTO.PUSH_COMPLETED, result.getResultCode());
    }

//    /**
//     * 测试真实SQL查询 - 验证SQL是否正确执行
//     */
//    @Test
//    @Sql("/sql/create_tables.sql")
//    void testPushWarehouse_WithRealDatabase_ShouldExecuteSQL() {
//        // 准备参数 - 使用数据库中真实存在的数据
//        XiaoeCommodityPushIDTO realRequest = new XiaoeCommodityPushIDTO();
//        realRequest.setBindId(1L); // 使用create_tables.sql中插入的测试数据
//        realRequest.setForceStatus(0); // 预览模式
//        realRequest.setDeliveryDate("2025-07-22");
//
//        // Mock 外部依赖，但保留数据库查询
//        DictionaryODTO mockDictionary = new DictionaryODTO();
//        mockDictionary.setOptionValue("19:00");
//        when(dictionaryClient.getDictionaryByCode("xiaoe_warehouse_push_cutoff_time"))
//                .thenReturn(mockDictionary);
//
//        // 调用 - 这里会执行真实的SQL查询
//        XiaoeCommodityPushResultODTO result = xiaoeCommodityBindService.pushWarehouse(realRequest);
//
//        // 断言 - 验证SQL查询结果
//        assertNotNull(result);
//        assertEquals(XiaoeCommodityPushResultODTO.FIRST_PREVIEW, result.getResultCode());
//        assertNotNull(result.getData());
//        assertNotNull(result.getData().getDeliveryDate());
//        // 验证数据库查询确实执行了
//        // 通过查询绑定关系验证SQL执行
//        XiaoeCommodityBind binding = xiaoeCommodityBindMapper.selectByPrimaryKey(1L);
//        assertNotNull(binding);
//        assertEquals(Long.valueOf(100), binding.getCommodityId());
//        assertEquals("resource123", binding.getResourceId());
//
//        // 验证统计查询SQL
//        XiaoeCommodityPushVO pushVO = new XiaoeCommodityPushVO();
//        pushVO.setCommodityId(100L);
//        pushVO.setResourceId("resource123");
//        pushVO.setSpuId("spu123");
//        pushVO.setSkuId("sku123");
//
//        BigDecimal unPushedQuantity = xiaoeOrderItemMapper.countUnPushedQuantity(pushVO);
//        assertNotNull(unPushedQuantity);
//        assertTrue(unPushedQuantity.compareTo(BigDecimal.ZERO) >= 0);
//
//        XiaoePushCommodityItemODTO statistics = xiaoeOrderItemMapper.countPushStatistics(pushVO);
//        assertNotNull(statistics);
//
//        log.info("SQL验证完成 - 未推送数量: {}, 统计信息: {}", unPushedQuantity, statistics);
//    }

    /**
     * 测试 processPushResult 方法 - 成功推送的情况
     */
    @Test
    void testProcessPushResult_SuccessPush_ShouldReturnTrue() {
        // 准备参数
        List<XiaoeTongPushOrderIDTO> pushResults = createMockPushResults(3, 1); // 3个订单，1个成功
        
        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushResults.get(0).getBindId()))
                .thenReturn(mockBinding);
        when(xiaoeOrderClient.updatePushStatus(any())).thenReturn(true);

        // 调用
        Boolean result = xiaoeCommodityBindService.processPushResult(pushResults);

        // 断言
        assertTrue(result);
        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushResults.get(0).getBindId());
        verify(xiaoeOrderClient, times(1)).updatePushStatus(any());
        verify(mqSenderComponent, times(1)).sendMessage(any(), any());
    }

    /**
     * 测试 processPushResult 方法 - 全部推送失败的情况
     */
    @Test
    void testProcessPushResult_AllPushFailed_ShouldReturnFalse() {
        // 准备参数 - 所有订单推送失败
        List<XiaoeTongPushOrderIDTO> pushResults = createMockPushResults(3, 0); // 3个订单，0个成功

        // 调用
        Boolean result = xiaoeCommodityBindService.processPushResult(pushResults);

        // 断言
        assertFalse(result);
        verify(xiaoeCommodityBindMapper, never()).selectByPrimaryKey(any());
        verify(xiaoeOrderClient, never()).updatePushStatus(any());
        verify(mqSenderComponent, never()).sendMessage(any(), any());
    }

    /**
     * 测试 processPushResult 方法 - 绑定关系不存在的情况
     */
    @Test
    void testProcessPushResult_BindingNotFound_ShouldThrowException() {
        // 准备参数
        List<XiaoeTongPushOrderIDTO> pushResults = createMockPushResults(2, 1); // 2个订单，1个成功
        
        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushResults.get(0).getBindId()))
                .thenReturn(null);

        // 调用并断言
        BizLogicException exception = assertThrows(BizLogicException.class,
                () -> xiaoeCommodityBindService.processPushResult(pushResults));

        assertEquals("未找到对应绑定关系", exception.getMessage());
        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushResults.get(0).getBindId());
        verify(xiaoeOrderClient, never()).updatePushStatus(any());
    }

    /**
     * 测试 processPushResult 方法 - 空推送结果列表的情况
     */
    @Test
    void testProcessPushResult_EmptyPushResults_ShouldReturnFalse() {
        // 准备参数
        List<XiaoeTongPushOrderIDTO> pushResults = new ArrayList<>();

        // 调用
        Boolean result = xiaoeCommodityBindService.processPushResult(pushResults);

        // 断言
        assertFalse(result);
        verify(xiaoeCommodityBindMapper, never()).selectByPrimaryKey(any());
        verify(xiaoeOrderClient, never()).updatePushStatus(any());
        verify(mqSenderComponent, never()).sendMessage(any(), any());
    }

    /**
     * 测试 processPushResult 方法 - 部分推送成功的情况
     */
    @Test
    void testProcessPushResult_PartialPushSuccess_ShouldReturnTrue() {
        // 准备参数 - 5个订单，3个成功
        List<XiaoeTongPushOrderIDTO> pushResults = createMockPushResults(5, 3);
        
        when(xiaoeCommodityBindMapper.selectByPrimaryKey(pushResults.get(0).getBindId()))
                .thenReturn(mockBinding);
        when(xiaoeOrderClient.updatePushStatus(any())).thenReturn(true);

        // 调用
        Boolean result = xiaoeCommodityBindService.processPushResult(pushResults);

        // 断言
        assertTrue(result);
        verify(xiaoeCommodityBindMapper, times(1)).selectByPrimaryKey(pushResults.get(0).getBindId());
        verify(xiaoeOrderClient, times(1)).updatePushStatus(any());
        
        // 验证传递给 updatePushStatus 的参数
        verify(xiaoeOrderClient).updatePushStatus(argThat(req -> {
            assertEquals(mockBinding.getCommodityId(), req.getCommodityId());
            assertEquals(mockBinding.getSkuId(), req.getSkuId());
            assertEquals(mockBinding.getSpuId(), req.getSpuId());
            assertEquals(pushResults.get(0).getOrderTime(), req.getDeliveryDate());
            assertEquals(3, req.getXdOrderIds().size()); // 只有3个成功的订单
            return true;
        }));
        
        verify(mqSenderComponent, times(1)).sendMessage(any(), any());
    }

    /**
     * 创建模拟推送结果列表
     * @param totalCount 总订单数
     * @param successCount 成功推送的订单数
     * @return 推送结果列表
     */
    private List<XiaoeTongPushOrderIDTO> createMockPushResults(int totalCount, int successCount) {
        List<XiaoeTongPushOrderIDTO> pushResults = new ArrayList<>();
        
        for (int i = 0; i < totalCount; i++) {
            XiaoeTongPushOrderIDTO pushResult = new XiaoeTongPushOrderIDTO();
            pushResult.setOrderId(randomLong());
            pushResult.setBindId(mockBinding.getId());
            pushResult.setOrderTime("2025-01-15");
            pushResult.setCommodityId(mockBinding.getCommodityId());
            pushResult.setOrderQuantity(BigDecimal.ONE);
            pushResult.setPrice(new BigDecimal("10.00"));
            pushResult.setOrderCreateTime("2025-01-15 10:00:00");
            
            // 前 successCount 个订单推送成功，其余失败
            if (i < successCount) {
                pushResult.setOrderStauts(1); // 推送成功
            } else {
                pushResult.setOrderStauts(0); // 推送失败
            }
            
            pushResults.add(pushResult);
        }
        
        return pushResults;
    }

    /**
     * 创建模拟订单项列表
     */
    private List<XiaoeOrderItem> createMockOrderItems(int count) {
        XiaoeOrderItem[] items = new XiaoeOrderItem[count];
        for (int i = 0; i < count; i++) {
            XiaoeOrderItem orderItem = new XiaoeOrderItem();
            orderItem.setId(randomLong());
            orderItem.setXdOrderId(randomLong());
            orderItem.setShopId(randomLong());
            orderItem.setCommodityId(mockBinding.getCommodityId());
            orderItem.setRealQuantity(BigDecimal.ONE);
            orderItem.setStatus(0); // 未推送
            orderItem.setCreateTime(new Date());
            items[i] = orderItem;
        }
        return Arrays.asList(items);
    }
}
