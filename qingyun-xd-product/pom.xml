<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-parent</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xd-product</artifactId>
<!--    <version>1.0.5-SNAPSHOT</version>-->
	<packaging>pom</packaging>
    <modules>
        <module>qingyun-xd-product-client</module>
<!--        <module>qingyun-xd-product-service</module>-->
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <poi.version>4.1.1</poi.version>
        <poi-ooxml.version>4.1.1</poi-ooxml.version>
    </properties>

    <dependencies>

    </dependencies>

    <dependencyManagement>
       <dependencies>
           <dependency>
               <groupId>org.apache.poi</groupId>
               <artifactId>poi</artifactId>
               <version>${poi.version}</version>
           </dependency>
           <dependency>
               <groupId>org.apache.poi</groupId>
               <artifactId>poi-ooxml</artifactId>
               <version>${poi-ooxml.version}</version>
           </dependency>

           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-marketing-client</artifactId>
               <version>${qingyun.marketing.version}</version>
           </dependency>

           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-search-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-price-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-order-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-cms-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-promotion-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-wms-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-price-client</artifactId>
               <version>${qingyun.price.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-base-db</artifactId>
               <version>${qingyun.base.db.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-base-mvc</artifactId>
               <version>${qingyun.base.mvc.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-storage-client</artifactId>
               <version>${qingyun.storage.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-mq</artifactId>
               <version>${qingyun.mq.version}</version>
           </dependency>
           <dependency>
               <groupId>com.pinshang.qingyun</groupId>
               <artifactId>qingyun-xd-elm-order-client</artifactId>
               <version>${project.version}</version>
           </dependency>
           <dependency>
               <groupId>qingyun-infrastructure</groupId>
               <artifactId>qingyun-infrastructure-test</artifactId>
               <version>${qingyun.infrastructure.version}</version>
           </dependency>
       </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>