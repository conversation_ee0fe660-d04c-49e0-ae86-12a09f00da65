package com.pinshang.qingyun.xd.product.hystrix;

import com.pinshang.qingyun.xd.product.service.XiaoeCommodityBindClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @ClassName XiaoeShopBindClientHystrix
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/14 14:22
 * @Version 1.0
 */
@Component
public class XiaoeCommodityBindClientHystrix implements FallbackFactory<XiaoeCommodityBindClient> {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public XiaoeCommodityBindClient create(Throwable throwable) {
        return new XiaoeCommodityBindClient() {
            @Override
            public void autoSettleDaily() {

            }
        };
    }
}
