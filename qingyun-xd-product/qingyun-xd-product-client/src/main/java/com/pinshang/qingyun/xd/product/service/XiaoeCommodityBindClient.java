package com.pinshang.qingyun.xd.product.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xd.product.dto.XiaoePushResultIDTO;
import com.pinshang.qingyun.xd.product.hystrix.XiaoeCommodityBindClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <p>
 * 小鹅商品与清美商品绑定  client
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XD_PRODUCT_SERVICE, fallbackFactory = XiaoeCommodityBindClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XiaoeCommodityBindClient {

    @RequestMapping(value = "/xiaoeCommodityBind/xiaoe/processPushResult", method = RequestMethod.POST)
    Boolean processPushResult(@RequestBody List<XiaoePushResultIDTO> xiaoePushResult);
}
