<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xd-parent</artifactId>
        <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xd-elm-order</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>qingyun-xd-elm-order-client</module>
        <!--        <module>qingyun-xd-elm-order-service</module>-->


    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mybatis.plus>3.2.0</mybatis.plus>

    </properties>

    <dependencies>
   
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--  project version start   -->
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-mq</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-metrics-client</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-switch</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-components-inventory</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-test</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-cms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-wms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-cache</artifactId>
                <version>${qingyun.cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-common-client</artifactId>
                <version>${qingyun.common.version}</version>
            </dependency>

            <!--  project version end   -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${org.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${org.boot.version}</version>
            </dependency>

            <!-- core -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${org.spring.version}</version>
            </dependency>

            <!--spring web相关包-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.6</version>
            </dependency>

            <!--mybatis依赖包-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.1</version>
            </dependency>
            <!--tk.mybatis-->
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>4.0.1</version>
            </dependency>
            <!-- pagehelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>

            <!--开发相关-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
                <version>${org.boot.version}</version>
            </dependency>

            <!--数据库-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.41</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.24</version>
            </dependency>

            <!-- redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.13.6</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-pos-client</artifactId>
                <version>3.4.0-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-compiler-plugin</artifactId>-->
    <!--                <configuration>-->
    <!--                    <source>1.8</source>-->
    <!--                    <target>1.8</target>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->
</project>