package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWarehouseShelfDTO extends Pagination<QueryWarehouseShelfResult> {

    @ApiModelProperty(value = "货位编号")
    private Long shelfId;

    @ApiModelProperty(value = "货位状态 1-启用 0-停用")
    private Integer status;

    @ApiModelProperty(value = "是否占用，0-全部，1-启用，2-空闲")
    private Integer occupy;

    @ApiModelProperty(value = "货位属性:1-拣货位,2-配送取货位,3-加工点取货位")
    private Integer type;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
}
