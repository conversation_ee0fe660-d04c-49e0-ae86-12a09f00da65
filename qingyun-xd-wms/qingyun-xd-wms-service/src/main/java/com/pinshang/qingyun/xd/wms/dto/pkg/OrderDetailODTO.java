package com.pinshang.qingyun.xd.wms.dto.pkg;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2022/1/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetailODTO {
    private Long orderId;

    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    @ApiModelProperty(value = "云超订单编码")
    private String orderCode;
    @ApiModelProperty(value = "下单时间")
    private Date createTime;

    @ApiModelProperty(value = "送货日期")
    private Date orderTime;
    @ApiModelProperty(value = "配送批次 0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次(装箱用)")
    private Integer deliveryBatch;
    @ApiModelProperty(value = "预约到货时间")
    private String arriveTime;


    @ApiModelProperty(value = "条形码")
    private String barCode;
    private Long commodityId;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "商品Code")
    private String commodityCode;
    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;
    @ApiModelProperty(value = "单位")
    private String commodityUnitName;
    @ApiModelProperty(value = "订货数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "订货份数")
    private Integer number;
    @ApiModelProperty(value = "实发数量")
    private BigDecimal realQuantity;
    @ApiModelProperty(value = "实发份数")
    private Integer realNumber;
    @ApiModelProperty(value = "1=称重，0=非称重")
    private Integer isWeight;

    @ApiModelProperty(value = "包裹号")
    private String packageOrderCode;
    @ApiModelProperty(value = "包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货")
    private Integer packageStatus;


    @ApiModelProperty(value = "收货人")
    private String receiveMan;
    @ApiModelProperty(value = "收货人手机")
    private String receiveMobile;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;
}
