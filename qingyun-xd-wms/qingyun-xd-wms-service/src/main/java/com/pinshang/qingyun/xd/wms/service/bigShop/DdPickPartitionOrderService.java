package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.EmployeeStateEnums;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.msg.AppMessageTypeEnum;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryModeEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.xd.cms.dto.XdBackSettingODTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.msg.dto.zsmd.MessageDTO;
import com.pinshang.qingyun.msg.service.RadioMsgClient;
import com.pinshang.qingyun.msg.service.zsmd.ZSMessageClient;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.dto.ShopCommodityStockODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.PartitionPickItemColorEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.PickPartitionOrderOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.event.HandoverDeliveryEvent;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.EmployeeUserMapper;
import com.pinshang.qingyun.xd.wms.mapper.OrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.OrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdPickAreaEmployeeMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdPickPartitionOrderMapper;
import com.pinshang.qingyun.xd.wms.model.*;
import com.pinshang.qingyun.xd.wms.model.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.*;
import com.pinshang.qingyun.xd.wms.service.platform.MeiTuanPickOrderService;
import com.pinshang.qingyun.xd.wms.service.platform.PlatFormPickOrderService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import com.pinshang.qingyun.xd.wms.service.state.PickOrderContext;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.util.XdWmsConstantUtil;
import com.pinshang.qingyun.xd.wms.vo.bigShop.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;


/**
 * <p>
 * 大店分区拣货单表  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Slf4j
@Service
public class DdPickPartitionOrderService extends ServiceImpl<DdPickPartitionOrderMapper, DdPickPartitionOrder> {

    @Autowired
    private DdPickPartitionOrderMapper ddPickPartitionOrderMapper;

    @Autowired
    private PickOrderItemService pickOrderItemService;

    @Autowired
    private DdPackingStationService ddPackingStationService;

    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private DdPackingOrderService ddPackingOrderService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private DdPickAreaEmployeeService ddPickAreaEmployeeService;

    @Autowired
    private XdBackSettingService xdBackSettingService;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    @Autowired
    private DdDisplayPositionCommodityService ddDisplayPositionCommodityService;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private RadioMsgClient radioMsgClient;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private StallService stallService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private MeiTuanPickOrderService meiTuanPickOrderService;

    @Autowired
    private WarehouseShelfService warehouseShelfService;

    @Autowired
    private PickWorkOrderService pickWorkOrderService;

    @Autowired
    private WarehouseWorkCommodityService warehouseWorkCommodityService;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private IRenderService renderService;

    @Value("${biz.dd.partition.pick.query.interval.day:1}")
    private Integer intervalDayNum;

    @Value("${biz.group.pick.max.size:3}")
    private Integer groupPickMaxSize;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private XdOrderClient xdOrderClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private DdPickAreaEmployeeMapper ddPickAreaEmployeeMapper;

    @Autowired
    private EmployeeUserMapper employeeUserMapper;

    @Autowired
    private PlatFormPickOrderService platFormPickOrderService;

    @Autowired
    private ZSMessageClient zSMessageClient;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private XdSendLogService xdSendLogService;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Value("${config.beginTime:60}")
    private int configBeginTime;

    @Autowired
    private ApplicationContext applicationContext;


    /**
     * 根据id查询拣货单详情
     *
     * @param partitionPickOrderId
     * @return
     */
    public PickPartitionOrderListResult partitionPickOrderById(Long partitionPickOrderId) {
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        PickPartitionOrderListResult pickPartitionOrderListResult = ddPickPartitionOrderMapper.partitionPickOrderById(partitionPickOrderId, warehouseId);
        if (null != pickPartitionOrderListResult) {
            Order order = orderMapper.getOneById(pickPartitionOrderListResult.getOrderId());
            if (null != order) {
                pickPartitionOrderListResult.setReceiveMan(order.getReceiveMan());
                pickPartitionOrderListResult.setReceiveMobile(order.getReceiveMobile());
            }
        }
        return pickPartitionOrderListResult;
    }

    /**
     * 分区拣货-拣货中
     *
     * @param tokenInfo
     * @param req
     * @return
     */
    public List<PickPartitionOrderPageODTO> listPickingStatus(TokenInfo tokenInfo, PickPartitionOrderPageIDTO req) {

        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();

        List<PickPartitionOrderPageVO> pickPartitionOrderPageVOList = ddPickPartitionOrderMapper.listPickingStatus(shopId, employeeId, intervalDayNum);

        if (SpringUtil.isEmpty(pickPartitionOrderPageVOList)) {
            return Collections.emptyList();
        }

        List<Long> pickPartitionOrderIdList = pickPartitionOrderPageVOList.stream().map(PickPartitionOrderPageVO::getPickPartitionOrderId).collect(Collectors.toList());
        List<PickOrderItem> pickOrderItemList = pickOrderItemService.listByPickPartitionOrderIdList(pickPartitionOrderIdList);
        QYAssert.notEmpty(pickOrderItemList, "拣货单详情不能为空");

        Set<Long> packingStationSet = pickPartitionOrderPageVOList.stream().map(PickPartitionOrderPageVO::getPackingStationId).collect(Collectors.toSet());
        Map<Long, DdPackingStation> packingStationGroupById = ddPackingStationService.mapByIds(packingStationSet);

        List<PickPartitionOrderPageODTO> resultList = new ArrayList<>(pickPartitionOrderPageVOList.size());

        Map<Long, List<PickOrderItem>> pickOrderItemGroupByPickPartitionOrderId = pickOrderItemList.stream()
                .collect(Collectors.groupingBy(PickOrderItem::getPickPartitionOrderId, Collectors.toList()));

        for (PickPartitionOrderPageVO pickPartitionOrderPageVO : pickPartitionOrderPageVOList) {
            Long pickPartitionOrderId = pickPartitionOrderPageVO.getPickPartitionOrderId();
            List<PickOrderItem> pickOrderItems = pickOrderItemGroupByPickPartitionOrderId.get(pickPartitionOrderId);

            if (SpringUtil.isEmpty(pickOrderItems)) {
                continue;
            }

            PickPartitionOrderPageODTO pickPartitionOrderPageODTO = BeanCloneUtils.copyTo(pickPartitionOrderPageVO, PickPartitionOrderPageODTO.class);

            Long pickedNum = pickOrderItems.stream().filter(data -> YesOrNoEnums.YES.getCode().equals(data.getIsComplete())).count();
            pickPartitionOrderPageODTO.setPickedNum(pickedNum.intValue());
            pickPartitionOrderPageODTO.setTotalPickNum(pickOrderItems.size());

            DdPackingStation ddPackingStation = packingStationGroupById.get(pickPartitionOrderPageVO.getPackingStationId());
            pickPartitionOrderPageODTO.setPackingPort(ddPackingStation.getPackingPort());

            Integer itemColor = getItemColor(pickPartitionOrderPageVO);
            pickPartitionOrderPageODTO.setItemColor(itemColor);
            resultList.add(pickPartitionOrderPageODTO);
        }

        return resultList;
    }

    private Integer getItemColor(PickPartitionOrderPageVO pickPartitionOrderPageVO) {
        Integer pickOrderStatus = pickPartitionOrderPageVO.getPickOrderStatus();
        Date orderDeliveryBeginTime = pickPartitionOrderPageVO.getOrderDeliveryBeginTime();
        Date pickWarnTime = calculatePickWarnTime(orderDeliveryBeginTime, getDistributionBeginWarningTime());

        if (XdPickOrderStatusEnum.CANCEL.convert().equals(pickOrderStatus)) {
            return PartitionPickItemColorEnum.GREY.getCode();
        }

        Date current = new Date();

        if (current.compareTo(pickWarnTime) < 0) {
            return PartitionPickItemColorEnum.WHITE.getCode();
        }

        if (current.compareTo(orderDeliveryBeginTime) > 0) {
            return PartitionPickItemColorEnum.RED.getCode();
        }

        return PartitionPickItemColorEnum.YELLOW.getCode();
    }

    /**
     * 分区拣货-待拣货
     *
     * @param tokenInfo
     * @param req
     * @return
     */
    public List<PickPartitionOrderPageODTO> listWaitingStatus(TokenInfo tokenInfo, PickPartitionOrderPageIDTO req) {
        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();

        DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
        if (Objects.isNull(ddPickAreaEmployee) || Objects.isNull(ddPickAreaEmployee.getPickAreaId())) {
            return Collections.emptyList();
        }

        List<PickPartitionOrderPageVO> pickPartitionOrderPageVOList = ddPickPartitionOrderMapper.listWaitingStatus(shopId, employeeId, ddPickAreaEmployee.getPickAreaId(), intervalDayNum);

        if (SpringUtil.isEmpty(pickPartitionOrderPageVOList)) {
            return Collections.emptyList();
        }

        return buildPackingPort(pickPartitionOrderPageVOList);
    }

    /**
     * 分区拣货-待交接
     *
     * @param tokenInfo
     * @param req
     * @return
     */
    public List<PickPartitionOrderPageODTO> listWaitingHandover(TokenInfo tokenInfo, PickPartitionOrderPageIDTO req) {

        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();

        List<PickPartitionOrderPageVO> pickPartitionOrderPageVOList = ddPickPartitionOrderMapper.listWaitingHandover(shopId, employeeId, intervalDayNum);

        if (SpringUtil.isEmpty(pickPartitionOrderPageVOList)) {
            return Collections.emptyList();
        }

        return buildPackingPort(pickPartitionOrderPageVOList);
    }

    private List<PickPartitionOrderPageODTO> buildPackingPort(List<PickPartitionOrderPageVO> pickPartitionOrderPageVOList) {
        List<PickPartitionOrderPageODTO> resultList = new ArrayList<>(pickPartitionOrderPageVOList.size());

        Set<Long> packingStationSet = pickPartitionOrderPageVOList.stream()
                .map(PickPartitionOrderPageVO::getPackingStationId)
                .collect(Collectors.toSet());
        Map<Long, DdPackingStation> packingStationGroupById = ddPackingStationService.mapByIds(packingStationSet);

        for (PickPartitionOrderPageVO pickPartitionOrderPageVO : pickPartitionOrderPageVOList) {
            PickPartitionOrderPageODTO pickPartitionOrderPageODTO = BeanCloneUtils.copyTo(pickPartitionOrderPageVO, PickPartitionOrderPageODTO.class);

            DdPackingStation ddPackingStation = packingStationGroupById.get(pickPartitionOrderPageVO.getPackingStationId());
            if (Objects.nonNull(ddPackingStation)) {
                pickPartitionOrderPageODTO.setPackingPort(ddPackingStation.getPackingPort());
            }

            Integer itemColor = getItemColor(pickPartitionOrderPageVO);
            pickPartitionOrderPageODTO.setItemColor(itemColor);

            resultList.add(pickPartitionOrderPageODTO);
        }

        return resultList;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean beginPickOrder(TokenInfo tokenInfo, Long pickPartitionOrderId) {

        DdPickPartitionOrder ddPickPartitionOrder = checkAndGetPickPartitionOrder(pickPartitionOrderId);
        QYAssert.isTrue(XdPickOrderStatusEnum.WAIT.convert().equals(ddPickPartitionOrder.getPickStatus()), "拣货子单不是待拣货状态，请刷新后重试");

        Long pickOrderId = ddPickPartitionOrder.getPickOrderId();
        pickOrderService.beginPickOrder(pickOrderId);

        // 更新拣货子单状态  t_dd_pick_partition_order
        DdPickPartitionOrder update = new DdPickPartitionOrder();
        update.setId(pickPartitionOrderId);
        update.setPickStatus(XdPickOrderStatusEnum.MIDDLE.convert());
        update.setPickBeginTime(new Date());
        this.updateById(update);

        PickOrder pickOrder = pickOrderService.getById(pickOrderId);
        saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.BEGIN_PICK_ORDER, pickOrder.getPickCode(),
                ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                PickStatusEnum.MIDDLE, tokenInfo.getEmployeeId());

        return Boolean.TRUE;
    }


    /**
     * 待拣货-抢单
     *
     * @param req
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void orderRush(DdPickOrderRushIDTO req) {
        QYAssert.notNull(req.getPickPartitionOrderId(), "子拣货单id不能为空");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long employeeId = tokenInfo.getEmployeeId();

        redisLockService.lock(RedisLockEnums.PICK_ORDER_RUSH, req.getPickPartitionOrderId().toString(), () -> {
            Long pickPartitionOrderId = req.getPickPartitionOrderId();
            DdPickPartitionOrder ddPickPartitionOrder = checkAndGetPickPartitionOrder(pickPartitionOrderId);
            PickOrder pickOrder = pickOrderService.getById(ddPickPartitionOrder.getPickOrderId());
            if (XdPickOrderStatusEnum.CANCEL.convert().equals(pickOrder.getPickStatus())) {
                QYAssert.isFalse("拣货子单已取消，请刷新重试");
            }

            QYAssert.isNull(ddPickPartitionOrder.getPickId(), "拣货单已分配拣货员，无法抢单");

            distributePartitionPickOrder(pickOrder, Collections.singletonList(ddPickPartitionOrder), Boolean.FALSE);

            DdPickPartitionOrder update = new DdPickPartitionOrder();
            update.setId(pickPartitionOrderId);
            update.setPickId(employeeId);
            this.baseMapper.updateById(update);

            this.beginPickOrder(tokenInfo, pickPartitionOrderId);

            saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.DISTRIBUTE_PICKER, pickOrder.getPickCode(),
                    ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                    PickStatusEnum.getByCode(ddPickPartitionOrder.getPickStatus()), tokenInfo.getEmployeeId());
        });
    }

    private DdPickPartitionOrder checkAndGetPickPartitionOrder(Long pickPartitionOrderId) {
        DdPickPartitionOrder ddPickPartitionOrder = this.baseMapper.selectById(pickPartitionOrderId);
        QYAssert.notNull(ddPickPartitionOrder, "拣货子单不存在");
        QYAssert.isTrue(!XdPickOrderStatusEnum.CANCEL.convert().equals(ddPickPartitionOrder.getPickStatus()), "拣货子单已取消，请刷新重试");

        return ddPickPartitionOrder;
    }

    private CheckAndListPickPartitionOrderResultInfo checkAndListPickPartitionOrder(Collection<Long> pickPartitionOrderIdList) {
        Collection<DdPickPartitionOrder> ddPickPartitionOrderList = this.listByIds(pickPartitionOrderIdList);
        QYAssert.notEmpty(ddPickPartitionOrderList, "拣货子单不存在");
        ddPickPartitionOrderList.forEach(data -> {
            QYAssert.isTrue(!XdPickOrderStatusEnum.CANCEL.convert().equals(data.getPickStatus()), "拣货子单已取消，请刷新重试");
        });

        List<Long> pickOrderIdList = ddPickPartitionOrderList.stream().map(DdPickPartitionOrder::getPickOrderId).collect(Collectors.toList());
        Collection<PickOrder> pickOrders = pickOrderService.listByIds(pickOrderIdList);
        pickOrders.forEach(data -> {
            QYAssert.isTrue(!XdPickOrderStatusEnum.CANCEL.convert().equals(data.getPickStatus()), "拣货单已取消，请刷新重试");
        });

        CheckAndListPickPartitionOrderResultInfo result = new CheckAndListPickPartitionOrderResultInfo();
        result.setPickOrders(pickOrders);
        result.setDdPickPartitionOrderList(ddPickPartitionOrderList);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelPartitionPickOrder(TokenInfo tokenInfo, DdPickCancelIDTO req) {
        Long pickPartitionOrderId = req.getPickPartitionOrderId();
        DdPickPartitionOrder ddPickPartitionOrder = getById(pickPartitionOrderId);

        cancelPickPartitionOrderAndReleasePackingStation(ddPickPartitionOrder);

        completeShelfWork(pickPartitionOrderId, null, Boolean.FALSE, null);

        // 拣货员任务数-1
        PickOrder pickOrder = pickOrderService.getById(ddPickPartitionOrder.getPickOrderId());
        ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), ddPickPartitionOrder.getPickId(), -1);

        saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.CANCEL_PICK_ORDER, pickOrder.getPickCode(),
                ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                PickStatusEnum.CANCEL, tokenInfo.getEmployeeId());

    }

    /**
     * 完成加工点取货位
     *
     * @param pickPartitionId
     * @param pickOrderItemId
     * @param ifComplete      true 完成 false 取消
     * @param commodityId
     */
    @Transactional
    public void completeShelfWork(Long pickPartitionId, Long pickOrderItemId, Boolean ifComplete, Long commodityId) {

        List<PickOrderItem> processPickOrderItemList = pickOrderItemService.listProcessByPickPartitionOrderIdList(pickPartitionId, pickOrderItemId, commodityId);

        if (SpringUtil.isEmpty(processPickOrderItemList)) {
            return;
        }

        List<Long> processPickOrderItemIdList = processPickOrderItemList.stream()
                .map(PickOrderItem::getId)
                .collect(Collectors.toList());

        // 完成加工点取货位
        // 加工点取货位
        LambdaQueryWrapper<PickWorkOrder> workQuery = new LambdaQueryWrapper<PickWorkOrder>()
                .in(PickWorkOrder::getPickOrderItemId, processPickOrderItemIdList);

        List<PickWorkOrder> workOrderList = pickWorkOrderService.list(workQuery);
        if (SpringUtil.isEmpty(workOrderList)) {
            log.warn("拣货商品订单 [{}] 处理拣货单时存在加工商品未设置加工点情况 ", processPickOrderItemIdList);
            return;
        }

        String shelfNo = workOrderList.get(0).getShelfNo();
        if (ifComplete) {
            Set<Long> unPickOrderItemIdSet = processPickOrderItemList.stream()
                    .filter(data -> Objects.isNull(data.getPickQuantity()) || XdWmsConstantUtil.PICK_QUANTITY_CONSTANT.equals(data.getPickQuantity()))
                    .map(PickOrderItem::getId)
                    .collect(Collectors.toSet());
            if (SpringUtil.isNotEmpty(unPickOrderItemIdSet)) {
                List<PickWorkOrder> zeroPickWorkOrderList = workOrderList.stream()
                        .filter(work -> unPickOrderItemIdSet.contains(work.getPickOrderItemId()))
                        .collect(Collectors.toList());
                updatePickWorkOrders(zeroPickWorkOrderList, shelfNo, WorkOrderStatusEnum.CANCEL);

                workOrderList.removeAll(zeroPickWorkOrderList);
            }

            if (SpringUtil.isNotEmpty(workOrderList)) {
                updatePickWorkOrders(workOrderList, shelfNo, WorkOrderStatusEnum.FINISH);
            }
        } else {
            List<PickWorkOrder> waitPickWorkOrderList = workOrderList.stream()
                    .filter(data -> Objects.equals(data.getWorkStatus(), WorkOrderStatusEnum.WAIT.getCode()))
                    .collect(Collectors.toList());
            updatePickWorkOrders(waitPickWorkOrderList, shelfNo, WorkOrderStatusEnum.CANCEL);
        }
    }

    @Transactional
    public void updatePickWorkOrders(List<PickWorkOrder> pickWorkOrderList, String shelfNo, WorkOrderStatusEnum workOrderStatusEnum) {
        if (SpringUtil.isNotEmpty(pickWorkOrderList)) {
            List<PickWorkOrder> updateList = new ArrayList<>(pickWorkOrderList.size());

            boolean completeShelfWorkFlag = Boolean.TRUE;
            for (PickWorkOrder pickWorkOrder : pickWorkOrderList) {
                PickWorkOrder updatePickWorkOrder = new PickWorkOrder();
                updatePickWorkOrder.setId(pickWorkOrder.getId());
                updatePickWorkOrder.setWorkStatus(workOrderStatusEnum.getCode());

                if (Objects.equals(workOrderStatusEnum, WorkOrderStatusEnum.FINISH)) {
                    updatePickWorkOrder.setCompleteTime(new Date());
                }

                updateList.add(updatePickWorkOrder);

                Integer status = pickWorkOrder.getWorkStatus();
                if (Objects.nonNull(status)) {
                    boolean isCanceledOrFinished =
                            status == WorkOrderStatusEnum.CANCEL.getCode() ||
                                    status == WorkOrderStatusEnum.FINISH.getCode();
                    completeShelfWorkFlag = !isCanceledOrFinished;
                }
            }

            pickWorkOrderService.updateBatchById(updateList);

            if (completeShelfWorkFlag) {
                warehouseShelfService.completeShelfWork(shelfNo);
            }
        }
    }

    @Transactional
    public void confirmCancel(TokenInfo tokenInfo, DdPartitionPickConfirmCancelIDTO req) {
        Long pickPartitionOrderId = req.getPickPartitionOrderId();
        DdPickPartitionOrder ddPickPartitionOrder = getById(pickPartitionOrderId);

        QYAssert.isTrue(XdPickOrderStatusEnum.FINISH.convert().equals(ddPickPartitionOrder.getPickStatus()), "分区拣货单未完成拣货");

        cancelPickPartitionOrderAndReleasePackingStation(ddPickPartitionOrder);

        PickOrder pickOrder = pickOrderService.getById(ddPickPartitionOrder.getPickOrderId());
        saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.CANCEL_ORDER_RECYCLE, pickOrder.getPickCode(),
                ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                PickStatusEnum.CANCEL, tokenInfo.getEmployeeId());
    }

    @Transactional
    public void cancelPickPartitionOrderAndReleasePackingStation(DdPickPartitionOrder ddPickPartitionOrder) {

        Long pickPartitionOrderId = ddPickPartitionOrder.getId();
        DdPickPartitionOrder update = new DdPickPartitionOrder();
        update.setId(pickPartitionOrderId);
        update.setPickStatus(XdPickOrderStatusEnum.CANCEL.convert());
        this.baseMapper.updateById(update);

        // 释放打包口
        // releasePackingStationCancelOrder(ddPickPartitionOrder.getPickOrderId());
    }

    // /**
    //  * 释放打包口
    //  *
    //  * @param pickOrderId
    //  */
    // @Transactional
    // public void releasePackingStationCancelOrder(Long pickOrderId) {
    //     redisLockService.lock(RedisLockEnums.RELEASE_PACKING_STATION, pickOrderId.toString(), () -> {
    //         PickOrder pickOrder = pickOrderService.getById(pickOrderId);
    //         QYAssert.notNull(pickOrder, "拣货单不存在");
    //
    //         List<DdPickPartitionOrder> ddPickPartitionOrders = listByPickOrderId(pickOrderId);
    //         QYAssert.notEmpty(ddPickPartitionOrders, "订单没有分区拣货子单");
    //
    //         List<DdPickPartitionOrder> notCancelPickPartitionOrderList = ddPickPartitionOrders.stream()
    //                 .filter(data -> !XdPickOrderStatusEnum.CANCEL.convert().equals(data.getPickStatus()))
    //                 .collect(Collectors.toList());
    //
    //         if (SpringUtil.isNotEmpty(notCancelPickPartitionOrderList)) {
    //             return;
    //         }
    //
    //         Long orderId = pickOrder.getOrderId();
    //         releasePackingStation(orderId);
    //     });
    // }

    /**
     * 释放打包口
     *
     * @param orderId
     */
    @Transactional
    public void releasePackingStation(Long orderId) {
        PickOrderResult pickOrder = pickOrderService.getPickOrderByOrderId(orderId);
        QYAssert.notNull(pickOrder, "拣货单不存在");

        Long packingStationId = pickOrder.getPackingStationId();
        Long warehouseId = pickOrder.getWarehouseId();
        Integer i = ddPackingOrderService.releasePackingStation(warehouseId, packingStationId, pickOrder.getOrderId());
        if (i > 0) {
            ddPackingStationService.releasePackingStation(warehouseId, packingStationId);
        }
    }

    private List<DdPickPartitionOrder> listByPickOrderId(Long pickOrderId) {
        if (Objects.isNull(pickOrderId)) {
            return Collections.emptyList();
        }

        return this.baseMapper.selectList(new LambdaQueryWrapper<DdPickPartitionOrder>().eq(DdPickPartitionOrder::getPickOrderId, pickOrderId));
    }

    /**
     * 接口只有合单拣货的时候使用了
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    public DdConfirmPickODTO confirmPick(DdConfirmPickIDTO req, Boolean processPickOrderItemComplete, Boolean useReqPickQuantity) {
        req.checkData();
        List<DdConfirmPickDetailIDTO> groupPickOrderDetailList = req.getGroupPickOrderDetailList();
        processLackPickNumberAndQuantity(groupPickOrderDetailList);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String commodityId = req.getCommodityId();

        Set<Long> pickOrderIdSet = new HashSet<>(groupPickOrderDetailList.size());
        Map<Long, List<Long>> pickPartitionOrderIdMapItemIds = new HashMap<>(groupPickOrderDetailList.size());

        processConfirmPickReqPickOrderIdSetAndPickPartitionOrderIdItemIdMap(groupPickOrderDetailList, pickOrderIdSet, pickPartitionOrderIdMapItemIds);

        CheckAndListPickPartitionOrderResultInfo checkAndListPickPartitionOrderResultInfo = checkAndListPickPartitionOrder(pickPartitionOrderIdMapItemIds.keySet());
        Collection<DdPickPartitionOrder> ddPickPartitionOrderList = checkAndListPickPartitionOrderResultInfo.getDdPickPartitionOrderList();
        ddPickPartitionOrderList.forEach(data ->
                QYAssert.isTrue(XdPickOrderStatusEnum.MIDDLE.convert().equals(data.getPickStatus()), String.format("拣货子单:[%s]状态不是拣货中", data.getId()))
        );

        Collection<PickOrder> pickOrders = checkAndListPickPartitionOrderResultInfo.getPickOrders();
        Map<Long, PickOrder> pickOrderMap = pickOrders.stream().collect(toMap(PickOrder::getId, Function.identity()));

        Map<Long, DdPickPartitionOrder> pickPartitionOrderMap = ddPickPartitionOrderList.stream().collect(toMap(DdPickPartitionOrder::getId, Function.identity()));

        Collection<PickOrderItem> pickOrderItems = pickOrderItemService.listByPickPartitionOrderIdListAndCommodityId(pickPartitionOrderIdMapItemIds, Long.parseLong(commodityId));
        pickOrderItems.forEach(data -> {
            if (YesOrNoEnums.YES.getCode().equals(data.getIsComplete()) && YesOrNoEnums.YES.getCode().equals(data.getIsProcess())) {
                log.warn("拣货明细已拣货,pickOrderItemId:[{}]", data.getId());
                QYAssert.isFalse("拣货明细已拣货");
            }
        });
        Map<Long, PickOrderItem> pickOrderItemMap = pickOrderItems.stream().collect(toMap(PickOrderItem::getId, Function.identity()));

        for (DdConfirmPickDetailIDTO ddConfirmPickIDTO : groupPickOrderDetailList) {
            Long pickPartitionOrderId = ddConfirmPickIDTO.getPickPartitionOrderId();

            Long pickOrderId = ddConfirmPickIDTO.getPickOrderId();
            PickOrder pickOrder = pickOrderMap.get(pickOrderId);

            Long pickOrderItemId = ddConfirmPickIDTO.getPickOrderItemId();
            PickOrderItem pickOrderItem = pickOrderItemMap.get(pickOrderItemId);

            ddConfirmPickIDTO.setCommodityId(commodityId);
            PickCompleteItemDTO pickCompleteItemDTO = BeanCloneUtils.copyTo(ddConfirmPickIDTO, PickCompleteItemDTO.class);

            if (useReqPickQuantity) {
                pickOrderService.processPickOrderItemPickQuantityAndNum(pickOrderItem, pickCompleteItemDTO, pickOrder);
            } else {
                processPickOrderItemPickQuantityAndNum(pickOrderItem, pickCompleteItemDTO, pickOrder);
            }

            processPickOrderItemComplete(processPickOrderItemComplete, pickOrderItemId, pickOrderItem);
            completeShelfWork(pickPartitionOrderId, pickOrderItemId, Boolean.TRUE, Long.parseLong(commodityId));
        }

        return redisLockService.multiLock(RedisLockEnums.PARTITION_PICK_CONFIRM_OR_COMPLETE, pickOrderIdSet, () -> {
            Set<Long> processedPickPartitionOrderId = new HashSet<>();

            // 返回值构建
            DdConfirmPickODTO result = new DdConfirmPickODTO();
            Set<String> waitHandoverOrderNumList = new HashSet<>(groupPickOrderDetailList.size());

            for (DdConfirmPickDetailIDTO ddConfirmPickIDTO : groupPickOrderDetailList) {
                Long pickPartitionOrderId = ddConfirmPickIDTO.getPickPartitionOrderId();

                if (processedPickPartitionOrderId.contains(pickPartitionOrderId)) continue;

                Long pickOrderId = ddConfirmPickIDTO.getPickOrderId();

                List<PickOrderItem> partitionPickOrderIems = pickOrderItemService.listByPickPartitionOrderIdList(Collections.singletonList(pickPartitionOrderId));
                QYAssert.notEmpty(partitionPickOrderIems, "拣货商品明细不存在");

                // 存在还未拣货的商品，直接返回
                List<PickOrderItem> notPickItems = partitionPickOrderIems.stream()
                        .filter(data -> Objects.isNull(data.getIsComplete()) || YesOrNoEnums.NO.getCode().equals(data.getIsComplete()))
                        .collect(Collectors.toList());
                if (SpringUtil.isNotEmpty(notPickItems)) {
                    log.info("存在还未拣货的商品,pickPartitionOrderId:[{}],notPickItems:[{}]", pickPartitionOrderId, JSONObject.toJSONString(notPickItems));
                    processedPickPartitionOrderId.add(pickPartitionOrderId);
                    continue;
                }

                DdPickPartitionOrder update = new DdPickPartitionOrder();
                update.setId(pickPartitionOrderId);
                update.setPickEndTime(new Date());
                update.setPickStatus(XdPickOrderStatusEnum.WAIT_HANDOVER.convert());
                this.updateById(update);

                updatePickOrderStatus(partitionPickOrderIems, pickOrderId,pickPartitionOrderId, userId);
                processedPickPartitionOrderId.add(pickPartitionOrderId);

                PickOrder pickOrder = pickOrderService.getById(pickOrderId);
                DdPickPartitionOrder ddPickPartitionOrder = pickPartitionOrderMap.get(pickPartitionOrderId);
                if (PickStatusEnum.CANCEL.getCode() != pickOrder.getPickStatus()) {
                    saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.COMPLETE_PICK_ORDER, pickOrder.getPickCode(),
                            ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                            PickStatusEnum.WAIT_HANDOVER, tokenInfo.getEmployeeId());
                }

                DdPickPartitionOrder partitionOrder = this.getById(pickPartitionOrderId);
                if (Objects.equals(partitionOrder.getPickStatus(), PickStatusEnum.WAIT_HANDOVER.getCode())) {
                    waitHandoverOrderNumList.add(pickOrder.getOrderNum());
                }
            }
            result.setOrderNumList(waitHandoverOrderNumList);
            return result;
        });
    }

    /**
     * 兼容客户端特殊逻辑 ，未拣货居然传 -1
     *
     * @param groupPickOrderDetailList
     */
    private void processLackPickNumberAndQuantity(List<DdConfirmPickDetailIDTO> groupPickOrderDetailList) {

        for (DdConfirmPickDetailIDTO ddConfirmPickDetailIDTO : groupPickOrderDetailList) {
            if (ddConfirmPickDetailIDTO.getPickNumber() < 0 || ddConfirmPickDetailIDTO.getPickQuantity().compareTo(BigDecimal.ZERO) < 0) {
                ddConfirmPickDetailIDTO.setPickNumber(0);
                ddConfirmPickDetailIDTO.setPickQuantity(XdWmsConstantUtil.PICK_QUANTITY_CONSTANT);
            }
        }
    }

    @Transactional
    public void processPickOrderItemPickQuantityAndNum(PickOrderItem item, PickCompleteItemDTO pickCompleteItemDTO, PickOrder pickOrder) {

        if (YesOrNoEnums.YES.getCode().equals(item.getIsWeight())) {
            if (Objects.nonNull(item.getPickNumber()) && item.getPickNumber() > 0) {
                throw new BizLogicException("此商品已完成拣货，请将多扫描的数量放回原处");
            }

            int realPickNumber = pickCompleteItemDTO.getPickQuantity().divide(item.getQuantity(), 2, BigDecimal.ROUND_DOWN)
                    .multiply(BigDecimal.valueOf(item.getStockNumber())).setScale(0, BigDecimal.ROUND_UP).intValue();
            item.setPickNumber(realPickNumber > item.getStockNumber() ? item.getStockNumber() : realPickNumber);
            item.setPickQuantity(pickCompleteItemDTO.getPickQuantity());
            if (pickOrder.getOrderType() != null && !pickOrder.getOrderType().equals(XdOrderTypeEnum.CLOUDXJ.getCode()) &&
                    !pickOrder.getSourceType().equals(OrderSourceTypeEnum.JIULANG.getCode()) &&
                    !pickOrder.getSourceType().equals(OrderSourceTypeEnum.LIQING.getCode()) &&
                    pickCompleteItemDTO.getPickQuantity().compareTo(item.getQuantity().multiply(new BigDecimal("2"))) >= 0) {
                Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "请注意称重品的计量单位为kg,0≤称重品拣货量<订货数量*2");
            }
        } else {
            if (pickCompleteItemDTO.getPickQuantity().compareTo(item.getQuantity()) > 0) {
                Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "0≤标品拣货量≤订货数量");
            }

            Integer pickNumber = pickCompleteItemDTO.getPickNumber();
            BigDecimal pickQuantity = pickCompleteItemDTO.getPickQuantity();
            if (pickQuantity.stripTrailingZeros().scale() > 0) {
                throw new BizLogicException("请输入正确的拣货数量");
            }

            if (pickQuantity.intValue() != pickNumber) {
                throw new BizLogicException("请输入正确的拣货数量");
            }


            int originPickNumber = item.getPickNumber() == null ? 0 : item.getPickNumber();
            int savePickNum = pickCompleteItemDTO.getPickQuantity().intValue() + originPickNumber;
            if (savePickNum > item.getStockNumber()) {
                throw new BizLogicException("此商品已完成拣货，请将多扫描的数量放回原处");
            }
            item.setPickNumber(savePickNum);

            BigDecimal originPickQuantity = item.getPickQuantity() == null ? BigDecimal.ZERO : item.getPickQuantity();
            item.setPickQuantity(pickCompleteItemDTO.getPickQuantity().add(originPickQuantity));

        }
        pickOrderItemService.updateById(item);
    }


    /**
     * 合单拣货点击确认拣货 需要强制完成当前的拣货单明细
     * 当个分区拣货单 拣货需要根据实际情况判断是否完成了拣货
     *
     * @param processPickOrderItemComplete
     * @param pickOrderItemId
     * @param pickOrderItem
     */
    @Transactional
    public Integer processPickOrderItemComplete(Boolean processPickOrderItemComplete, Long pickOrderItemId, PickOrderItem pickOrderItem) {
        PickOrderItem updateComplete = null;

        if (processPickOrderItemComplete) {
            updateComplete = new PickOrderItem();
        } else {
            PickOrderItem latestPickOrderItem = pickOrderItemService.getById(pickOrderItemId);
            if (latestPickOrderItem.getPickNumber() != null &&
                    latestPickOrderItem.getPickNumber().compareTo(pickOrderItem.getStockNumber()) >= 0) {
                updateComplete = new PickOrderItem();
            }
        }

        if (updateComplete != null) {
            updateComplete.setId(pickOrderItem.getId());
            updateComplete.setIsComplete(YesOrNoEnums.YES.getCode());
            pickOrderItemService.updateById(updateComplete);
            return updateComplete.getIsComplete();
        }

        return YesOrNoEnums.NO.getCode();
    }

    private static void processConfirmPickReqPickOrderIdSetAndPickPartitionOrderIdItemIdMap(List<DdConfirmPickDetailIDTO> groupPickOrderDetailList, Set<Long> pickOrderIdSet, Map<Long, List<Long>> pickPartitionOrderIdMapItemIds) {
        groupPickOrderDetailList.forEach(ddConfirmPickDetailIDTO -> {
            pickOrderIdSet.add(ddConfirmPickDetailIDTO.getPickOrderId());

            Long pickPartitionOrderId = ddConfirmPickDetailIDTO.getPickPartitionOrderId();

            List<Long> pickOrderItemIds = pickPartitionOrderIdMapItemIds.computeIfAbsent(
                    pickPartitionOrderId, key -> new ArrayList<>()
            );

            pickOrderItemIds.add(ddConfirmPickDetailIDTO.getPickOrderItemId());
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void completePickOrder(DdPickCompleteIDTO req) {

        List<Long> pickPartitionOrderIdList = req.getPickPartitionOrderIdList();
        QYAssert.notEmpty(pickPartitionOrderIdList, "拣货子单不能为空");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();

        CheckAndListPickPartitionOrderResultInfo result = checkAndListPickPartitionOrder(pickPartitionOrderIdList);
        Collection<DdPickPartitionOrder> ddPickPartitionOrderList = result.getDdPickPartitionOrderList();

        List<String> pickerOrderIdList = new ArrayList<>(ddPickPartitionOrderList.size());
        List<DdPickPartitionOrder> batchUpdateList = new ArrayList<>(ddPickPartitionOrderList.size());

        for (DdPickPartitionOrder ddPickPartitionOrder : ddPickPartitionOrderList) {
            Long pickPartitionOrderId = ddPickPartitionOrder.getId();
            pickerOrderIdList.add(ddPickPartitionOrder.getPickOrderId().toString());

            completeShelfWork(pickPartitionOrderId, null, Boolean.TRUE, null);

            DdPickPartitionOrder update = new DdPickPartitionOrder();
            update.setId(pickPartitionOrderId);
            update.setPickEndTime(new Date());
            update.setPickStatus(XdPickOrderStatusEnum.WAIT_HANDOVER.convert());
            batchUpdateList.add(update);
        }

        this.updateBatchById(batchUpdateList);

        pickOrderItemService.update(new LambdaUpdateWrapper<PickOrderItem>()
                .set(PickOrderItem::getPickQuantity, XdWmsConstantUtil.PICK_QUANTITY_CONSTANT)
                .set(PickOrderItem::getPickNumber, 0)
                .set(PickOrderItem::getIsComplete, YesOrNoEnums.YES.getCode())
                .in(PickOrderItem::getPickPartitionOrderId, pickPartitionOrderIdList)
                .isNull(PickOrderItem::getPickQuantity));

        for (DdPickPartitionOrder ddPickPartitionOrder : ddPickPartitionOrderList) {
            PartitionPickConfirmOrCompleteIDTO msg = new PartitionPickConfirmOrCompleteIDTO();
            msg.setOperateType(PartitionPickConfirmOrCompleteIDTO.COMPLETE);
            Long pickOrderId = ddPickPartitionOrder.getPickOrderId();
            msg.setPickOrderId(pickOrderId);
            msg.setPickPartitionOrderId(ddPickPartitionOrder.getId());
            msg.setUserId(userId);
            msg.setTokenInfo(tokenInfo);

            // 完成拣货消息
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE_TOPIC,
                            msg,
                            MqMessage.MQ_KAFKA,
                            KafkaMessageTypeEnum.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE.name(),
                            KafkaMessageOperationTypeEnum.UPDATE.getCode());
                }
            });

        }
    }

    @Transactional
    public void processCompletePickMsg(PartitionPickConfirmOrCompleteIDTO msg) {
        redisLockService.lock(RedisLockEnums.PARTITION_PICK_CONFIRM_OR_COMPLETE, msg.getPickOrderId().toString(), () -> {
            Long pickPartitionOrderId = msg.getPickPartitionOrderId();
            Long pickOrderId = msg.getPickOrderId();

            List<PickOrderItem> partitionPickOrderIems = pickOrderItemService.listByPickPartitionOrderIdList(Collections.singletonList(pickPartitionOrderId));

            updatePickOrderStatus(partitionPickOrderIems, pickOrderId, msg.getPickPartitionOrderId(), msg.getUserId());

            // 记录日志
            PickOrder pickOrder = pickOrderService.getById(pickOrderId);
            DdPickPartitionOrder ddPickPartitionOrder = getById(pickPartitionOrderId);
            if (PickStatusEnum.CANCEL.getCode() != pickOrder.getPickStatus()) {
                TokenInfo tokenInfo = msg.getTokenInfo();
                saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.COMPLETE_PICK_ORDER, pickOrder.getPickCode(),
                        ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                        PickStatusEnum.WAIT_HANDOVER, tokenInfo.getEmployeeId());
            }
        });
    }

    @Transactional
    public void updatePickOrderStatus(List<PickOrderItem> partitionPickOrderIems, Long pickOrderId,Long pickPartitionOrderId, Long userId) {
        List<PickOrderItem> partitionZeroPickQuantityItems = partitionPickOrderIems.stream()
                .filter(data -> YesOrNoEnums.YES.getCode().equals(data.getIsComplete()) &&
                        (Objects.isNull(data.getPickQuantity()) || XdWmsConstantUtil.PICK_QUANTITY_CONSTANT.equals(data.getPickQuantity())))
                .collect(Collectors.toList());

        PickOrder pickOrder = pickOrderService.getById(pickOrderId);

        // 当前子拣货单 所有商品拣货 数量都等于 0 直接完成当前子拣货单
        boolean partitionZeroPick = partitionZeroPickQuantityItems.size() == partitionPickOrderIems.size();
        if (partitionZeroPick) {
            DdPickPartitionOrder update = new DdPickPartitionOrder();
            update.setId(partitionPickOrderIems.get(0).getPickPartitionOrderId());
            update.setPickStatus(XdPickOrderStatusEnum.FINISH.convert());
            update.setHandoverEndTime(new Date());
            this.updateById(update);

            // 拣货员任务数-1
            DdPickPartitionOrder ddPickPartitionOrder = this.getById(pickPartitionOrderId);
            ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), ddPickPartitionOrder.getPickId(), -1);
        }

        List<DdPickPartitionOrder> allPartitionPickOrderList = this.listByPickOrderId(pickOrderId);
        boolean existWaitOrMiddleStatusPartitionPickOrder = allPartitionPickOrderList.stream()
                .anyMatch(data -> XdPickOrderStatusEnum.WAIT.convert().equals(data.getPickStatus()) || XdPickOrderStatusEnum.MIDDLE.convert().equals(data.getPickStatus()));

        if (existWaitOrMiddleStatusPartitionPickOrder) {
            return;
        }

        List<PickOrderItem> allPickOrderItems = pickOrderItemService.listByPickOrderId(pickOrderId);
        Long notPickItemCount = allPickOrderItems.stream()
                .filter(data -> YesOrNoEnums.YES.getCode().equals(data.getIsComplete()) &&
                        Objects.isNull(data.getPickQuantity()) || XdWmsConstantUtil.PICK_QUANTITY_CONSTANT.equals(data.getPickQuantity()))
                .count();

        boolean cancelPickOrder = notPickItemCount.intValue() == allPickOrderItems.size();
        Long orderId = pickOrder.getOrderId();
        if (cancelPickOrder) {
            cancelAllPartitionPickOrders(pickOrder);

            pickOrderService.cancelPickOrder(pickOrder, orderId, userId, Boolean.FALSE);

            releasePackingStation(orderId);
        }

        warehouseShelfService.completeShelfDelivery(orderId, pickOrder.getShelfNo());

        // 模拟一次送货交接
        if (partitionZeroPick && !cancelPickOrder) {
            finishPickOrderIfAllPartitionPickOrderFinish(pickOrderId, allPartitionPickOrderList, allPickOrderItems);
        }
    }

    @Transactional
    public void cancelAllPartitionPickOrders(PickOrder pickOrder) {
        Long pickOrderId = pickOrder.getId();
        LambdaUpdateWrapper<DdPickPartitionOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DdPickPartitionOrder::getPickStatus, XdPickOrderStatusEnum.CANCEL.convert())
                .set(DdPickPartitionOrder::getUpdateTime, new Date())
                .eq(DdPickPartitionOrder::getPickOrderId, pickOrderId);

        ddPickPartitionOrderMapper.update(null, updateWrapper);


        List<DdPickPartitionOrder> ddPickPartitionOrders = ddPickPartitionOrderMapper.selectList(
                new LambdaQueryWrapper<DdPickPartitionOrder>()
                        .eq(DdPickPartitionOrder::getPickOrderId, pickOrderId)
        );

        // 发取消拣货日志
        sendPartitionPickOrderLog(FastThreadLocalUtil.getQY(), pickOrder, ddPickPartitionOrders);
    }

    private void sendPartitionPickOrderLog(TokenInfo tokenInfo, PickOrder pickOrder, List<DdPickPartitionOrder> ddPickPartitionOrders) {
        List<PartitionPickOrderLogVO> partitionPickOrderLogVOList = new ArrayList<>(ddPickPartitionOrders.size());
        for (DdPickPartitionOrder ddPickPartitionOrder : ddPickPartitionOrders) {

            Long pickId = Optional.ofNullable(tokenInfo).map(TokenInfo::getEmployeeId).orElse(XdWmsConstantUtil.SYSTEM_PICK_ID);
            PartitionPickOrderLogVO partitionPickOrderLogVO = bulidPartitionPickOrderLogVO(null, pickOrder.getWarehouseId(),
                    PickPartitionOrderOperationTypeEnum.CANCEL_ORDER_RECYCLE, pickOrder.getPickCode(),
                    ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                    PickStatusEnum.CANCEL, pickId);
            partitionPickOrderLogVOList.add(partitionPickOrderLogVO);
        }
        xdSendLogService.sendLog(partitionPickOrderLogVOList, "t_log_dd_pick_partition_order");
    }

    public List<CancelPickOrderODTO> listCancelPickOrder() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long employeeId = tokenInfo.getEmployeeId();
        Long shopId = tokenInfo.getShopId();

        DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
        if (Objects.isNull(ddPickAreaEmployee) || Objects.isNull(ddPickAreaEmployee.getPickAreaId())) {
            return Collections.emptyList();
        }

        List<CancelPickOrderODTO> cancelPickOrderODTOS = this.baseMapper.listCancelPickOrder(shopId, employeeId, ddPickAreaEmployee.getPickAreaId(), intervalDayNum);

        if (SpringUtil.isEmpty(cancelPickOrderODTOS)) {
            return Collections.emptyList();
        }

        setPackingPort(cancelPickOrderODTOS);

        return cancelPickOrderODTOS;
    }

    private void setPackingPort(List<CancelPickOrderODTO> cancelPickOrderList) {
        Set<Long> packingStationIdSet = cancelPickOrderList.stream()
                .map(CancelPickOrderODTO::getPackingStationId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (SpringUtil.isEmpty(packingStationIdSet)) {
            return;
        }

        Collection<DdPackingStation> ddPackingStations = ddPackingStationService.listByIds(packingStationIdSet);
        if (SpringUtil.isEmpty(ddPackingStations)) {
            return;
        }

        Map<Long, String> packingIdMapPort = ddPackingStations.stream().collect(toMap(DdPackingStation::getId, DdPackingStation::getPackingPort));

        cancelPickOrderList.forEach(data -> {
            data.setPackingPort(packingIdMapPort.get(data.getPackingStationId()));
        });
    }

    public MPage<PickPartitionOrderListResult> pickPartitionOrderList(PickPartitionOrderDTO pickPartitionOrderDTO) {

        MPage<PickPartitionOrderListResult> pickPartitionOrderListResultMPage = ddPickPartitionOrderMapper.pickPartitionOrderList(pickPartitionOrderDTO);
        List<PickPartitionOrderListResult> list = pickPartitionOrderListResultMPage.getList();

        if (SpringUtil.isNotEmpty(list)) {
            int distributionBeginWarningTime = getDistributionBeginWarningTime();

            Map<Long, DdPackingStation> packingStationMap = Collections.emptyMap();
            List<Long> packingStationIdList = list.stream().map(PickPartitionOrderListResult::getPackingStationId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (SpringUtil.isNotEmpty(packingStationIdList)) {
                packingStationMap = ddPackingStationService.mapByIds(packingStationIdList);
            }

            Map<Long, DdPackingStation> finalPackingStationMap = packingStationMap;
            list.forEach(pickPartitionOrderListResult -> {

                // 设置分区拣货预警时间
                pickPartitionOrderListResult.setPickWarnTime(calculatePickWarnTime(pickPartitionOrderListResult.getOrderDeliveryBeginTime(), distributionBeginWarningTime));

                // 设置订单打包口code
                pickPartitionOrderListResult.setPackingStationName(getPackingStationName(pickPartitionOrderListResult.getPackingStationId(), finalPackingStationMap));
            });
            renderService.render(list, "/dd/pick/partition/pickPartitionOrderList");
        }

        return pickPartitionOrderListResultMPage;

    }


    public MPage<PartitionPickOrderPageODTO> pagePartitionPickOrder(PartitionPickOrderPageIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();

        DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
        if (Objects.isNull(ddPickAreaEmployee) || Objects.isNull(ddPickAreaEmployee.getPickAreaId())) {
            return new PartitionPickOrderPageIDTO();
        }
        req.setPickAreaId(ddPickAreaEmployee.getPickAreaId());

        List<Integer> pickStatus = initQueryPickStatus(req);
        req.setPickStatusList(pickStatus);

        MPage<PartitionPickOrderPageODTO> partitionPickOrderPageODTOMPage = ddPickPartitionOrderMapper.pagePartitionPickOrder(req, shopId, intervalDayNum);
        if (Objects.nonNull(partitionPickOrderPageODTOMPage) && SpringUtil.isNotEmpty(partitionPickOrderPageODTOMPage.getList())) {
            renderService.render(partitionPickOrderPageODTOMPage.getList(), "/dd/pick/partition/pagePartitionPickOrder");
        }
        return partitionPickOrderPageODTOMPage;
    }

    // private List<Long> getUnassignedPickAreaAndEmployeePickArea(Long shopId, Long employeeId) {
    //     List<Long> pickAreaIdList = new ArrayList<>(2);
    //     PickAreaODTO pickAreaODTO = ddPickAreaClient.queryUnassignedPickArea(shopId);
    //     if (Objects.nonNull(pickAreaODTO)) {
    //         pickAreaIdList.add(pickAreaODTO.getPickAreaId());
    //     }
    //
    //     DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
    //     if (Objects.nonNull(ddPickAreaEmployee)) {
    //         Long pickAreaId = ddPickAreaEmployee.getPickAreaId();
    //         pickAreaIdList.add(pickAreaId);
    //     }
    //     return pickAreaIdList;
    // }

    private static List<Integer> initQueryPickStatus(PartitionPickOrderPageIDTO req) {
        Boolean completed = req.getCompleted();
        List<Integer> pickStatus = new ArrayList<>(4);
        if (BooleanUtils.isTrue(completed)) {
            pickStatus.add(XdPickOrderStatusEnum.FINISH.convert());
            pickStatus.add(XdPickOrderStatusEnum.CANCEL.convert());
        } else {
            pickStatus.add(XdPickOrderStatusEnum.WAIT.convert());
            pickStatus.add(XdPickOrderStatusEnum.MIDDLE.convert());
            pickStatus.add(XdPickOrderStatusEnum.WAIT_HANDOVER.convert());
        }
        return pickStatus;
    }

    private Date calculatePickWarnTime(Date orderDeliveryBeginTime, int distributionBeginWarningTime) {
        if (orderDeliveryBeginTime != null && distributionBeginWarningTime > 0) {
            return DateUtil.addMinute(orderDeliveryBeginTime, -distributionBeginWarningTime);
        }
        return null;
    }

    private String getPackingStationName(Long packingStationId, Map<Long, DdPackingStation> finalPackingStationMap) {
        if (packingStationId != null) {
            DdPackingStation station = finalPackingStationMap.get(packingStationId);
            if (station != null) {
                return station.getPackingPort();
            }
        }
        return null;
    }

    private int getDistributionBeginWarningTime() {
        // 处理分期拣货预警时间
        XdBackSettingODTO xdBackSettingDetails = xdBackSettingService.findXdBackSettingDetails();
        return Optional.ofNullable(xdBackSettingDetails).map(XdBackSettingODTO::getDistributionBeginWarning).filter(time -> Objects.nonNull(time) && time.intValue() > 0).orElse(0);

    }

    /**
     * 合单拣货详情
     *
     * @param req
     * @return
     */
    public List<GroupPickDetailODTO> groupPickDetail(GroupPickDetailIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Set<Long> pickPartitionOrderIdList = req.getPickPartitionOrderIdList();
        QYAssert.isTrue(SpringUtil.isNotEmpty(pickPartitionOrderIdList), "请选择拣货单");

        if (YesOrNoEnums.YES.getCode().equals(req.getCheckPickStatus())) {
            checkAndListPickPartitionOrder(pickPartitionOrderIdList);
        }

        Long shopId = tokenInfo.getShopId();
        List<GroupPickDetailVO> groupPickDetailVOList = ddPickPartitionOrderMapper.groupPickDetail(shopId, null, pickPartitionOrderIdList);

        if (SpringUtil.isEmpty(groupPickDetailVOList)) {
            return Collections.emptyList();
        }

        Boolean singlePartitionPickFlag = pickPartitionOrderIdList.size() == 1;
        return buildGroupPickDetailODTOList(tokenInfo.getShopId(), groupPickDetailVOList, singlePartitionPickFlag);
    }

    private List<GroupPickDetailODTO> buildGroupPickDetailODTOList(Long shopId, List<GroupPickDetailVO> groupPickDetailVOList, Boolean singlePartitionPickFlag) {
        Map<String, List<GroupPickDetailVO>> groupByCommodityId = groupPickDetailVOList.stream()
                .collect(Collectors.groupingBy(GroupPickDetailVO::getCommodityId,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream().sorted(Comparator.comparing(GroupPickDetailVO::getOrderNum))
                                        .collect(Collectors.toList()))));

        Set<String> commodityIdSet = groupByCommodityId.keySet();

        Map<String, GoodsAllocationCommodity> goodsAllocationMap = queryCommodityGoodsAllocationMap(shopId, commodityIdSet);
        Map<String, String> displayPositionMap = queryCommodityGoodsDisplayPositionMap(shopId, commodityIdSet);
        Map<Long, String> processNameMap = queryPickOrderItemIdProcessName(groupPickDetailVOList);
        Map<Long, List<String>> barCodeMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIdSet.stream().map(Long::parseLong).collect(Collectors.toList()));

        List<GroupPickDetailODTO> result = new ArrayList<>(commodityIdSet.size());

        if (singlePartitionPickFlag) {
            groupPickDetailVOList.forEach(groupPickDetailVO ->
                    result.add(buildSinglePartitionPickDetail(shopId, groupPickDetailVO, processNameMap, barCodeMap, goodsAllocationMap, displayPositionMap)));
        } else {
            commodityIdSet.forEach(commodityId -> {
                List<GroupPickDetailVO> details = groupByCommodityId.get(commodityId);
                GroupPickDetailODTO groupPickDetailODTO = buildCombinedPickDetail(shopId, commodityId, details, processNameMap, barCodeMap, goodsAllocationMap, displayPositionMap);
                if (groupPickDetailODTO != null) {
                    result.add(groupPickDetailODTO);
                }
            });
        }

        return sortGroupPickDetailODTOList(result);
    }

    private GroupPickDetailODTO buildSinglePartitionPickDetail(Long shopId, GroupPickDetailVO detailVO, Map<Long, String> processNameMap, Map<Long, List<String>> barCodeMap, Map<String, GoodsAllocationCommodity> goodsAllocationMap, Map<String, String> displayPositionMap) {
        GroupPickDetailODTO odto = new GroupPickDetailODTO();
        String commodityId = detailVO.getCommodityId();
        odto.setBarCodeList(barCodeMap.get(Long.parseLong(commodityId)));
        odto.setOrderStatus(detailVO.getOrderStatus());
        odto.setReceiveMan(detailVO.getReceiveMan());
        odto.setReceiveMobile(detailVO.getReceiveMobile());
        odto.setOrderCode(detailVO.getOrderCode());

        if (YesOrNoEnums.YES.getCode().equals(detailVO.getIsComplete()) || Objects.equals(detailVO.getPickPartitionOrderStatus(), XdPickOrderStatusEnum.WAIT_HANDOVER.getCode())) {
            odto.setAlreadyPicked(Boolean.TRUE);
        }

        GroupPickOrderDetailODTO orderDetailODTO = BeanCloneUtils.copyTo(detailVO, GroupPickOrderDetailODTO.class);
        orderDetailODTO.setProcessName(processNameMap.get(orderDetailODTO.getPickOrderItemId()));

        odto.setCommodityPackageKind(detailVO.getCommodityPackageKind());
        odto.setCommodityId(commodityId);
        odto.setStallId(detailVO.getStallId());
        odto.setGroupPickOrderDetailList(Collections.singletonList(orderDetailODTO));

        setPickQuantityAndNumber(Collections.singletonList(detailVO), odto);
        setGoodsAllocationOrDisplayPosition(shopId, detailVO.getStallId(), commodityId, odto, goodsAllocationMap, displayPositionMap);

        return odto;
    }

    private GroupPickDetailODTO buildCombinedPickDetail(Long shopId, String commodityId, List<GroupPickDetailVO> details, Map<Long, String> processNameMap, Map<Long, List<String>> barCodeMap, Map<String, GoodsAllocationCommodity> goodsAllocationMap, Map<String, String> displayPositionMap) {
        List<GroupPickOrderDetailODTO> orderDetails = details.stream()
                .filter(detail -> YesOrNoEnums.NO.getCode().equals(detail.getIsComplete()) && !Objects.equals(detail.getPickPartitionOrderStatus(), XdPickOrderStatusEnum.WAIT_HANDOVER.getCode()))
                .map(detail -> {
                    GroupPickOrderDetailODTO orderDetail = BeanCloneUtils.copyTo(detail, GroupPickOrderDetailODTO.class);
                    orderDetail.setProcessName(processNameMap.get(orderDetail.getPickOrderItemId()));
                    return orderDetail;
                })
                .collect(Collectors.toList());

        if (orderDetails.isEmpty()) return null;

        GroupPickDetailODTO odto = new GroupPickDetailODTO();
        odto.setBarCodeList(barCodeMap.get(Long.parseLong(commodityId)));
        odto.setCommodityPackageKind(details.get(0).getCommodityPackageKind());
        odto.setCommodityId(commodityId);
        odto.setStallId(details.get(0).getStallId());
        odto.setGroupPickOrderDetailList(orderDetails);

        setPickQuantityAndNumber(details, odto);
        setGoodsAllocationOrDisplayPosition(shopId, details.get(0).getStallId(), commodityId, odto, goodsAllocationMap, displayPositionMap);

        return odto;
    }

    private void setGoodsAllocationOrDisplayPosition(Long shopId, Long stallId, String commodityId, GroupPickDetailODTO odto, Map<String, GoodsAllocationCommodity> goodsAllocationMap, Map<String, String> displayPositionMap) {
        String mapKey = buildShopIdStallIdCommodityMapKey(shopId, stallId, Long.parseLong(commodityId));
        GoodsAllocationCommodity allocation = goodsAllocationMap.get(mapKey);

        if (Objects.nonNull(allocation) && SpringUtil.hasText(allocation.getGoodsAllocationCode())) {
            odto.setGoodsAllocationCode(allocation.getGoodsAllocationCode());
            odto.setGoodsAllocationSortNum(allocation.getSortNum());
        } else {
            odto.setDisplayPositionName(displayPositionMap.get(mapKey));
        }
    }

    private List<GroupPickDetailODTO> sortGroupPickDetailODTOList(List<GroupPickDetailODTO> list) {
        return list.stream().sorted(
                Comparator.comparing((GroupPickDetailODTO o) -> o.getAlreadyPicked() != null) // 未拣在前
                        .thenComparing(o -> {
                            // 分组逻辑：按你要求的顺序进行分组排序
                            if (o.getDisplayPositionName() != null) {
                                return 0; // 第一优先级：有陈列位
                            } else if (o.getDisplayPositionName() == null && o.getGoodsAllocationCode() == null) {
                                return 1; // 第二优先级：都没有
                            } else {
                                return 2; // 第三优先级：只有拣货位
                            }
                        })
                        .thenComparing(GroupPickDetailODTO::getCategorySortNum, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(GroupPickDetailODTO::getDisplayPositionName, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(GroupPickDetailODTO::getGoodsAllocationSortNum, Comparator.nullsLast(Comparator.naturalOrder()))
        ).collect(Collectors.toList());

    }

    private static void setPickQuantityAndNumber(List<GroupPickDetailVO> groupPickDetailVOS, GroupPickDetailODTO groupPickDetailODTO) {
        BigDecimal totalPickQuantity = groupPickDetailVOS.stream()
                .map(GroupPickDetailVO::getQuantity)
                .reduce(BigDecimal::add)
                .orElse(XdWmsConstantUtil.PICK_QUANTITY_CONSTANT);
        groupPickDetailODTO.setTotalPickQuantity(totalPickQuantity);

        Integer totalPickNumber = groupPickDetailVOS.stream().map(vo -> vo.getStockNumber() == null ? 0 : vo.getStockNumber())
                .reduce(Integer::sum).orElse(0);
        groupPickDetailODTO.setTotalPickNumber(totalPickNumber);

        BigDecimal pickedQuantity = groupPickDetailVOS.stream()
                .map(GroupPickDetailVO::getPickQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(XdWmsConstantUtil.PICK_QUANTITY_CONSTANT);
        groupPickDetailODTO.setPickedQuantity(pickedQuantity);

        Integer pickedNumber = groupPickDetailVOS.stream()
                .map(data -> Objects.isNull(data.getPickNumber()) ? 0 : data.getPickNumber())
                .reduce(Integer::sum).orElse(0);
        groupPickDetailODTO.setPickedNumber(pickedNumber);

        Integer waitPickNumber = Optional.of(totalPickNumber).orElse(0) - (Optional.of(pickedNumber).orElse(0));
        groupPickDetailODTO.setWaitPickNumber(waitPickNumber);
        if (Objects.equals(waitPickNumber, 0)) {
            groupPickDetailODTO.setWaitPickQuantity(XdWmsConstantUtil.PICK_QUANTITY_CONSTANT);
        } else {
            BigDecimal waitPickQuantity = Optional.of(totalPickQuantity).orElse(BigDecimal.ZERO).subtract(Optional.of(pickedQuantity).orElse(BigDecimal.ZERO));
            groupPickDetailODTO.setWaitPickQuantity(waitPickQuantity);
        }
    }

    private Map<Long, String> queryPickOrderItemIdProcessName(List<GroupPickDetailVO> groupPickDetailVOList) {
        List<Long> processPickOrderIdList = groupPickDetailVOList.stream()
                .filter(data -> YesOrNoEnums.YES.getCode().equals(data.getIsProcess()))
                .map(GroupPickDetailVO::getPickOrderId)
                .collect(Collectors.toList());

        if (SpringUtil.isEmpty(processPickOrderIdList)) {
            return Collections.emptyMap();
        }

        List<PickWorkOrder> pickWorkOrders = pickWorkOrderService.listByPickOrderIds(processPickOrderIdList);
        return pickWorkOrders.stream()
                .collect(Collectors.toMap(
                        PickWorkOrder::getPickOrderItemId,
                        item -> Optional.ofNullable(item.getProcessName()).orElse("")
                ));

    }

    private Map<String, GoodsAllocationCommodity> queryCommodityGoodsAllocationMap(Long shopId, Collection<String> commodityIdSet) {
        List<Long> commodityIdList = commodityIdSet.stream().map(Long::parseLong).collect(Collectors.toList());
        List<GoodsAllocationCommodity> goodsAllocationCommodities = goodsAllocationCommodityService.listPickingAreaByCommodityIdsWithAllocationCode(shopId, commodityIdList);

        return goodsAllocationCommodities.stream().collect(toMap(data -> buildShopIdStallIdCommodityMapKey(data.getShopId(), data.getStallId(), data.getCommodityId()), Function.identity()));
    }

    private Map<String, String> queryCommodityGoodsDisplayPositionMap(Long shopId, Collection<String> commodityIdSet) {
        List<Long> commodityIdList = commodityIdSet.stream().map(Long::parseLong).collect(Collectors.toList());
        List<DdDisplayPositionCommodityVO> ddDisplayPositionCommodityVOList = ddDisplayPositionCommodityService.listByShopIdAndCommodityIdListWithShelveAndPositionName(shopId, commodityIdList);

        return ddDisplayPositionCommodityVOList.stream().collect(toMap(data -> buildShopIdStallIdCommodityMapKey(data.getShopId(), data.getStallId(), data.getCommodityId()), DdDisplayPositionCommodityVO::getDisplayPositionName));
    }

    private static String buildShopIdStallIdCommodityMapKey(Long shopId, Long stallId, Long commodityId) {
        return shopId + "_" + stallId + "_" + commodityId;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createPickOrder(OrderDTO orderDTO) {
        LockUtils.checkLock(LockUtils.PICK_CREATE, String.valueOf(orderDTO.getOrderId()), 1, TimeUnit.SECONDS);
        PickOrderResult pickOrderByOrderId = pickOrderService.getPickOrderByOrderId(orderDTO.getOrderId());
        if (Objects.nonNull(pickOrderByOrderId)) {
            // 存在拣货单,消息处理过了
            return;
        }

        String code = codeClient.createCode("PICK_XD_CODE");

        // 接收消息 调用创建拣货单
        PickOrder pickOrder = new PickOrder();
        pickOrder.setPickCode(code);
        pickOrder.setOrderId(orderDTO.getOrderId());
        String orderCode = orderDTO.getOrderCode();
        pickOrder.setOrderCode(orderCode);
        Long shopId = orderDTO.getShopId();
        pickOrder.setWarehouseId(shopId);
        pickOrder.setOrderDeliveryBeginTime(orderDTO.getDeliveryBeginTime());
        pickOrder.setOrderDeliveryEndTime(orderDTO.getDeliveryEndTime());
        pickOrder.setSourceType(orderDTO.getSourceType());
        pickOrder.setOriginalOrderCode(orderDTO.getOriginalOrderCode());
        pickOrder.setOrderType(null == orderDTO.getOrderType() ? YesOrNoEnums.NO.getCode() : orderDTO.getOrderType());
        pickOrder.setOrderNum(orderDTO.getOrderNum());
        pickOrder.setPickId(XdWmsConstantUtil.SYSTEM_PICK_ID);
        pickOrderService.save(pickOrder);

        List<OrderItemDTO> orderItemDTOS = orderDTO.getItems();
        Set<Long> stallIdSet = orderItemDTOS.stream().map(OrderItemDTO::getStallId).collect(Collectors.toSet());

        Map<Long, Long> stallIdPickAreaIdMap = stallService.queryStallIdPickAreaIdMap(shopId, stallIdSet);
        Set<Long> pickAreaIdSet = new HashSet<>(stallIdPickAreaIdMap.values());

        Map<Long, Long> pickAreaIdPickPartitionOrderIdMap = new HashMap<>(pickAreaIdSet.size());
        List<DdPickPartitionOrder> ddPickPartitionOrderSaveList = new ArrayList<>(pickAreaIdSet.size());
        List<PartitionPickOrderLogVO> partitionPickOrderLogVOList = new ArrayList<>(pickAreaIdSet.size());
        for (Long pickAreaId : pickAreaIdSet) {
            Long pickOrderId = pickOrder.getId();

            DdPickPartitionOrder ddPickPartitionOrder = buildSaveDdPickPartitionOrder(pickAreaId, pickOrderId);

            pickAreaIdPickPartitionOrderIdMap.put(pickAreaId, ddPickPartitionOrder.getId());
            ddPickPartitionOrderSaveList.add(ddPickPartitionOrder);

            PartitionPickOrderLogVO partitionPickOrderLogVO = bulidPartitionPickOrderLogVO(null, shopId, PickPartitionOrderOperationTypeEnum.PARTION_PICK_ORDER_CREATE, code,
                    ddPickPartitionOrder.getPickPartitionOrderCode(), pickAreaId, orderCode, null, null);
            partitionPickOrderLogVOList.add(partitionPickOrderLogVO);
        }

        this.saveBatch(ddPickPartitionOrderSaveList);


        // 拣货单明细增加成本价
        List<Long> commodityIdList = orderDTO.getItems().stream().map(OrderItemDTO::getCommodityId).collect(Collectors.toList());
        Map<Long, ShopCommodityStockODTO> shopCommodityMap = shopCommodityClient.queryShopCommodityValidStock(shopId, commodityIdList);
        List<PickOrderItem> pickOrderItemSaveList = new ArrayList<>(orderItemDTOS.size());
        for (OrderItemDTO orderItem : orderItemDTOS) {
            PickOrderItem pickItem = buildSavePickOrderItem(orderDTO, orderItem, pickOrder);

            Long pickAreaId = stallIdPickAreaIdMap.get(orderItem.getStallId());
            Long pickPartitionOrderId = pickAreaIdPickPartitionOrderIdMap.get(pickAreaId);
            pickItem.setPickPartitionOrderId(pickPartitionOrderId);
            pickItem.setIsComplete(YesOrNoEnums.NO.getCode());
            if (null != shopCommodityMap && shopCommodityMap.get(orderItem.getCommodityId()) != null) {
                pickItem.setWeightPrice(shopCommodityMap.get(orderItem.getCommodityId()).getWeightPrice());
            }
            pickOrderItemSaveList.add(pickItem);
        }
        pickOrderItemService.saveBatch(pickOrderItemSaveList);

        // 修改冻结逻辑,根据冻结（需冻结、已冻结）处理状态
        pickOrderService.updateStockOutStatus(orderDTO.getOrderId(), orderCode, orderDTO.getItems());

        if (XdOrderTypeEnum.ORDINARY.getCode() == pickOrder.getOrderType()
                || XdOrderTypeEnum.XD_WAREHOUSE.getCode() == pickOrder.getOrderType()
                || XdOrderTypeEnum.FARM.getCode() == pickOrder.getOrderType()) {
            radioMsgClient.broadcast(shopId, RadioTemplateEnum.NEW_ORDER.getCode());
        }
        if (XdOrderTypeEnum.THIRD.getCode() == pickOrder.getOrderType()) {
            radioMsgClient.sendDynamicMsgByShopId(shopId, RadioTemplateEnum.NEW_THIRD_ORDER.getName());
        }

        // 记录操作日志
        xdSendLogService.sendLog(partitionPickOrderLogVOList, "t_log_dd_pick_partition_order");
    }

    private static PickOrderItem buildSavePickOrderItem(OrderDTO orderDTO, OrderItemDTO orderItem, PickOrder pickOrder) {
        PickOrderItem pickItem = new PickOrderItem();
        pickItem.setId(IdWorker.getId());
        pickItem.setOrderId(orderDTO.getOrderId());
        pickItem.setStallId(orderItem.getStallId());
        pickItem.setOrderItemId(orderItem.getItemId());
        pickItem.setPickOrderId(pickOrder.getId());
        pickItem.setCommodityId(orderItem.getCommodityId());
        pickItem.setStockNumber(orderItem.getStockNumber());
        pickItem.setQuantity(orderItem.getQuantity());
        pickItem.setIsProcess(orderItem.getIsProcess());
        pickItem.setIsWeight(orderItem.getIsWeight());
        pickItem.setOriginSubBizId(orderItem.getOriginSubBizId());
        return pickItem;
    }

    private DdPickPartitionOrder buildSaveDdPickPartitionOrder(Long pickAreaId, Long pickOrderId) {
        DdPickPartitionOrder ddPickPartitionOrder = new DdPickPartitionOrder();
        ddPickPartitionOrder.setId(IdWorker.getId());
        ddPickPartitionOrder.setPickOrderId(pickOrderId);

        String pickPartitionOrderCode = getPickPartitionOrderCode();
        ddPickPartitionOrder.setPickPartitionOrderCode(pickPartitionOrderCode);

        ddPickPartitionOrder.setPickAreaId(pickAreaId);
        ddPickPartitionOrder.setPickStatus(XdPickOrderStatusEnum.WAIT.convert());
        ddPickPartitionOrder.setCreateId(XdWmsConstantUtil.SYSTEM_PICK_ID);
        ddPickPartitionOrder.setUpdateId(XdWmsConstantUtil.SYSTEM_PICK_ID);
        return ddPickPartitionOrder;
    }

    public String getPickPartitionOrderCode() {
        Date date = new Date();
        String orderDate = DateUtil.get4yMdNoDash(date);
        RAtomicLong atomicLong = redissonClient.getAtomicLong("WMS:PICK_PARTITION_ORDER_CODE" + orderDate);
        long sid = 10001L;
        if (atomicLong.isExists()) {
            sid = atomicLong.incrementAndGet();
        } else {
            atomicLong.set(sid);
        }
        atomicLong.expire(2, TimeUnit.DAYS);
        return "ZJHD" + orderDate + sid;
    }

    @Transactional
    public void cancelPickOrder(PickOrder pickOrder, Long orderId, Long userId) {

        Order order = orderMapper.selectById(orderId);

        // 团购单 取消时[未提货完成]无拣货单
        if (!(pickOrder == null && OrderTypeEnum.GROUP.getCode().equals(order.getOrderType()))) {
            QYAssert.isTrue(pickOrder != null, "无效拣货单");
            QYAssert.isTrue(PickStatusEnum.CANCEL.getCode() != pickOrder.getPickStatus(), "拣货单已取消");
            QYAssert.isTrue(PickStatusEnum.FINISH.getCode() != pickOrder.getPickStatus(), "拣货单已完成");

            // 京东到家 无法后台拣货取消
            if (userId != null && OrderSourceTypeEnum.JDDJ.getCode().equals(pickOrder.getSourceType())) {
                throw new BizLogicException("无法取消拣货，请联系顾客取消订单。");
            }
            // 取消拣货单
            PickOrderContext pickOrderContext = new PickOrderContext();
            pickOrderContext.setPickOrder(pickOrder);
            pickOrderContext.doCancel(mqSenderComponent, meiTuanPickOrderService, userId);
            pickOrderService.updateById(pickOrder);

            if (OrderSourceTypeEnum.ELM.getCode().equals(pickOrder.getSourceType()) || OrderSourceTypeEnum.MTSG.getCode().equals(pickOrder.getSourceType())) {
                pickOrderService.orderCancelPickOrder(pickOrder.getOrderId());
            }

            // 完成配送取货位
            warehouseShelfService.completeShelfDelivery(orderId, pickOrder.getShelfNo());

            List<DdPickPartitionOrder> ddPickPartitionOrders = listByPickOrderId(pickOrder.getId());
            // 完成||取消 加工单
            completeShelfWork(ddPickPartitionOrders.get(0).getId(), null, Boolean.FALSE, null);

            List<DdPickPartitionOrder> waitPickPartitionList = ddPickPartitionOrders.stream()
                    .filter(ddPickPartitionOrder -> XdPickOrderStatusEnum.WAIT.convert().equals(ddPickPartitionOrder.getPickStatus()))
                    .collect(Collectors.toList());

            if (SpringUtil.isNotEmpty(waitPickPartitionList)) {
                List<DdPickPartitionOrder> updateList = new ArrayList<>(waitPickPartitionList.size());
                for (DdPickPartitionOrder ddPickPartitionOrder : waitPickPartitionList) {
                    DdPickPartitionOrder update = new DdPickPartitionOrder();
                    update.setId(ddPickPartitionOrder.getId());
                    update.setPickStatus(XdPickOrderStatusEnum.CANCEL.convert());
                    updateList.add(update);
                }
                updateBatchById(updateList);

                sendPartitionPickOrderLog(FastThreadLocalUtil.getQY(), pickOrder, waitPickPartitionList);
            }

        }
        // 解冻库存 冻结多少 解冻多少
        shopCommodityService.stockUnFreeze(orderId, order.getOrderCode(), order.getShopId());

        // 释放打包口
        releasePackingStation(orderId);
    }

    /**
     * 保存分区拣货日志
     *
     * @param tokenInfo
     * @param operationTypeEnum
     * @param pickCode
     * @param pickPartitionOrderCode
     * @param pickAreaId
     * @param orderCode
     * @param pickStatusEnum
     * @param pickId
     */
    public void saveLog(TokenInfo tokenInfo,
                        PickPartitionOrderOperationTypeEnum operationTypeEnum,
                        String pickCode,
                        String pickPartitionOrderCode,
                        Long pickAreaId,
                        String orderCode,
                        PickStatusEnum pickStatusEnum,
                        Long pickId) {

        Long shopId = tokenInfo.getShopId();

        PartitionPickOrderLogVO saveLogVO = bulidPartitionPickOrderLogVO(tokenInfo, shopId, operationTypeEnum, pickCode,
                pickPartitionOrderCode, pickAreaId, orderCode, pickStatusEnum, pickId);
        xdSendLogService.sendLog(Collections.singletonList(saveLogVO), "t_log_dd_pick_partition_order");
    }

    public PartitionPickOrderLogVO bulidPartitionPickOrderLogVO(
            TokenInfo tokenInfo,
            Long shopId,
            PickPartitionOrderOperationTypeEnum operationTypeEnum,
            String pickCode,
            String pickPartitionOrderCode,
            Long pickAreaId,
            String orderCode,
            PickStatusEnum pickStatusEnum,
            Long pickId
    ) {
        PartitionPickOrderLogVO saveLogVO = new PartitionPickOrderLogVO();
        saveLogVO.setShopId(shopId);
        saveLogVO.setOperationType(operationTypeEnum.getCode());
        saveLogVO.setPickCode(pickCode);
        saveLogVO.setPickPartitionOrderCode(pickPartitionOrderCode);
        saveLogVO.setPickAreaId(pickAreaId);
        saveLogVO.setOrderCode(orderCode);
        saveLogVO.setStatus(Optional.ofNullable(pickStatusEnum).map(PickStatusEnum::getCode).orElse(null));
        saveLogVO.setPickId(pickId);
        saveLogVO.setOperateTime(new Date());

        String operateUserCode = Optional.ofNullable(tokenInfo).map(TokenInfo::getEmployeeNumber).orElse("-1");
        saveLogVO.setOperateUserCode(operateUserCode);

        String realName = Optional.ofNullable(tokenInfo).map(TokenInfo::getRealName).orElse("系统");
        saveLogVO.setOperateUserName(realName);

        Long operateUserId = Optional.ofNullable(tokenInfo).map(TokenInfo::getUserId).orElse(-1L);
        saveLogVO.setOperateUserId(operateUserId);
        renderService.render(saveLogVO, "savePartitionPickOrderLogVO");

        if (Objects.equals(pickId, XdWmsConstantUtil.SYSTEM_PICK_ID)) {
            saveLogVO.setPickEmployeeName(XdWmsConstantUtil.SYSTEM_PICK_NAME);
        }
        return saveLogVO;
    }

    public void distributePartitionPickOrderList(String shopId) {
        redisLockService.lock(RedisLockEnums.PICK_PARTITION_ORDER_DISTRIBUTE, RedisLockEnums.PICK_PARTITION_ORDER_DISTRIBUTE.getPrefix(), () -> {
            try {
                DictionaryODTO dictionary = dictionaryClient.getDictionaryById(DictionaryEnums.BIG_WAREHOUSE_DISTRIBUTE.getId());
                configBeginTime = Integer.valueOf(dictionary.getOptionValue());
            } catch (Exception e) {
                log.error("获取分配提前时间失败");
            }
            List<PickOrder> pickOrderList = pickOrderService.listWaitDistributePickOrderList(PickingMethodEnum.ZONE_ORDER_PICKING, configBeginTime, shopId);

            if (SpringUtil.isEmpty(pickOrderList)) {
                return;
            }

            Map<Long, PickOrder> idPickOrderMap = pickOrderList.stream().collect(toMap(PickOrder::getId, Function.identity(), (old, now) -> old));
            Set<Long> pickOrderIdList = idPickOrderMap.keySet();

            List<DdPickPartitionOrder> ddPickPartitionOrders = listByPickOrderIdList(pickOrderIdList);

            log.info("分区拣货单分配：查询到返回内容 -> {} ", JsonUtil.java2json(ddPickPartitionOrders));

            Map<Long, List<DdPickPartitionOrder>> groupByPickOrderId = ddPickPartitionOrders.stream()
                    .collect(Collectors.groupingBy(DdPickPartitionOrder::getPickOrderId));

            for (PickOrder pickOrder : pickOrderList) {

                Long pickOrderId = pickOrder.getId();

                try {
                    List<DdPickPartitionOrder> ddPickPartitionOrderList = groupByPickOrderId.get(pickOrderId);
                    if (SpringUtil.isEmpty(ddPickPartitionOrderList)) {
                        log.warn("拣货单：[{}],没有需要分配的拣货子单", pickOrderId);
                        continue;
                    }

                    distributePartitionPickOrder(pickOrder, ddPickPartitionOrderList, Boolean.TRUE);
                } catch (Exception e) {
                    log.error("分区拣货单分配异常，pickOrderId:[{}]", pickOrderId, e);
                }
            }

        });
    }

    private List<DdPickPartitionOrder> listByPickOrderIdList(Collection<Long> pickOrderIdList) {
        if (SpringUtil.isEmpty(pickOrderIdList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<DdPickPartitionOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DdPickPartitionOrder::getPickOrderId, pickOrderIdList)
                .eq(DdPickPartitionOrder::getPickStatus, XdPickOrderStatusEnum.WAIT.convert())
                .isNull(DdPickPartitionOrder::getPickId);
        return this.baseMapper.selectList(queryWrapper);
    }

    public void distributePartitionPickOrder(PickOrder pickOrder, List<DdPickPartitionOrder> ddPickPartitionOrderList, Boolean distributePicker) {
        Long pickOrderId = pickOrder.getId();

        redisLockService.lock(RedisLockEnums.DISTRIBUTE_PARTITION_PICK_ORDER, pickOrderId.toString(), () -> {

            transactionTemplate.execute(action -> {
                // 打包口分配
                acquirePackingStationAndShelfNo(pickOrderId);

                // 子单分配拣货人
                if (distributePicker) {
                    distributePartitionPicker(pickOrder, ddPickPartitionOrderList);
                }

                PickOrder pickOrderInfo = pickOrderService.getById(pickOrderId);
                if (Objects.nonNull(pickOrderInfo) && Objects.equals(YesOrNoEnums.YES.getCode(), pickOrderInfo.getHasProcess())) {
                    return null;
                }

                // 所有商品 绑定加工位就生成加工单
                List<PickOrderItem> pickOrderItems = pickOrderItemService.listByPickOrderId(pickOrderId);
                List<Long> processList = pickOrderItems.stream().map(PickOrderItem::getCommodityId).collect(Collectors.toList());

                Map<Long, WorkCommodityListResult> workCommodityMap;
                List<WorkCommodityListResult> workCommodities = warehouseWorkCommodityService.queryWorkCommodityList(processList, pickOrder.getWarehouseId());
                if (SpringUtil.isNotEmpty(workCommodities)) {
                    workCommodityMap = workCommodities.stream().collect(toMap(WorkCommodityListResult::getId, it -> it));
                    pickOrder.setHasProcess(YesOrNoEnums.YES.getCode());
                    pickOrderService.updateById(pickOrder);


                    LambdaQueryWrapper<XdOrderItem> xdOrderItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    xdOrderItemLambdaQueryWrapper.eq(XdOrderItem::getOrderId, pickOrder.getOrderId())
                            .in(XdOrderItem::getCommodityId, workCommodityMap.keySet());
                    List<XdOrderItem> xdOrderItems = orderItemMapper.selectList(xdOrderItemLambdaQueryWrapper);
                    Map<Long, XdOrderItem> idOrderItemMap = xdOrderItems.stream()
                            .collect(toMap(XdOrderItem::getId, Function.identity()));

                    for (PickOrderItem pickOrderItem : pickOrderItems) {

                        if (Objects.isNull(workCommodityMap.get(pickOrderItem.getCommodityId()))) {
                            continue;
                        }

                        PickWorkOrder workOrder = new PickWorkOrder();
                        workOrder.setOrderId(pickOrder.getOrderId());
                        workOrder.setPickOrderId(pickOrderId);
                        workOrder.setPickOrderItemId(pickOrderItem.getId());
                        workOrder.setCommodityId(pickOrderItem.getCommodityId());

                        XdOrderItem xdOrderItem = idOrderItemMap.get(pickOrderItem.getOrderItemId());
                        workOrder.setProcessId(xdOrderItem.getProcessId());
                        workOrder.setProcessName(xdOrderItem.getProcessName());

                        // 商品加工点
                        workOrder.setWorkId(workCommodityMap.get(workOrder.getCommodityId()).getWorkId());
                        workOrder.setWorkName(workCommodityMap.get(workOrder.getCommodityId()).getWorkName());

                        // 分配加工点取货位
                        String workShelfNo = warehouseShelfService.distributeShelfWork();
                        if (workShelfNo == null) {
                            workShelfNo = PickOrderService.INIT_SHELF_NO;
                        }
                        workOrder.setShelfNo(workShelfNo);
                        pickWorkOrderService.save(workOrder);

                        pickWorkOrderService.printWorkOrder(workOrder.getId(), pickOrder.getWarehouseId());
                    }

                }
                return null;
            });

        });
    }

    @Transactional
    public void distributePartitionPicker(PickOrder pickOrder, List<DdPickPartitionOrder> ddPickPartitionOrderList) {
        QYAssert.notEmpty(ddPickPartitionOrderList, "分区拣货单为空");

        List<DdPickPartitionOrder> ddPickPartitionOrderUpdateList = new ArrayList<>(ddPickPartitionOrderList.size());

        List<PartitionPickOrderLogVO> partitionPickOrderLogVOList = new ArrayList<>(ddPickPartitionOrderList.size());
        for (DdPickPartitionOrder ddPickPartitionOrder : ddPickPartitionOrderList) {
            Long picker = ddPickAreaEmployeeService.distributePicker(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId());

            // 更新分区子单打包员id
            if (Objects.nonNull(picker)) {
                DdPickPartitionOrder update = new DdPickPartitionOrder();
                update.setId(ddPickPartitionOrder.getId());
                update.setPickId(picker);
                ddPickPartitionOrderUpdateList.add(update);

                // 构建日志
                PartitionPickOrderLogVO partitionPickOrderLogVO = bulidPartitionPickOrderLogVO(null, pickOrder.getWarehouseId(),
                        PickPartitionOrderOperationTypeEnum.DISTRIBUTE_PICKER, pickOrder.getPickCode(),
                        ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                        PickStatusEnum.WAIT, picker);
                partitionPickOrderLogVOList.add(partitionPickOrderLogVO);
            }
        }

        if (SpringUtil.isNotEmpty(ddPickPartitionOrderUpdateList)) {
            this.updateBatchById(ddPickPartitionOrderUpdateList);

            // 新拣货员的任务数+1，并语音提醒新拣货员有新单
            Set<Long> employeeIdList = ddPickPartitionOrderUpdateList.stream()
                    .map(DdPickPartitionOrder::getPickId)
                    .collect(Collectors.toSet());
            sendPickOrderMessage(employeeIdList);

            // 发送拣货子单 分配拣货员 日志
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    xdSendLogService.sendLog(partitionPickOrderLogVOList, "t_log_dd_pick_partition_order");
                }
            });
        }
    }

    /**
     * 激光推送消息
     *
     * @param employeeIdList
     */
    public void sendPickOrderMessage(Collection<Long> employeeIdList) {
        log.info("更新分区拣货员 employeeIdList :{} ", employeeIdList);

        LambdaQueryWrapper query = new LambdaQueryWrapper<EmployeeUser>()
                .in(EmployeeUser::getEmployeeId, new ArrayList<>(employeeIdList));
        List<EmployeeUser> employeeList = employeeUserMapper.selectList(query);
        if (CollectionUtils.isNotEmpty(employeeList)) {
            List<Long> userIdList = employeeList.stream().map(item -> item.getUserId()).collect(Collectors.toList());

            // 激光推送新拣货任务,大店定义新的content
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setTypeEnum(AppMessageTypeEnum.ORDER);
            messageDTO.setSendScope(1);
            messageDTO.setTitle("来新订单了，请注意查收！");
            messageDTO.setContent("NEW-DD-PICK-ORDER");
            messageDTO.setUserIds(userIdList);
            messageDTO.setTargetUrl("来新订单了，请注意查收！");
            try {
                zSMessageClient.sendMessage(messageDTO);
            } catch (Exception e) {
                log.error("推送新拣货任务失败", e);
            }
        }
    }


    @Transactional
    public void acquirePackingStationAndShelfNo(Long pickOrderId) {

        redisLockService.lock(RedisLockEnums.ACQUIRE_PACKING_STATION, pickOrderId.toString(), () -> {
            PickOrder pickOrder = pickOrderService.getById(pickOrderId);
            if (Objects.nonNull(pickOrder.getPackingStationId())) {
                return;
            }

            Long packingStationId = ddPackingStationService.distributePackingStation(pickOrder.getWarehouseId(), pickOrder.getOrderId());

            String pickShelfNo = warehouseShelfService.distributeShelfDelivery();
            if (pickShelfNo == null) {
                pickShelfNo = PickOrderService.INIT_SHELF_NO;
            }

            // 更新拣货单打包口
            PickOrder update = new PickOrder();
            update.setId(pickOrderId);
            update.setPackingStationId(packingStationId);
            update.setShelfNo(pickShelfNo);
            pickOrderService.updateById(update);
        });
    }

    @Transactional
    public void handoverDelivery(TokenInfo tokenInfo, HandoverDeliveryIDTO req) {
        Long pickPartitionOrderId = req.getPickPartitionOrderId();
        QYAssert.notNull(pickPartitionOrderId, "分区拣货单id不能为空");

        DdPickPartitionOrder ddPickPartitionOrder = checkAndGetPickPartitionOrder(pickPartitionOrderId);
        Long pickOrderId = ddPickPartitionOrder.getPickOrderId();

        PickOrder pickOrder = pickOrderService.getById(pickOrderId);
        if (Objects.equals(pickOrder.getPickStatus(), XdPickOrderStatusEnum.CANCEL.convert())) {
            QYAssert.isFalse("拣货单已取消，请刷新后重试");
        }

        redisLockService.lock(RedisLockEnums.HANDOVER_DELIVERY_SYNC, pickOrderId.toString(), () -> {
            DdPickPartitionOrder update = new DdPickPartitionOrder();
            update.setId(pickPartitionOrderId);
            update.setPickStatus(XdPickOrderStatusEnum.FINISH.convert());
            update.setHandoverEndTime(new Date());
            this.updateById(update);

            // 维护拣货员分配队列
            ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), ddPickPartitionOrder.getPickId(), -1);

            saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.HANDLOVER_DELIVERY, pickOrder.getPickCode(),
                    ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                    PickStatusEnum.getByCode(XdPickOrderStatusEnum.FINISH.getCode()), tokenInfo.getEmployeeId());

            HandoverDeliveryEvent handoverDeliveryEvent = new HandoverDeliveryEvent(pickOrderId);
            handoverDeliveryEvent.setDdPickPartitionOrders(this.listByPickOrderId(pickOrderId));

            // 发异步事件
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    applicationContext.publishEvent(handoverDeliveryEvent);
                }
            });
        });
    }

    @Transactional
    public void finishPickOrderIfAllPartitionPickOrderFinish(Long pickOrderId, List<DdPickPartitionOrder> allPartitionPickOrderList, List<PickOrderItem> allPickOrderItems) {
        redisLockService.lock(RedisLockEnums.HANDOVER_DELIVERY, pickOrderId.toString(), () -> {
            PickOrder pickOrder = pickOrderService.getById(pickOrderId);
            if (Objects.equals(pickOrder.getPickStatus(), XdPickOrderStatusEnum.CANCEL.convert())) {
                log.warn("拣货单已取消，pickOrderId:{}", pickOrderId);
                return;
            }

            if (Objects.equals(XdPickOrderStatusEnum.FINISH.convert(), pickOrder.getPickStatus())) {
                log.warn("拣货单已完成，不进行后续处理，pickOrderId:{}", pickOrderId);
                return;
            }

            List<DdPickPartitionOrder> ddPickPartitionOrders = SpringUtil.isEmpty(allPartitionPickOrderList)
                    ? listByPickOrderId(pickOrderId) : allPartitionPickOrderList;

            Long finishStatusCount = ddPickPartitionOrders.stream()
                    .filter(it -> XdPickOrderStatusEnum.FINISH.convert().equals(it.getPickStatus()))
                    .count();
            if (!Objects.equals(finishStatusCount.intValue(), ddPickPartitionOrders.size())) {
                log.warn("分区拣货单未全部完成，不进行后续处理，pickOrderId:{}", pickOrderId);
                return;
            }

            String pickOrderCode = pickOrder.getOrderCode();
            Long orderId = pickOrder.getOrderId();

            List<PickOrderItem> pickOrderItems = SpringUtil.isEmpty(allPickOrderItems) ?
                    pickOrderItemService.listByPickOrderId(pickOrderId) : allPickOrderItems;

            ImmutablePair<Long, String> idAndCode = new ImmutablePair<>(orderId, pickOrder.getOrderCode());
            stockServiceAdapter.stockCompletePick(pickOrderId, pickOrderCode, pickOrder.getWarehouseId(), idAndCode, pickOrderItems, pickOrder.getOrderType());

            // 触发订单实发回写
            PickOrderContext pickOrderContext = new PickOrderContext();
            pickOrderContext.setPickOrder(pickOrder);
            pickOrderContext.setPickOrderItems(pickOrderItems);
            pickOrderContext.doComplete(mqSenderComponent, meiTuanPickOrderService, platFormPickOrderService);

            PickOrder pickOrderUpdate = new PickOrder();
            pickOrderUpdate.setId(pickOrderId);
            pickOrderUpdate.setPickStatus(XdPickOrderStatusEnum.FINISH.convert());
            pickOrderUpdate.setPickEndTime(new Date());
            pickOrderService.updateById(pickOrderUpdate);

            xdOrderClient.tiotXdOrderSmallTicketPrint(pickOrder.getOrderId());
        });
    }

    public PickPartitionOrderODTO listPartitionPickOrder(TokenInfo tokenInfo, PickPartitionOrderPageIDTO req) {
        Integer queryType = req.getQueryType();
        QYAssert.notNull(queryType, "参数错误");
        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();

        PickPartitionOrderODTO result = new PickPartitionOrderODTO();

        List<PickPartitionOrderPageODTO> pickPartitionOrderList = null;

        if (Objects.equals(queryType, 1)) {

            pickPartitionOrderList = listPickingStatus(tokenInfo, req);

            result.setPickingOrderNum(SpringUtil.isEmpty(pickPartitionOrderList) ? 0 : pickPartitionOrderList.size());
            DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
            if (Objects.nonNull(ddPickAreaEmployee) && Objects.nonNull(ddPickAreaEmployee.getPickAreaId())) {
                result.setWaitingPickOrderNum(ddPickPartitionOrderMapper.listWaitingStatusOrderNum(shopId, employeeId, ddPickAreaEmployee.getPickAreaId(), intervalDayNum));
            }

            result.setWaitingHandoverOrderNum(ddPickPartitionOrderMapper.listWaitingHandoverOrderNum(shopId, employeeId, intervalDayNum));

        } else if (Objects.equals(queryType, 2)) {

            pickPartitionOrderList = listWaitingStatus(tokenInfo, req);

            result.setWaitingPickOrderNum(SpringUtil.isEmpty(pickPartitionOrderList) ? 0 : pickPartitionOrderList.size());

            result.setPickingOrderNum(ddPickPartitionOrderMapper.listPickingStatusOrderNum(shopId, employeeId, intervalDayNum));

            result.setWaitingHandoverOrderNum(ddPickPartitionOrderMapper.listWaitingHandoverOrderNum(shopId, employeeId, intervalDayNum));

        } else if (Objects.equals(queryType, 3)) {

            pickPartitionOrderList = listWaitingHandover(tokenInfo, req);

            result.setWaitingHandoverOrderNum(SpringUtil.isEmpty(pickPartitionOrderList) ? 0 : pickPartitionOrderList.size());

            result.setPickingOrderNum(ddPickPartitionOrderMapper.listPickingStatusOrderNum(shopId, employeeId, intervalDayNum));

            DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);
            if (Objects.nonNull(ddPickAreaEmployee) && Objects.nonNull(ddPickAreaEmployee.getPickAreaId())) {
                result.setWaitingPickOrderNum(ddPickPartitionOrderMapper.listWaitingStatusOrderNum(shopId, employeeId, ddPickAreaEmployee.getPickAreaId(), intervalDayNum));
            }

            List<CancelPickOrderODTO> cancelPickOrderODTOS = listCancelPickOrder();
            result.setCancelPickOrderNum(SpringUtil.isNotEmpty(cancelPickOrderODTOS) ? cancelPickOrderODTOS.size() : null);
        } else {
            QYAssert.isFalse("参数错误");
        }

        result.setPickPartitionOrderList(pickPartitionOrderList);
        return result;
    }

    @Transactional
    public void distributePicker(TokenInfo tokenInfo, PickChangeDTO pickChangeDTO) {
        DdPickPartitionOrder ddPickPartitionOrder = ddPickPartitionOrderMapper.selectById(pickChangeDTO.getPickOrderId());
        // (待拣货) 且 未分配拣货人 才允许分配
        boolean canChange = ddPickPartitionOrder.getPickStatus().equals(PickStatusEnum.WAIT.getCode()) && null == ddPickPartitionOrder.getPickId();
        QYAssert.isTrue(canChange, "分配失败，拣货单已取消");

        Long pickId = pickChangeDTO.getPickId();

        checkPickEmployeeSatatus(pickId);

        log.warn("分配拣货人,订单号{}, 分配为{}", ddPickPartitionOrder.getPickPartitionOrderCode(), pickId);
        ddPickPartitionOrder.setPickId(pickId);
        ddPickPartitionOrderMapper.updateById(ddPickPartitionOrder);

        PickOrder pickOrder = pickOrderService.getById(ddPickPartitionOrder.getPickOrderId());
        if (Objects.isNull(pickOrder.getPackingStationId())) {
            distributePartitionPickOrder(pickOrder, Collections.singletonList(ddPickPartitionOrder), Boolean.FALSE);
        }

        sendPickOrderMessage(Collections.singletonList(pickId));

        ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), pickId, 1);
        saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.DISTRIBUTE_PICKER, pickOrder.getPickCode(),
                ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                PickStatusEnum.getByCode(ddPickPartitionOrder.getPickStatus()), pickId);
    }

    private void checkPickEmployeeSatatus(Long pickId) {
        DdPickAreaEmployeePageIDTO ddPickAreaEmployeePageIDTO = new DdPickAreaEmployeePageIDTO();
        ddPickAreaEmployeePageIDTO.setShopId(FastThreadLocalUtil.getQY().getShopId());
        ddPickAreaEmployeePageIDTO.setEmployeeId(pickId);
        ddPickAreaEmployeePageIDTO.setWorkStatus(WorkStatusEnum.ENABLE.getCode());
        ddPickAreaEmployeePageIDTO.setEmployeeState(EmployeeStateEnums.在职.getCode());
        List<DdPickAreaEmployeeODTO> ddPickAreaEmployeeODTOS = ddPickAreaEmployeeMapper.pickAreaEmployeePageList(ddPickAreaEmployeePageIDTO);
        QYAssert.notEmpty(ddPickAreaEmployeeODTOS, "拣货员信息发生变化，请刷新后重试");
    }

    public void changePicker(TokenInfo tokenInfo, PickChangeDTO pickChangeDTO) {
        DdPickPartitionOrder ddPickPartitionOrder = ddPickPartitionOrderMapper.selectById(pickChangeDTO.getPickOrderId());
        // (待拣货 或者 拣货中 状态) 且 已分配了拣货人 才允许改派
        boolean canChange = (ddPickPartitionOrder.getPickStatus().equals(PickStatusEnum.MIDDLE.getCode())
                || ddPickPartitionOrder.getPickStatus().equals(PickStatusEnum.WAIT.getCode())) && null != ddPickPartitionOrder.getPickId();
        QYAssert.isTrue(canChange, "改派失败，拣货子单已完成或已取消");

        Long pickId = pickChangeDTO.getPickId();
        checkPickEmployeeSatatus(pickId);

        log.warn("改派拣货人,订单号{}, 从{} --> 改为{}", ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickId(), pickChangeDTO.getPickId());
        Long oldPicker = ddPickPartitionOrder.getPickId();
        ddPickPartitionOrder.setPickId(pickId);
        ddPickPartitionOrderMapper.updateById(ddPickPartitionOrder);

        sendPickOrderMessage(Collections.singletonList(pickId));

        PickOrder pickOrder = pickOrderService.getById(ddPickPartitionOrder.getPickOrderId());

        ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), pickId, 1);
        ddPickAreaEmployeeService.processPickAreaEmployeeOrder(pickOrder.getWarehouseId(), ddPickPartitionOrder.getPickAreaId(), oldPicker, -1);

        saveLog(tokenInfo, PickPartitionOrderOperationTypeEnum.CHANGE_PICKER, pickOrder.getPickCode(),
                ddPickPartitionOrder.getPickPartitionOrderCode(), ddPickPartitionOrder.getPickAreaId(), pickOrder.getOrderCode(),
                PickStatusEnum.getByCode(ddPickPartitionOrder.getPickStatus()), pickId);
    }

    public Integer getGroupPickLimitConfig() {
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        Long employeeId = FastThreadLocalUtil.getQY().getEmployeeId();
        DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeService.getByEmployeeId(shopId, employeeId);

        return Objects.nonNull(ddPickAreaEmployee) && YesOrNoEnums.YES.getCode().equals(ddPickAreaEmployee.getPartitionPickStatus()) ? groupPickMaxSize : 0;
    }

    public MPage<OrderPackingODTO> pageOrderPacking(OrderPackingIDTO req) {
        QYAssert.notNull(req.getQueryType(), "查询类型不能为空");
        Integer queryType = req.getQueryType();
        QYAssert.isTrue(queryType == 0 || queryType == 1, "查询类型错误");
        req.setShopId(FastThreadLocalUtil.getQY().getShopId());

        MPage<OrderPackingODTO> orderPackingODTOMPage = orderMapper.pageOrderPacking(req, intervalDayNum);
        if (Objects.isNull(orderPackingODTOMPage) || SpringUtil.isEmpty(orderPackingODTOMPage.getList())) {
            return orderPackingODTOMPage;
        }

        List<OrderPackingODTO> orderPackingODTOS = orderPackingODTOMPage.getList();

        for (OrderPackingODTO orderPackingODTO : orderPackingODTOS) {
            Integer orderStatus = orderPackingODTO.getOrderStatus();
            XdOrderStatusEnum orderStatusEnum = XdOrderStatusEnum.getByCode(orderStatus);
            if (Objects.equals(queryType, 1)) {
                orderPackingODTO.setShowPackingComplete(Boolean.FALSE);
                if (XdOrderStatusEnum.WAITING_DELIVERY.equals(orderStatusEnum)
                        || XdOrderStatusEnum.DELIVERING.equals(orderStatusEnum)
                        || XdOrderStatusEnum.DELIVERED.equals(orderStatusEnum)
                        || XdOrderStatusEnum.DELIVERY_FAIL.equals(orderStatusEnum)) {
                    orderPackingODTO.setShowPrintTicket(Boolean.TRUE);
                }
            } else {
                Integer packingStatus = orderPackingODTO.getPackingStatus();
                if (YesOrNoEnums.YES.getCode().equals(packingStatus)) {
                    orderPackingODTO.setShowPackingComplete(Boolean.FALSE);
                } else {
                    if (XdOrderStatusEnum.WAITING_DELIVERY.equals(orderStatusEnum)) {
                        orderPackingODTO.setShowPackingComplete(Boolean.TRUE);
                    } else {
                        orderPackingODTO.setShowPackingComplete(Boolean.FALSE);
                    }
                }
                if (XdOrderStatusEnum.WAITING_DELIVERY.equals(orderStatusEnum)) {
                    orderPackingODTO.setShowPrintTicket(Boolean.TRUE);
                }
            }
        }

        List<Long> pickOrderIdList = orderPackingODTOS.stream()
                .map(OrderPackingODTO::getPickOrderId)
                .collect(Collectors.toList());

        List<PickNumberSummary> pickNumberSummaries = pickOrderItemService.selectPickNumberSummaryByPickOrderIds(pickOrderIdList);
        if (SpringUtil.isNotEmpty(pickOrderIdList)) {
            Map<Long, Integer> pickOrderIdPickNumberMap = pickNumberSummaries.stream()
                    .collect(toMap(PickNumberSummary::getPickOrderId, PickNumberSummary::getTotalPickNumber));
            orderPackingODTOS.forEach(data ->
                    data.setPickNumber(pickOrderIdPickNumberMap.get(data.getPickOrderId()))
            );
        }

        renderService.render(orderPackingODTOS, "/dd/pick/partition/pageOrderPacking");

        orderPackingODTOS.forEach(data -> {
            if (Objects.equals(data.getPackingId(), XdWmsConstantUtil.SYSTEM_PICK_ID)) {
                data.setPackingEmployeeName(XdWmsConstantUtil.SYSTEM_PICK_NAME);
            }
        });
        return orderPackingODTOMPage;
    }

    @Transactional
    public Boolean completeOrderPacking(CompleteOrderPackingIDTO req) {
        QYAssert.hasText(req.getOrderId(), "订单id不能为空");
        long orderId = Long.parseLong(req.getOrderId());
        PickOrderResult pickOrderByOrderId = pickOrderService.getPickOrderByOrderId(orderId);
        QYAssert.notNull(pickOrderByOrderId, "拣货单不存在");
        QYAssert.isTrue(YesOrNoEnums.NO.getCode().equals(pickOrderByOrderId.getPackingStatus()), "订单已打包完成");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long shopId = tokenInfo.getShopId();
        Long employeeId = tokenInfo.getEmployeeId();
        QYAssert.isTrue(shopId.equals(pickOrderByOrderId.getWarehouseId()), "此订单不是当前门店订单");

        PickOrder update = new PickOrder();
        update.setId(pickOrderByOrderId.getPickOrderId());
        update.setUpdateTime(pickOrderByOrderId.getUpdateTime());
        update.setPackingStatus(YesOrNoEnums.YES.getCode());
        update.setPackingId(employeeId);
        update.setPackingEndTime(new Date());
        pickOrderService.updateById(update);

        releasePackingStation(orderId);

        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("push-sf-node");
        if (null != dictionaryODTO && Objects.equals("PACKAGING_COMPLETED", dictionaryODTO.getOptionValue())) {

            Order order = orderMapper.selectById(orderId);
            if (order.getDeliveryMode() == XdDeliveryModeEnum.SF_DELIVERY.getCode() ) {
                ThirdDeliveryKafkaDTO kafkaDTO = new ThirdDeliveryKafkaDTO();
                kafkaDTO.setSheetType(1);
                kafkaDTO.setOrderIds(Arrays.asList(orderId));

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        mqSenderComponent.send(
                                QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.SF_CREATE_ORDER_TOPIC.getTopic(),
                                kafkaDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SF_CREATE_ORDER_TOPIC.name(),
                                KafkaMessageOperationTypeEnum.UPDATE.name());
                    }
                });
            }
        }
        return Boolean.TRUE;
    }

    public OrderDetailODTO orderPartitionPickDetail(String orderIdStr) {
        long orderId = Long.parseLong(orderIdStr);
        Order order = orderMapper.selectById(orderId);
        QYAssert.notNull(order, "订单不存在");

        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        QYAssert.isTrue(shopId.equals(order.getShopId()), "此订单不是当前门店订单");

        OrderDetailODTO resp = new OrderDetailODTO();
        resp.setOrderNum(order.getOrderNum());
        resp.setOrderCode(order.getOrderCode());
        resp.setOrderStatus(order.getOrderStatus());

        PickOrderResult pickOrderByOrderId = pickOrderService.getPickOrderByOrderId(orderId);
        QYAssert.notNull(pickOrderByOrderId, "拣货单不存在");

        Long packingStationId = pickOrderByOrderId.getPackingStationId();
        DdPackingStation packingStation = ddPackingStationService.getById(packingStationId);
        String packPort = Optional.ofNullable(packingStation)
                .map(DdPackingStation::getPackingPort)
                .orElse(null);
        resp.setPackingPort(packPort);

        Long pickOrderId = pickOrderByOrderId.getPickOrderId();
        List<DdPickPartitionOrder> ddPickPartitionOrders = listDdPickPartitionOrdersByPickOrderId(pickOrderId);
        List<PickOrderItem> pickOrderItems = pickOrderItemService.listByPickOrderId(pickOrderId);

        Map<Long, String> commodityIdPackageNameMap = getCommodityIdPackageNameMap(pickOrderItems);

        Map<Long, List<PickOrderItem>> pickOrderItemGroupByPickPartitionOrderId = pickOrderItems.stream()
                .collect(Collectors.groupingBy(PickOrderItem::getPickPartitionOrderId));

        List<PartitionPickOrderDetailODTO> pickOrderDetails = new ArrayList<>(ddPickPartitionOrders.size());
        resp.setPickOrderDetails(pickOrderDetails);

        for (DdPickPartitionOrder ddPickPartitionOrder : ddPickPartitionOrders) {
            PartitionPickOrderDetailODTO partitionPickOrderDetailODTO = BeanCloneUtils.copyTo(ddPickPartitionOrder, PartitionPickOrderDetailODTO.class);
            partitionPickOrderDetailODTO.setPickPartitionOrderCode(ddPickPartitionOrder.getPickPartitionOrderCode());
            pickOrderDetails.add(partitionPickOrderDetailODTO);

            List<PickOrderItem> pickOrderItemList = pickOrderItemGroupByPickPartitionOrderId.get(ddPickPartitionOrder.getId());

            List<PartitionPickOrderItemDetailODTO> partitionPickOrderItemDetails = new ArrayList<>(pickOrderItemList.size());
            partitionPickOrderDetailODTO.setPartitionPickOrderItemDetails(partitionPickOrderItemDetails);

            for (PickOrderItem pickOrderItem : pickOrderItemList) {
                PartitionPickOrderItemDetailODTO partitionPickOrderItemDetailODTO = JSONObject.parseObject(JSONObject.toJSONString(pickOrderItem), PartitionPickOrderItemDetailODTO.class);
                partitionPickOrderItemDetailODTO.setCommodityPackageKind(commodityIdPackageNameMap.get(pickOrderItem.getCommodityId()));
                partitionPickOrderItemDetails.add(partitionPickOrderItemDetailODTO);
            }
        }

        Integer orderStatus = order.getOrderStatus();
        XdOrderStatusEnum orderStatusEnum = XdOrderStatusEnum.getByCode(orderStatus);
        if (XdOrderStatusEnum.WAITING_DELIVERY.equals(orderStatusEnum)) {
            DdPackingOrder packingOrder = ddPackingOrderService.getByOrderId(order.getShopId(), orderId);
            if (Objects.nonNull(packingOrder)) {
                resp.setShowPackingComplete(Boolean.TRUE);
            }
        }

        if (XdOrderStatusEnum.WAITING_DELIVERY.equals(orderStatusEnum)
                || XdOrderStatusEnum.DELIVERING.equals(orderStatusEnum)
                || XdOrderStatusEnum.DELIVERED.equals(orderStatusEnum)
                || XdOrderStatusEnum.DELIVERY_FAIL.equals(orderStatusEnum)) {
            resp.setShowPrintTicket(Boolean.TRUE);
        }

        resp.setOrderId(orderIdStr);
        return resp;
    }

    private Map<Long, String> getCommodityIdPackageNameMap(List<PickOrderItem> pickOrderItems) {
        List<Long> commodityIds = pickOrderItems.stream()
                .map(PickOrderItem::getCommodityId)
                .distinct()
                .collect(Collectors.toList());
        List<Commodity> commodities = commodityMapper.queryCommodityByIdList(commodityIds);
        return commodities.stream()
                .collect(toMap(Commodity::getId, Commodity::getCommodityPackageName));
    }

    private List<DdPickPartitionOrder> listDdPickPartitionOrdersByPickOrderId(Long pickOrderId) {
        LambdaQueryWrapper<DdPickPartitionOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DdPickPartitionOrder::getPickOrderId, pickOrderId)
                .orderByDesc(DdPickPartitionOrder::getId);
        return ddPickPartitionOrderMapper.selectList(queryWrapper);
    }

    @Transactional
    public DdSingleConfirmPickODTO singleConfirmPick(DdSingleConfirmPickIDTO req) {
        req.checkData();

        return redisLockService.lock(RedisLockEnums.SINGLE_CONFIRM_PICK, req.getPickPartitionOrderId().toString(), () -> {
            DdSingleConfirmPickODTO ddSingleConfirmPickODTO = new DdSingleConfirmPickODTO();
            ddSingleConfirmPickODTO.setPickOrderItemId(req.getPickOrderItemId());

            DdConfirmPickDetailIDTO ddConfirmPickDetailIDTO = BeanCloneUtils.copyTo(req, DdConfirmPickDetailIDTO.class);
            DdConfirmPickIDTO confirmPickIDTO = new DdConfirmPickIDTO();
            confirmPickIDTO.setCommodityId(ddConfirmPickDetailIDTO.getCommodityId());
            confirmPickIDTO.setGroupPickOrderDetailList(Collections.singletonList(ddConfirmPickDetailIDTO));
            try {
                confirmPick(confirmPickIDTO, false, false);
            } catch (BizLogicException e) {
                if (e.getMessage().contains("已取消")) {
                    ddSingleConfirmPickODTO.setPickStatus(PickStatusEnum.CANCEL.getCode());

                    PickOrderItem pickOrderItem = pickOrderItemService.getById(req.getPickOrderItemId());
                    ddSingleConfirmPickODTO.setPickQuantity(pickOrderItem.getPickQuantity());
                    ddSingleConfirmPickODTO.setPickNumber(pickOrderItem.getPickNumber());
                    return ddSingleConfirmPickODTO;
                }
                throw e;
            }

            PickOrder pickOrder = pickOrderService.getById(req.getPickOrderId());
            ddSingleConfirmPickODTO.setPickStatus(pickOrder.getPickStatus());

            DdPickPartitionOrder ddPickPartitionOrder = ddPickPartitionOrderMapper.selectById(req.getPickPartitionOrderId());
            ddSingleConfirmPickODTO.setPartitionPickStatus(ddPickPartitionOrder.getPickStatus());

            PickOrderItem pickOrderItem = pickOrderItemService.getById(req.getPickOrderItemId());
            ddSingleConfirmPickODTO.setPickQuantity(pickOrderItem.getPickQuantity());
            ddSingleConfirmPickODTO.setPickNumber(pickOrderItem.getPickNumber());
            return ddSingleConfirmPickODTO;
        });
    }
}
