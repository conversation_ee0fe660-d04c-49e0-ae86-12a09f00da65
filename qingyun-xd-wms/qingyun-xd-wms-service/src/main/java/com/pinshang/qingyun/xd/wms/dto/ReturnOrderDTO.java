package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.enums.xd.XdReturnOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 退单消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnOrderDTO {
    /**
     * 退单id
     */
    private Long orderId;
    /**
     * 退单编号
     */
    private String orderCode;

    /**
     * 仓库id (门店id)
     */
    private Long warehouseId;

    /**
     * 退单状态
     */
    private XdReturnOrderStatusEnum status;

    /**
     * C端人员
     */
    private String receiveMan;

    /**
     * C端电话
     */
    private String receivePhone;

    /**
     * 经纬度
     */
    private BigDecimal longitude;

    private BigDecimal latitude;

    /**
     * C端地址
     */
    private String addressDetail;

    /**
     * 预约时间段
     */
    private String deliveryBeginTime;

    private String deliveryEndTime;
}