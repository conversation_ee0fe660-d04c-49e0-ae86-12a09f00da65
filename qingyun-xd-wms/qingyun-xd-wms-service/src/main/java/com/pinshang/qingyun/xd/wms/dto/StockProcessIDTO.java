package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存-加工单处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockProcessIDTO {
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @ApiModelProperty(value = "大店id")
    private Long stallId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "原材料list")
    private List<StockProcessItemDTO> originList;

    @ApiModelProperty(value = "产成品list")
    private List<StockProcessItemDTO> finishedList;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "自动加工 1支持  0不支持")
    private Integer autoProcess;

    public void checkData() {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        QYAssert.isTrue(referId != null, "关联单号id不能为空");
        QYAssert.isTrue(referCode != null, "关联单号code不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(originList), "商品list不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(finishedList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");

        for (StockProcessItemDTO stockProcessItemDTO : originList) {
            QYAssert.isTrue(stockProcessItemDTO.getNumber() != null, "份数不能为空");
            QYAssert.isTrue(stockProcessItemDTO.getQuantity() != null, "数量不能为空");
        }
        for (StockProcessItemDTO stockProcessItemDTO : finishedList) {
            QYAssert.isTrue(stockProcessItemDTO.getNumber() != null, "份数不能为空");
            QYAssert.isTrue(stockProcessItemDTO.getQuantity() != null, "数量不能为空");
        }
    }

    public List<StockItemDTO> toOriginList() {
        List<StockItemDTO> list = new ArrayList<>();
        for (StockProcessItemDTO itemDTO : originList) {
            list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getNumber(), itemDTO.getQuantity(),itemDTO.getDdStockInOutExtra()));
        }
        return list;
    }

    public List<StockItemDTO> toFinishedList() {
        List<StockItemDTO> list = new ArrayList<>();
        for (StockProcessItemDTO itemDTO : finishedList) {
            list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getNumber(), itemDTO.getQuantity(),itemDTO.getDdStockInOutExtra()));
        }
        return list;
    }
}