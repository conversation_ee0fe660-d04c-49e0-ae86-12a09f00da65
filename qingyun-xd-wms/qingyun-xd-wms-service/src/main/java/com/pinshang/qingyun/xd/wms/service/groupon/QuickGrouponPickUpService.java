package com.pinshang.qingyun.xd.wms.service.groupon;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.xd.order.dto.XdOrderItemDTO;
import com.pinshang.qingyun.xd.order.dto.XdOrderODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.groupon.QuickGrouponCommodityDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickOrderItemPickUpDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickOrderPickUpDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickPickUpSubmitDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.WarnCodeDTO;
import com.pinshang.qingyun.xd.wms.enums.PickStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.WorkOrderStatusEnum;
import com.pinshang.qingyun.xd.wms.mapper.OrderQuickGrouponMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickWorkOrderMapper;
import com.pinshang.qingyun.xd.wms.model.*;
import com.pinshang.qingyun.xd.wms.service.CommodityBarCodeService;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfService;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 清美团团提货
 */

@Service
public class QuickGrouponPickUpService {

    @Autowired
    private OrderQuickGrouponMapper orderQuickGrouponMapper;

    @Autowired
    private XdOrderClient xdOrderClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private WarehouseShelfService warehouseShelfService;
    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private PickWorkOrderMapper pickWorkOrderMapper;

    @Autowired
    private PickOrderItemMapper pickOrderItemMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private KafkaTemplate<String,String> kafkaTemplate;

    public static final String INIT_SHELF_NO = "0001";

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 清美团团核销
     */
    public QuickOrderPickUpDTO cancelAfterVerification(String pickupCode) {

        QuickOrderPickUpDTO dto = new QuickOrderPickUpDTO();
        QYAssert.isTrue(!StringUtil.isBlank(pickupCode), "提货码不能为空！");
        LambdaQueryWrapper query = new LambdaQueryWrapper<OrderQuickGroupon>()
                .eq(OrderQuickGroupon::getPickupCode, pickupCode);
        OrderQuickGroupon orderQuickGroupon = orderQuickGrouponMapper.selectOne(query);
        if (null == orderQuickGroupon) {
            dto.setCode("001");
            return dto;
        }
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (!tokenInfo.getShopId().equals(orderQuickGroupon.getShopId())) {
            dto.setCode("001");
            return dto;
        }

        dto = orderQuickGrouponMapper.quickOrderByPickupCode(pickupCode);

        if (XdOrderStatusEnum.CANCEL.getCode() == dto.getOrderStatus()) {
            dto.setCode("002");
        }

        if (XdOrderStatusEnum.DELIVERED.getCode() == dto.getOrderStatus()) {
            dto.setCode("003");
        }

        List<QuickOrderItemPickUpDTO> commodityInfoList = orderQuickGrouponMapper.commodityInfoList(dto.getOrderId(), orderQuickGroupon.getGrouponId());

        //一品多码
        if (null != commodityInfoList && commodityInfoList.size() > 0) {
            List<Long> commodityIds = commodityInfoList.stream().map(QuickOrderItemPickUpDTO::getCommodityId).collect(Collectors.toList());
            Map<Long, List<String>> map = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
            for (QuickOrderItemPickUpDTO quickOrderItemPickUpDTO : commodityInfoList) {
                quickOrderItemPickUpDTO.setBarCodeList(map.get(quickOrderItemPickUpDTO.getCommodityId()));
            }
        }

        dto.setItems(commodityInfoList);

        return dto;
    }

    /**
     * 开始核销  订单状态：待支付、已取消、待提货=2、已完成
     */
    public WarnCodeDTO startWritingOff(Long orderId) {

        WarnCodeDTO dto = new WarnCodeDTO();

        String code = null;
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyPrefixConst.QUICK_ORDER_PICK_LOCK + orderId);
        //同一个人还是允许再次进来的
        if (!StringUtil.isBlank(bucket.get()) && !bucket.get().equals(tokenInfo.getUserId().toString())) {
            dto.setCode("004");
            return dto;
        }

        XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(orderId);
        if (XdOrderStatusEnum.CANCEL.getCode() == xdOrderODTO.getOrderStatus().getCode()) {
            dto.setCode("002");
            return dto;
        }

        if (XdOrderStatusEnum.DELIVERED.getCode() == xdOrderODTO.getOrderStatus().getCode()) {
            dto.setCode("003");
            return dto;
        }

        //只有订单等于2的时候才可以核销
        QYAssert.isTrue(XdOrderStatusEnum.WAITING_PICK.getCode() == xdOrderODTO.getOrderStatus().getCode(), "订单状态不对，不可以核销");
        //进入核销状态，锁定该订单
        if (null == code) {
            bucket.set(tokenInfo.getUserId().toString(), 10L, TimeUnit.MINUTES);
        }
        return dto;
    }

    /**
     * 提交核销
     * 称重商品是数量，少了需要退差价
     */
    @Transactional
    public Boolean submitWritingOff(List<QuickPickUpSubmitDTO> submitList) {

        QYAssert.isTrue(null != submitList && submitList.size() > 0, "未进行商品核销不允许提交");
        submitList = submitList.stream().filter(e -> e.getQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        QYAssert.isTrue(null != submitList && submitList.size() > 0, "未进行商品核销不允许提交");

        Long orderId = submitList.get(0).getOrderId();
        PickOrderResult pickOrderResult = pickOrderService.getPickOrderByOrderId(orderId);
        if(pickOrderResult != null){
            QYAssert.isTrue(false,"此单已生成拣货单!");
        }

        XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(orderId);
        QYAssert.notNull(xdOrderODTO,"订单不存在");

        //只有待拣货的状态才可以核销
        QYAssert.isTrue(XdOrderStatusEnum.WAITING_PICK.getCode() == xdOrderODTO.getOrderStatus().getCode(), "订单状态不可以核销");

        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(orderId);
        orderDTO.setOrderCode(xdOrderODTO.getOrderCode());
        orderDTO.setShopId(xdOrderODTO.getShopId());
        orderDTO.setStatus(xdOrderODTO.getOrderStatus());
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        orderDTO.setDeliveryBeginTime(sdf1.format(xdOrderODTO.getReceiveTimeBegin()));
        orderDTO.setDeliveryEndTime(sdf1.format(xdOrderODTO.getReceiveTimeEnd()));
        orderDTO.setSourceType(xdOrderODTO.getSourceType().getCode());
        orderDTO.setOriginalOrderCode(xdOrderODTO.getOriginalOrderCode());
        orderDTO.setOrderType(xdOrderODTO.getOrderType().getCode());

        List<XdOrderItemDTO> items = xdOrderClient.queryItemsByOrderId(orderId);
        //判断提交的是否都是当前订单的
        List<Long> orderOrderItemIds = items.stream().map(XdOrderItemDTO::getId).collect(Collectors.toList());
        List<Long> submitOrderItemIds = submitList.stream().map(QuickPickUpSubmitDTO::getOrderItemId).collect(Collectors.toList());
        QYAssert.isTrue(orderOrderItemIds.containsAll(submitOrderItemIds), "存在不是订单内的商品，请重新确认");

        if (items != null && items.size() > 0) {
            OrderItemDTO orderItemDTO = null;
            List<OrderItemDTO> list = new ArrayList<>();
            for (XdOrderItemDTO item : items) {
                orderItemDTO = new OrderItemDTO();
                orderItemDTO.setOrderId(orderId);
                orderItemDTO.setItemId(item.getId());
                orderItemDTO.setCommodityId(item.getCommodityId());
                orderItemDTO.setQuantity(item.getQuantity());
                orderItemDTO.setStockNumber(item.getNumber());
                orderItemDTO.setIsWeight(item.getIsWeight().getCode());
                orderItemDTO.setIsProcess(item.getIsProcess().getCode());
                orderItemDTO.setProcessId(null != item.getProcessId() ?item.getProcessId().longValue(): null );
                orderItemDTO.setProcessName(item.getProcessName());
                orderItemDTO.setOriginSubBizId(item.getOriginSubBizId());
                list.add(orderItemDTO);
            }
            orderDTO.setItems(list);
        }

        //生成拣货单
        pickOrderService.createPickOrder(orderDTO);
        //更新拣货单
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        PickOrderMqDTO pickOrderMqDTO = pickOrderService.pickOrderByOrderId(orderId);
        PickOrder pickOrder = new PickOrder();
        pickOrder.setId(pickOrderMqDTO.getPickOrderId());
        pickOrder.setPickBeginTime(new Date());
        pickOrder.setPickId(tokenInfo.getEmployeeId());
        pickOrder.setPickStatus(PickStatusEnum.MIDDLE.getCode());

        //分配货位
        String pickShelfNo = warehouseShelfService.distributeShelfDelivery();
        if(pickShelfNo == null){
            pickShelfNo = INIT_SHELF_NO;
        }
        pickOrder.setShelfNo(pickShelfNo);
        pickOrderMapper.updateById(pickOrder);

        //完成加工单
        LambdaQueryWrapper query = new LambdaQueryWrapper<PickWorkOrder>()
                .eq(PickWorkOrder::getPickOrderId, pickOrder.getId());
        List<PickWorkOrder> workOrderList = pickWorkOrderMapper.selectList(query);
        if (null != workOrderList && workOrderList.size() > 0) {
            PickWorkOrder one = null;
            for (PickWorkOrder pickWorkOrder : workOrderList) {
                one = new PickWorkOrder();
                one.setId(pickWorkOrder.getId());
                one.setCompleteTime(new Date());
                one.setWorkStatus(WorkOrderStatusEnum.FINISH.getCode());
                one.setWorkUserId(tokenInfo.getUserId());
                pickWorkOrderMapper.updateById(one);
            }
        }

        //完成拣货单
        PickCompleteDTO pickCompleteDTO = new PickCompleteDTO();
        pickCompleteDTO.setPickOrderId(pickOrder.getId());
        LambdaQueryWrapper queryItem = new LambdaQueryWrapper<PickOrderItem>()
                .eq(PickOrderItem::getPickOrderId, pickOrder.getId());
        List<PickOrderItem> pickOrderItems = pickOrderItemMapper.selectList(queryItem);

        List<QuickGrouponCommodityDTO> lackCommodityList = new ArrayList<>();
        if (null != pickOrderItems && pickOrderItems.size() > 0) {
            Map<Long, BigDecimal> map = submitList.stream().collect(Collectors.toMap(QuickPickUpSubmitDTO::getOrderItemId, QuickPickUpSubmitDTO::getQuantity));
            List<PickCompleteItemDTO> pickCompleteList = new ArrayList<>();
            QuickGrouponCommodityDTO lackCommodity = null;

            for (int i = 0; i < pickOrderItems.size(); i++) {
                lackCommodity = new QuickGrouponCommodityDTO();
                PickCompleteItemDTO pickCompleteItemDTO = new PickCompleteItemDTO();
                pickCompleteItemDTO.setPickOrderId(pickOrderItems.get(i).getPickOrderId());
                pickCompleteItemDTO.setPickOrderItemId(pickOrderItems.get(i).getId());
                pickCompleteItemDTO.setCommodityId(pickOrderItems.get(i).getCommodityId());
                Boolean ifPickQuantity = null == map.get(pickOrderItems.get(i).getOrderItemId());
                pickCompleteItemDTO.setPickQuantity(ifPickQuantity ? BigDecimal.ZERO: map.get(pickOrderItems.get(i).getOrderItemId()));
                pickCompleteItemDTO.setIsWeight(pickOrderItems.get(i).getIsWeight());


                if (YesOrNoEnums.NO.getCode().equals(pickCompleteItemDTO.getIsWeight()) &&
                        pickOrderItems.get(i).getQuantity().compareTo(pickCompleteItemDTO.getPickQuantity()) > 0) {
                    BigDecimal lack = pickOrderItems.get(i).getQuantity().subtract(pickCompleteItemDTO.getPickQuantity());
                    lackCommodity.setNumber(lack.intValue());
                    lackCommodity.setCommodityId(pickOrderItems.get(i).getCommodityId());
                    lackCommodityList.add(lackCommodity);
                } else if (YesOrNoEnums.YES.getCode().equals(pickCompleteItemDTO.getIsWeight()) &&
                        pickCompleteItemDTO.getPickQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    lackCommodity.setNumber(pickOrderItems.get(i).getStockNumber());
                    lackCommodity.setCommodityId(pickOrderItems.get(i).getCommodityId());
                    lackCommodityList.add(lackCommodity);
                }

                pickCompleteList.add(pickCompleteItemDTO);
            }
            pickCompleteDTO.setItems(pickCompleteList);
        }

        pickOrderService.completePickOrder(pickCompleteDTO);

        //模拟 发送配送完成的单子
        DeliveryOrderKafkaDTO deliveryOrderKafkaDTO = new DeliveryOrderKafkaDTO();
        deliveryOrderKafkaDTO.setOrderId(orderId);
        deliveryOrderKafkaDTO.setOrderCode(xdOrderODTO.getOrderCode());
        deliveryOrderKafkaDTO.setDeliveryStatus(XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode());
        deliveryOrderKafkaDTO.setWarehouseId(xdOrderODTO.getShopId());
        deliveryOrderKafkaDTO.setDeliveryType(XdDeliveryOrderTypeEnum.DELIVERY.getCode());
        deliveryOrderKafkaDTO.setDeliveryUserId(tokenInfo.getUserId());
        deliveryOrderKafkaDTO.setDeliveryUserName(tokenInfo.getRealName());
        deliveryOrderKafkaDTO.setDeliveryUserPhone("");
        deliveryOrderKafkaDTO.setDeliveryEndTime(new Date());
        deliveryOrderKafkaDTO.setOrderDeliveryEndTime(new Date());
        deliveryOrderKafkaDTO.setSourceType(OrderSourceTypeEnum.QUICK_GROUPON);
        deliveryOrderKafkaDTO.setIfOvertime(0);
        List<PickOrderItem> pickCompleteOrderItems = pickOrderItemMapper.selectList(queryItem);
        List<PickOrderItemMqDTO> itemMqs = new ArrayList<>();
        if (null != pickCompleteOrderItems) {
            PickOrderItemMqDTO mqDto = null;
            for (PickOrderItem pickOrderItem : pickCompleteOrderItems) {
                mqDto = new PickOrderItemMqDTO();
                BeanUtils.copyProperties(pickOrderItem, mqDto);
                itemMqs.add(mqDto);
            }
        }
        deliveryOrderKafkaDTO.setItems(itemMqs);

//        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_DELIVERY_CHANGE_TYPE
//                , deliveryOrderKafkaDTO, KafkaMessageOperationTypeEnum.UPDATE);
//
//        //事务以后发消息
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//            @Override
//            public void afterCommit() {
//                kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XD_DELIVERY_CHANGE_TOPIC, JsonUtil.java2json(message));
//            }
//        });

        MqMessage msg = new MqMessage();
        msg.setTopic(KafkaTopicConstant.XD_DELIVERY_CHANGE_TOPIC);
        msg.setData(deliveryOrderKafkaDTO);
        mqSenderComponent.send(msg);

        //更新核销人
        LambdaQueryWrapper queryQuickGroupon = new LambdaQueryWrapper<OrderQuickGroupon>()
                .eq(OrderQuickGroupon::getOrderId, orderId);
        OrderQuickGroupon orderQuickGroupon = orderQuickGrouponMapper.selectOne(queryQuickGroupon);
        orderQuickGroupon.setDeliveryMan(tokenInfo.getRealName());
        orderQuickGrouponMapper.updateById(orderQuickGroupon);

        //缺件需要返回给快团团活动库存
        if (!lackCommodityList.isEmpty()) {
            Map<Long, Integer> lackMap = lackCommodityList.stream().collect(Collectors.groupingBy(QuickGrouponCommodityDTO::getCommodityId, Collectors.summingInt(QuickGrouponCommodityDTO::getNumber)));
            for (Long key : lackMap.keySet()) {
                orderQuickGrouponMapper.updateStock(orderQuickGroupon.getGrouponId(), key, lackMap.get(key));
            }
        }

        //拣货完成，取消锁定
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyPrefixConst.QUICK_ORDER_PICK_LOCK + orderId);
        bucket.getAndDelete();

        return Boolean.TRUE;
    }

    /**
     * 取消锁定
     */
    public Boolean unlock(Long orderId) {

        /**
         * 释放缓存
         */
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyPrefixConst.QUICK_ORDER_PICK_LOCK + orderId);
        bucket.getAndDelete();

        return Boolean.TRUE;
    }

}
