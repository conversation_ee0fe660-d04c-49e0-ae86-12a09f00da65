package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdCommodityLimit;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName DdStockService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 18:21
 * @Version 1.0
 */
@Slf4j
@Service
public class DdStockService {
    @Autowired
    private StallCommodityStockMapper stallCommodityStockMapper;

    /**
     * 大店盘点2.0获取商品
     * @param queryIDTO
     * @return
     */
    public List<DdMdInventoryResponseODTO> queryMdInventoryCommodityList(DdMdInventoryQueryIDTO queryIDTO) {
        List<DdMdInventoryResponseODTO> result = new ArrayList<>();
        QYAssert.notNull(queryIDTO.getShopId(),"门店id不能为空");
        QYAssert.notNull(queryIDTO.getStallId(),"档口id不能为空");
        QYAssert.notNull(queryIDTO.getStorageArea(),"库区不能为空");

        // 排面区商品从t_stall_commodity_stock 获取，货位都是null
        if(StorageAreaEnum.SHELF_AREA.getCode().equals(queryIDTO.getStorageArea())) {
            result = stallCommodityStockMapper.queryMdInventoryCommodityStockList(queryIDTO);
        }else {
            // 拣货区或者存储区从货位和商品绑定表 t_dd_goods_allocation_commodity 获取
            result = stallCommodityStockMapper.queryMdInventoryCommodityAllocationList(queryIDTO);
        }

        return result;
    }
}
