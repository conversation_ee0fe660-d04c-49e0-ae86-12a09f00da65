package com.pinshang.qingyun.xd.wms.vo.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("AddTaskCommodityReqVO")
public class AddTaskCommodityReqVO {

//    @ApiModelProperty("任务ID（从全部任务进入确认添加任务）")
//    private Long taskId;

    @ApiModelProperty("档口ID（从点击添加商品进入确认添加任务）")
    private Long stallId;

    @ApiModelProperty("1-排面补货 2-拣货位补货（从点击添加商品进入确认添加任务）")
    /**
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskTypeEnum
     */
    private Integer taskType;

    @ApiModelProperty("商品ID （从点击添加商品进入确认添加任务）")
    private Long commodityId;



}