package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCommodityList {

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "档口id")
    private Long stallId;

    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "stallId")
    @ApiModelProperty(value = "档口名称")
    private String stallName;

    @ApiModelProperty(value = "拣货单详情id")
    private Long pickOrderItemId;

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "已经拣货的份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty(value = "1=称重，0=非称重")
    private Integer isWeight;

    @ApiModelProperty(value = "货位编号,加工商品表加工拣货位，非加工商品表示拣货位")
    private String shelfNo;

    @ApiModelProperty(value = "缺货数量")
    private Integer stockOutNumber;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;


    /**
     * 加工商品特有的
     */
    @ApiModelProperty(value = "处理名称(加工方式)")
    private String processName;

    @ApiModelProperty(value = "加工点名称")
    private String workName;

    @ApiModelProperty(value = "加工人名称")
    private String employeeName;

    private Long workOrderId;
}
