package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderReturnODTO {
    private Long orderId;

    @ApiModelProperty("提货人")
    private String consignee;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("退货商品")
    private String refundCommodity;

    @ApiModelProperty("退货数量")
    private String refundNum;

    @ApiModelProperty("订单状态")
    private String status;

    @ApiModelProperty("提货时间 或 取消时间")
    private String pickTime;

    @ApiModelProperty("提示语")
    private String msg;

    @ApiModelProperty("退款金额")
    private String refundAmount;

    @ApiModelProperty("提货预约日期")
    private Date arrivalTime;

    private Long commodityId;

    private Long shopId;
    private String shopCode;
    private String shopName;

    private String completeMsg;

    private Integer orderType;

    private Long orderItemId;

    private String barCode;
}
