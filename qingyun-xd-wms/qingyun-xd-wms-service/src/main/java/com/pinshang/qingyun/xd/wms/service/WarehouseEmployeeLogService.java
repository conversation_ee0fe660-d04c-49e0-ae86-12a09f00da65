package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.xd.wms.dto.QueryLog;
import com.pinshang.qingyun.xd.wms.dto.WarehouseEmployeeLogDTO;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseEmployeeLogMapper;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployeeLog;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class WarehouseEmployeeLogService {

    @Autowired
    private WarehouseEmployeeLogMapper warehouseEmployeeLogMapper;

    public int batchInsert(List<WarehouseEmployeeLog> list) {
        return warehouseEmployeeLogMapper.batchInsert(list);
    }

    public int insert(WarehouseEmployeeLog warehouseEmployeeLog) {
        return warehouseEmployeeLogMapper.insert(warehouseEmployeeLog);
    }

    public MPage<WarehouseEmployeeLogDTO> logList(QueryLog queryLog) {
        if (!StringUtils.isEmpty(queryLog.getStartTime())) {
            queryLog.setStartTime(queryLog.getStartTime()+"  00:00:00");
        }
        if (!StringUtils.isEmpty(queryLog.getEndTime())) {
            queryLog.setEndTime(queryLog.getEndTime()+" 23:59:59");
        }

        return warehouseEmployeeLogMapper.logList(queryLog);
    }
}
