package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderItemODTO {

    private Long shopId;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty(value = "订单时间")
    private Date orderTime;

    @ApiModelProperty(value = "收货人姓名")
    private String receiveMan;



    private String cancelledOrderItemId;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "商品条码(子码列表)")
    private String barCodes;

    @ApiModelProperty(value = "包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    @ApiModelProperty(value = "订单数")
    private Integer orderNumber;

    @ApiModelProperty(value = "发货数")
    private Integer deliverNumber;

    @ApiModelProperty(value = "正常品入库份数")
    private Integer normalNumber;

    @ApiModelProperty(value = "异常品入库份数")
    private Integer abnormalNumber;
}
