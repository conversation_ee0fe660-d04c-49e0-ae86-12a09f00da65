package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 大店补货任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@ToString
@TableName("t_dd_replenishment_task")
public class DdReplenishmentTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属门店ID
     */
    private Long shopId;

    /**
     * 档口ID
     */
    private Long stallId;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 任务来源，1-系统预警，2-手动添加
     *
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskSourceEnum
     */
    private Integer taskSource;

    /**
     * 任务类型，1-排面补货，2-拣货区补货
     *
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskTypeEnum
     */
    private Integer taskType;

    /**
     * 计划补货数量
     */
    private BigDecimal plannedQuantity;

    /**
     * 建议补货数量
     */
    private BigDecimal suggestedQuantity;

    /**
     * 实际补货数量
     */
    private BigDecimal realQuantity;

    /**
     * 对应单据编号(t_dd_transfer_record)
     */
    private String transferCode;

    /**
     * 补货人ID
     */
    private Long replenishUserId;

    /**
     * 补货人姓名
     */
    private String replenishUserName;

    /**
     * 补货时间
     */
    private Date replenishTime;

    /**
     * 绑定时间
     */
    private Date bindingTime;

    /**
     * 任务状态，1-生效中，2-已过期
     */
    private Integer status;

    /**
     * 是否完成补货，0-未完成，1-已完成
     */
    private Integer replenishedStatus;

    /**
     * 补货操作标志，0-未点击完成，1-已点击完成
     */
    private Integer flag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
