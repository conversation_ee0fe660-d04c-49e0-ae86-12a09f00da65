package com.pinshang.qingyun.xd.wms.util;

import com.pinshang.qingyun.box.utils.DateUtil;

import java.math.BigDecimal;
import java.util.Date;

public class DdUtils {

    public static final BigDecimal STOCK_RESTRICT = new BigDecimal("99999");

    /**
     * 根据循环时间loopTime 计算下次时间
     * @param loopTime
     * @return
     */
    public static Long getDelayTime(String loopTime){
        Date newDate = new Date();
        Date nowHHmmss = DateUtil.parseDate(DateUtil.getDateFormate(newDate, "HH:mm:ss"),"HH:mm:ss");
        Date loopHHmmss = DateUtil.parseDate(loopTime, "HH:mm:ss");
        Long delayTime;
        if(loopHHmmss.getTime() > nowHHmmss.getTime()){
            delayTime = loopHHmmss.getTime() - nowHHmmss.getTime();
        }else {
            String tomorrowDateStr = DateUtil.getDateFormate(DateUtil.addDay(new Date(),1),"yyyy-MM-dd") + " " + loopTime;
            Date tomorrowDate = DateUtil.parseDate(tomorrowDateStr, "yyyy-MM-dd HH:mm:ss");
            delayTime = tomorrowDate.getTime() - newDate.getTime();
        }
        return delayTime;
    }
}
