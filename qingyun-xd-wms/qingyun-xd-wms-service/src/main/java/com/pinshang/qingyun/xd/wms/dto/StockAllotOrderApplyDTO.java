package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 新增调拨申请DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderApplyDTO {

    @ApiModelProperty(value = "调拨类型:1调拨入库申请 2调拨出库申请")
    private Integer allotType;
    @ApiModelProperty(value = "调入调出门店ID")
    private Long shopId;
    @ApiModelProperty(value = "调拨申请商品信息")
    private List<StockAllotOrderItemApplyDTO> items;
    @ApiModelProperty(value = "调出档口ID")
    private Long outStallId;
    @ApiModelProperty(value = "调入档口ID")
    private Long inStallId;

}
