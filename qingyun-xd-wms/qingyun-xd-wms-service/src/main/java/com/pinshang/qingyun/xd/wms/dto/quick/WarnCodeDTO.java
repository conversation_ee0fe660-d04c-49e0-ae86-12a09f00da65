package com.pinshang.qingyun.xd.wms.dto.quick;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WarnCodeDTO {
    @ApiModelProperty("001:该提货码不是本门店团购提货码，不支持提货核销。" +
            "002:该订单已取消，不支持核销" +
            "003:该订单已完成，不支持核销" +
            "004：该订单其他人正在核销中")
    private String code;
}
