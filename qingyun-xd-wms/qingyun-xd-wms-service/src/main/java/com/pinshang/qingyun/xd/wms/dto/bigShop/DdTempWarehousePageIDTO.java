package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName DdTempWarehousePageIDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 12:42
 * @Version 1.0
 */
@Data
@ToString
@ApiModel("DdTempWarehousePageIDTO")
public class DdTempWarehousePageIDTO extends Pagination {
    @ApiModelProperty(value = "门店Id")
    private Long shopId;
    @ApiModelProperty(value = "档口Id")
    private Long stallId;
    @ApiModelProperty(value = "货位号")
    private String goodsAllocationCode;
    @ApiModelProperty(value = "货位Id")
    private Long goodsAllocationId;
    @ApiModelProperty(value = "档口Id列表")
    private List<Long> stallIds;
}
