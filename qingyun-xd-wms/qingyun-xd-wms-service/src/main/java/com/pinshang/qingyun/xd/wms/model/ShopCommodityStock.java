package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("t_shop_commodity_stock")
@ApiModel(value = "ShopCommodityStock对象", description = "门店商品库存表")
public class ShopCommodityStock extends BaseEntity {

    private Long shopId;

    private Long commodityId;

    private BigDecimal stockQuantity;

    private Integer stockNumber;

    private Integer freezeNumber;

    private Integer qualityNumber;

    private BigDecimal qualityQuantity;

    private BigDecimal monthInitStock;

    private Integer minStock;

    private Integer maxStock;

    /**拼接的门第id_商品id**/
    private String shopCommodity;

    /**
     * 预留库存
     */
    private BigDecimal reserveStock;

    public ShopCommodityStock(Long shopId, Long commodityId, BigDecimal stockQuantity, Integer stockNumber, Integer freezeNumber,
                              Integer qualityNumber, BigDecimal qualityQuantity, BigDecimal monthInitStock, Integer minStock,
                              Integer maxStock,String shopCommodity,Long createId,Date createTime,Long updateId,Date updateTime) {
        this.shopId = shopId;
        this.commodityId = commodityId;
        this.stockQuantity = stockQuantity;
        this.stockNumber = stockNumber;
        this.freezeNumber = freezeNumber;
        this.qualityNumber = qualityNumber;
        this.qualityQuantity = qualityQuantity;
        this.monthInitStock = monthInitStock;
        this.minStock = minStock;
        this.maxStock = maxStock;
        this.shopCommodity = shopCommodity;
        this.setCreateId(createId);
        this.setUpdateId(updateId);
        this.setCreateTime(createTime);
        this.setUpdateTime(updateTime);
    }

    public static ShopCommodityStock forInsert(Long shopId, Long commodityId,Long createId,Date createTime){
        return new ShopCommodityStock(shopId,commodityId,BigDecimal.ZERO,0,0,0,BigDecimal.ZERO,BigDecimal.ZERO,null,null,
                shopId + "_" + commodityId,createId,createTime,createId,createTime);
    }
}
