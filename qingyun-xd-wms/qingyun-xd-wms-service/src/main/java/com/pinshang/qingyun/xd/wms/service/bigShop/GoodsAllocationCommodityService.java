package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.AreaMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.GoodsAllocationCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.GoodsAllocationMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.XdSendLogService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 货位和商品绑定
 */
@Service
public class GoodsAllocationCommodityService extends ServiceImpl<GoodsAllocationCommodityMapper, GoodsAllocationCommodity> {

    @Autowired
    private GoodsAllocationMapper goodsAllocationMapper;

    @Autowired
    private GoodsAllocationCommodityMapper goodsAllocationCommodityMapper;

    @Autowired
    private GoodsAllocationService goodsAllocationService;

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private XdSendLogService xdSendLogService;

    @Autowired
    private StallMapper stallMapper;

    @Autowired
    private UserStallServiceImpl userStallServiceImpl;

    @Autowired
    private StallCommodityService stallCommodityService;

    @Autowired
    private RedisLockService redisLockService;
    /**
     * 门店拣货位绑定商品列表
     *
     * @param dto
     * @return
     */
    public MPage<GoodsAllocationCommodityPageDTO> page(GoodsAllocationCommodityPageIDTO dto) {
        return goodsAllocationCommodityMapper.page(dto);
    }

    private void setToken() {
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(16L);
        tokenInfo.setUserId(923L);
        tokenInfo.setEmployeeNumber("51362");
        tokenInfo.setRealName("gggg");
        //       FastThreadLocalUtil.setQY(tokenInfo);
    }

    /**
     * 拣货位和商品绑定
     *
     * @param dto
     * @return
     */
    @Transactional
    public Boolean pickingAreaBind(GoodsAllocationCommodityBindDTO dto) {
        this.setToken();
        dto.check();
        GoodsAllocation goodsAllocation = goodsAllocationMapper.selectById(dto.getGoodsAllocationId());
        QYAssert.isTrue(null != goodsAllocation, "货位信息不存在");
        QYAssert.isTrue(StorageAreaEnum.PICKING_AREA.getCode().equals(goodsAllocation.getStorageArea()), "货位不是拣货位");

        QYAssert.isTrue(StatusEnum.ENABLE.getCode().equals(goodsAllocation.getStatus()), "拣货位不是启用状态");


        QYAssert.isTrue(dto.getStallId().equals(goodsAllocation.getStallId()), "拣货位不属于当前档口，请刷新后重试");

        //查看是否已经绑定拣货位
        LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                .eq(GoodsAllocationCommodity::getShopId, dto.getShopId())
                .eq(GoodsAllocationCommodity::getCommodityId, dto.getCommodityId())
                .eq(GoodsAllocationCommodity::getStorageArea, StorageAreaEnum.PICKING_AREA.getCode());
        List<GoodsAllocationCommodity> pickCommodityList = goodsAllocationCommodityMapper.selectList(query);
        QYAssert.isTrue(SpringUtil.isEmpty(pickCommodityList), "商品已绑定拣货位");

//        //查看拣货位是否已经绑定
//        LambdaQueryWrapper<GoodsAllocationCommodity> query1 = new LambdaQueryWrapper<GoodsAllocationCommodity>()
//                .eq(GoodsAllocationCommodity::getShopId, dto.getShopId())
//                .eq(GoodsAllocationCommodity::getGoodsAllocationId, dto.getGoodsAllocationId())
//                .eq(GoodsAllocationCommodity::getStorageArea, StorageAreaEnum.PICKING_AREA.getCode());
//        List<GoodsAllocationCommodity> pickCommodityList1 = goodsAllocationCommodityMapper.selectList(query1);
//        QYAssert.isTrue(SpringUtil.isEmpty(pickCommodityList1), "拣货位已经绑定了商品");

        GoodsAllocationCommodity goodsAllocationCommodity = BeanCloneUtils.copyTo(dto, GoodsAllocationCommodity.class);

        goodsAllocationCommodity.setId(IdWorker.getId());
        goodsAllocationCommodity.setAreaId(goodsAllocation.getAreaId());
        goodsAllocationCommodity.setStorageArea(StorageAreaEnum.PICKING_AREA.getCode());
        goodsAllocationCommodityMapper.insert(goodsAllocationCommodity);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        saveLog(Arrays.asList(goodsAllocationCommodity), 1, tokenInfo);

        return Boolean.TRUE;
    }

    //查询货位库存
    public List<GoodsAllocationCommodity> getGoodsAllocationList(Long shopId, List<Long> goodsAllocationIds) {
        if (SpringUtil.isNotEmpty(goodsAllocationIds)) {
            LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                    .eq(GoodsAllocationCommodity::getShopId, shopId)
                    .in(GoodsAllocationCommodity::getGoodsAllocationId, goodsAllocationIds);
            return goodsAllocationCommodityMapper.selectList(query);
        } else {
            return new ArrayList<>();
        }
    }

    public List<GoodsAllocationCommodity> listByCommodityIds(Long shopId, List<Long> commodityIds) {
        if ( null != shopId && SpringUtil.isNotEmpty(commodityIds)) {
            LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                    .eq(GoodsAllocationCommodity::getShopId, shopId)
                    .in(GoodsAllocationCommodity::getCommodityId, commodityIds);
            return goodsAllocationCommodityMapper.selectList(query);
        } else {
           return new ArrayList<>();
        }
    }

    public List<GoodsAllocationCommodity> listByCommodityIdsAndStorageArea(Long shopId, List<Long> commodityIds, StorageAreaEnum storageAreaEnum) {
        if (null == shopId && SpringUtil.isEmpty(commodityIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                .eq(GoodsAllocationCommodity::getShopId, shopId)
                .in(GoodsAllocationCommodity::getCommodityId, commodityIds)
                .eq(GoodsAllocationCommodity::getStorageArea, storageAreaEnum.getCode());
        return goodsAllocationCommodityMapper.selectList(query);
    }

    public List<GoodsAllocationCommodity> listPickingAreaByCommodityIdsWithAllocationCode(Long shopId, List<Long> commodityIds) {

        List<GoodsAllocationCommodity> pickingAreaGoodsAllocationCommodities = listByCommodityIdsAndStorageArea(shopId, commodityIds, StorageAreaEnum.PICKING_AREA);
        if (SpringUtil.isEmpty(pickingAreaGoodsAllocationCommodities)) {
            return Collections.emptyList();
        }

        List<Long> goodsAllocationIdList = pickingAreaGoodsAllocationCommodities.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).collect(Collectors.toList());
        List<GoodsAllocation> goodsAllocations = goodsAllocationMapper.selectBatchIds(goodsAllocationIdList);
        Map<Long, GoodsAllocation> goodsAllocationCodeMap = goodsAllocations.stream().collect(Collectors.toMap(GoodsAllocation::getId, Function.identity()));

        pickingAreaGoodsAllocationCommodities
                .forEach(e -> {
                    GoodsAllocation goodsAllocation = goodsAllocationCodeMap.get(e.getGoodsAllocationId());
                    e.setGoodsAllocationCode(goodsAllocation.getGoodsAllocationCode());
                    e.setSortNum(goodsAllocation.getSortNum());
                });
        return pickingAreaGoodsAllocationCommodities;
    }

    /**
     * 解除拣货位和商品绑定
     *
     * @param goodsAllocationCommodityId
     * @return
     */
    @Transactional
    public Boolean cancelBind(Long goodsAllocationCommodityId) {
        this.setToken();
        GoodsAllocationCommodity goodsAllocationCommodity = goodsAllocationCommodityMapper.selectById(goodsAllocationCommodityId);
        QYAssert.isTrue(null != goodsAllocationCommodity, "绑定关系不存在");

        QYAssert.isTrue(goodsAllocationCommodity.getStock().compareTo(BigDecimal.ZERO) == 0, "货位商品库存不为0");

        goodsAllocationCommodityMapper.deleteById(goodsAllocationCommodityId);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        saveLog(Arrays.asList(goodsAllocationCommodity), 3, tokenInfo);
        return Boolean.TRUE;
    }

    /**
     * 批量绑定拣货位
     *
     * @param wb
     */
    public ExcelResult importPickingAreaBind(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        int totalRow = sheet.getLastRowNum() + 1;
        QYAssert.isTrue(totalRow <= 501, "一次最多导入500行");

        List<ImportPickingAreaBindDTO> list = new ArrayList<>();
        ImportPickingAreaBindDTO dto = null;
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货位号*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "商品编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 2, "拣货位最小数量"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 3, "拣货位最大数量"), "模板不正确");
            }

            if (rowNum > 0) {
                if (row.getCell(0) == null || row.getCell(1) == null || "".equals(row.getCell(0).getStringCellValue().trim())
                || "".equals(row.getCell(1).getStringCellValue().trim()) ) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }
                try {
                    dto = new ImportPickingAreaBindDTO();
                    dto.setGoodsAllocationCode(row.getCell(0).getStringCellValue().trim());
                    dto.setCommodityCode(row.getCell(1).getStringCellValue().trim());
                    if (null != row.getCell(2) && !"".equals(row.getCell(2).getStringCellValue())) {
                        dto.setMinStock(new BigDecimal(row.getCell(2).getStringCellValue()).setScale(3, RoundingMode.HALF_UP));
                    }
                    if (null != row.getCell(3) && !"".equals(row.getCell(3).getStringCellValue())) {
                        dto.setMaxStock(new BigDecimal(row.getCell(3).getStringCellValue()).setScale(3, RoundingMode.HALF_UP));
                    }

                    if (null != dto.getMinStock() &&
                            (dto.getMinStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || dto.getMinStock().compareTo(BigDecimal.ZERO) < 0) ) {
                        errorList.add((rowNum + 1) + "最小数量最大数量请输入0-99999之间的数字");
                        continue;
                    }
                    if (null != dto.getMaxStock() &&
                            (dto.getMaxStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || dto.getMaxStock().compareTo(BigDecimal.ZERO) < 0) ) {
                        errorList.add((rowNum + 1) + "最小数量最大数量请输入0-99999之间的数字");
                        continue;
                    }
                    if (null != dto.getMaxStock() && null != dto.getMinStock() && dto.getMinStock().compareTo(dto.getMaxStock()) > 0) {
                        errorList.add((rowNum + 1) + "行最小数量不可＞最大数量");
                        continue;
                    }
                    list.add(dto);
                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }
            }
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long shopId = tokenInfo.getShopId();

        //数据完整继续进行
        if (SpringUtil.isEmpty(errorList)) {
            QYAssert.isTrue(SpringUtil.isNotEmpty(list), "导入数据不能为空");
            //查询货位信息
            List<String> goodsAllocationCodeList = list.stream().map(ImportPickingAreaBindDTO::getGoodsAllocationCode).distinct().collect(Collectors.toList());
            List<GoodsAllocation> goodsAllocations = goodsAllocationService.goodsAllocationByCodesList(goodsAllocationCodeList, tokenInfo.getShopId());
            Map<String, GoodsAllocation> goodsAllocationMap = goodsAllocations.stream().filter(e -> StorageAreaEnum.PICKING_AREA.getCode().equals(e.getStorageArea())).collect(Collectors.toMap(GoodsAllocation::getGoodsAllocationCode, e -> e));

//            List<Long> bindGoodsAllocationIds = new ArrayList<>();
//            if (SpringUtil.isNotEmpty(goodsAllocations)) {
//                //查询货位绑定商品的信息
//                List<Long> goodsAllocationIds = goodsAllocations.stream().map(GoodsAllocation::getId).collect(Collectors.toList());
//                List<GoodsAllocationCommodity> goodsAllocationCommodityList = getGoodsAllocationList(tokenInfo.getShopId(), goodsAllocationIds);
//                bindGoodsAllocationIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).collect(Collectors.toList());
//            }

            //用户拥有的档口权限
            List<Long> stallIds = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());

            //查询商品id
            List<String> commodityCodes = list.stream().map(ImportPickingAreaBindDTO::getCommodityCode).distinct().collect(Collectors.toList());
            List<Commodity> commodityList = commodityMapper.queryCommodityByCodeList(commodityCodes);
            Map<String, Long> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Commodity::getId));

            List<Long> commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
            //查询商品绑定拣货位
            List<GoodsAllocationCommodity> goodsAllocationCommodities = listByCommodityIds(shopId, commodityIds).stream().filter(e -> StorageAreaEnum.PICKING_AREA.getCode().equals(e.getStorageArea())).collect(Collectors.toList());
            List<Long> bindCommodityIds = goodsAllocationCommodities.stream().map(GoodsAllocationCommodity::getCommodityId).collect(Collectors.toList());

            //查询商品所属的档口
            List<StallCommodity> stallCommodities = stallCommodityService.getStallByCommodityIds(shopId, commodityIds);
            Map<Long, List<Long>> longListMap = stallCommodities.stream().collect(Collectors.groupingBy(StallCommodity::getCommodityId, Collectors.mapping(StallCommodity::getStallId, Collectors.toList())));

            //拣货位和商品编码是否存在重复
//            Map<String, Integer> repeatGoodsAllocation = new HashMap<>();
            Map<String, Integer> repeatCommodityCode = new HashMap<>();

            GoodsAllocationCommodity goodsAllocationCommodity = null;
            List<GoodsAllocationCommodity> addList = new ArrayList<>();

            for (ImportPickingAreaBindDTO importDto : list) {
//                if (repeatGoodsAllocation.containsKey(importDto.getGoodsAllocationCode())) {
//                    if (1 == repeatGoodsAllocation.get(importDto.getGoodsAllocationCode())) {
//                        errorList.add(importDto.getGoodsAllocationCode() + "货位号存在重复");
//                    }
//                    repeatGoodsAllocation.put(importDto.getGoodsAllocationCode(), 2);
//                    continue;
//                }

                if (repeatCommodityCode.containsKey(importDto.getCommodityCode())) {
                    if ( 1 == repeatCommodityCode.get(importDto.getCommodityCode()) ) {
                        errorList.add(importDto.getCommodityCode() + "商品编码存在重复");
                    }
                    repeatCommodityCode.put(importDto.getCommodityCode(), 2);
                    continue;
                }

                if (!goodsAllocationMap.containsKey(importDto.getGoodsAllocationCode())) {
                    errorList.add(importDto.getGoodsAllocationCode() + "货位号不存在");
                    continue;
                }

                GoodsAllocation goodsAllocation = goodsAllocationMap.get(importDto.getGoodsAllocationCode());

                if (null == goodsAllocation.getStallId()) {
                    errorList.add(importDto.getGoodsAllocationCode() + "货位未分配档口");
                    continue;
                }

                if (!stallIds.contains(goodsAllocation.getStallId())) {
                    errorList.add("无" + importDto.getGoodsAllocationCode() + "货位的档口权限");
                    continue;
                }

//                if (bindGoodsAllocationIds.contains(goodsAllocation.getId())) {
//                    errorList.add(importDto.getGoodsAllocationCode() + "货位号已绑定商品");
//                    continue;
//                }

                if (!commodityMap.containsKey(importDto.getCommodityCode())) {
                    errorList.add(importDto.getCommodityCode() + "商品编码不存在");
                    continue;
                }

                Long commodityId = commodityMap.get(importDto.getCommodityCode());

                if (bindCommodityIds.contains(commodityId)) {
                    errorList.add(importDto.getCommodityCode() + "商品编码已绑定拣货位");
                    continue;
                }

                //商品所属的档口必须包含拣货位的档口
                if (!longListMap.containsKey(commodityId) || !longListMap.get(commodityId).contains(goodsAllocation.getStallId())) {
                    errorList.add(importDto.getCommodityCode() + "商品编码与货位的所属档口不匹配");
                    continue;
                }

                goodsAllocationCommodity = new GoodsAllocationCommodity();
                goodsAllocationCommodity.setShopId(shopId);
                goodsAllocationCommodity.setAreaId(goodsAllocation.getAreaId());
                goodsAllocationCommodity.setStallId(goodsAllocation.getStallId());
                goodsAllocationCommodity.setStorageArea(goodsAllocation.getStorageArea());
                goodsAllocationCommodity.setGoodsAllocationId(goodsAllocation.getId());
                goodsAllocationCommodity.setCommodityId(commodityMap.get(importDto.getCommodityCode()));
                goodsAllocationCommodity.setStock(BigDecimal.ZERO);
                goodsAllocationCommodity.setMinStock(importDto.getMinStock());
                goodsAllocationCommodity.setMaxStock(importDto.getMaxStock());
                addList.add(goodsAllocationCommodity);
//                repeatGoodsAllocation.put(importDto.getGoodsAllocationCode(), 1);
                repeatCommodityCode.put(importDto.getCommodityCode(), 1);
            }
            //数据全对就插入
            if (SpringUtil.isEmpty(errorList)) {
                saveBatch(addList);

                //保存日志
                saveLog(addList, 2, tokenInfo);
            }
        }

        return new ExcelResult(errorList, null);
    }

    /**
     * 批量解绑
     */
    public BatchCancelBindDTO batchCancelBind(BatchCancelBindIDTO dto) {
        this.setToken();
        dto.check();
        List<Commodity> commodityList = commodityMapper.queryCommodityByCodeList(dto.getCommodityCodes());
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "所有商品都不存在");
        Map<String, Long> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Commodity::getId));

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<Long> commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        //查询商品所属的档口
        List<StallCommodity> stallCommodities = stallCommodityService.getStallByCommodityIds(tokenInfo.getShopId(), commodityIds);
        Map<Long, List<Long>> longListMap = stallCommodities.stream().collect(Collectors.groupingBy(StallCommodity::getCommodityId, Collectors.mapping(StallCommodity::getStallId, Collectors.toList())));

        //查询商品绑定的拣货位
        List<GoodsAllocationCommodity> goodsAllocationCommodityList = listByCommodityIds(tokenInfo.getShopId(), commodityIds).stream().filter(e -> StorageAreaEnum.PICKING_AREA.getCode().equals(e.getStorageArea())).collect(Collectors.toList());
        Map<Long, GoodsAllocationCommodity> goodsAllocationCommodityMap = goodsAllocationCommodityList.stream().collect(Collectors.toMap(GoodsAllocationCommodity::getCommodityId, e -> e));

        int successNum = 0;
        int failNum = 0;
        List<GoodsAllocationCommodity> deleteDate = new ArrayList<>();

        for (String code : dto.getCommodityCodes()) {
            //商品编码不存在
            if (!commodityMap.containsKey(code)) {
                failNum++;
                continue;
            }
            Long commodityId = commodityMap.get(code);

            //商品所属的档口不包含当前所选的档口
            if (!longListMap.containsKey(commodityId) || !longListMap.get(commodityId).contains(dto.getStallId())) {
                failNum++;
                continue;
            }

            //当前商品没有绑定关系 或者库存不为0
            if (!goodsAllocationCommodityMap.containsKey(commodityId) || goodsAllocationCommodityMap.get(commodityId).getStock().compareTo(BigDecimal.ZERO) != 0) {
                failNum++;
                continue;
            }

            deleteDate.add(goodsAllocationCommodityMap.get(commodityId));
            successNum++;
        }

        if (SpringUtil.isNotEmpty(deleteDate)) {
            List<Long> ids = deleteDate.stream().map(GoodsAllocationCommodity::getId).collect(Collectors.toList());
            goodsAllocationCommodityMapper.deleteBatchIds(ids);
            saveLog(deleteDate, 4, tokenInfo);
        }
        BatchCancelBindDTO res = new BatchCancelBindDTO();
        String msg = "解绑成功" + successNum + "个，失败" + failNum + "个";
        res.setMsg(msg);
        return res;

    }

    /**
     * 设置安全库存
     *
     * @return
     */
    @Transactional
    public Boolean setSecureStock(SecureStockIDTO dto) {
        this.setToken();
        QYAssert.isTrue(null != dto.getId(), "id不能为空");
        GoodsAllocationCommodity goodsAllocationCommodity = goodsAllocationCommodityMapper.selectById(dto.getId());
        QYAssert.isTrue(null != goodsAllocationCommodity, "绑定关系不存在");

        BigDecimal minStock = dto.getMinStock();
        BigDecimal maxStock = dto.getMaxStock();

        QYAssert.isTrue(null == minStock || (minStock.compareTo(BigDecimal.ZERO) >= 0 && minStock.compareTo(DdUtils.STOCK_RESTRICT) <= 0), "最小库存设置不合规");
        QYAssert.isTrue(null == maxStock || (maxStock.compareTo(BigDecimal.ZERO) >= 0 && maxStock.compareTo(DdUtils.STOCK_RESTRICT) <= 0), "最大库存设置不合规");

        if (null != minStock && null != maxStock) {
            QYAssert.isTrue(maxStock.compareTo(minStock) >= 0, "最大库存必须大于最小库存");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        this.baseMapper.updateSecureStock(dto.getId(), minStock, maxStock, tokenInfo.getUserId());

        //保存日志
        GoodsAllocationCommodity goodsAllocationCommodity1 = goodsAllocationCommodityMapper.selectById(dto.getId());

        saveLog(Arrays.asList(goodsAllocationCommodity1), 5, tokenInfo);

        return Boolean.TRUE;
    }

    /**
     * 导入安全库存
     *
     * @param wb
     * @return
     */
    @Transactional
    public ExcelResult importSecureStock(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        int totalRow = sheet.getLastRowNum() + 1;
        QYAssert.isTrue(totalRow <= 1001, "每次最多导入1000行");

        List<ImportPickingAreaBindDTO> dtoList = new ArrayList<>();
        ImportPickingAreaBindDTO importPickingAreaBindDTO = null;

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "商品编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "拣货位最小数量"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 2, "拣货位最大数量"), "模板不正确");
            }

            if (rowNum > 0) {
                if (row.getCell(0) == null || "".equals(row.getCell(0).getStringCellValue().trim())) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }
                try {
                    importPickingAreaBindDTO = new ImportPickingAreaBindDTO();
                    importPickingAreaBindDTO.setCommodityCode(row.getCell(0).getStringCellValue().trim());
                    if (null != row.getCell(1) && !"".equals(row.getCell(1).getStringCellValue())) {
                        importPickingAreaBindDTO.setMinStock(new BigDecimal(row.getCell(1).getStringCellValue()).setScale(3, RoundingMode.HALF_UP));
                    }
                    if (null != row.getCell(2) && !"".equals(row.getCell(2).getStringCellValue()) ) {
                        importPickingAreaBindDTO.setMaxStock(new BigDecimal(row.getCell(2).getStringCellValue()));
                    }

                    if (null != importPickingAreaBindDTO.getMinStock() && (importPickingAreaBindDTO.getMinStock().compareTo(DdUtils.STOCK_RESTRICT) > 0
                            || importPickingAreaBindDTO.getMinStock().compareTo(BigDecimal.ZERO) < 0)) {
                        errorList.add((rowNum + 1) + "最小数量最大数量请输入0-99999之间的数字");
                        continue;
                    }
                    if (null != importPickingAreaBindDTO.getMaxStock() && (importPickingAreaBindDTO.getMaxStock().compareTo(DdUtils.STOCK_RESTRICT) > 0
                            || importPickingAreaBindDTO.getMaxStock().compareTo(BigDecimal.ZERO) < 0)) {
                        errorList.add((rowNum + 1) + "最小数量最大数量请输入0-99999之间的数字");
                        continue;
                    }
                    if (null != importPickingAreaBindDTO.getMinStock() && null != importPickingAreaBindDTO.getMaxStock() && importPickingAreaBindDTO.getMinStock().compareTo(importPickingAreaBindDTO.getMaxStock()) > 0) {
                        errorList.add((rowNum + 1) + "行最小数量不可＞最大数量");
                        continue;
                    }

                    dtoList.add(importPickingAreaBindDTO);
                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }
            }
        }

        //数据格式正确继续导入
        if (SpringUtil.isEmpty(errorList)) {
            QYAssert.isTrue(SpringUtil.isNotEmpty(dtoList), "导入数据不能为空");

            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

            List<String> commodityCodes = dtoList.stream().map(ImportPickingAreaBindDTO::getCommodityCode).collect(Collectors.toList());
            //获取重复数据
            Map<String, Integer> duplicates = new HashMap<>();
            Set<String> set = new HashSet<>();
            commodityCodes.forEach(e -> {
                if (!set.add(e)) {
                    duplicates.put(e, 0);
                }
            });

            List<Commodity> commodities = commodityMapper.queryCommodityByCodeList(commodityCodes);
            Map<String, Long> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Commodity::getId));

            //查询商品绑定的货位
            List<Long> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
            List<GoodsAllocationCommodity> goodsAllocationCommodities = listByCommodityIds(tokenInfo.getShopId(), commodityIds);
            Map<Long, GoodsAllocationCommodity> goodsAllocationCommodityMap = goodsAllocationCommodities.stream().filter(e -> StorageAreaEnum.PICKING_AREA.getCode().equals(e.getStorageArea())).collect(Collectors.toMap(GoodsAllocationCommodity::getCommodityId, e -> e));

            //查询当前用户拥有的档口权限
            List<Long> stallIds = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());

            List<GoodsAllocationCommodity> updateDate = new ArrayList<>();
            List<GoodsAllocationCommodity> logDate = new ArrayList<>();
            for (ImportPickingAreaBindDTO dto : dtoList) {
                if (!commodityMap.containsKey(dto.getCommodityCode())) {
                    errorList.add(dto.getCommodityCode() + "商品编码不存在");
                    continue;
                }

                //重复的编码只提醒一次
                if (duplicates.containsKey(dto.getCommodityCode())) {
                    if (0 == duplicates.get(dto.getCommodityCode())) {
                        errorList.add(dto.getCommodityCode() + "商品编码重复");
                    } else {
                        duplicates.put(dto.getCommodityCode(), 1);
                    }
                    continue;
                }

                Long commodityId = commodityMap.get(dto.getCommodityCode());

                if (!goodsAllocationCommodityMap.containsKey(commodityId)) {
                    errorList.add(dto.getCommodityCode() + "商品编码未绑定拣货位");
                    continue;
                }

                if (!stallIds.contains(goodsAllocationCommodityMap.get(commodityId).getStallId())) {
                    errorList.add("无" + dto.getCommodityCode() + "商品编码的档口权限");
                    continue;
                }

                GoodsAllocationCommodity goodsAllocationCommodity = new GoodsAllocationCommodity();
                goodsAllocationCommodity.setId(goodsAllocationCommodityMap.get(commodityId).getId());
                goodsAllocationCommodity.setMinStock(dto.getMinStock());
                goodsAllocationCommodity.setMaxStock(dto.getMaxStock());
                updateDate.add(goodsAllocationCommodity);

                logDate.add(goodsAllocationCommodityMap.get(commodityId));

            }

            if (SpringUtil.isEmpty(errorList) && SpringUtil.isNotEmpty(updateDate)) {
                updateBatchById(updateDate);

                saveLog(logDate, 6, tokenInfo);
            }


        }

        return new ExcelResult(errorList, null);
    }

    public void saveLog(List<GoodsAllocationCommodity> goodsAllocationCommodityList, Integer type, TokenInfo tokenInfo) {
        // 1新增绑定 2批量绑定 3解绑 4批量解绑 5设置安全库存 6导入安全库存
        List<LogDdGoodsAllocationCommodityDTO> list = new ArrayList<>();
        LogDdGoodsAllocationCommodityDTO dto = null;

        //查询区域信息
        List<Long> areaIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getAreaId).distinct().collect(Collectors.toList());
        List<Area> areaList = areaMapper.selectBatchIds(areaIds);
        Map<Long, String> areaMap = areaList.stream().collect(Collectors.toMap(Area::getId, Area::getAreaCode));

        //查询商品信息
        List<Long> commodityIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getCommodityId).collect(Collectors.toList());
        List<Commodity> commodityList = commodityMapper.selectBatchIds(commodityIds);
        Map<Long, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, e -> e));

        //查询档口信息
        List<Long> stallIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getStallId).distinct().collect(Collectors.toList());
        List<Stall> stallList = stallMapper.selectBatchIds(stallIds);
        Map<Long, String> stallMap = stallList.stream().collect(Collectors.toMap(Stall::getId, Stall::getStallName));

        //查询货位信息
        List<Long> goodsAllocationIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).distinct().collect(Collectors.toList());
        List<GoodsAllocation> goodsAllocationList = goodsAllocationMapper.selectBatchIds(goodsAllocationIds);
        Map<Long, String> goodsAllocationMap = goodsAllocationList.stream().collect(Collectors.toMap(GoodsAllocation::getId, GoodsAllocation::getGoodsAllocationCode));

        Date date = new Date();
        for (GoodsAllocationCommodity goodsAllocationCommodity : goodsAllocationCommodityList) {
            dto = new LogDdGoodsAllocationCommodityDTO();
            dto.setType(type);
            dto.setShopId(goodsAllocationCommodity.getShopId());
            dto.setStallId(goodsAllocationCommodity.getStallId());
            dto.setStallName(stallMap.get(goodsAllocationCommodity.getStallId()));
            dto.setAreaCode(areaMap.get(goodsAllocationCommodity.getAreaId()));
            dto.setGoodsAllocationId(goodsAllocationCommodity.getGoodsAllocationId());
            dto.setGoodsAllocationCode(goodsAllocationMap.get(goodsAllocationCommodity.getGoodsAllocationId()));
            dto.setCommodityId(goodsAllocationCommodity.getCommodityId());
            Commodity commodity = commodityMap.containsKey(goodsAllocationCommodity.getCommodityId()) ? commodityMap.get(goodsAllocationCommodity.getCommodityId()) : new Commodity();
            dto.setCommodityCode(commodity.getCommodityCode());
            dto.setCommodityName(commodity.getCommodityName());
            dto.setCommoditySpec(commodity.getCommoditySpec());
            dto.setCommodityUnitName(commodity.getCommodityUnitName());
            dto.setMinStock(goodsAllocationCommodity.getMinStock());
            dto.setMaxStock(goodsAllocationCommodity.getMaxStock());
            dto.setCreateId(tokenInfo.getUserId());
            dto.setCreateCode(tokenInfo.getEmployeeNumber());
            dto.setCreateName(tokenInfo.getRealName());
            dto.setCreateTime(date);
            list.add(dto);
        }

        xdSendLogService.sendLog(list, "t_log_dd_goods_allocation_commodity");

    }

    /**
     * 校验货位号是否存在
     */
    public Boolean checkGoodsAllocationId(Long shopId, Long stallId, Long commodityId, Long goodsAllocationId) {
        LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>().eq(GoodsAllocationCommodity::getShopId, shopId).eq(GoodsAllocationCommodity::getStallId, stallId).eq(GoodsAllocationCommodity::getCommodityId, commodityId).eq(GoodsAllocationCommodity::getGoodsAllocationId, goodsAllocationId);
        return CollectionUtils.isNotEmpty(goodsAllocationCommodityMapper.selectList(query));
    }

    public Long getCommodityPickingPositionId(Long shopId, Long stallId, Long commodityId) {
        LambdaQueryWrapper<GoodsAllocationCommodity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(GoodsAllocationCommodity::getGoodsAllocationId);
        queryWrapper.eq(GoodsAllocationCommodity::getShopId, shopId);
        queryWrapper.eq(GoodsAllocationCommodity::getStallId, stallId);
        queryWrapper.eq(GoodsAllocationCommodity::getCommodityId, commodityId);
        queryWrapper.eq(GoodsAllocationCommodity::getStorageArea, StorageAreaEnum.PICKING_AREA.getCode());
        Long commodityPickingPositionId = Optional.ofNullable(this.getOne(queryWrapper)).map(GoodsAllocationCommodity::getGoodsAllocationId).orElse(null);
        return commodityPickingPositionId;
    }

    public GoodsAllocationCommodity getPickingGoodsAllocationCommodity(Long shopId, Long stallId, Long commodityId) {
        LambdaQueryWrapper<GoodsAllocationCommodity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoodsAllocationCommodity::getShopId, shopId);
        queryWrapper.eq(GoodsAllocationCommodity::getStallId, stallId);
        queryWrapper.eq(GoodsAllocationCommodity::getCommodityId, commodityId);
        queryWrapper.eq(GoodsAllocationCommodity::getStorageArea, StorageAreaEnum.PICKING_AREA.getCode());
        GoodsAllocationCommodity goodsAllocationCommodity = goodsAllocationCommodityMapper.selectOne(queryWrapper);
        if (Objects.nonNull(goodsAllocationCommodity)) {
            GoodsAllocation allocation = goodsAllocationService.getById(goodsAllocationCommodity.getGoodsAllocationId());
            if (Objects.nonNull(allocation)) {
                goodsAllocationCommodity.setGoodsAllocationCode(allocation.getGoodsAllocationCode());
            }
        }
        return goodsAllocationCommodity;
    }

    public List<GoodsAllocationCommodity> list(Long shopId, Long stallId, Long commodityId, Integer storageArea) {

        LambdaQueryWrapper<GoodsAllocationCommodity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoodsAllocationCommodity::getShopId, shopId);
        queryWrapper.eq(GoodsAllocationCommodity::getStallId, stallId);
        queryWrapper.eq(GoodsAllocationCommodity::getCommodityId, commodityId);
        queryWrapper.eq(Objects.nonNull(storageArea), GoodsAllocationCommodity::getStorageArea, storageArea);

        queryWrapper.gt(GoodsAllocationCommodity::getStock, BigDecimal.ZERO);
        List<GoodsAllocationCommodity> goodsAllocationCommodityList = goodsAllocationCommodityMapper.selectList(queryWrapper);
        if (SpringUtil.isEmpty(goodsAllocationCommodityList)) {
            return Collections.emptyList();
        }

        List<Long> goodsAllocationIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).collect(Collectors.toList());
        List<GoodsAllocation> goodsAllocationList = goodsAllocationService.listByGoodAllocationIds(goodsAllocationIds);
        Map<Long, String> goodsAllocationIdCodeMap = goodsAllocationList.stream().collect(Collectors.toMap(GoodsAllocation::getId, GoodsAllocation::getGoodsAllocationCode));

        goodsAllocationCommodityList.forEach(goodsAllocationCommodity -> {
            goodsAllocationCommodity.setGoodsAllocationCode(goodsAllocationIdCodeMap.get(goodsAllocationCommodity.getGoodsAllocationId()));
        });

        return goodsAllocationCommodityList;

    }

    /**
     * 根据商品id 查询已上货位 按库存升序取前三个
     * 和排面库存
     *
     * @param req 搜索条件
     * @return 已上货位
     */
    public QueryGoodsAllocatedODTO queryGoodsAllocated(GoodsAllocatedIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getStallId(), "档口id不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品id不能为空");
        LambdaQueryWrapper<GoodsAllocationCommodity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GoodsAllocationCommodity::getShopId, req.getShopId())
                .eq(GoodsAllocationCommodity::getStallId, req.getStallId())
                .eq(GoodsAllocationCommodity::getCommodityId, req.getCommodityId())
                .gt(GoodsAllocationCommodity::getStock, 0)
                .orderByAsc(GoodsAllocationCommodity::getStock)
                .last(" limit 3");

        List<GoodsAllocationCommodity> goodsAllocationCommodityList = goodsAllocationCommodityMapper.selectList(wrapper);
        List<Long> goodsAllocationIds = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).collect(Collectors.toList());
        List<GoodsAllocation> goodsAllocationList = goodsAllocationService.listByGoodAllocationIds(goodsAllocationIds);
        Map<Long, String> goodsAllocationIdCodeMap = goodsAllocationList.stream().collect(Collectors.toMap(GoodsAllocation::getId, GoodsAllocation::getGoodsAllocationCode));

        goodsAllocationCommodityList.forEach(goodsAllocationCommodity -> goodsAllocationCommodity.setGoodsAllocationCode(goodsAllocationIdCodeMap.get(goodsAllocationCommodity.getGoodsAllocationId())));
        // 查询排面库存
        StallCommodityStock stallCommodityStock = stallCommodityStockService.queryOneStock(req.getShopId(), req.getStallId(), req.getCommodityId());

        List<GoodsAllocatedODTO> odtos = BeanCloneUtils.copyTo(goodsAllocationCommodityList, GoodsAllocatedODTO.class);

        QueryGoodsAllocatedODTO res = new QueryGoodsAllocatedODTO();
        res.setGoodsAllocatedList(odtos);
        if (Objects.nonNull(stallCommodityStock)) {
            res.setShelfAreaStock(stallCommodityStock.getStockQuantity());
        }
        res.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
        return res;
    }

    /**
     * 初始化【存储区 临时库】商品信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void initWarehouseAreaCommodity(GoodsAllocationCommodityInitIDTO req) {
        redisLockService.lock(RedisLockEnums.INIT_WAREHOUSE_AREA_COMMODITY, req.getStallId().toString(), () -> {
            List<GoodsAllocationCommodityListInitIDTO> goodsAllocationCommodityList = req.getGoodsAllocationCommodityList();
            QYAssert.notEmpty(goodsAllocationCommodityList, "初始化存储区商品信息不能为空");
            List<Long> goodAllocationIdList = goodsAllocationCommodityList.stream().map(GoodsAllocationCommodityListInitIDTO::getGoodsAllocationId).collect(Collectors.toList());

            // 根据货位号批量查询货位信息
            List<GoodsAllocation> goodsAllocations = goodsAllocationService.listByGoodAllocationIds(goodAllocationIdList);
            QYAssert.notEmpty(goodsAllocations, "未查询到对应货位信息");

            boolean hasShelfOrPickingArea = goodsAllocations.stream().anyMatch(x -> Objects.equals(x.getStorageArea(), StorageAreaEnum.SHELF_AREA.getCode())
                    || Objects.equals(x.getStorageArea(), StorageAreaEnum.PICKING_AREA.getCode()));
            QYAssert.isTrue(!hasShelfOrPickingArea, "非【存储区、临时库】无法初始化");

            Map<Long, GoodsAllocation> goodsAllocationIdsMap = goodsAllocations.stream().collect(Collectors.toMap(GoodsAllocation::getId, Function.identity()));

            // 查询已存在的商品信息
            LambdaQueryWrapper<GoodsAllocationCommodity> wrapper = new LambdaQueryWrapper<>();

            // 使用组合条件查询货位ID与商品ID同时匹配的记录
            goodsAllocationCommodityList.forEach(dto -> wrapper.or(
                    wrapper1 -> wrapper1.eq(GoodsAllocationCommodity::getShopId, req.getShopId())
                            .eq(GoodsAllocationCommodity::getStallId, req.getStallId())
                            .eq(GoodsAllocationCommodity::getCommodityId, dto.getCommodityId())
                            .eq(GoodsAllocationCommodity::getGoodsAllocationId, dto.getGoodsAllocationId())
            ));

            List<GoodsAllocationCommodity> existingCommodities = goodsAllocationCommodityMapper.selectList(wrapper);

            // 已存在的货位Ids
            Set<Long> existingGoodsAllocationIds = existingCommodities.stream().map(GoodsAllocationCommodity::getGoodsAllocationId).collect(Collectors.toSet());

            // 找出不存在的货位
            List<Long> notExistGoodsAllocationIdList = goodAllocationIdList.stream().filter(id -> !existingGoodsAllocationIds.contains(id)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(notExistGoodsAllocationIdList)) {
                // 存储位上都有相应的商品信息，则不需要初始化，直接返回
                return;
            }

            // 构建新的GoodsAllocationCommodity对象列表
            List<GoodsAllocationCommodity> newCommodities = goodsAllocationCommodityList.stream()
                    .filter(x -> notExistGoodsAllocationIdList.contains(x.getGoodsAllocationId()))
                    .map(y -> createGoodsAllocationCommodity(req, y.getGoodsAllocationId(), y.getCommodityId(), goodsAllocationIdsMap.get(y.getGoodsAllocationId())))
                    .collect(Collectors.toList());

            // 批量保存
            if (CollectionUtils.isNotEmpty(newCommodities)) {
                saveBatch(newCommodities);
            }
        });
    }

    /**
     * 创建 GoodsAllocationCommodity
     */
    private GoodsAllocationCommodity createGoodsAllocationCommodity(GoodsAllocationCommodityInitIDTO req, Long goodsAllocationId, Long commodityId, GoodsAllocation goodsAllocation) {
        GoodsAllocationCommodity commodity = new GoodsAllocationCommodity();
        commodity.setShopId(req.getShopId());
        commodity.setAreaId(goodsAllocation.getAreaId());
        commodity.setStallId(req.getStallId());
        commodity.setStorageArea(goodsAllocation.getStorageArea());
        commodity.setGoodsAllocationId(goodsAllocationId);
        commodity.setCommodityId(commodityId);
        commodity.setStock(BigDecimal.ZERO);
        commodity.setCreateTime(new Date());
        commodity.setCreateId(-1L);
        return commodity;
    }


    public void processGoodsAllocationCommodityStock(Long warehouseId, DdStockInOutExtraVO ddStockInOutExtraVO, StockItemDTO commodity) {
        if (BigDecimal.ZERO.compareTo(commodity.getQuantity()) == 0) {
            return;
        }

        Integer storageArea = ddStockInOutExtraVO.getStorageArea();

        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);

        if (StorageAreaEnum.SHELF_AREA.equals(storageAreaEnum)) {
            return;
        }

        if (StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(storageArea) || StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(storageArea)) {
            stockInInitWarehouseAreaCommodity(commodity, warehouseId, ddStockInOutExtraVO);
        }

        int resultCount = this.baseMapper.processStock(warehouseId, commodity, ddStockInOutExtraVO);

        if (resultCount == 0 && commodity.getQuantity() != null && commodity.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
            Commodity comm = commodityMapper.selectById(commodity.getCommodityId());
            throw new BizLogicException(comm.getCommodityCode() + comm.getCommodityName() + "库存不足,出库失败!");
        }
    }

    public void stockInInitWarehouseAreaCommodity(StockItemDTO commodity, Long warehouseId, DdStockInOutExtraVO ddStockInOutExtraVO) {
        GoodsAllocationCommodityInitIDTO goodsAllocationCommodityInitIDTO = new GoodsAllocationCommodityInitIDTO();
        goodsAllocationCommodityInitIDTO.setShopId(warehouseId);
        goodsAllocationCommodityInitIDTO.setStallId(ddStockInOutExtraVO.getStallId());
        List<GoodsAllocationCommodityListInitIDTO> goodsAllocationCommodityList = new ArrayList<>();

        GoodsAllocationCommodityListInitIDTO goodsAllocationCommodityListInitIDTO = new GoodsAllocationCommodityListInitIDTO(commodity.getCommodityId(), ddStockInOutExtraVO.getGoodsAllocationId());
        goodsAllocationCommodityList.add(goodsAllocationCommodityListInitIDTO);

        goodsAllocationCommodityInitIDTO.setGoodsAllocationCommodityList(goodsAllocationCommodityList);
        GoodsAllocationCommodityService goodsAllocationCommodityService = SpringBeanFinder.getBean(GoodsAllocationCommodityService.class);
        goodsAllocationCommodityService.initWarehouseAreaCommodity(goodsAllocationCommodityInitIDTO);
    }

    public void processGoodsAllocationCommodityStockUnCheck(Long warehouseId, DdStockInOutExtraVO ddStockInOutExtraVO, StockItemDTO commodity) {
        Integer storageArea = ddStockInOutExtraVO.getStorageArea();

        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);

        if (StorageAreaEnum.SHELF_AREA.equals(storageAreaEnum)) {
            return;
        }

        if (StorageAreaEnum.WAREHOUSE_AREA.equals(storageAreaEnum) || StorageAreaEnum.PROVISIONAL_AREA.equals(storageAreaEnum)) {
            stockInInitWarehouseAreaCommodity(commodity, warehouseId, ddStockInOutExtraVO);
        }


        this.baseMapper.processStockUncheck(warehouseId, commodity, ddStockInOutExtraVO);
    }

    public List<GoodsAllocationCommodity> queryGoodsAllocationHasStock(Long shopId, Long stallId, List<Long> commodityIds) {
        if(SpringUtil.isEmpty(commodityIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                .eq(GoodsAllocationCommodity::getShopId, shopId)
                .eq(GoodsAllocationCommodity::getStallId,stallId)
                .gt(GoodsAllocationCommodity::getStock,BigDecimal.ZERO)
                .in(GoodsAllocationCommodity::getCommodityId, commodityIds);
        return goodsAllocationCommodityMapper.selectList(query);
    }


    /**
     * 根据商品和货位ID 查询库存 （拣货位或者存储区）
     */
    public GoodsAllocationCommodityDTO queryGoodsAllocateStock(GoodsAllocationCommodityIDTO req) {
        LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                .eq(GoodsAllocationCommodity::getShopId, req.getShopId())
                .eq(GoodsAllocationCommodity::getStallId, req.getStallId())
                .eq(GoodsAllocationCommodity::getCommodityId, req.getCommodityId())
                .eq(GoodsAllocationCommodity::getGoodsAllocationId, req.getGoodsAllocationId());
        GoodsAllocationCommodity goodsAllocationCommodity = goodsAllocationCommodityMapper.selectOne(query);
        if (Objects.isNull(goodsAllocationCommodity)) {
            return null;
        }
        return BeanCloneUtils.copyTo(goodsAllocationCommodity, GoodsAllocationCommodityDTO.class);
    }

    /**
     * 查询多个库区的商品库存
     * @param shopId 门店ID
     * @param stallId 档口ID
     * @param commodityId 商品ID
     * @param storageAreas 库区列表
     * @return 商品库存列表
     */
    public List<GoodsAllocationCommodity> listByStorageAreas(Long shopId, Long stallId, Long commodityId, List<Integer> storageAreas) {
        if (Objects.isNull(shopId) || Objects.isNull(stallId) || Objects.isNull(commodityId) || CollectionUtils.isEmpty(storageAreas)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<GoodsAllocationCommodity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoodsAllocationCommodity::getShopId, shopId)
                .eq(GoodsAllocationCommodity::getStallId, stallId)
                .eq(GoodsAllocationCommodity::getCommodityId, commodityId)
                .in(GoodsAllocationCommodity::getStorageArea, storageAreas)
                .gt(GoodsAllocationCommodity::getStock, BigDecimal.ZERO);

        List<GoodsAllocationCommodity> goodsAllocationCommodityList = goodsAllocationCommodityMapper.selectList(queryWrapper);
        if (SpringUtil.isEmpty(goodsAllocationCommodityList)) {
            return Collections.emptyList();
        }

        return goodsAllocationCommodityList;
    }
}
