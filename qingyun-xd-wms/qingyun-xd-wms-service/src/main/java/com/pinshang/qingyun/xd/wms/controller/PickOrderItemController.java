package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.OrderCommodityList;
import com.pinshang.qingyun.xd.wms.dto.OrderCommodityResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.PartitionOrderCommodityResult;
import com.pinshang.qingyun.xd.wms.service.PickOrderItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/pick/order/item")
@Api(value = "仓库拣货单明细表", tags = "PickOrderItemController")
public class PickOrderItemController {

    @Autowired
    private PickOrderItemService pickOrderItemService;


    @ApiOperation(value = "后端：根据拣货单id查询商品")
    @GetMapping("/orderCommodityDetail/{pickOrderId}")
    public OrderCommodityResult orderCommodityDetail(@PathVariable("pickOrderId") Long pickOrderId) {
        return pickOrderItemService.orderCommodityDetail(pickOrderId);
    }

    @ApiOperation(value = "手持：根据拣货单id查询商品列表")
    @GetMapping("/orderCommodityList/{pickOrderId}")
    public List<OrderCommodityList> orderCommodityList(@PathVariable("pickOrderId") Long pickOrderId) {
        return pickOrderItemService.orderCommodityList(pickOrderId);
    }

    @ApiOperation(value = "后端：根据分区拣货单id查询商品")
    @GetMapping("/partitionOrderCommodityDetail/{pickPartitionOrderId}")
    @MethodRender
    public PartitionOrderCommodityResult partitionOrderCommodityDetail(@PathVariable("pickPartitionOrderId") Long pickPartitionOrderId) {
        return pickOrderItemService.partitionOrderCommodityDetail(pickPartitionOrderId);
    }

}
