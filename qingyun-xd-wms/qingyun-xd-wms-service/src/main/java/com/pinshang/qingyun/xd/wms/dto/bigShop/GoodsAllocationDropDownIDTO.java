package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class GoodsAllocationDropDownIDTO implements Serializable {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区 4临时库")
    private Integer storageArea;

    @ApiModelProperty("货位号 模糊")
    private String goodsAllocationCode;

}
