package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.controller.BaseController;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceiveAddIDTO;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageIDTO;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageODTO;
import com.pinshang.qingyun.xd.wms.service.KitchenReceiveService;
import com.pinshang.qingyun.xd.wms.util.ExcelExportUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName KitchenReceiveController
 * <AUTHOR>
 * @Date 2021/10/20 11:24
 * @Description KitchenReceiveController
 * @Version 1.0
 */
@RequestMapping("/kitchenReceive")
@RestController
public class KitchenReceiveController extends BaseController {
    @Autowired
    private KitchenReceiveService kitchenReceiveService;

    @PostMapping("/add")
    @ApiOperation(value = "添加后厨领用订单", notes = "添加后厨领用订单")
    public Boolean add(@RequestBody KitchenReceiveAddIDTO idto){
        return kitchenReceiveService.add(idto);
    }

    @PostMapping("/page")
    @ApiOperation(value = "后厨领用记录分页", notes = "后厨领用记录分页")
    public PageInfo<KitchenReceivePageODTO> page(@RequestBody KitchenReceivePageIDTO idto){
        return kitchenReceiveService.page(idto);
    }

    @GetMapping("/export")
    @ApiOperation(value = "后厨领用记录分页-导出", notes = "后厨领用记录分页-导出")
    public void export(KitchenReceivePageIDTO idto, HttpServletResponse response){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<KitchenReceivePageODTO> res = kitchenReceiveService.page(idto);
        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "后厨领用记录" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("门店名称");
        tableHeader.add("商品编码");
        tableHeader.add("商品名称");
        tableHeader.add("条形码");
        tableHeader.add("规格");
        tableHeader.add("单位");
        tableHeader.add("领用数量");
        tableHeader.add("领用份数");
        tableHeader.add("成本价");
        tableHeader.add("领用日期");
        tableHeader.add("领用单");
        tableHeader.add("领用人");
        StringBuilder stringBuilder = new StringBuilder();
        List<List<String>> dataList = new ArrayList<>();
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(res!=null && SpringUtil.isNotEmpty(res.getList())) {
            for (KitchenReceivePageODTO e : res.getList()) {
                stringBuilder.setLength(0);
                List<String> row = new ArrayList<>();
                row.add(e.getShopName());
                row.add(e.getCommodityCode());
                row.add(e.getCommodityName());
                row.add(e.getBarCode());
                row.add(e.getCommoditySpec());
                row.add(e.getCommodityUnitName());
                row.add(e.getQuantity()+"");
                row.add(e.getNumber()+"");
                row.add(e.getWeightPrice()+"");
                row.add(sdf1.format(e.getCreateTime()));
                row.add(e.getReceiveCode());
                row.add(e.getEmployeeName());
                dataList.add(row);
            }
        }
        XSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbook(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcel(response,fileName,xb);
    }
}
