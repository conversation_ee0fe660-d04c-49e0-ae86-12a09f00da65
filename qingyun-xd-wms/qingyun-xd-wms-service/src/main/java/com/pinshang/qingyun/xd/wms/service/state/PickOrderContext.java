package com.pinshang.qingyun.xd.wms.service.state;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.XdOrderCancelReasonEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.integration.elm.dto.order.*;
import com.pinshang.qingyun.integration.elm.enums.ElmRefundReasonCodeEnum;
import com.pinshang.qingyun.integration.elm.enums.ElmRefundTypeEnum;
import com.pinshang.qingyun.integration.elm.utils.ElmClient;
import com.pinshang.qingyun.integration.jddj.dto.order.*;
import com.pinshang.qingyun.integration.jddj.utils.JddjClient;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.xd.wms.dto.PickOrderItemMqDTO;
import com.pinshang.qingyun.xd.wms.dto.PickOrderMqDTO;
import com.pinshang.qingyun.xd.wms.enums.OrderTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.PickStatusEnum;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.PickOrder;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import com.pinshang.qingyun.xd.wms.service.cloud.NewCloudService;
import com.pinshang.qingyun.xd.wms.service.platform.MeiTuanPickOrderService;
import com.pinshang.qingyun.xd.wms.service.platform.PlatFormPickOrderService;
import com.sankuai.meituan.shangou.open.sdk.request.OrderCancelRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 拣货单状态处理类
 * Created by chenqi
 */
@Slf4j
public class PickOrderContext {

    private PickOrder pickOrder;

    private List<PickOrderItem> pickOrderItems;

    private Map<Long, Commodity> commodityMap;

    public void setPickOrder(PickOrder pickOrder) {
        this.pickOrder = pickOrder;
    }

    public void setPickOrderItems(List<PickOrderItem> pickOrderItems) {
        this.pickOrderItems = pickOrderItems;
    }

    public void setCommodityMap(Map<Long, Commodity> commodityMap) {
        this.commodityMap = commodityMap;
    }

    /**
     * 开始拣货 -> 拣货中
     */
    public void doPick(IMqSenderComponent mqSenderComponent, MeiTuanPickOrderService meiTuanPickOrderService){
        QYAssert.isTrue(this.pickOrder.getPickStatus().equals(PickStatusEnum.WAIT.getCode()), "状态异常，无法分配拣货");
        this.pickOrder.setPickStatus(PickStatusEnum.MIDDLE.getCode());
        this.pickOrder.setUpdateTime(new Date());

        //发消息 订单 —— 出库中
        pickOrderSender(mqSenderComponent, meiTuanPickOrderService, null,XdPickOrderStatusEnum.MIDDLE);
    }

    /**
     * 完成拣货 -> 拣货完成
     */
    public void doComplete(IMqSenderComponent mqSenderComponent, MeiTuanPickOrderService meiTuanPickOrderService, PlatFormPickOrderService platFormPickOrderService) {
        QYAssert.isTrue(this.pickOrder.getPickStatus().equals(PickStatusEnum.MIDDLE.getCode())
                || this.pickOrder.getPickStatus().equals(PickStatusEnum.WAIT_HANDOVER.getCode()), "状态异常，无法完成");
        this.pickOrder.setPickStatus(PickStatusEnum.FINISH.getCode());
        this.pickOrder.setUpdateTime(new Date());

        //发消息 配送单 —— 待配送  订单 —— 待配送
        pickOrderSender(mqSenderComponent, meiTuanPickOrderService, platFormPickOrderService,XdPickOrderStatusEnum.FINISH);
    }

    /**
     * 取消拣货 -> 取消
     */
    public void doCancel(IMqSenderComponent mqSenderComponent, MeiTuanPickOrderService meiTuanPickOrderService, Long userId){
        QYAssert.isTrue(!this.pickOrder.getPickStatus().equals(PickStatusEnum.FINISH.getCode()), "拣货已完成，无法取消");
        this.pickOrder.setPickStatus(PickStatusEnum.CANCEL.getCode());
        this.pickOrder.setUpdateTime(new Date());

        //发消息 配送单 —— 已取消 订单 —— 已取消  后台操作才发消息， 没用户的是收到取消消息不用再次发送
        if(userId != null){
            pickOrderSender(mqSenderComponent, meiTuanPickOrderService, null,XdPickOrderStatusEnum.CANCEL);
        }
    }

    private void pickOrderSender(IMqSenderComponent mqSenderComponent, MeiTuanPickOrderService meiTuanPickOrderService, PlatFormPickOrderService platFormPickOrderService,XdPickOrderStatusEnum statusEnum) {
        PickOrderMqDTO mqDTO = new PickOrderMqDTO();
        List<PickOrderItemMqDTO> items = new ArrayList<>();
        mqDTO.setPickOrderId(this.pickOrder.getId());
        mqDTO.setOrderId(this.pickOrder.getOrderId());
        mqDTO.setOrderCode(this.pickOrder.getOrderCode());
        mqDTO.setSourceType(this.pickOrder.getSourceType());
        mqDTO.setWarehouseId(this.pickOrder.getWarehouseId());
        mqDTO.setShelfNo(this.pickOrder.getShelfNo());
        mqDTO.setPickStatus(statusEnum.getCode());
        mqDTO.setOriginalOrderCode(this.pickOrder.getOriginalOrderCode());
        mqDTO.setOrderType(this.pickOrder.getOrderType());
        mqDTO.setOperatorId(this.pickOrder.getPickId());
        if(this.pickOrderItems != null) {
            for (PickOrderItem item : this.pickOrderItems) {
                PickOrderItemMqDTO dto = new PickOrderItemMqDTO();
                BeanUtils.copyProperties(item, dto);
                items.add(dto);
            }
            mqDTO.setItems(items);
        }
        sendMessage(mqSenderComponent, meiTuanPickOrderService, platFormPickOrderService,mqDTO);
    }

    /**
     * 事务后发消息
     * @param mqDTO
     */
    private void sendMessage(IMqSenderComponent mqSenderComponent, MeiTuanPickOrderService meiTuanPickOrderService, PlatFormPickOrderService platFormPickOrderService,PickOrderMqDTO mqDTO) {
        class SendMessageTransactionSynchronizationAdapter extends TransactionSynchronizationAdapter {
            @Override
            public void afterCommit() {
                if (!afterCommitPlatform(mqDTO, meiTuanPickOrderService, platFormPickOrderService)){
                    return;
                }

                /*KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_PICK_ORDER_CHANGE_TYPE
                        , mqDTO, KafkaMessageOperationTypeEnum.UPDATE);*/
                mqDTO.setCancelType(XdOrderCancelReasonEnum.PICK_CANCEL.getCode());
                mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XD_PICK_ORDER_CHANGE_TOPIC,
                        mqDTO,
                        MqMessage.MQ_KAFKA,
                        KafkaMessageTypeEnum.XD_PICK_ORDER_CHANGE_TYPE.name(),
                        KafkaMessageOperationTypeEnum.UPDATE.name());
                log.info("发送拣货单状态变更通知, orderCode:{}, pickStatus:{} ", mqDTO.getOrderCode(), mqDTO.getPickStatus());
            }
        }
        SendMessageTransactionSynchronizationAdapter sendMessage = new SendMessageTransactionSynchronizationAdapter();
        TransactionSynchronizationManager.registerSynchronization(sendMessage);

    }

    private boolean afterCommitPlatform(PickOrderMqDTO mqDTO, MeiTuanPickOrderService meiTuanPickOrderService, PlatFormPickOrderService platFormPickOrderService) {
        //处理第三方订单
        if(OrderSourceTypeEnum.ELM.getCode().equals(mqDTO.getSourceType())){
            try{
                if(PickStatusEnum.CANCEL.getCode() == mqDTO.getPickStatus()){
                    OrderApplyRefundReqDTO orderApplyRefundReqDTO = new OrderApplyRefundReqDTO();
                    orderApplyRefundReqDTO.setOrderId(mqDTO.getOriginalOrderCode());
                    orderApplyRefundReqDTO.setRefundType(ElmRefundTypeEnum.TOTAL_REFUND.getCode() + "");
                    orderApplyRefundReqDTO.setReasonRemarks(ElmRefundReasonCodeEnum.COMMODITY_SELLOUT.getDesc());
                    orderApplyRefundReqDTO.setReasonCode(ElmRefundReasonCodeEnum.COMMODITY_SELLOUT.getCode());
                    orderApplyRefundReqDTO.setIdempotentId(UUID.randomUUID().toString());
                    new ElmClient(new RestTemplate()).execute(orderApplyRefundReqDTO, OrderApplyRefundResp.class);
                    //取消的 直接 改状态 不发消息了
                    return false;
                }else if(PickStatusEnum.FINISH.getCode() == mqDTO.getPickStatus()){
                    List<PickOrderItemMqDTO> partList = new ArrayList();
                    for (PickOrderItemMqDTO item : mqDTO.getItems()) {
                        if(item.getPickNumber() < item.getStockNumber()){
                            PickOrderItemMqDTO dto = new PickOrderItemMqDTO();
                            dto.setOriginSubBizId(item.getOriginSubBizId());
                            dto.setCommodityId(item.getCommodityId());
                            dto.setStockNumber(item.getStockNumber() - item.getPickNumber());
                            partList.add(dto);
                        }
                    }

                    if(SpringUtil.isNotEmpty(partList)){
                        //部分退款
                        OrderApplyRefundReqDTO orderApplyRefundReqDTO = new OrderApplyRefundReqDTO();
                        orderApplyRefundReqDTO.setOrderId(mqDTO.getOriginalOrderCode());
                        orderApplyRefundReqDTO.setRefundType(ElmRefundTypeEnum.PART_REFUND.getCode() + "");
                        orderApplyRefundReqDTO.setReasonRemarks(ElmRefundReasonCodeEnum.PRICE_DIFFERENCE.getDesc());
                        orderApplyRefundReqDTO.setReasonCode(ElmRefundReasonCodeEnum.PRICE_DIFFERENCE.getCode());
                        List<RefundProductList> products = new ArrayList();
                        for (PickOrderItemMqDTO partItem : partList) {
                            RefundProductList product = new RefundProductList();
                            product.setSubBizOrderId(partItem.getOriginSubBizId());
                            product.setPlatformSkuId(partItem.getCommodityId().toString());
                            // 默认按份数退
                            product.setNumber(partItem.getStockNumber().toString());
                            products.add(product);
                        }
                        orderApplyRefundReqDTO.setRefundProductList(products);
                        orderApplyRefundReqDTO.setIdempotentId(UUID.randomUUID().toString());
                        OrderApplyRefundResp partResp = new ElmClient(new RestTemplate()).execute(orderApplyRefundReqDTO, OrderApplyRefundResp.class);

                        log.warn("饿了么部分退款,orderCode{},error{},errno{},data{}",
                                mqDTO.getOriginalOrderCode(),partResp.getError(), partResp.getErrno(),partResp.getData());
                    }

                    //饿了么拣货完成
                    PickCompleteReq req = new PickCompleteReq();
                    req.setOrderId(mqDTO.getOriginalOrderCode());
                    new ElmClient(new RestTemplate()).execute(req, PickCompleteResp.class);
                }
            }catch (Exception e){
                log.error("调用饿了么SDK失败 {}", e);
            }
        }else if(OrderSourceTypeEnum.JDDJ.getCode().equals(mqDTO.getSourceType())){
            try{
                //京东到家 无法拣货取消 -> 产品
                if(PickStatusEnum.CANCEL.getCode() == mqDTO.getPickStatus()){
                    //取消的 直接 改状态 不发消息了
                    return false;
                }else if(PickStatusEnum.FINISH.getCode() == mqDTO.getPickStatus()){
                    if(SpringUtil.isNotEmpty(mqDTO.getItems())){
                        //部分退款
                        JddjAdjustReq req = new JddjAdjustReq();
                        req.setOrderId(Long.valueOf(mqDTO.getOriginalOrderCode()));
                        req.setOperPin("jddj");
                        req.setRemark("缺货");
                        List<AdjustSkuReq> products = new ArrayList();
                        for (PickOrderItemMqDTO partItem : mqDTO.getItems()) {
                            //如果某商品数量调整为0时，商品明细中不能包含该商品；
                            if(0 != partItem.getPickNumber()) {
                                AdjustSkuReq product = new AdjustSkuReq();
                                //转商品编码 partItem.getCommodityId()
                                product.setOutSkuId(commodityMap.get(partItem.getCommodityId()).getCommodityCode());
                                product.setSkuCount(partItem.getPickNumber());
                                products.add(product);
                            }
                        }
                        req.setOaosAdjustDTOList(products);
                        log.info("京东到家部分退款发消息：{}",req);
                        new JddjClient().execute(req, JddjAdjustResp.class);
                    }

                    //饿了么拣货完成
                    JddjPickCompleteReq req = new JddjPickCompleteReq();
                    req.setOrderId(mqDTO.getOriginalOrderCode());
                    req.setOperator("jddj");
                    new JddjClient().execute(req, JddjPickCompleteResp.class);
                }
            }catch (Exception e){
                log.error("调用京东到家SDK失败 {}", e);
            }
        }else if(OrderSourceTypeEnum.MTSG.getCode().equals(mqDTO.getSourceType())){

            //美团闪购 无法拣货取消 -> 产品
            if(PickStatusEnum.CANCEL.getCode() == mqDTO.getPickStatus()){
                meiTuanPickOrderService.cancelMtOrder(mqDTO);
            }else if(PickStatusEnum.FINISH.getCode() == mqDTO.getPickStatus()){
                meiTuanPickOrderService.commitPickAndReturnFill(mqDTO);
            }

        } else if ( OrderSourceTypeEnum.getSfDeliverySource().contains(mqDTO.getSourceType()) ||
                Objects.equals(OrderSourceTypeEnum.FARM_APP.getCode(), mqDTO.getSourceType()) ||
                Objects.equals(OrderSourceTypeEnum.FARM_MINI.getCode(), mqDTO.getSourceType()) ) {

            //拣货完成判断是否需要推送顺丰
            if (PickStatusEnum.FINISH.getCode() == mqDTO.getPickStatus()) {
                Boolean sf = platFormPickOrderService.cloudPickSuccess(mqDTO);
                if (sf) {
                    mqDTO.setPushSf(YesOrNoEnums.YES.getCode());
                } else {
                    mqDTO.setPushSf(YesOrNoEnums.NO.getCode());
                }
            }

        }
        return true;
    }
}
