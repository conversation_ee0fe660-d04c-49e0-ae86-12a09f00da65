package com.pinshang.qingyun.xd.wms.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.SaleTypeEnum;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.SmsMessageClientVo;
import com.pinshang.qingyun.common.service.WeChatSendMessageClient;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.dto.CommodityStockIDTO;
import com.pinshang.qingyun.xd.wms.dto.CommodityStockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.CommodityXsStockIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseDefaultDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@OnlineSwitchWatcher
public class StockListener extends BaseKafkaOnlineSwitchProcessor {

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private WeChatSendMessageClient weChatSendMessageClient;

    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;

    /**
     * 库存处理-只处理POS
     * @param message
     */
//    @KafkaListener(id="${application.name.switch}"+KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC,
//            topics = {"${application.name.switch}"+KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC},
//            containerFactory = "kafkaListenerContainerFactory",errorHandler ="kafkaConsumerErrorHandler")
    @KafkaListener(id="${application.name.switch}"+KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}"+ KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}"+ KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC
            },
            containerFactory = "kafkaListenerContainerFactory",
            errorHandler ="kafkaConsumerErrorHandler")
    public void commodityStockListen(String message) {
        //1.0版本上线时 需要把shop 的监听 停止
        String msg = "topic:" + KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC + " ====================message==" + message;
        log.info(msg);
//        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            if(messageWrapper != null && messageWrapper.getData() != null) {
                CommodityStockIDTO stockIDTO = JSON.parseObject(messageWrapper.getData().toString(), CommodityStockIDTO.class);
                log.info("收到门店商品库存处理通知, shopId:{}", stockIDTO.getShopId());

                //把commodityList 拆为 正常库 临时库[折扣特价码]
                ImmutablePair<Long, String> idAndCode =  ImmutablePair.of(stockIDTO.getReferId(), stockIDTO.getReferCode());

                List<CommodityStockItemDTO> allList = stockIDTO.getCommodityList();
                //判断是不是有子母商品
                List<CommodityStockItemDTO> motherList = allList.stream()
                        .filter(e -> YesOrNoEnums.YES.getCode().equals(e.getIfMotherCommodity()))
                        .collect(Collectors.toList());
                if (SpringUtil.isNotEmpty(motherList)) {
                    if (SaleTypeEnum.SELL.getCode().equals(stockIDTO.getSaleType())) {
                        List<StockItemDTO> mother = toCommodityList(motherList, StockInOutEnum.OUT);
                        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_POS_CHILD_MONTH
                                , mother, stockIDTO.getShopId(), stockIDTO.getUserId());
                        stockServiceAdapter.stockInOut(stockInOutVO);
                    } else {
                        List<StockItemDTO> mother = toCommodityList(motherList, StockInOutEnum.IN);

                        // 大店补充临时库 货位
                        processStallProbisionalAreaGoodsAllocation(mother, TempOperationTypeEnum.POS_RETURN);

                        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_POS_CHILD_MONTH
                                , mother, stockIDTO.getShopId(), stockIDTO.getUserId());
                        stockServiceAdapter.stockInOut(stockInOutVO);
                    }

                    stockIDTO.setCommodityList(allList.stream().filter(e -> !YesOrNoEnums.YES.getCode().equals(e.getIfMotherCommodity())).collect(Collectors.toList()));
                    idAndCode =  ImmutablePair.of(stockIDTO.getReferId(), stockIDTO.getReferCode()+"-");
                }
                if(SaleTypeEnum.SELL.getCode().equals(stockIDTO.getSaleType())){
                    List<CommodityStockItemDTO> normalList = stockIDTO.getCommodityList().stream()
                            .filter(item -> item.getIfCouponCode().equals(YesOrNoEnums.NO.getCode())).collect(Collectors.toList());
                    if(SpringUtil.isNotEmpty(normalList)){
                        List<StockItemDTO> normals = toCommodityList(normalList, StockInOutEnum.OUT);
    //                    shopCommodityService.reduceStockUnCheck(normals, stockIDTO.getShopId());
                        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_POS_NORMAL
                                , normals, stockIDTO.getShopId(), stockIDTO.getUserId());
                        stockServiceAdapter.stockInOut(stockInOutVO);
                    }

                    List<CommodityStockItemDTO> qualityList = stockIDTO.getCommodityList().stream()
                            .filter(item -> item.getIfCouponCode().equals(YesOrNoEnums.YES.getCode())).collect(Collectors.toList());
                    if(SpringUtil.isNotEmpty(qualityList)){
                        List<StockItemDTO> qualitys = toCommodityList(qualityList, StockInOutEnum.OUT);
    //                    shopCommodityService.reduceQualityStockUnCheck(qualitys, stockIDTO.getShopId());
                        Long stallId = qualityList.get(0).getStallId();

                        StockInOutVO stockInOutVO;
                        if (Objects.nonNull(stallId)) {
                            stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.DD_OUT_POS_CODE_QUALITY
                                    , qualitys, stockIDTO.getShopId(), stockIDTO.getUserId());
                            // 大店 需要把 库区改为临时库
                            qualitys.forEach(data -> data
                                    .setDdStockInOutExtraVO(DdStockInOutExtraVO.buildProvisionaAreaDdStockInOutExtraVO(data.getCommodityId(), data.getDdStockInOutExtraVO().getStallId())));

                            // 大店补充临时库 货位
                            processStallProbisionalAreaGoodsAllocation(qualitys, TempOperationTypeEnum.DISCOUNT_RETURN);
                        } else {
                            stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_POS_QUALITY
                                    , qualitys, stockIDTO.getShopId(), stockIDTO.getUserId());
                        }

                        stockServiceAdapter.stockInOut(stockInOutVO);
                    }
                } else if(SaleTypeEnum.RETURN.getCode().equals(stockIDTO.getSaleType())){
                    List<StockItemDTO> list = toCommodityList(stockIDTO.getCommodityList(), StockInOutEnum.IN);

                    // 大店补充临时库 货位
                    processStallProbisionalAreaGoodsAllocation(list, TempOperationTypeEnum.POS_RETURN);

                    StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_POS_QUALITY
                            , list, stockIDTO.getShopId(), stockIDTO.getUserId());
                    stockServiceAdapter.stockInOut(stockInOutVO);
                }

            }
//        } catch (Exception e) {
//            log.error(msg + "库存处理-只处理POS处理异常,异常:{}", e);
//        }
    }


    /**
     * 大店补充临时库 货位
     *
     * @param stockItems
     */
    private void processStallProbisionalAreaGoodsAllocation(List<StockItemDTO> stockItems, TempOperationTypeEnum tempOperationTypeEnum) {
        if (SpringUtil.isEmpty(stockItems)) {
            return;
        }

        Set<Long> stallIdSet = Optional.ofNullable(stockItems).orElse(Collections.emptyList()).stream()
                .filter(data -> Objects.nonNull(data.getDdStockInOutExtraVO()))
                .map(data -> data.getDdStockInOutExtraVO().getStallId())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap
                = ddTempWarehouseAllocationService.getStallIdProvisionalAreaGoodsAllocationMap(stallIdSet, tempOperationTypeEnum);
        if (SpringUtil.isEmpty(stallIdProvisionalAreaGoodsAllocationMap)) {
            return;
        }
        for (StockItemDTO stockItemDTO : stockItems) {
            DdTempWarehouseDefaultDTO ddTempWarehouseDefaultDTO = stallIdProvisionalAreaGoodsAllocationMap.get(stockItemDTO.getDdStockInOutExtraVO().getStallId());
            if (Objects.isNull(ddTempWarehouseDefaultDTO)) {
                continue;
            }
            DdStockInOutExtraVO ddStockInOutExtraVO = stockItemDTO.getDdStockInOutExtraVO();
            ddStockInOutExtraVO.setGoodsAllocationId(ddTempWarehouseDefaultDTO.getGoodsAllocationId());
            ddStockInOutExtraVO.setGoodsAllocationCode(ddTempWarehouseDefaultDTO.getGoodsAllocationCode());
        }
    }

    private List<StockItemDTO> toCommodityList(List<CommodityStockItemDTO> commodityList, StockInOutEnum stockInOutEnum) {
        List<StockItemDTO> list = new ArrayList<>();
        for (CommodityStockItemDTO dto : commodityList) {
            StockItemDTO stockItemDTO;

            Long commodityId = dto.getCommodityId();

            Long stallId = dto.getStallId();
            if (Objects.nonNull(stallId)) {
                DdStockInOutExtraVO ddStockInOutExtraVO = StockInOutEnum.OUT.equals(stockInOutEnum)
                        ? DdStockInOutExtraVO.buildShelfAreaDdStockInOutExtraVO(commodityId, dto.getStallId())
                        : DdStockInOutExtraVO.buildProvisionaAreaDdStockInOutExtraVO(commodityId, dto.getStallId());

                stockItemDTO = new StockItemDTO(commodityId, dto.getStockNumber(), dto.getQuantity(), ddStockInOutExtraVO);
            } else {
                stockItemDTO = new StockItemDTO(commodityId, dto.getStockNumber(), dto.getQuantity(), null);
            }
//            stockItemDTO.setIfMother(dto.getIfMotherCommodity());
            list.add(stockItemDTO);
        }
        return list;
    }

    /**
     * OPERATE-UPDATE 库存修改   Deprecated 改为接口调用
     * OPERATE-COPY   库存覆盖【鲜食盘点】
     * @param message
     */
//    @KafkaListener(id="${application.name.switch}"+KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC,
//            topics = {"${application.name.switch}"+KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC},
//            containerFactory = "kafkaListenerContainerFactory",errorHandler ="kafkaConsumerErrorHandler")
    @KafkaListener(id="${application.name.switch}"+KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC + "xdWmsKafkaListenerContainerFactory",
//            topics = {"${application.name.switch}"+KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC},
            topics = {
                    "${application.name.switch}"+ KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}"+ KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC
            },
            containerFactory = "kafkaListenerContainerFactory",
            errorHandler ="kafkaConsumerErrorHandler")
    public void shopStockListen(String message) {
        String msg = "topic:" + KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC + " ====================message==" + message;
        log.info(msg);
//        try {
            //多种业务场景 都得区分 转换成 Enum
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            if(messageWrapper != null && messageWrapper.getData() != null) {
                CommodityXsStockIDTO stockIDTO = JSON.parseObject(messageWrapper.getData().toString(), CommodityXsStockIDTO.class);
                log.info("收到门店商品库存处理通知, shopId:{}", stockIDTO.getShopId());

                if(KafkaMessageOperationTypeEnum.UPDATE.equals(messageWrapper.getOptionType())){
                    ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());

                    StockInOutTypeEnums stockInOutTypeEnums = StockInOutTypeEnums.fromCode(stockIDTO.getTypeCode());
                    if (stockInOutTypeEnums == null) {
                        log.error("收到无效门店商品库存处理通知:{}", stockIDTO);
                        return;
                    }
                }else if(KafkaMessageOperationTypeEnum.COPY.equals(messageWrapper.getOptionType())){
                    if(SpringUtil.isEmpty(stockIDTO.getCommodityList())){
                        return;
                    }
                    ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());

                    LockUtils.checkLock(LockUtils.STOCK_INOUT, "inventory:" + stockIDTO.getReferId() + stockIDTO.getReferCode() + stockIDTO.getCommodityList().hashCode(), 10, TimeUnit.SECONDS);

                    try {
                        stockServiceAdapter.stockInventory(stockIDTO, idAndCode);
                    } catch (Exception e) {
                        log.error("库存覆盖【鲜食盘点】监听异常:{}", e);

                        //发送微信模板信息
                        SmsMessageClientVo smsMessageIDTO = new SmsMessageClientVo();
                        //content长度限制
                        smsMessageIDTO.setContent("鲜食盘点监听异常");
                        smsMessageIDTO.setMessageTypeCode(SmsMessageTypeEnums.XD_WMS_INFO_WARN.getCode());
                        weChatSendMessageClient.xdMessageWeiXinWarning(smsMessageIDTO);
                    }
                }
            }
//        } catch (Exception e) {
//            log.error(msg + "库存处理-处理异常,异常:{}", e);
//        }
    }

    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC + "xdWmsKafkaListenerContainerFactory",
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.SHOP_STOCK_CHANGE_TOPIC + "xdWmsKafkaListenerContainerFactory"
        );
    }
}
