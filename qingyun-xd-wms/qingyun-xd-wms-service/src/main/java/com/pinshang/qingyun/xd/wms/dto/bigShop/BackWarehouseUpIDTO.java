package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("BackWarehouseUpIDTO")
public class BackWarehouseUpIDTO {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("上架数量")
    private BigDecimal upQuantity;

    @ApiModelProperty("上架货位号")
    private String goodsAllocationCode;

    @ApiModelProperty("确认加工,1是 0否")
    private Integer needBomProcess;

}