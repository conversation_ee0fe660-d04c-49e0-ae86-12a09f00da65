package com.pinshang.qingyun.xd.wms.vo.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("CompleteReplenishmentCommodityReqVO")
public class CompleteReplenishmentCommodityReqVO {

    @ApiModelProperty("补货类型： 1：排面补货 2：拣货位补货")
    private Integer type = 1;

    @ApiModelProperty("任务ID")
    private Long taskId;

}