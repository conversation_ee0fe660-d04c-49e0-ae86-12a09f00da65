package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsAllocationCommodityBindDTO {

    private Long shopId;

    private Long stallId;

    /**
     * 货位id
     */
    private Long goodsAllocationId;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 最小库存
     */
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    private BigDecimal maxStock;

    public void check() {
        QYAssert.isTrue(null != shopId, "门店信息不能为空");
        QYAssert.isTrue(null != stallId, "档口信息不能为空");
        QYAssert.isTrue(null != goodsAllocationId, "货位信息不能为空");
        QYAssert.isTrue(null != commodityId, "商品信息不能为空");

        if (null != minStock) {
            QYAssert.isTrue(minStock.compareTo(BigDecimal.ZERO) >= 0 && minStock.compareTo(DdUtils.STOCK_RESTRICT) <= 0 ,"拣货位最小数量不能超过99999" );
        }

        if (null != maxStock) {
            QYAssert.isTrue(maxStock.compareTo(BigDecimal.ZERO) >= 0 && maxStock.compareTo(DdUtils.STOCK_RESTRICT) <= 0,"拣货位最大数量不能超过99999" );
        }
    }
}
