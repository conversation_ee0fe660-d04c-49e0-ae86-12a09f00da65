package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_xd_quality_return_order_pic")
public class QualityReturnOrderPic {

    /**主键ID*/
    @TableId
    private Long id;

    /**退货单表主键ID*/
    private Long qualityReturnOrderId;

    /** 类型：1-普通图片、2-长图、3-视频 */
    private Integer picType;

    /**URL*/
    private String picUrl;

    private Date createTime;
    private Long createId;

    public enum PicTypeEnums {
        PIC(1),
        LONG_PIC(2),
        VIDEO(3),
        ;
        private Integer code;
        PicTypeEnums(Integer code) {
            this.code = code;
        }
        public Integer getCode() {
            return code;
        }
    }
}
