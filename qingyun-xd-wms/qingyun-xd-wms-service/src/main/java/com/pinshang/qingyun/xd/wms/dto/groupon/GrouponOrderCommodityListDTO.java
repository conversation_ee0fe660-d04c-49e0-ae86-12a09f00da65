package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderCommodityListDTO {

    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("已购数量")
    private Integer number;

    @ApiModelProperty("商品主码副码")
    private List<String> barCodeList;

    @ApiModelProperty("条形码")
    private String barCode;


//    @ApiModelProperty("商品名称")
//    private String commodityName;
//
//    @ApiModelProperty("商品规格")
//    private String commoditySpec;
//
//    @ApiModelProperty("商品计量单位")
//    private String commodityUnitName;
//
//    @ApiModelProperty("订单完成时间")
//    private Date orderCompleteDate;
//
//    @ApiModelProperty("订单编号")
//    private Long orderId;
//
//    @ApiModelProperty("已购数量")
//    private Integer number;
//
//    @ApiModelProperty("商品编码")
//    private String barCode;
//
//    @ApiModelProperty("收货人")
//    private String receiveMan;
//
//    @ApiModelProperty("收货人手机号")
//    private String receiveMobile;
//
//    private Long orderItemId;
//
//    @ApiModelProperty("是否称重  1=称重，0=非称重")
//    private Integer isWeight;
//
//    @ApiModelProperty("金额")
//    private BigDecimal totalAmount;
//
//    private Integer orderStatus;
}
