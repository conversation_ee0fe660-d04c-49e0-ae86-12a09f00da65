package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CloudDetailDTO {

    private Long shopId;

    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderCode;

    /**
     * XdOrderStatusEnum
     */
    @ApiModelProperty("0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定")
    private Integer orderStatus;

    @ApiModelProperty("预约开始时间")
    private Date receiveTimeBegin;

    @ApiModelProperty("预约结束时间")
    private Date receiveTimeEnd;

    @ApiModelProperty("总包裹数")
    private Integer sumPackages;

    @ApiModelProperty("已拣包裹数")
    private Integer pickPackages;

    @ApiModelProperty("订单短号")
    private String orderNum;

    @ApiModelProperty("2云超订单  6云超团购订单")
    private Integer orderType;

    private Integer sourceType;

    @ApiModelProperty("2云超订单 包裹列表详情")
    private List<PackageListDTO> packages;

    @ApiModelProperty("6云超团购订单 商品列表")
    private List<CloudGroupCommodityDTO> commodityList;

}
