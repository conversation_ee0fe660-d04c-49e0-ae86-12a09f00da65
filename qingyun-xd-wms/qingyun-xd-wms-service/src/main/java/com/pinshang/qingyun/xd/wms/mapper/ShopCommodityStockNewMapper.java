package com.pinshang.qingyun.xd.wms.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2022/9/6/006 13:55
 */
public interface ShopCommodityStockNewMapper{

    /**
     * 门店id和商品id拼接集合查询存在的，门店商品库存数据
     * @param shopIdAndCommodityIdList
     * @return
     */
    List<String> findShopCommodityStockListByParams(@Param("shopIdAndCommodityIdList") List<String> shopIdAndCommodityIdList );

    /**
     * 查询所有门店id集合
     * @return
     */
    List<Long> findAllShopIdList();

    /**
     * 门店id查询商品库存id集合
     * @param shopId
     * @return
     */
    List<Long> findShopCommodityStockIdListByShopId(@Param("shopId") Long shopId);

    /**
     * 期初门店商品id字段
     * @param id
     */
    void initShopCommodityById(@Param("id") Long id);
}
