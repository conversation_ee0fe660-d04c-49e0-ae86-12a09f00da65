package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotSingleQueryDTO extends Pagination<StockAllotOrderListDTO> {

    @ApiModelProperty(value = "入库门店")
    private Long inShopId;

    @ApiModelProperty(value = "调出门店")
    private Long outShopId;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private Long createId;

    @ApiModelProperty(value = "开始创建时间")
    private String startCreateTime;

    @ApiModelProperty(value = "结束创建时间")
    private String endCreateTime;

    @ApiModelProperty(value = "开始审核时间")
    private String startAuditTime;

    @ApiModelProperty(value = "结束审核时间")
    private String endAuditTime;

    @ApiModelProperty(value = "开始出库日期")
    private String startOutTime;

    @ApiModelProperty(value = "结束出库日期")
    private String endOutTime;

    @ApiModelProperty(value = "开始入库日期")
    private String startInTime;

    @ApiModelProperty(value = "结束日库日期")
    private String endInTime;
}
