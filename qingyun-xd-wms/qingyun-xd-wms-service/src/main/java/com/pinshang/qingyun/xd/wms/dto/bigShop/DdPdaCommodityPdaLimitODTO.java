package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
public class DdPdaCommodityPdaLimitODTO {

    @ApiModelProperty("限量销售设置id")
    private String limitId;

    private Long stallId;

    private Long commodityId;

    @ApiModelProperty("限量销售份数")
    private Integer limitNumber;

    @ApiModelProperty("限量计算方式 1=从0计算  2=累加计算")
    private Integer limitType;

    @ApiModelProperty("生效方式: 1 不循环生效 2 每天循环生效")
    private Integer effectType;

    @ApiModelProperty("生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectBeginTime;

    @ApiModelProperty("循环开始时间")
    private String loopTime;

    @ApiModelProperty("排面区库存")
    private BigDecimal stockQuantity;

    @ApiModelProperty("拣货区库存")
    private BigDecimal pickingAreaStock;

    @ApiModelProperty("存储区库存")
    private BigDecimal warehouseAreaStock;

    @ApiModelProperty("临时库存")
    private BigDecimal stockProvisional;

    @ApiModelProperty("冻结库存")
    private BigDecimal freezeQuantity;

    @ApiModelProperty("预留库存")
    private BigDecimal reserveStock;

}
