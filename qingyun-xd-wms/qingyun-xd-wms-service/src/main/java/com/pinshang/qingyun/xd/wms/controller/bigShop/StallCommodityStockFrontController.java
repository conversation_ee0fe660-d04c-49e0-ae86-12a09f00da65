package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockFrontService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/12
 * @Version 1.0
 */
@Api(tags = "档口库存查询", description = "档口库存查询")
@RestController
@RequestMapping("/bigShop/stallCommodityStockFront")
@Slf4j
public class StallCommodityStockFrontController {

    @Autowired
    private StallCommodityStockFrontService stockService;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @PostMapping(value = "/stockPage")
    @ApiOperation(value = "档口库存分页查询")
    @MethodRender
    public PageInfo<StallCommodityStockPageODTO> stockPage(@RequestBody StallCommodityStockPageIDTO idto){
        QYAssert.isTrue(null != idto.getShopId() , "请选择门店！");
        ddTokenShopIdService.processReadDdTokenShopId(idto.getShopId(), idto.getStallId());
        return stockService.stockPage(idto);
    }

    @PostMapping(value = "/qualityStockPage")
    @ApiOperation(value = "质检档口库存分页查询")
    @MethodRender
    public PageInfo<StallCommodityStockPageODTO> qualityStockPage(@RequestBody StallCommodityStockPageIDTO idto){
        QYAssert.isTrue(null != idto.getShopId() , "请选择门店！");
        QYAssert.isTrue(null != idto.getStallId() , "请选择档口！");
        ddTokenShopIdService.processReadDdTokenShopId(idto.getShopId(), idto.getStallId());
        return stockService.stockPage(idto);
    }

    @PostMapping(value = "/listStockFreezeStatusLog")
    @ApiOperation(value = "档口商品冻结明细")
    public PageInfo<StockLogFreezeLogODTO> listStockFreezeStatusLog(@RequestBody StockLogFreezeLogIDTO idto) {
        return stockService.listStockFreezeStatusLog(idto);
    }

    @ApiOperation(value = "档口库存分页查询导出", notes = "档口库存分页查询导出")
    @GetMapping("/export")
    public void exportManualSort( StallCommodityStockPageIDTO idto, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(null != idto.getShopId() , "请选择门店！");
        idto.initExportPage();
        List<StallCommodityStockPageODTO> list = stockService.stockPage(idto).getList();
        renderService.render(list, "/stockPage/export");
        try {
            String fileName = "档口库存分页查询表_" + LocalDateTime.now().toString("yyyy-MM-dd");
            ExcelUtil.setFileNameAndHead(response, fileName);

            if (1 == idto.getType()) {
                List<StallCommodityStockExport1ODTO> exportList = BeanCloneUtils.copyTo(list,StallCommodityStockExport1ODTO.class);

                EasyExcel.write(response.getOutputStream(), StallCommodityStockExport1ODTO.class).autoCloseStream(Boolean.FALSE).sheet("档口库存分页查询")
                        .doWrite( exportList );
            }else if (3 == idto.getType()) {
                List<StallCommodityStockExport3ODTO> exportList = BeanCloneUtils.copyTo(list,StallCommodityStockExport3ODTO.class);

                EasyExcel.write(response.getOutputStream(), StallCommodityStockExport3ODTO.class).autoCloseStream(Boolean.FALSE).sheet("档口库存分页查询")
                        .doWrite( exportList );
            }
        }catch (Exception e){
            ExcelUtil.setExceptionResponse( response );
            log.error("",e);
        }
    }


    @ApiOperation(value = "库存查询-barcode", notes = "库存查询-barcode")
    @GetMapping("/selectStockByCommodity")
    @MethodRender
    public StallCommodityStockForPDAODTO selectStockByCommodity(@RequestParam("barCode") String barCode, @RequestParam("stallId") Long stallId){
        return stockService.selectStockByCommodity(barCode, stallId);
    }

}
