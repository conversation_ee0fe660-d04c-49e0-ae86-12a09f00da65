package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("AddTaskCommodityRspVO")
public class AddTaskCommodityRspVO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("条形码")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.barCode,keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityUnit,keyName = "commodityId")
    private String commodityUnitName;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.isWeight,keyName = "commodityId")
    private Integer isWeight;

    private String isWeightStr;

    public String getIsWeightStr() {
        return YesOrNoEnums.getName(isWeight);
    }


    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("排面库存（排面补货）")
    private BigDecimal stockQuantity;

    @ApiModelProperty("商品陈列位最小数量（排面补货）")
    private BigDecimal commodityDisplayPositionMinStock;

    @ApiModelProperty("商品陈列位最大数量（排面补货）")
    private BigDecimal commodityDisplayPositionMaxStock;

    @ApiModelProperty("后仓库存（排面补货）")
    private BigDecimal postWarehouseStock;

    @ApiModelProperty("拣货位库存（拣货位补货）")
    private BigDecimal pickingPositionQuantity;

    @ApiModelProperty("商品拣货位最小数量（拣货位补货）")
    private BigDecimal pickingPositionMinStock;

    @ApiModelProperty("商品拣货位最大数量（拣货位补货）")
    private BigDecimal pickingPositionMaxStock;

    @ApiModelProperty("存储位库存（拣货位补货）")
    private BigDecimal warehouseStock;

    @ApiModelProperty("计划补货数量")
    private BigDecimal plannedQuantity;

    @ApiModelProperty("警告信息")
    private String warnMsg;



}