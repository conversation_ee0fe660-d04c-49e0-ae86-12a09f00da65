package com.pinshang.qingyun.xd.wms.vo;

import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import lombok.ToString;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

@ToString
public class StockInOutVO {

    private Pair<Long, String> idAndCode;

    private StockInOutTypeEnums typeEnum;

    private List<StockItemDTO> commodityList;

    private Long warehouseId;

    private Long userId;

    public static StockInOutVO buildStockInOutVO(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum
            , List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        StockInOutVO stockInOutVO = new StockInOutVO();
        stockInOutVO.setIdAndCode(idAndCode);
        stockInOutVO.setTypeEnum(typeEnum);
        stockInOutVO.setCommodityList(commodityList);
        stockInOutVO.setWarehouseId(warehouseId);
        stockInOutVO.setUserId(userId);
        return stockInOutVO;
    }

    public Pair<Long, String> getIdAndCode() {
        return idAndCode;
    }

    public void setIdAndCode(Pair<Long, String> idAndCode) {
        this.idAndCode = idAndCode;
    }

    public StockInOutTypeEnums getTypeEnum() {
        return typeEnum;
    }

    public void setTypeEnum(StockInOutTypeEnums typeEnum) {
        this.typeEnum = typeEnum;
    }

    public List<StockItemDTO> getCommodityList() {
        return commodityList;
    }

    public void setCommodityList(List<StockItemDTO> commodityList) {
        this.commodityList = commodityList;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

}
