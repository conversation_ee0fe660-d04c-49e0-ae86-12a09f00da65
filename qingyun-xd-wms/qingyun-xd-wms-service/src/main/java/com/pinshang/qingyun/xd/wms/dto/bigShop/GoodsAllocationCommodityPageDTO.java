package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocationCommodityPageDTO {

    @ExcelIgnore
    private Long goodsAllocationId;

    @ExcelIgnore
    @ApiModelProperty("如果该字段有值表示已经绑定，否在表示没有绑定")
    private Long goodsAllocationCommodityId;

    @ApiModelProperty("区域编码")
    @ExcelProperty("区域")
    private String areaCode;

    @ApiModelProperty("拣货位")
    @ExcelProperty("拣货位")
    private String goodsAllocationCode;

    @ApiModelProperty("状态")
    @ExcelIgnore
    private Integer status;

    @ExcelProperty("货位状态")
    private String statusName;

    @ApiModelProperty("所属档口")
    @ExcelProperty("所属档口")
    private String stallName;

    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @ExcelProperty("计量单位")
    private String commodityUnitName;

    @ApiModelProperty("品类")
    @ExcelProperty("品类")
    private String commodityThirdKindName;

    @ApiModelProperty("拣货位最小数量")
    @ExcelProperty("拣货位最小数量")
    private BigDecimal minStock;

    @ApiModelProperty("拣货位最大数量")
    @ExcelProperty("拣货位最大数量")
    private BigDecimal maxStock;

    public String getStatusName() {
        StatusEnum statusEnum = StatusEnum.fromCode(status);
        return null != statusEnum ? statusEnum.getName() : null;
    }

}
