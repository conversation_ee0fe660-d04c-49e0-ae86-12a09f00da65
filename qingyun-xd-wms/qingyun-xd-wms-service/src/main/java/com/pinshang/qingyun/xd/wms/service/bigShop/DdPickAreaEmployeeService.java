package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.SystemUserConstant;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.shop.dto.bigShop.PickAreaODTO;
import com.pinshang.qingyun.shop.dto.bigShop.PickAreaQueryIDTO;
import com.pinshang.qingyun.shop.service.bigShop.DdPickAreaClient;
import com.pinshang.qingyun.smm.dto.employee.SelectShopEmployeeInfoListIDTO;
import com.pinshang.qingyun.smm.dto.employee.ShopEmployeeInfoODTO;
import com.pinshang.qingyun.smm.service.EmployeeServiceClient;
import com.pinshang.qingyun.xd.wms.bo.PartitionPickDistributeEmployeeBO;
import com.pinshang.qingyun.xd.wms.dto.SearchWarehouseEmployeeDTO;
import com.pinshang.qingyun.xd.wms.dto.WarehouseEmployeeInfoDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdPickAreaEmployeeMapper;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPickAreaEmployee;
import com.pinshang.qingyun.xd.wms.service.WarehouseEmployeeService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RPriorityQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/11/5
 */
@Slf4j
@Service
public class DdPickAreaEmployeeService extends ServiceImpl<DdPickAreaEmployeeMapper, DdPickAreaEmployee> {

    @Autowired
    private DdPickAreaEmployeeMapper ddPickAreaEmployeeMapper;

    @Autowired
    private EmployeeServiceClient employeeServiceClient;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private DdPickAreaClient ddPickAreaClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WarehouseEmployeeService employeeService;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private RedisLockService redisLockService;

    private static final String PICK_EMPLOYEE_V2 = "WMS:PICK_EMPLOYEE_V2:";

    /**
     * 获取分区map
     *
     * @param pickAreaIdList
     * @return
     */
    public Map<Long, PickAreaODTO> getPickAreaMap(List<Long> pickAreaIdList) {
        Map<Long, PickAreaODTO> pickAreaMap = new HashMap<>(pickAreaIdList.size());
        List<PickAreaODTO> pickAreaODTOS = ddPickAreaClient.queryPickAreaByIds(pickAreaIdList);
        if (CollectionUtils.isNotEmpty(pickAreaODTOS)) {
            pickAreaMap = pickAreaODTOS.stream().collect(Collectors.toMap(PickAreaODTO::getPickAreaId, item -> item));
        }
        return pickAreaMap;
    }

    /**
     * 分区拣货员列表
     *
     * @param req
     * @return
     */
    public PageInfo<DdPickAreaEmployeeODTO> pickAreaEmployeePageList(DdPickAreaEmployeePageIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店不能为空");

        PageInfo<DdPickAreaEmployeeODTO> pageInfo = PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
            ddPickAreaEmployeeMapper.pickAreaEmployeePageList(req);
        });

        List<DdPickAreaEmployeeODTO> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            // 获取区域信息
            Set<Long> pickAreaIdList = list.stream().map(item -> item.getPickAreaId()).collect(Collectors.toSet());
            Map<Long, PickAreaODTO> pickAreaMap = getPickAreaMap(new ArrayList<>(pickAreaIdList));

            Set<Long> employeeIdList = list.stream().map(item -> Long.valueOf(item.getEmployeeId())).collect(Collectors.toSet());
            List<DdPickAreaEmployeeODTO> pickCountList = pickOrderMapper.queryPickOrderCountByEmployee(new ArrayList<>(employeeIdList));
            Map<String, DdPickAreaEmployeeODTO> pickCountMap = new HashMap<>(list.size());
            if (CollectionUtils.isNotEmpty(pickCountList)) {
                pickCountList.forEach(item -> {
                    pickCountMap.put(item.getEmployeeId() + "_" + item.getPickAreaId(), item);
                });
            }
            list.forEach(item -> {
                PickAreaODTO pickAreaODTO = pickAreaMap.get(item.getPickAreaId());
                if (pickAreaODTO != null) {
                    item.setPickAreaName(pickAreaODTO.getPickAreaName());
                }

                String key = item.getEmployeeId() + "_" + item.getPickAreaId();
                if (pickCountMap.containsKey(key)) {
                    DdPickAreaEmployeeODTO countOdto = pickCountMap.get(key);
                    item.setTaskCount(countOdto.getTaskCount());
                } else {
                    item.setTaskCount(0);
                }

            });
        }
        return pageInfo;
    }

    /**
     * 保存分区拣货员
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePickAreaEmployee(DdPickAreaEmployeeSaveIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        req.setShopId(tokenInfo.getShopId());

        Long employeeId = req.getEmployeeId();
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        QYAssert.notNull(employeeId, "职员不能为空");
        QYAssert.notNull(req.getPickAreaId(), "拣货分区不能为空");

        // 查找当前门店启用的拣货分区list
        PickAreaQueryIDTO pickAreaQueryIDTO = new PickAreaQueryIDTO();
        pickAreaQueryIDTO.setShopId(req.getShopId());
        pickAreaQueryIDTO.setStatus(YesOrNoEnums.YES.getCode());
        pickAreaQueryIDTO.setPickAreaId(req.getPickAreaId());
        List<PickAreaODTO> pickAreaODTOS = ddPickAreaClient.queryPickAreaByShopId(pickAreaQueryIDTO);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(pickAreaODTOS), "门店分区职员信息变化，请刷新重试");

        // 验证职员是否当前门店下，并且在职状态
        SelectShopEmployeeInfoListIDTO selectShopEmployeeInfoListIDTO = new SelectShopEmployeeInfoListIDTO();
        selectShopEmployeeInfoListIDTO.setShopId(req.getShopId());
        selectShopEmployeeInfoListIDTO.setEmployeeState(YesOrNoEnums.YES.getCode());
        List<ShopEmployeeInfoODTO> employeeInfoList = employeeServiceClient.selectShopEmployeeInfoList(selectShopEmployeeInfoListIDTO);
        if (CollectionUtils.isEmpty(employeeInfoList)) {
            QYAssert.isFalse("门店分区职员信息变化，请刷新重试");
        } else {
            List<Long> employeeIdList = employeeInfoList.stream().map(item -> item.getEmployeeId()).collect(Collectors.toList());
            if (!employeeIdList.contains(employeeId)) {
                QYAssert.isFalse("门店分区职员信息变化，请刷新重试");
            }
        }

        LambdaQueryWrapper<DdPickAreaEmployee> pickAreaEmployeeQuery = new LambdaQueryWrapper<DdPickAreaEmployee>()
                .eq(DdPickAreaEmployee::getEmployeeId, employeeId);
        List<DdPickAreaEmployee> pickAreaEmployees = ddPickAreaEmployeeMapper.selectList(pickAreaEmployeeQuery);
        if (CollectionUtils.isNotEmpty(pickAreaEmployees)) {
            Long pickAreaId = pickAreaEmployees.get(0).getPickAreaId();
            Map<Long, PickAreaODTO> pickAreaMap = getPickAreaMap(Collections.singletonList(pickAreaId));

            // 查询分区信息
            QYAssert.isFalse("此拣货员已添加到" + pickAreaMap.get(pickAreaId).getPickAreaName() + "区");
        }

        WarehouseEmployee warehouseEmployee = employeeService.queryEmployee(employeeId, WarehouseEmployeeTypeEnum.PICK.getCode());
        Integer workStatus = warehouseEmployee.getWorkStatus();
        // 保存分区拣货员
        Long userId = tokenInfo.getUserId();
        Date now = new Date();
        DdPickAreaEmployee ddPickAreaEmployee = BeanCloneUtils.copyTo(req, DdPickAreaEmployee.class);
        ddPickAreaEmployee.setWorkStatus(workStatus);
        ddPickAreaEmployee.setCreateId(userId);
        ddPickAreaEmployee.setCreateTime(now);
        ddPickAreaEmployee.setUpdateId(userId);
        ddPickAreaEmployee.setUpdateTime(now);
        ddPickAreaEmployeeMapper.insert(ddPickAreaEmployee);

        if (YesOrNoEnums.YES.getCode().equals(workStatus)) {
            addOrUpdatePicker(req.getShopId(), req.getPickAreaId(), employeeId);
        }
        return Boolean.TRUE;
    }

    /**
     * 移除分区拣货员
     *
     * @param employeeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Integer deleteByEmployeeId(Long employeeId) {
        QYAssert.notNull(employeeId, "职员id不能为空");

        // 一个职员只会在一个区域
        DdPickAreaEmployee ddPickAreaEmployee = ddPickAreaEmployeeMapper.selectOne(new LambdaQueryWrapper<DdPickAreaEmployee>().eq(DdPickAreaEmployee::getEmployeeId, employeeId));
        QYAssert.isTrue(ddPickAreaEmployee != null, "分区拣货员不存在");

        // 删除
        ddPickAreaEmployeeMapper.deleteById(ddPickAreaEmployee.getId());

        // 若拣货员名下存在未完成的拣货单，则同时清除对应拣货单的拣货员
        pickOrderMapper.clearPickOrderEmployee(ddPickAreaEmployee.getEmployeeId());

        // 删除分区拣货员，从rediss移除
        removePicker(ddPickAreaEmployee.getShopId(), ddPickAreaEmployee.getPickAreaId(), employeeId);
        return 1;
    }

    /**
     * 查询已是当前门店拣货员的职员
     *
     * @param shopId
     * @return
     */
    public List<Long> queryPickAreaEmployeeList(Long shopId) {
        QYAssert.notNull(shopId, "门店id不能为空");

        List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(new LambdaQueryWrapper<DdPickAreaEmployee>().eq(DdPickAreaEmployee::getShopId, shopId));
        if (CollectionUtils.isNotEmpty(ddPickAreaEmployees)) {
            return ddPickAreaEmployees.stream().distinct().map(item -> item.getEmployeeId()).collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 接单或者停止接单
     *
     * @param idList
     * @param workStatus
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer workOnOrOff(List<Long> idList, Integer workStatus) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(idList), "idList不能为空");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = Optional.ofNullable(tokenInfo)
                .map(TokenInfo::getUserId)
                .orElse(SystemUserConstant.SYSTEM_USER_ID);

        Date now = new Date();

        // 开始接单，查询未接单的。停止接单，查询已接单的
        List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(
                new LambdaQueryWrapper<DdPickAreaEmployee>()
                        .in(DdPickAreaEmployee::getId, idList)
                        .eq(DdPickAreaEmployee::getWorkStatus, (YesOrNoEnums.YES.getCode().equals(workStatus) ? 0 : 1)));

        if (SpringUtil.isEmpty(ddPickAreaEmployees)) {
            return 0;
        }

        ddPickAreaEmployees.forEach(item -> {
            item.setWorkStatus(workStatus);
            item.setUpdateTime(now);
            item.setUpdateId(userId);
        });

        // 批量更新
        this.updateBatchById(ddPickAreaEmployees);

        // 维护rediss
        ddPickAreaEmployees.forEach(item -> {

            if (YesOrNoEnums.YES.getCode().equals(workStatus)) {
                addOrUpdatePicker(item.getShopId(), item.getPickAreaId(), item.getEmployeeId());
            } else {
                removePicker(item.getShopId(), item.getPickAreaId(), item.getEmployeeId());
            }
        });

        return ddPickAreaEmployees.size();
    }

    /**
     * pda接单或者停止接单(pda调用)
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaWorkOnOrOff(Long shopId, Long employeeId, Integer workStatus) {
        List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(
                new LambdaQueryWrapper<DdPickAreaEmployee>()
                        .eq(DdPickAreaEmployee::getShopId, shopId)
                        .eq(DdPickAreaEmployee::getEmployeeId, employeeId));
        if (CollectionUtils.isNotEmpty(ddPickAreaEmployees)) {
            workOnOrOff(Collections.singletonList(ddPickAreaEmployees.get(0).getId()), workStatus);
        }
        return Boolean.TRUE;
    }

    /**
     * px后台切换分区拣货员接单状态
     *
     * @param shopId
     * @param employeeIdList
     * @param workStatus
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateWorkStatus(Long shopId, List<Long> employeeIdList, Integer workStatus) {
        List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(
                new LambdaQueryWrapper<DdPickAreaEmployee>()
                        .eq(DdPickAreaEmployee::getShopId, shopId)
                        .in(DdPickAreaEmployee::getEmployeeId, employeeIdList));

        if (CollectionUtils.isEmpty(ddPickAreaEmployees)) {
            return Boolean.FALSE;
        }

        List<Long> idList = ddPickAreaEmployees.stream()
                .map(DdPickAreaEmployee::getId)
                .collect(Collectors.toList());

        workOnOrOff(idList, workStatus);
        return Boolean.TRUE;
    }


    /**
     * pda查询我的状态(pda调用)
     *
     * @return
     */
    public WarehouseEmployee queryPicker4Me(Long shopId, Long employeeId) {
        WarehouseEmployee employee = new WarehouseEmployee();

        DdPickAreaEmployeePageIDTO req = new DdPickAreaEmployeePageIDTO();
        req.setShopId(shopId);
        req.setEmployeeId(employeeId);
        List<DdPickAreaEmployeeODTO> pickAreaEmployeeList = ddPickAreaEmployeeMapper.pickAreaEmployeePageList(req);
        if (CollectionUtils.isNotEmpty(pickAreaEmployeeList)) {

            employee = BeanCloneUtils.copyTo(pickAreaEmployeeList.get(0), WarehouseEmployee.class);
            employee.setEmployeeId(employeeId);
            employee.setWarehouseId(shopId);
            employee.setType(1);
        }
        return employee;
    }


    /**
     * 大店分配拣货员
     *
     * @return
     */
    public Long distributePicker(Long shopId, Long pickAreaId) {
        QYAssert.notNull(shopId, "门店id不能为空");
        QYAssert.notNull(pickAreaId, "区域id不能为空");

        if (initEmployeeQueueIfEmpty(shopId, pickAreaId)) return null;

        PartitionPickDistributeEmployeeBO bestPicker = getBestPicker(shopId, pickAreaId);
        return Optional.ofNullable(bestPicker)
                .map(PartitionPickDistributeEmployeeBO::getEmployeeId)
                .orElse(null);
    }

    private boolean initEmployeeQueueIfEmpty(Long shopId, Long pickAreaId) {
        log.warn("初始化拣货员队列,shopId:[{}],pickAreaId:[{}]", shopId, pickAreaId);
        RPriorityQueue<PartitionPickDistributeEmployeeBO> queue = getQueue(shopId, pickAreaId);
        if (Objects.isNull(queue) || queue.isEmpty()) {
            List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(
                    new LambdaQueryWrapper<DdPickAreaEmployee>()
                            .eq(DdPickAreaEmployee::getShopId, shopId)
                            .eq(DdPickAreaEmployee::getPickAreaId, pickAreaId)
                            .eq(DdPickAreaEmployee::getWorkStatus, YesOrNoEnums.YES.getCode()));

            if (SpringUtil.isEmpty(ddPickAreaEmployees)) {
                return true;
            }

            ddPickAreaEmployees.forEach(it -> {
                addOrUpdatePicker(shopId, pickAreaId, it.getEmployeeId());
            });
            log.warn("初始化拣货员队列完成,shopId:[{}],pickAreaId:[{}]", shopId, pickAreaId);
        }
        return false;
    }

    /**
     * 获取 匹配的 拣货员
     * 任务数最小的，分配到任务时间最早的
     *
     * @param shopId
     * @param pickAreaId
     * @return
     */
    public PartitionPickDistributeEmployeeBO getBestPicker(Long shopId, Long pickAreaId) {
        RPriorityQueue<PartitionPickDistributeEmployeeBO> pickerQueue = getQueue(shopId, pickAreaId);

        PartitionPickDistributeEmployeeBO peekPicker = pickerQueue.peek();
        if (peekPicker == null) {
            return null;
        }

        DictionaryODTO dictionary = dictionaryClient
                .getDictionaryById(DictionaryEnums.BIG_WAREHOUSE_PARTITION_PICK_ORDER_MAX_COUNT.getId());

        Integer maxTaskCount = (dictionary != null && dictionary.getOptionValue() != null)
                ? Integer.parseInt(dictionary.getOptionValue())
                : null;

        String lockKey = shopId + "_" + pickAreaId + "_" + peekPicker.getEmployeeId();

        return redisLockService.lock(RedisLockEnums.ADD_OR_UPDATE_PICKER, lockKey, () -> {
            // 重新获取最新的头部元素，避免 peek 与 poll 之间被其他线程修改
            PartitionPickDistributeEmployeeBO picker = pickerQueue.peek();
            if (picker == null) {
                return null;
            }

            if (maxTaskCount != null && picker.getPickPartitionOrderCount() >= maxTaskCount) {
                return null;
            }

            // poll 出队并更新
            PartitionPickDistributeEmployeeBO latestPicker = pickerQueue.poll();
            latestPicker.setPickPartitionOrderCount(latestPicker.getPickPartitionOrderCount() + 1);
            latestPicker.setDistributeTimestamp(System.currentTimeMillis());

            // 放回队列维持排序
            pickerQueue.add(latestPicker);

            return latestPicker; // 返回更新后的对象
        });
    }


    /**
     * 构建队列Key
     */
    private String buildQueueKey(Long shopId, Long pickAreaId) {
        return PICK_EMPLOYEE_V2 + shopId + "_" + pickAreaId;
    }

    /**
     * 获取优先队列对象
     */
    private RPriorityQueue<PartitionPickDistributeEmployeeBO> getQueue(Long shopId, Long pickAreaId) {
        String queueKey = buildQueueKey(shopId, pickAreaId);
        return redissonClient.getPriorityQueue(queueKey);
    }

    /**
     * 新增拣货员到Redis队列
     */
    public void addOrUpdatePicker(Long shopId, Long pickAreaId, Long employeeId) {
        log.warn("新增拣货员,shopId:[{}],pickAreaId:[{}],employeeId:[{}]", shopId, pickAreaId, employeeId);
        String lockKey = shopId + "_" + pickAreaId + "_" + employeeId;
        redisLockService.lock(RedisLockEnums.ADD_OR_UPDATE_PICKER, lockKey, () -> {
            RPriorityQueue<PartitionPickDistributeEmployeeBO> queue = getQueue(shopId, pickAreaId);

            List<DdPickAreaEmployeeODTO> ddPickAreaEmployeeODTOS = pickOrderMapper.queryPickOrderCountByEmployee(Collections.singletonList(employeeId));

            PartitionPickDistributeEmployeeBO picker = new PartitionPickDistributeEmployeeBO();
            picker.setEmployeeId(employeeId);
            int taskCount = Optional.ofNullable(ddPickAreaEmployeeODTOS)
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))
                    .map(DdPickAreaEmployeeODTO::getTaskCount)
                    .orElse(0);

            picker.setPickPartitionOrderCount(taskCount);
            picker.setDistributeTimestamp(System.currentTimeMillis());

            // 先去重
            queue.removeIf(p -> p.getEmployeeId().equals(picker.getEmployeeId()));

            // 再加入新数据
            queue.add(picker);
            log.warn("新增拣货员完成,shopId:[{}],pickAreaId:[{}],employeeId:[{}],pickPartitionOrderCount:[{}]", shopId, pickAreaId, employeeId, taskCount);
        });
    }

    /**
     * 删除拣货员
     */
    public boolean removePicker(Long shopId, Long pickAreaId, Long employeeId) {
        log.warn("删除拣货员,shopId:[{}],pickAreaId:[{}],employeeId:[{}]", shopId, pickAreaId, employeeId);
        RPriorityQueue<PartitionPickDistributeEmployeeBO> queue = getQueue(shopId, pickAreaId);
        return queue.removeIf(p -> p.getEmployeeId().equals(employeeId));
    }

    /**
     * 维护拣货员分配队列
     *
     * @param shopId
     * @param pickAreaId
     * @param employeeId
     */
    public void processPickAreaEmployeeOrder(Long shopId, Long pickAreaId, Long employeeId, Integer changeCount) {
        log.warn("维护拣货员分配队列,shopId:[{}],pickAreaId:[{}],employeeId:[{}]", shopId, pickAreaId, employeeId);
        String lockKey = shopId + "_" + pickAreaId + "_" + employeeId;
        redisLockService.lock(RedisLockEnums.ADD_OR_UPDATE_PICKER, lockKey, () -> {

            RPriorityQueue<PartitionPickDistributeEmployeeBO> queue = getQueue(shopId, pickAreaId);
            PartitionPickDistributeEmployeeBO target = null;
            for (PartitionPickDistributeEmployeeBO item : queue) {
                if (Objects.equals(item.getEmployeeId(), employeeId)) {
                    target = item;
                    break;
                }
            }
            if (Objects.nonNull(target)) {
                queue.remove(target);
                int latestOrderCount = target.getPickPartitionOrderCount() + changeCount;
                if (latestOrderCount < 0) {
                    log.warn("维护拣货员分配队列完成,无任务,shopId:[{}],pickAreaId:[{}],employeeId:[{}],最新任务数量:[{}],", shopId, pickAreaId, employeeId, target.getPickPartitionOrderCount());
                    return;
                }
                target.setPickPartitionOrderCount(latestOrderCount);
                target.setDistributeTimestamp(System.currentTimeMillis());
                queue.add(target);
                log.warn("维护拣货员分配队列完成,shopId:[{}],pickAreaId:[{}],employeeId:[{}],最新任务数量:[{}],",
                        shopId, pickAreaId, employeeId, target.getPickPartitionOrderCount());
            }
        });
    }


    /**
     * 刷新拣货职员
     */
    public void refreshPickAreaEmployee() {
        List<DdPickAreaEmployee> ddPickAreaEmployees = ddPickAreaEmployeeMapper.selectList(
                new LambdaQueryWrapper<>());

        if (CollectionUtils.isNotEmpty(ddPickAreaEmployees)) {
            Map<String, List<DdPickAreaEmployee>> pickAreaEmployeeMap = ddPickAreaEmployees.stream().collect(Collectors.groupingBy(item -> {
                return item.getShopId() + "_" + item.getPickAreaId();
            }));

            pickAreaEmployeeMap.forEach((k, v) -> {
                if (SpringUtil.isNotEmpty(v)) {
                    v.forEach(item -> {
                        if (YesOrNoEnums.YES.getCode().equals(item.getWorkStatus())) {
                            addOrUpdatePicker(item.getShopId(), item.getPickAreaId(), item.getEmployeeId());
                        } else {
                            removePicker(item.getShopId(), item.getPickAreaId(), item.getEmployeeId());
                        }
                    });
                }
            });
        }

    }

    public DdPickAreaEmployee getByEmployeeId(Long shopId, Long employeeId) {
        QYAssert.notNull(employeeId, "员工id不能为空");

        return this.baseMapper.selectOne(
                new LambdaQueryWrapper<DdPickAreaEmployee>()
                        .eq(DdPickAreaEmployee::getEmployeeId, employeeId)
                        .eq(DdPickAreaEmployee::getShopId, shopId)
                        .last(" limit 1")
        );
    }

    public List<DdPickEmployeeInfoODTO> selectPickAreaEmployeeList() {

        SearchWarehouseEmployeeDTO searchDTO = new SearchWarehouseEmployeeDTO();
        searchDTO.setPageSize(65535);
        searchDTO.setTypeEnum(WarehouseEmployeeTypeEnum.PICK);
        PageInfo<WarehouseEmployeeInfoDTO> pageInfo = employeeService.searchEmployeeByType(searchDTO);
        if (Objects.isNull(pageInfo) || SpringUtil.isEmpty(pageInfo.getList())) {
            return Collections.emptyList();
        }
        List<WarehouseEmployeeInfoDTO> pickEmployeeList = pageInfo.getList();

        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        SelectShopEmployeeInfoListIDTO selectShopEmployeeInfoListIDTO = new SelectShopEmployeeInfoListIDTO();
        selectShopEmployeeInfoListIDTO.setEmployeeAccountState(YesOrNoEnums.YES.getCode());
        selectShopEmployeeInfoListIDTO.setEmployeeState(YesOrNoEnums.YES.getCode());
        selectShopEmployeeInfoListIDTO.setShopId(shopId);

        List<Long> execludeEmployeeIdList = queryPickAreaEmployeeList(shopId);
        selectShopEmployeeInfoListIDTO.setExcludeEmployeeIdList(execludeEmployeeIdList);

        List<Long> pickEmployeeIdList = pickEmployeeList.stream().map(WarehouseEmployeeInfoDTO::getEmployeeId).collect(Collectors.toList());
        selectShopEmployeeInfoListIDTO.setIncludeEmployeeIdList(pickEmployeeIdList);
        List<ShopEmployeeInfoODTO> shopEmployeeInfoODTOS = employeeServiceClient.selectShopEmployeeInfoList(selectShopEmployeeInfoListIDTO);
        return BeanCloneUtils.copyTo(shopEmployeeInfoODTOS, DdPickEmployeeInfoODTO.class);
    }

    public Boolean switchPartitionPickStatus(DdPickAreaEmployeePartitionPickSwitchIDTO req) {

        Long shopIdReq = req.getShopId();
        Long shopId = Objects.nonNull(shopIdReq) ? shopIdReq : FastThreadLocalUtil.getQY().getShopId();
        QYAssert.notNull(shopId, "门店不能为空");
        Long employeeId = req.getEmployeeId();
        QYAssert.notNull(employeeId, "员工不能为空");
        Integer partitionPickStatus = req.getPartitionPickStatus();
        QYAssert.notNull(partitionPickStatus, "分区拣货开关状态不能为空");

        DdPickAreaEmployee ddPickAreaEmployee = getByEmployeeId(shopId, employeeId);

        QYAssert.notNull(ddPickAreaEmployee, "员工不存在");

        DdPickAreaEmployee update = new DdPickAreaEmployee();
        update.setId(ddPickAreaEmployee.getId());
        update.setPartitionPickStatus(partitionPickStatus);
        update.setUpdateTime(new Date());

        return ddPickAreaEmployeeMapper.updateById(update) > 0;
    }
}
