package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.groupon.WarehouseDeliveryDetailsIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.WarehouseDeliveryDetailsODTO;
import com.pinshang.qingyun.xd.wms.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.xd.wms.service.StrockOutService;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/31 11:19
 * @Description
 */
@RestController
@RequestMapping("/stockOut")
@Api(value = "缺货", tags = "StockOutController")
@Slf4j
public class StockOutController {
    @Autowired
    private StrockOutService strockOutService;

    /**
     * 缺货统计
     * @return
     */
    @GetMapping("/strockOutStatistics")
    public Boolean strockOutStatistics() {
        strockOutService.strockOutStatistics();
        return true;
    }
    @PostMapping("/page")
    @ApiOperation("前置仓库存缺货监控")
    public PageInfo<StockOutCollectODTO> page(@RequestBody StockOutCollectIDTO idto){
        return strockOutService.page(idto);
    }

    @GetMapping("/export")
    @ApiOperation("导出前置仓库存缺货监控")
    @FileCacheQuery(bizCode = "WMS_SHORTAGE_MONITOR")
    public ModelAndView export(@FileCacheQueryParameter StockOutCollectIDTO idto, HttpServletResponse response) throws IOException {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<StockOutCollectODTO> pageInfo =strockOutService.page(idto);

        List<StockOutCollectODTO> list = pageInfo.getList();
        list.forEach(item ->{
            item.setBarCodeList(null);
            item.setShopId(null);
            item.setCommodityId(null);
        });
        String fileName = "前置仓库存缺货监控" ;
        try {
            List<StockOutCollectODTO> exportList = new ArrayList<>();
            exportList.addAll(list);
            ExcelUtil.setFileNameAndHead( response,fileName);
            EasyExcel.write(response.getOutputStream(), StockOutCollectODTO.class).autoCloseStream(Boolean.FALSE).sheet("前置仓库存缺货监控")
                    .doWrite( exportList );
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("前置仓库存缺货监控导出异常",e);
        }


        Map<String, List<String>> data = new HashMap<>();
        ReportUtil.buildData(list, data ,3,"yyyy-MM-dd HH:mm:ss");
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.STOCK_OUT_MONITOR, data);


    }
}
