package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfResult;
import com.pinshang.qingyun.xd.wms.dto.ShelfCommodityDTO;
import com.pinshang.qingyun.xd.wms.dto.WarehouseShelfnoCommodityDTO;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelfCommodity;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseShelfCommodityMapper extends BaseMapper<WarehouseShelfCommodity> {

    /**
     * 根据shelfId货位号查询绑定商品
     * @param shelfId
     * @return
     */
    WarehouseShelfCommodity queryShelfCommodityByShelfid(@Param("shelfId") Long shelfId);

    /**
     * 根据商品id查询绑定位关系
     * @param commodityId
     * @return
     */
    WarehouseShelfCommodity queryShelfCommodityByCommodityid(@Param("commodityId") Long commodityId, @Param("warehouseId") Long warehouseId);

    /**
     * 查询绑定关系
     * @param commodityId
     * @param shelfId
     * @return
     */
    WarehouseShelfCommodity queryByCommodityAndShelf(@Param("commodityId") Long commodityId, @Param("shelfId") Long shelfId);

    /**
     * 根据货位查询商品信息
     * @return
     */
    MPage<QueryCommodityByShelfResult> queryCommodityByWarehouseShelf(@Param("e") QueryCommodityByShelfDTO queryCommodityByShelfDTO);

    /**
     * 根据商品id查询拣货位信息
     * @return
     */
    List<WarehouseShelfnoCommodityDTO> queryShelfnoByCommodity(@Param("warehouseId") Long warehouseId, @Param("longs") List<Long> longs);

    /**
     * 根据商品id,查询已经绑定的商品名称和code,和货位信息
     * @param warehouseId
     * @param longs
     * @return
     */
    List<ShelfCommodityDTO> queryBindCommodity(@Param("warehouseId") Long warehouseId, @Param("commodityIds") List<Long> commodityIds, @Param("shelfIds") List<Long> shelfIds);

}
