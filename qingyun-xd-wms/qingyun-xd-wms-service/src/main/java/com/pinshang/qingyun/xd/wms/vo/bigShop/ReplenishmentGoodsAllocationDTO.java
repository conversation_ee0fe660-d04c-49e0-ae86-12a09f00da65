package com.pinshang.qingyun.xd.wms.vo.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReplenishmentGoodsAllocationDTO {

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    /**
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum
     */
    private Integer storageAreaType;

    @ApiModelProperty("库区ID")
    private Long goodsAllocationId;

    @ApiModelProperty("库区CODE")
    private String goodsAllocationCode;

    @ApiModelProperty("库位库存")
    private BigDecimal replenishmentQuantity;
}
