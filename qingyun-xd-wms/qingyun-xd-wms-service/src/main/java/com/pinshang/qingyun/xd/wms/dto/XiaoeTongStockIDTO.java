package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("XiaoeTongStockIDTO")
public class XiaoeTongStockIDTO implements Serializable {

    private static final long serialVersionUID = -8595288839343712106L;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "出入库枚举")
    private StockInOutTypeEnums stockInOutTypeEnum;

    @ApiModelProperty(value = "商品list")
    private List<StockItemDTO> commodityList;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    public void checkData() {
        QYAssert.isTrue(warehouseId != null, "仓库id不能为空");
        QYAssert.isTrue(referId != null, "关联单号id不能为空");
        QYAssert.isTrue(referCode != null, "关联单号code不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");
        QYAssert.isTrue(stockInOutTypeEnum != null, "出入库枚举不能为空");
        QYAssert.isTrue(StockInOutTypeEnums.XIAOE_TOING_OUT_CLIENT_NORMAL.equals(stockInOutTypeEnum)
                || StockInOutTypeEnums.XIAOE_TOING_IN_CLIENT_QUALITY.equals(stockInOutTypeEnum), "小鹅通出入库枚举错误");
    }
}
