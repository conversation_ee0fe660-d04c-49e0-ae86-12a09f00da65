package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@ApiModel("ExecuteReplenishmentCommodityRspVO")
public class ExecuteReplenishmentCommodityRspVO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("条形码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.barCode,keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityUnit,keyName = "commodityId")
    private String commodityUnitName;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.isWeight,keyName = "commodityId")
    private Integer isWeight;

    private String isWeightStr;

    public String getIsWeightStr() {
        return YesOrNoEnums.getName(isWeight);
    }

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("包装类型")
    private String commodityPackageKind;

    @ApiModelProperty("移入货位code")
    private String inGoodsAllocationCode;

    @ApiModelProperty("待补货区库存数量")
    private BigDecimal stockQuantity;


    @ApiModelProperty("计划补货数量")
    private BigDecimal plannedQuantity;

    @ApiModelProperty("待拣货订单冻结数量")
    private BigDecimal freezeQuantity;

    @ApiModelProperty("存储位库存信息列表")
    private List<GoodsAllocationStockDTO> goodsAllocationStockDTOList;

    public void addOutGoodsAllocationStock(Integer storageAreaType,Long goodsAllocationId,String goodsAllocationCode,BigDecimal stock){
        if(SpringUtil.isEmpty(goodsAllocationStockDTOList)){
            goodsAllocationStockDTOList = new ArrayList<>();
        }

        GoodsAllocationStockDTO goodsAllocationStockDTO = new GoodsAllocationStockDTO();
        goodsAllocationStockDTO.setStorageAreaType(storageAreaType);
        goodsAllocationStockDTO.setGoodsAllocationId(goodsAllocationId);
        goodsAllocationStockDTO.setGoodsAllocationCode(goodsAllocationCode);
        goodsAllocationStockDTO.setStock(stock);
        goodsAllocationStockDTOList.add(goodsAllocationStockDTO);
    }




}