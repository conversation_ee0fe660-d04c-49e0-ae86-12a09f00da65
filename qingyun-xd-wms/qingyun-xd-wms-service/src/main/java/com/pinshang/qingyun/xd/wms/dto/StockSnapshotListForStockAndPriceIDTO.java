package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022-05-24-15:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotListForStockAndPriceIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("查询日期 yyyy-MM-dd")
    private String date;
}
