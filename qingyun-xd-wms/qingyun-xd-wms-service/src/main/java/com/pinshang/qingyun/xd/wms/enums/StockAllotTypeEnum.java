package com.pinshang.qingyun.xd.wms.enums;

/**
 * 大店调拨类型
 * <AUTHOR>
 */
public enum StockAllotTypeEnum {
    IN_STORE_ALLOT(1,"店内调拨"),
    OUT_STORE_ALLOT(2,"店外调拨"),

    ;
    private Integer code;
    private String name;

    StockAllotTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static StockAllotTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (StockAllotTypeEnum typeEnum : StockAllotTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
