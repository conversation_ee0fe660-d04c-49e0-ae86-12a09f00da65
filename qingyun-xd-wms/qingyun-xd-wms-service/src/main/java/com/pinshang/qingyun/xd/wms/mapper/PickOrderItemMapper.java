package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.OrderCommodityList;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.bigShop.PickNumberSummary;
import com.pinshang.qingyun.xd.wms.vo.DcShopPackageOrderItemReqVo;
import com.pinshang.qingyun.xd.wms.vo.PackageOrderMakeUpReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 仓库拣货单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@Repository
public interface PickOrderItemMapper extends BaseMapper<PickOrderItem> {

    Integer savePackageOrderSyncData(@Param("list") List<DcShopPackageOrderItemReqVo> list);

    Integer updatePackageOrderSyncData(@Param("list") List<DcShopPackageOrderItemReqVo> list);

    /**
     * 根据拣货单查询商品
     * @param pickOrderId
     * @return
     */
    List<OrderCommodityList> orderCommodityList(@Param("pickOrderId") Long pickOrderId);

    Integer queryPackageOrderItemCount(PackageOrderMakeUpReqVo vo);

    List<Long> queryOrderSyncId(@Param("orderIdList")List<Long> orderIdList);

    List<OrderCommodityList> partitionOrderCommodityList(@Param("partitionPickOrderId") Long partitionPickOrderId);

    List<PickNumberSummary> selectPickNumberSummaryByPickOrderIds(@Param("pickOrderIds") List<Long> pickOrderIds);

}
