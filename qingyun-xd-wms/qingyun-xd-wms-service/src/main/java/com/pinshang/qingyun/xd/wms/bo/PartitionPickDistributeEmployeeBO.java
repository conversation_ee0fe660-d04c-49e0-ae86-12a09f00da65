package com.pinshang.qingyun.xd.wms.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PartitionPickDistributeEmployeeBO implements Serializable, Comparable<PartitionPickDistributeEmployeeBO> {

    private static final long serialVersionUID = 9166248357053056009L;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 分配的分区拣货单数量
     */
    private Integer pickPartitionOrderCount;

    /**
     * 最新分配时间戳
     */
    private Long distributeTimestamp;

    @Override
    public int compareTo(PartitionPickDistributeEmployeeBO other) {
        // 先按任务数升序
        int cmp = Integer.compare(this.pickPartitionOrderCount, other.pickPartitionOrderCount);
        if (cmp != 0) return cmp;
        // 任务数相同则按分配时间升序（越早优先）
        return Long.compare(this.distributeTimestamp, other.distributeTimestamp);
    }
}
