package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liu zhen
 * @DateTime: 2023/4/3 15:14
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockOutLogODTO {
    private Long commodityId;
    private BigDecimal normalChangeQuantity;
    private BigDecimal changeQuantity;
    private Date createTime;
}
