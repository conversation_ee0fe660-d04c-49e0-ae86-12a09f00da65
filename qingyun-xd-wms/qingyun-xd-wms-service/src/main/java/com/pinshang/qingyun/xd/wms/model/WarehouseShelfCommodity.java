package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_xd_warehouse_shelf_commodity")
public class WarehouseShelfCommodity extends BaseEntity {

    @ApiModelProperty(value = "仓库货位id")
    private Long shelfId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;
}
