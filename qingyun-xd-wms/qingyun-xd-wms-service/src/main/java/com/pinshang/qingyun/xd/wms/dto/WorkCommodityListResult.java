package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkCommodityListResult {

    @ApiModelProperty(value = "商品id")
    private Long id;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "加工点id")
    private Long workId;

    @ApiModelProperty(value = "加工点名字")
    private String workName;

    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "前台加工方式")
    private String processName;

    private Long warehouseWorkCommodityId;
}
