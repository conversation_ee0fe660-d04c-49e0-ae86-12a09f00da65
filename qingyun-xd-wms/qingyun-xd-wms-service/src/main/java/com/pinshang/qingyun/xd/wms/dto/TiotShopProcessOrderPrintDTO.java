package com.pinshang.qingyun.xd.wms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TiotShopProcessOrderPrintDTO {

    @ApiModelProperty(value = "订单ID ")
    private Long id;

    @ApiModelProperty(value = "订单短号 ")
    private Long orderNum;

    @ApiModelProperty(value = "拣货单号 ")
    private String pickCode;

    @ApiModelProperty("品名")
    private String commodityName;

    @ApiModelProperty("是否称重品 1是0否")
    private Integer isWight;

    @ApiModelProperty("下单订购份数")
    private BigDecimal number;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("加工方式")
    private String processName;

    @ApiModelProperty(value = "加工任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;


}
