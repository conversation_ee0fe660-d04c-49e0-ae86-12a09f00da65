package com.pinshang.qingyun.xd.wms.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ShopPackageStatusEnum;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.order.dto.store.StoreODTO;
import com.pinshang.qingyun.order.service.StoreClient2;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.mapper.ShopPackageOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/16 15:34
 */
@Slf4j
@Service
public class ShopPackageOrderService {
    @Autowired
    private ShopPackageOrderMapper shopPackageOrderMapper;
    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private StoreClient2 storeClient2;
    @Autowired
    private SMMUserClient smmUserClient;

    /**
     * 团购提货页面
     *
     * @param idto
     * @return
     */
    public PageInfo<ShopPackageOrderODTO> grouponOrderPage(ShopPackageOrderIDTO idto) {
        idto.checkData();
        PageInfo<ShopPackageOrderODTO> pageInfo;
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        idto.setShopIdList(shopIdList);
        if (StringUtils.isNotBlank(idto.getArrivalBeginTime()) && StringUtils.isNotBlank(idto.getArrivalEndTime())) {
            idto.setArrivalBeginTime(idto.getArrivalBeginTime() + " 00:00:00");
            idto.setArrivalEndTime(idto.getArrivalEndTime() + " 23:59:59");
        }
        if (StringUtils.isNotBlank(idto.getOrderCompleteDateBeginTime()) && StringUtils.isNotBlank(idto.getOrderCompleteDateEndTime())) {
            idto.setOrderCompleteDateBeginTime(idto.getOrderCompleteDateBeginTime() + " 00:00:00");
            idto.setOrderCompleteDateEndTime(idto.getOrderCompleteDateEndTime() + " 23:59:59");
            // 如果搜索条件有完成时间 且状态是空, 则将状态置为已完成 再进行查询
            if (null == idto.getPackageStatus()) {
                idto.setPackageStatus(ShopPackageStatusEnum.FINISH_PICK.getCode());
                // 如果搜索条件有完成时间 且状态是不是已完成它 ， 直接返回空结果
            } else if (!idto.getPackageStatus().equals(ShopPackageStatusEnum.FINISH_PICK.getCode())) {
                PageHelper.startPage(idto.getPageNo(), idto.getPageSize());
                pageInfo = new PageInfo<>(new ArrayList<>());
                return pageInfo;
            }
        }
        pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(
                () -> shopPackageOrderMapper.queryShopPackageOrderPage(idto).stream().peek(
                        e -> {
                            // 只有包裹状态为“顾客已提货”的记录，“完成提货时间”和“操作人”的字段才有值。
                            if (!ShopPackageStatusEnum.FINISH_PICK.getCode().equals(e.getPackageStatus())) {
                                e.setUpdateTime(null);
                                e.setUpdateMan(null);
                            }
                        }
                ).collect(toList()));
        return pageInfo;
    }

    public ShopPackageDetailDTO queryPackageDetail(String orderCode) {
        QYAssert.isTrue(StringUtils.isNotBlank(orderCode), "包裹号不能为空");
        ShopPackageDetailDTO shopPackageOrderODTO = shopPackageOrderMapper.queryShopPackageByOrderCode(orderCode);
        if (shopPackageOrderODTO == null) {
            throw new BizLogicException("没有匹配到包裹，请输入正确的包裹号！", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        List<ShopPackageDetailItemODTO> shopPackageDetailItemODTOS = shopPackageOrderMapper.queryPackageDetailByPackOrderId(shopPackageOrderODTO.getId());
        if (shopPackageDetailItemODTOS.isEmpty()) {
            throw new BizLogicException("没有匹配到包裹详情", ApiErrorCodeEnum.NULLPOINTEREXCEPTION);
        }
        Boolean flag = false;
        for (int i = 0; i < shopPackageDetailItemODTOS.size(); i++) {
            if (shopPackageDetailItemODTOS.get(i).getPackageQuantity().compareTo(shopPackageDetailItemODTOS.get(i).getQuantity()) > -1) {
                shopPackageDetailItemODTOS.get(i).setLessGoods(false);
            } else {
                shopPackageDetailItemODTOS.get(i).setLessGoods(true);
                flag = true;
            }
        }
        shopPackageOrderODTO.setOrderItemList(shopPackageDetailItemODTOS);
        shopPackageOrderODTO.setLessGoods(flag);
        return shopPackageOrderODTO;
    }

    /**
     * @param orderCode
     * @param flag      是否校验时间标记 true 判断时间
     * @return
     */
    public ShopPackageDetailDTO checkPackage(String orderCode, boolean flag) {
        ShopPackageDetailDTO orderODTO = queryPackageDetail(orderCode);
        //门店不一致
        if (!FastThreadLocalUtil.getQY().getShopId().equals(orderODTO.getShopId())) {
            throw new BizLogicException(String.format("这不是当前门店的包裹！\n所属门店：%s", orderODTO.getShopName()), ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        //校验是否已经验收
        if (!ShopPackageStatusEnum.WAIT_VERIFY.getCode().equals(orderODTO.getPackageStatus())) {
            String checkTimeStr = DateFormatUtils.format(orderODTO.getCheckTime(), "yyyy-MM-dd HH:mm:ss");
            throw new BizLogicException(String.format("此包裹已验收！\n操作人：%s \n操作时间:%s", orderODTO.getChecker(), checkTimeStr), ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        //校验时间 判断是不是同一天
        if (flag && !DateUtils.isSameDay(new Date(), orderODTO.getArrivalTime())) {
            String arrivalTimeStr = DateFormatUtils.format(orderODTO.getArrivalTime(), "yyyy-MM-dd");
            throw new BizLogicException(String.format("这是%s的包裹！\n确认要验收吗？", arrivalTimeStr), ApiErrorCodeEnum.PDA_CONFIRM_WARN);
        }
        //验收包裹，修改包裹状态
        Integer result = shopPackageOrderMapper.updateStatusByOrderCode(orderCode, ShopPackageStatusEnum.WAIT_PICK.getCode(),
                FastThreadLocalUtil.getQY().getUserId(), FastThreadLocalUtil.getQY().getRealName());
        if (result < 1) {
            throw new BizLogicException("验收失败", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        return orderODTO;
    }

    public TablePageInfo<WarehouseDeliveryDetailsODTO> queryWarehouseDeliveryPage(WarehouseDeliveryDetailsIDTO idto) {
        if (StringUtils.isNotBlank(idto.getCreateTimeBegin()) && StringUtils.isNotBlank(idto.getCreateTimeEnd())) {
            idto.setCreateTimeBegin(idto.getCreateTimeBegin() + " 00:00:00");
            idto.setCreateTimeEnd(idto.getCreateTimeEnd() + " 23:59:59");
        }
        if (StringUtils.isNotBlank(idto.getOrderTimeBegin()) && StringUtils.isNotBlank(idto.getOrderTimeEnd())) {
            idto.setOrderTimeBegin(idto.getOrderTimeBegin() + " 00:00:00");
            idto.setOrderTimeEnd(idto.getOrderTimeEnd() + " 23:59:59");
        }
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        idto.setShopIdList(shopIdList);
        idto.checkData();
        PageInfo<WarehouseDeliveryDetailsODTO> page = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            shopPackageOrderMapper.queryWarehouseDeliveryPage(idto);
        });
        if (!page.getList().isEmpty()){
            List<Long> commoditys = page.getList().stream().map(WarehouseDeliveryDetailsODTO::getCommodityId).collect(Collectors.toList());
            //客户编码
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commoditys);
            List<Long> storeIds = page.getList().stream().map(WarehouseDeliveryDetailsODTO::getStoreId).collect(Collectors.toList());

            List<StoreODTO> stores = storeClient2.findStoreListByStoreIdList(storeIds);
            page.getList().stream().forEach(e -> {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
                for (int i = 0; i < stores.size(); i++) {
                    if (stores.get(i).getId().equals(e.getStoreId())) {
                        e.setUserCode(stores.get(i).getStoreCode());
                        break;
                    }
                }
            });
        }
        TablePageInfo   info = BeanUtil.pageInfo2TablePageInfo(page, TablePageInfo.class);
        WarehouseDeliveryDetailsODTO header = shopPackageOrderMapper.sumWarehouseDelivery(idto);
        info.setHeader(header);
        return info;
    }

}
