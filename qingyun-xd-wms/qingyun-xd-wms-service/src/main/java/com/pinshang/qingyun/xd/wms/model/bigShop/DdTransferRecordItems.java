package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 移库单主表
 * </p>
 *
 */
@Data
@ToString
@TableName("t_dd_transfer_record_items")
public class DdTransferRecordItems implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Long id;

    /**
     * 移库单表主键id
     */
    private Long transferRecordId;


    /**
     * 移出库区 1拣货区 2存储区 3排面区 4临时库
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.TransferStoreAreaEnum
     */
    private Integer outStoreArea;

    /**
     * 移出货位code
     */
    private String outGoodsAllocationCode;

    /**
     * 移入库区 1拣货区 2存储区 3排面区 4临时库
     */
    private Integer inStoreArea;

    /**
     * 移入货位code
     */
    private String inGoodsAllocationCode;

    /**
     * 移库数量
     */
    private BigDecimal moveQuantity;

    /**
     * create_id
     */
    private Long createId;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_id
     */
    private Long updateId;

    /**
     * update_time
     */
    private Date updateTime;

}
