package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.service.XdAbnormalOrderWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName AbnormalOrderWarnController
 * <AUTHOR>
 * @Date 2022/12/23 14:45
 * @Description AbnormalOrderWarnController
 * @Version 1.0
 */
@RestController
@Api(value = "鲜到异常订单提醒", tags = "AbnormalOrderWarnController" )
@Slf4j
@RequestMapping("xdAbnormalOrderWarning")
public class XdAbnormalOrderWarnController {
    @Autowired
    private XdAbnormalOrderWarnService service;

    @ApiOperation("鲜到异常订单提醒")
    @GetMapping("/abnormalOrderCode")
    public Boolean warnAbnormalOrder(@RequestParam("date") String date){
        return service.warnAbnormalOrder(date);
    }

}
