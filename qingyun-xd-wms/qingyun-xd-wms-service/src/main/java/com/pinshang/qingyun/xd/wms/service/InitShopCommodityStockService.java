package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockNewMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2022/10/11/011 11:07
 */
@Service
public class InitShopCommodityStockService {

    @Autowired
    private ShopCommodityStockNewMapper shopCommodityStockNewMapper;

    /**
     * 期初字段shop_commodity
     */
    public void initShopCommodityStockShopIdAndCommodityId(){
        //查询门店id集合
        List<Long> shopIdList = shopCommodityStockNewMapper.findAllShopIdList();
        if(SpringUtil.isNotEmpty(shopIdList)){
            for (Long shopId : shopIdList) {
                //根据门店id查询门店商品可采状态id集合
                List<Long> commodityPurchaseStatusIdList = shopCommodityStockNewMapper.findShopCommodityStockIdListByShopId(shopId);
                if(SpringUtil.isNotEmpty(commodityPurchaseStatusIdList)){
                    //更新字段:shop_commodity  = 门店id_商品id
                    for (Long id : commodityPurchaseStatusIdList) {
                        shopCommodityStockNewMapper.initShopCommodityById(id);
                    }
                }
            }
        }
    }
}
