package com.pinshang.qingyun.xd.wms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.settlement.OperateTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.BigShopOperateTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.DdDisplayPositionMapper;
import com.pinshang.qingyun.xd.wms.mapper.DdShelvesMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdDisplayPositionCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.DdDisplayPosition;
import com.pinshang.qingyun.xd.wms.model.DdShelves;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdDisplayPositionCommodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdDisplayPositionCommodityVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdDisplayPositionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 排面陈列位管理  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Slf4j
@Service
public class DdDisplayPositionService  extends ServiceImpl<DdDisplayPositionMapper, DdDisplayPosition> {

    @Autowired
    private DdDisplayPositionMapper ddDisplayPositionMapper;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private DdDisplayPositionCommodityMapper ddDisplayPositionCommodityMapper;

    @Autowired
    private DdShelvesMapper ddShelvesMapper;

    @Autowired
    private StallMapper stallMapper;

    @Autowired
    private DdDisplayPositionCommoditySettingService ddDisplayPositionCommoditySettingService;

    /**
    * 排面陈列位管理 列表
    * @param req
    * @return
    */
    public PageInfo<DdDisplayPositionODTO> page(DdDisplayPositionPageIDTO req) {
        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
            ddDisplayPositionMapper.page(req);
        });
    }

    /**
    * 保存 排面陈列位管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(DdDisplayPositionSaveIDTO req) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        Long userId = token.getUserId();
        String userName = token.getRealName();
        String employeeCode = token.getEmployeeNumber();
        Date now = new Date();

        DdShelves ddShelves = ddShelvesMapper.selectById(req.getShelveId());
        QYAssert.isTrue(null != ddShelves, "货架不存在");
        QYAssert.isTrue(ddShelves.getStallId().equals(req.getStallId()), "所选档口和货架所属档口不一致");

        Integer isRepeat = ddDisplayPositionMapper.isRepeat(req.getShopId(), req.getDisplayPositionName(), null);
        QYAssert.isTrue(null == isRepeat || 0 == isRepeat, "陈列位已存在");
        DdDisplayPosition displayPosition = BeanCloneUtils.copyTo(req, DdDisplayPosition.class);
        displayPosition.setCreateId(userId);
        displayPosition.setCreateTime(now);
        displayPosition.setUpdateId(userId);
        displayPosition.setUpdateTime(now);
        ddDisplayPositionMapper.insert(displayPosition);
        List<LogDdDisplayPositionVO> logList = ddDisplayPositionMapper.selectLog(Collections.singletonList(displayPosition.getId()));
        LogDdDisplayPositionVO logVo = logList.get(0);
        logVo.setOperateTime(DateUtil.get4yMdHms(now));
        logVo.setOperateUserId(userId);
        logVo.setOperateUserName(userName);
        logVo.setOperateUserCode(employeeCode);
        logVo.setOperateType(BigShopOperateTypeEnum.INSERT.getCode());
        this.sendDdDisplayPositionLogMessage(logList);
        return Boolean.TRUE;
    }

    /**
    * 更新 排面陈列位管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(DdDisplayPositionUpdateIDTO req) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        Long userId = token.getUserId();
        String userName = token.getRealName();
        String employeeCode = token.getEmployeeNumber();
        Date now = new Date();
        DdDisplayPosition oldRecord = ddDisplayPositionMapper.selectById(req.getId());
        Integer isRepeat = ddDisplayPositionMapper.isRepeat(oldRecord.getShopId(), req.getDisplayPositionName(), req.getId());
        QYAssert.isTrue(null == isRepeat || 0 == isRepeat, "陈列位已存在");
        DdDisplayPosition displayPosition = new DdDisplayPosition();
        displayPosition.setId(req.getId());
        displayPosition.setDisplayPositionName(req.getDisplayPositionName());
        displayPosition.setUpdateId(userId);
        displayPosition.setUpdateTime(now);
        ddDisplayPositionMapper.updateById(displayPosition);

        List<LogDdDisplayPositionVO> logList = ddDisplayPositionMapper.selectLog(Collections.singletonList(req.getId()));
        LogDdDisplayPositionVO logVo = logList.get(0);
        logVo.setOperateTime(DateUtil.get4yMdHms(now));
        logVo.setOperateUserId(userId);
        logVo.setOperateUserName(userName);
        logVo.setOperateType(BigShopOperateTypeEnum.UPDATE.getCode());
        logVo.setOperateUserCode(employeeCode);
        this.sendDdDisplayPositionLogMessage(logList);
        return Boolean.TRUE;
    }

    /**
    * 删除 排面陈列位管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(DdDisplayPositionDeleteIDTO req) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        Long userId = token.getUserId();
        String userName = token.getRealName();
        String employeeCode = token.getEmployeeNumber();
        Date now = new Date();
        String nowStr = DateUtil.get4yMdHms(now);

        List<LogDdDisplayPositionVO> logList = ddDisplayPositionMapper.selectLog(Collections.singletonList(req.getId()));

        // 1. 除陈列位
        ddDisplayPositionMapper.deleteById(req.getId());
        // 2. 解绑该陈列位的商品
        List<DdDisplayPositionCommodity> bindList = ddDisplayPositionCommodityMapper.selectList(new LambdaQueryWrapper<DdDisplayPositionCommodity>().
                eq(DdDisplayPositionCommodity::getDisplayPositionId, req.getId()));
        if(SpringUtil.isNotEmpty(bindList)) {
            List<Long> bindIdList = bindList.stream().map(DdDisplayPositionCommodity::getId).collect(Collectors.toList());
            List<LogDdDisplayPositionCommodityVO> voList = ddDisplayPositionCommodityMapper.selectLogInfoById(bindIdList);
             voList.forEach(vo -> {
                vo.setOperateType(BigShopOperateTypeEnum.BATCH_DELETE.getCode());
                vo.setOperateTime(nowStr);
                vo.setOperateUserName(userName);
                vo.setOperateUserId(userId);
                vo.setOperateUserCode(employeeCode);
                vo.setDisplayPositionName(logList.get(0).getDisplayPositionName());
            });
            ddDisplayPositionCommoditySettingService.sendDdDisplayPositionCommodityLogMessage(voList);
            ddDisplayPositionCommodityMapper.delete(
                    new LambdaQueryWrapper<DdDisplayPositionCommodity>().eq(DdDisplayPositionCommodity::getDisplayPositionId, req.getId()));
        }
        LogDdDisplayPositionVO logVo = logList.get(0);
        logVo.setOperateTime(nowStr);
        logVo.setOperateUserId(userId);
        logVo.setOperateUserName(userName);
        logVo.setOperateUserCode(employeeCode);
        logVo.setOperateType(BigShopOperateTypeEnum.DELETE.getCode());
        this.sendDdDisplayPositionLogMessage(logList);


        return Boolean.TRUE;
    }

    /**
    * 发送 排面陈列位管理 日志
    * @param operateType
    */
    private void sendDdDisplayPositionLogMessage(List<LogDdDisplayPositionVO> vo){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName", "t_log_dd_display_position");
        jsonObject.put("data", vo);
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.LOG_CREATE_TOPIC, jsonObject.toJSONString(),
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.LOG_CREATE.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }

    public List<DdDisplayPositionODTO> list(String displayPosition, Long stallId){
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        return ddDisplayPositionMapper.list(shopId, displayPosition, stallId);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importExcel(List<DdDisplayPositionSaveIDTO> positionList, Long stallId, Long shopId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String userName = tokenInfo.getRealName();
        String employeeCode = tokenInfo.getEmployeeNumber();
        Date now = new Date();
        List<String> shelveCodeList = positionList.stream().map(DdDisplayPositionSaveIDTO::getShelveCode).collect(Collectors.toList());
        List<DdShelves> shelveInfoList = ddShelvesMapper.selectList(new LambdaQueryWrapper<DdShelves>().in(DdShelves::getShelveCode, shelveCodeList).eq(DdShelves::getStallId, stallId));
        QYAssert.isTrue(SpringUtil.isNotEmpty(shelveInfoList), "货架编码不存在");
        List<Long> stallIdList = shelveInfoList.stream().map(DdShelves::getStallId).collect(Collectors.toList());
        List<Stall> stallInfoList = stallMapper.selectBatchIds(stallIdList);
        Map<Long, Stall> stallInfoMap = stallInfoList.stream().collect(Collectors.toMap(Stall::getId, Function.identity()));
        Map<String, DdShelves> shelveInfoMap = shelveInfoList.stream().collect(Collectors.toMap(DdShelves::getShelveCode, Function.identity()));
//        List<Long> shelveIdList = shelveInfoList.stream().map(DdShelves::getId).collect(Collectors.toList());
        List<DdDisplayPosition> oldRecord = ddDisplayPositionMapper.selectList(new LambdaQueryWrapper<DdDisplayPosition>()
                .eq(DdDisplayPosition::getShopId, shopId));
        List<String> oldDispositionName = oldRecord.stream().map(DdDisplayPosition::getDisplayPositionName).collect(Collectors.toList());
//        Map<Long, List<String>> recordMap = oldRecord.stream().collect(Collectors.groupingBy(DdDisplayPosition::getShelveId,
//                Collectors.mapping(DdDisplayPosition::getDisplayPositionName, Collectors.toList())));

        Map<String, Integer> displayPositionMap = new HashMap<>();
        List<DdDisplayPosition> insertList = new ArrayList<>(positionList.size());
        List<String> errorInfo = new ArrayList<>();
        List<LogDdDisplayPositionVO> logDdDisplayPositionVOList = new ArrayList<>(positionList.size());
        boolean flag = true;
        for (int i = 0; i < positionList.size(); i++) {
            DdDisplayPositionSaveIDTO item = positionList.get(i);

            if (displayPositionMap.containsKey(item.getDisplayPositionName())) {
                if ( 1 == displayPositionMap.get(item.getDisplayPositionName()) ) {
                    errorInfo.add(item.getDisplayPositionName() + "陈列位号重复");
                }
                displayPositionMap.put(item.getDisplayPositionName(), 2);
                flag = false;
                continue;
            }

            if (!shelveInfoMap.containsKey(item.getShelveCode())) {
                errorInfo.add(item.getShelveCode() + "货架编码不存在");
                flag = false;
                continue;
            }
            DdShelves shelve = shelveInfoMap.get(item.getShelveCode());
//            List<String> oldDispositionName = recordMap.get(shelve.getId());
            if (SpringUtil.isNotEmpty(oldDispositionName) && oldDispositionName.contains(item.getDisplayPositionName())) {
                errorInfo.add(item.getDisplayPositionName() + "陈列位已存在");
                flag = false;
                continue;
            }

            displayPositionMap.put(item.getDisplayPositionName(), 1);
            DdDisplayPosition insertItem = new DdDisplayPosition();
            insertItem.setDisplayPositionName(item.getDisplayPositionName());
            insertItem.setShelveId(shelveInfoMap.get(item.getShelveCode()).getId());
            insertItem.setShopId(shopId);
            insertItem.setStallId(stallId);
            insertItem.setDisplayPositionName(item.getDisplayPositionName());
            insertItem.setUpdateId(userId);
            insertItem.setCreateTime(now);
            insertItem.setCreateId(userId);
            insertItem.setUpdateTime(now);
            insertList.add(insertItem);

            Stall stall = stallInfoMap.get(shelve.getStallId());
            LogDdDisplayPositionVO logVo = new LogDdDisplayPositionVO();
            logVo.setShopId(shopId);
            logVo.setStallId(stallId);
            logVo.setShelveId(shelve.getId());
            logVo.setStallCode(stall.getStallCode());
            logVo.setStallName(stall.getStallName());
            logVo.setShelveCode(shelve.getShelveCode());
            logVo.setShelveName(shelve.getShelveName());
            logVo.setDisplayPositionName(item.getDisplayPositionName());
            logVo.setOperateType(BigShopOperateTypeEnum.BATCH_INSERT.getCode());
            logVo.setOperateUserId(userId);
            logVo.setOperateUserName(userName);
            logVo.setOperateUserCode(employeeCode);
            logVo.setOperateTime(DateUtil.get4yMdHms(now));
            logDdDisplayPositionVOList.add(logVo);
        }
        if(flag){
            saveBatch(insertList);
            this.sendDdDisplayPositionLogMessage(logDdDisplayPositionVOList);
        }
        return new ExcelResult(errorInfo, null);
    }

    public List<DdDisplayPositionODTO> listDdDisplayPosition(DdDisplayPositionListIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        String displayPositionName = req.getDisplayPositionName();
        List<DdDisplayPosition> ddDisplayPositions = ddDisplayPositionMapper.selectList(
                new LambdaQueryWrapper<DdDisplayPosition>()
                        .eq(DdDisplayPosition::getShopId, tokenInfo.getShopId())
                        .like(SpringUtil.hasText(displayPositionName), DdDisplayPosition::getDisplayPositionName, displayPositionName)
        );

        if (SpringUtil.isEmpty(ddDisplayPositions)) {
            return Collections.emptyList();
        }

        return BeanCloneUtils.copyTo(ddDisplayPositions, DdDisplayPositionODTO.class);
    }

    public DdDisplayPosition getByIdAndShopId(Long displayPositionId, Long shopId) {
        return ddDisplayPositionMapper.selectOne(
                new LambdaQueryWrapper<DdDisplayPosition>()
                        .eq(DdDisplayPosition::getId, displayPositionId)
                        .eq(DdDisplayPosition::getShopId, shopId)
                        .orderByDesc(DdDisplayPosition::getId)
                        .last(" limit 1")
        );
    }
}
