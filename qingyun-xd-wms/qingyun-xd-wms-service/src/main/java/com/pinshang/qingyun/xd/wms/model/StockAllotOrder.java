package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="StockAllotOrder对象", description="调入调出申请表")
@TableName("t_stock_allot_order")
public class StockAllotOrder extends BaseEntity {

    private Integer allotType;

    private String orderCode;

    private Long inShopId;

    private Long inStallId;

    private Integer inShopType;

    private Long outShopId;

    private Long outStallId;

    private Integer outShopType;

    private Integer status;

    private Long auditId;

    private Date auditTime;

    private Long outPerson;

    private Date outTime;

    private Long inPerson;

    private Date inTime;

    /** 是否店内调拨 0否 1是 */
    private Integer isInstoreAllot = 0;

}
