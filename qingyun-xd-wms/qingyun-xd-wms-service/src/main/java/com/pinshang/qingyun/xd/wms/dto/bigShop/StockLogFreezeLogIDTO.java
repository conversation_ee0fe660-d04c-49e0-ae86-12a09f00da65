package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@ApiModel("StockLogFreezeLogIDTO")
public class StockLogFreezeLogIDTO extends Pagination implements Serializable {

    private static final long serialVersionUID = 8327839930681978227L;

    @ApiModelProperty(name = "门店id", required = true)
    private Long shopId;

    @ApiModelProperty(name = "档口id", required = true)
    private Long stallId;

    @ApiModelProperty(name = "商品id", required = true)
    private String commodityId;

    @ApiModelProperty("单据编号")
    private String referCode;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
