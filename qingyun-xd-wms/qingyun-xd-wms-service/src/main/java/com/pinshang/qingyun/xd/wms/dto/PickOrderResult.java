package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrderResult {

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单编号")
    private String pickCode;

    @ApiModelProperty(value = "拣货单状态(0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货人姓名")
    private String employeeName;

    private Date pickBeginTime;
    private Date pickEndTime;
    private Date updateTime;

    @ApiModelProperty(value = "打包口ID")
    private Long packingStationId;

    @ApiModelProperty(value = "打包口名称")
    private String packingStationName;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    private Integer packingStatus;
}
