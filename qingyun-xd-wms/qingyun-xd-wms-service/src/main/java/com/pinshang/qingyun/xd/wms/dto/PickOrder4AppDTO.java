package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrder4AppDTO extends Pagination<PickOrderListResult> {

    @ApiModelProperty(value = "仓库id[前端不用传]")
    private Long warehouseId;

    @ApiModelProperty(value = "拣货单状态(1＝待拣货，2＝拣货完成")

    private Integer pickStatus;

    @ApiModelProperty(value = "拣货单状态可以查询多个 0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private List<Integer> pickStatusList;

}
