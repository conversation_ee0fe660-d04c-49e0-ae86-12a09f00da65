package com.pinshang.qingyun.xd.wms.dto.bigShop;

import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/11/11
 */
@Data
public class DdMdInventoryQueryIDTO {

    // 门店id
    private Long shopId;

    // 档口id
    private Long stallId;

    // 库区 1排面区 2拣货区 3存储区
    private Integer storageArea;

    // 货位号id
    private Long goodsAllocationId;

    private List<Long> commodityIdList;
    // 一级分类ids
    private List<Long> cateId1s;
    // 二级分类ids
    private List<Long> cateId2s;
    // 三级分类ids
    private List<Long> cateId3s;
}
