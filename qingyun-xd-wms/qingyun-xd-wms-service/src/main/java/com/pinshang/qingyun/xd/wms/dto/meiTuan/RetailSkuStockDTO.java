package com.pinshang.qingyun.xd.wms.dto.meiTuan;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName RetailSkuStockDTO
 * <AUTHOR>
 * @Date 2023/7/21 10:47
 * @Description RetailSkuStockDTO
 * @Version 1.0
 */
public class RetailSkuStockDTO {
    @ApiModelProperty("取我们系统的commodity_code")
    private String app_spu_code;

    private List<RetailSkuStockSkuDTO> skus;

    public RetailSkuStockDTO() {
    }

    public RetailSkuStockDTO(String app_spu_code, List<RetailSkuStockSkuDTO> skus) {
        this.app_spu_code = app_spu_code;
        this.skus = skus;
    }

    public String getApp_spu_code() {
        return app_spu_code;
    }

    public void setApp_spu_code(String app_spu_code) {
        this.app_spu_code = app_spu_code;
    }

    public List<RetailSkuStockSkuDTO> getSkus() {
        return skus;
    }

    public void setSkus(List<RetailSkuStockSkuDTO> skus) {
        this.skus = skus;
    }

    @Override
    public String toString() {
        return "RetailSkuStockDTO{" +
                "app_spu_code='" + app_spu_code + '\'' +
                ", skus=" + skus +
                '}';
    }
}
