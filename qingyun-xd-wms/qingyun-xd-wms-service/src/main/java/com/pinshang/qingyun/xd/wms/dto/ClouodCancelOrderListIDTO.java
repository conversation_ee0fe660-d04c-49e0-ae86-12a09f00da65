package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClouodCancelOrderListIDTO extends Pagination {
    @ApiModelProperty(value="业务模式", notes = " 2=云超普通订单 6 云超团购订单")
    private Integer orderType;

    @ApiModelProperty(value="下单时间-开始", notes = "yyyy-MM-dd")
    private String orderBeginTime;

    @ApiModelProperty(value="下单时间-结束", notes = "yyyy-MM-dd")
    private String orderEndTime;

    @ApiModelProperty(value = "订单", notes = "0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定")
    private Integer orderStatus;

    @ApiModelProperty(value = "收货人手机")
    private String receiveMobile;

    @ApiModelProperty(value = "预约到货日期", notes = "yyyy-MM-dd")
    private String arriveBeginTime;

    @ApiModelProperty(value = "预约到货日期", notes = "yyyy-MM-dd")
    private String arriveEndTime;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty(value = "下单人手机")
    private String orderMobile;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "xs用户名-下单人手机号")
    private Long xsUserId;
}
