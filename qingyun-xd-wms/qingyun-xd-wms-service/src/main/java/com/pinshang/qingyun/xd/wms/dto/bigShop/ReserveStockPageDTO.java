package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveStockPageDTO {

    @ExcelIgnore
    private Long stallCommodityStockId;

    @ExcelProperty("所属档口")
    @ApiModelProperty("所属档口")
    private String stallName;

    @ExcelProperty("商品编码")
    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    @ApiModelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    @ApiModelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("计量单位")
    @ApiModelProperty("计量单位")
    private String commodityUnitName;

    @ExcelProperty("线下预留库存数量")
    @ApiModelProperty("线下预留库存数量")
    private BigDecimal reserveStock;

    @ExcelIgnore
    private Integer commoditySaleStatus;

    @ExcelProperty("POS可售")
    @ApiModelProperty("POS可售")
    private String commoditySaleStatusName;

    @ExcelIgnore
    private Integer appStatus;

    @ExcelProperty("上架状态")
    @ApiModelProperty("上架状态")
    private String appStatusName;

    @ExcelIgnore
    private Long updateId;

    @ExcelProperty("更新人工号")
    @ApiModelProperty("更新人工号")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.employeeNumber,keyName = "updateId")
    private String updateCode;

    @ExcelProperty("更新人")
    @ApiModelProperty("更新人")
    private String updateName;

    @ExcelProperty("更新时间")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public String getCommoditySaleStatusName() {
        String res = null;
        if (YesOrNoEnums.YES.getCode().equals(commoditySaleStatus)) {
            res = "可售";
        } else if (YesOrNoEnums.NO.getCode().equals(commoditySaleStatus)) {
            res = "不可售";
        }
        return res;
    }

    public String getAppStatusName() {
        String res = null;
        if (YesOrNoEnums.YES.getCode().equals(appStatus)) {
            res = "未上架";
        } else if (YesOrNoEnums.NO.getCode().equals(appStatus)) {
            res = "已上架";
        }
        return res;
    }
}
