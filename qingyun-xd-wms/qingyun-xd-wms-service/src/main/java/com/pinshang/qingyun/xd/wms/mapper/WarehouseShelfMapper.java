package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelf;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseShelfMapper extends BaseMapper<WarehouseShelf> {

    int batchInsertWarehouseShelf(@Param("e") List<WarehouseShelf> list);

    WarehouseShelf selectOne(@Param("shelfNo") String shelfNo, @Param("warehouseId") Long warehouseId, @Param("type") Integer type);

    MPage<QueryWarehouseShelfResult> queryWarehouseShelf(@Param("e") QueryWarehouseShelfDTO queryWarehouseShelfDTO);

    /**
     * 查询所有可以用的拣货位
     * @param shelfNo
     * @param warehouseId
     * @return
     */
    List<WarehouseShelfListDTO> queryPickShelf(@Param("shelfNo") String shelfNo, @Param("warehouseId") Long warehouseId);

    MPage<ShelfFreeResult> shelfByCondition(@Param("dto") ShelfFreeDTO dto);

    List<ShopShelfResDTO> queryShopShelf(ShopShelfReqDTO dto);

    /**
     * 根据类型查询货位列表
     * @param dto
     * @return
     */
    MPage<WarehouseShelfListDTO> shelfListByType(@Param("dto") ShelfFreeDTO dto);
}
