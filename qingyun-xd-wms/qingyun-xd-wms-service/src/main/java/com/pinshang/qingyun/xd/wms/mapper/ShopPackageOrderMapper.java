package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.model.ShopPackageOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/15 17:15
 */
@Repository
public interface ShopPackageOrderMapper extends BaseMapper<ShopPackageOrder> {
    /**
     * 云超包裹查询
     *
     * @param idto
     * @return
     */
    List<ShopPackageOrderODTO> queryShopPackageOrderPage(ShopPackageOrderIDTO idto);

    /**
     * 查询包裹详情
     *
     * @param packOrderId
     * @return
     */
    List<ShopPackageDetailItemODTO> queryPackageDetailByPackOrderId(Long packOrderId);

    /**
     * 修改包裹状态
     */
    Integer updateStatusByOrderCode(String orderCode, Integer code, Long updateId, String checker);

    List<WarehouseDeliveryDetailsODTO> queryWarehouseDeliveryPage(WarehouseDeliveryDetailsIDTO warehouseDeliveryDetailsIDTO);

    WarehouseDeliveryDetailsODTO sumWarehouseDelivery(WarehouseDeliveryDetailsIDTO warehouseDeliveryDetailsIDTO);

    ShopPackageDetailDTO queryShopPackageByOrderCode(String orderCode);


}
