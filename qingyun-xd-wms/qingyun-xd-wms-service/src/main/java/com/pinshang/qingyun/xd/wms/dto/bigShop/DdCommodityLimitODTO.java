package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.EffectTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.LimitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
public class DdCommodityLimitODTO {

    @ExcelIgnore
    @ApiModelProperty("id")
    private String idStr;

    @ExcelIgnore
    private Long stallId;

    @ExcelProperty("档口编码")
    @ApiModelProperty("档口编码")
    private String stallCode;

    @ExcelProperty("档口名称")
    @ApiModelProperty("档口名称")
    private String stallName;

    @ExcelIgnore
    private Long commodityId;

    @ExcelProperty("商品编码")
    @ApiModelProperty("商品编码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityCode, keyName = "commodityId")
    private String commodityCode;

    @ExcelProperty("商品条码")
    @ApiModelProperty("条码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barcode;

    @ExcelProperty("商品名称")
    @ApiModelProperty("商品名称")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;

    @ExcelProperty("计量单位")
    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityUnit, keyName = "commodityId")
    private String commodityUnitName;

    @ExcelProperty("大类")
    @ApiModelProperty("大类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND, fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName, keyName = "commodityId")
    private String commodityFirstKindName;

    @ExcelProperty("中类")
    @ApiModelProperty("中类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND, fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName, keyName = "commodityId")
    private String commoditySecondKindName;

    @ExcelProperty("小类")
    @ApiModelProperty("小类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND, fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName, keyName = "commodityId")
    private String commodityThirdKindName;

    @ExcelIgnore
    @ApiModelProperty("上架状态 0 上架   1 下架")
    private Integer appStatus;
    @ExcelProperty("上架状态")
    private String appStatusName;

    @ExcelProperty("限量销售份数")
    @ApiModelProperty("限量销售份数")
    private Integer limitNumber;

    @ExcelIgnore
    @ApiModelProperty("限量计算方式 1=从0计算  2=累加计算")
    private Integer limitType;
    @ExcelProperty("限量计算方式")
    private String limitTypeName;

    @ExcelIgnore
    @ApiModelProperty("生效方式: 1 不循环生效 2 每天循环生效")
    private Integer effectType;
    @ExcelProperty("生效方式")
    private String effectTypeName;

    @ExcelIgnore
    @ApiModelProperty("生效时间")
    private Date effectBeginTime;
    @ExcelProperty("生效时间")
    private String effectBeginTimeStr;

    @ApiModelProperty("循环开始时间")
    @ExcelProperty("循环开始时间")
    private String loopTime;


    @ApiModelProperty("已下单份数")
    @ExcelProperty("已下单份数")
    private Integer purchaseNumber;

    @ExcelIgnore
    @ApiModelProperty("更新人")
    private Integer updateId;
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.employeeNumber, keyName = "updateId")
    private String updateUserCode;
    @ExcelIgnore
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.realName, keyName = "updateId")
    private String updateUserName;

    @ExcelProperty("更新人")
    private String updateUser;

    public String getUpdateUser(){
        return getUpdateUserCode() + "-" + getUpdateUserName();
    }

    public String getAppStatusName(){
        if(YesOrNoEnums.NO.getCode().equals(appStatus)){
            return "已上架";
        }else {
            return "未上架";
        }
    }

    public String getLimitTypeName(){
        return LimitTypeEnum.getNameByCode(limitType);
    }

    public String getEffectTypeName(){
        return EffectTypeEnum.getNameByCode(effectType);
    }

    public String getEffectBeginTimeStr(){
        return DateUtil.getDateFormate(effectBeginTime, "yyyy-MM-dd HH:mm:ss");
    }
}
