package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.pinshang.qingyun.base.page.Pagination;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudPickListIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("预约开始时间")
    private String receiveTimeBegin;

    @ApiModelProperty("预约结束时间")
    private String receiveTimeEnd;

    @ApiModelProperty("3=出库中 0=已取消 100=(0,3) 200=(0,4)")
    private Integer orderStatus;

    private Long orderId;

    private List<Long> cancelOrderIds;

    private List<Long> occupyList;

    @ApiModelProperty("搜索码：包裹码、订单编号")
    private String searchCode;

    @ApiModelProperty("手机号")
    private String tel;
}
