package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 补货任务状态
 *
 * <AUTHOR>
 */
public enum ReplenishmentTaskStatusEnum {
    ACTIVE(1, "生效中"),
    EXPIRED(2, "已过期"),
    ;
    private Integer code;
    private String name;

    ReplenishmentTaskStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ReplenishmentTaskStatusEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReplenishmentTaskStatusEnum typeEnum : ReplenishmentTaskStatusEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
