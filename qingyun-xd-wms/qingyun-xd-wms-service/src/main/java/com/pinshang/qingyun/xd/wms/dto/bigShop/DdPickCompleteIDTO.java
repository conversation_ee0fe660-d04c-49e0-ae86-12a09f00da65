package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("DdPickCompleteIDTO")
public class DdPickCompleteIDTO implements Serializable {

    private static final long serialVersionUID = -2846931079235662163L;

    @ApiModelProperty(value = "大店-分区拣货单id 集合")
    private List<Long> pickPartitionOrderIdList;

}
