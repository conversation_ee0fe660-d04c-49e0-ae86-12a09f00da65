package com.pinshang.qingyun.xd.wms.dto.bigShop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LogDdGoodsAllocationCommodityDTO {

    private Integer type;

    private Long shopId;

    private Long stallId;

    /**
     *档口编码
     */
    private String stallName;

    /**
     * 区域编码
     */
    private String areaCode;

    private Long goodsAllocationId;

    /**
     * 货位号
     */
    private String goodsAllocationCode;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 规格
     */
    private String commoditySpec;

    /**
     * 单位
     */
    private String commodityUnitName;

    /**
     * 最小库存预警
     */
    private BigDecimal minStock;

    /**
     * 最大库存预警
     */
    private BigDecimal maxStock;

    private Long createId;

    private String createCode;

    /**
     * 操作人
     */
    private String createName;

    /**
     * 操作时间
     */
    private Date createTime;

}
