package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname StockSnapshotCommodityWeekly
 * @Description TODO
 * @Date 2022/5/11 15:24
 * <AUTHOR> yaot0
 */
@Data
@TableName("t_xd_stock_snapshot_commodity_weekly")
public class StockSnapshotCommodityWeekly {

    @TableId
    private Long id;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("库存份数")
    private Integer number;

    @ApiModelProperty("库存数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "当期快照的开始日期", notes = "默认0点, 格式: yyy-MM-dd")
    private Date shotDate;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private Long createId;

}
