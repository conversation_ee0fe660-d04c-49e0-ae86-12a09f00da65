package com.pinshang.qingyun.xd.wms.enums;

/**
 * @ClassName GrouponOrderStatusEnum
 * <AUTHOR>
 * @Date 2020/12/23 10:00
 * @Description GrouponOrderStatusEnum 已完成对应order的配送完成, 订单取消对应订单中的已取消, 未完成对应订单中除前两者之外的其他状态
 * @Version 1.0
 */
public enum GrouponOrderStatusEnum {
    /**
     * 已完成
     */
    complete(1, 6, "已完成"),

    /**
     * 订单取消
     */
    cancel(0, 0, "订单取消"),
    /**
     * 订单未完成, -1非order状态值, 仅表示除0和6之外的状态
     */
    incomplete(3, -1, "未完成");

    private int code;
    private int orderStatus;
    private String desc;

    GrouponOrderStatusEnum(int code, int orderStatus, String desc) {
        this.code = code;
        this.orderStatus = orderStatus;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public int getOrderStatus() {
        return orderStatus;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据order表的status获取提货状态枚举
     * @param orderStatus
     * @return
     */
    public static GrouponOrderStatusEnum getEnumByOrderStatus(int orderStatus){
        for(GrouponOrderStatusEnum statusEnum: GrouponOrderStatusEnum.values()){
            if (orderStatus == statusEnum.orderStatus) {
                return statusEnum;
            }
        }
        return GrouponOrderStatusEnum.incomplete;
    }
}
