package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_xd_stock_out_relation_log")
@ApiModel(value = "StockOutRelationLog对象", description = "缺货联系客户记录")
public class StockOutRelationLog {

    @TableId
    private Long id;

    private Long orderId;

    private String orderCode;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private Long create_id;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

}
