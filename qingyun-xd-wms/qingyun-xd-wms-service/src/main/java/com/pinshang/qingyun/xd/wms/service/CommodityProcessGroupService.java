package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.xd.wms.dto.CommodityProcessName;
import com.pinshang.qingyun.xd.wms.mapper.CommodityProcessGroupMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CommodityProcessGroupService {

    @Autowired
    private CommodityProcessGroupMapper commodityProcessGroupMapper;

    /**
     * 根据商品id批量查询加工方式
     * @param list
     * @return
     */
    public List<CommodityProcessName> commodityProcessNameList(List<Long> list) {
        return commodityProcessGroupMapper.commodityProcessNameList(list);
    }
}
