package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.xd.wms.enums.UserTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Optional;

@Data
@ToString
@ApiModel("PrinterBindQueryRspVO")
public class PrinterBindQueryRspVO {

    @ApiModelProperty(value = "打印机绑定关系Id")
    private Long printerBindId;

    /**
     * 打印机编码
     */
    @ApiModelProperty(value = "打印机编码")
    private String printerCode;

    /**
     * 打印机安装位置
     */
    @ApiModelProperty(value = "打印机安装位置")
    private String printerMountingPosition;

    /**
     * 使用方类型
     */
    @ApiModelProperty(value = "使用方类型")
    private Integer userType;

    /**
     * 使用方类型名称
     */
    @ApiModelProperty(value = "使用方类型名称")
    private String userTypeName;

    private Long realUserId;

    /**
     * 使用方
     */
    @ApiModelProperty(value = "使用方名称")
    private String realUserName;

    public String getUserTypeName() {
        return Optional.ofNullable(UserTypeEnum.getTypeEnumByCode(userType)).map(UserTypeEnum::getName).orElse("");
    }
}
