package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "PickOrder对象", description = "仓库拣货单表")
@TableName("t_xd_pick_order")
public class PickOrder extends BaseEntity {

    @ApiModelProperty(value = "拣货单code")
    private String pickCode;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "订单来源")
    private Integer sourceType;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "拣货单状态(0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货人")
    private Long pickId;

    @ApiModelProperty(value = "客户要求配送完成时间")
    private String orderDeliveryEndTime;

    @ApiModelProperty(value = "客户要求配送开始时间")
    private String orderDeliveryBeginTime;

    @ApiModelProperty(value = "拣货开始时间(分配拣货人时间)")
    private Date pickBeginTime;

    @ApiModelProperty(value = "拣货完成时间")
    private Date pickEndTime;

    @ApiModelProperty(value = "配送取货位")
    private String shelfNo;

    @ApiModelProperty(value = "是否有加工商品 0＝否，1＝是")
    private Integer hasProcess;

    /**
     * 原始订单(第三方订单)
     */
    private String originalOrderCode;

    @ApiModelProperty(value = "是否缺货 1缺货 0不缺货")
    private Integer stockOutStatus;

    @ApiModelProperty(value = "0=普通订单 1=团购订单")
    private Integer orderType;

    /***
     * 用于PDA拣货列表使用
     * 目前该字段已经没有任何用处
     */
    @ApiModelProperty(value = "店内序号")
    private String storeSerialNum;

    /**
     * 订单短号
     */
    private String orderNum;

    /**
     * 是否打包，0未打包，1已打包
     */
    private Integer packingStatus;

    /**
     * 打包人id -1 系统
     */
    private Long packingId;

    /**
     * 打包结束时间
     */
    private Date packingEndTime;

    /**
     * 打包口id
     */
    private Long packingStationId;

}
