package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 收货IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockReceiptIDTO {
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "商品list")
    private List<StockReceiptItemDTO> commodityList;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "是否取消退单入库")
    private Boolean ifReturnReceipt;

    @ApiModelProperty(value = "出入库枚举")
    private StockInOutTypeEnums stockEnums;

//    @ApiModelProperty(value = "档口信息")
//    private Long stallId;

    public void checkData() {
        QYAssert.isTrue(warehouseId != null, "仓库id不能为空");
        QYAssert.isTrue(referId != null, "关联单号id不能为空");
        QYAssert.isTrue(referCode != null, "关联单号code不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");
    }

    public List<StockItemDTO> toNormalCommodity() {
        List<StockItemDTO> list = new ArrayList<>();
        for (StockReceiptItemDTO itemDTO : commodityList) {
            if(itemDTO.getNormalNumber() != null && itemDTO.getNormalNumber() > 0){
                list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getNormalNumber(), itemDTO.getNormalQuantity(),itemDTO.getDdStockInOutExtraVO()));
            }
        }
        return list;
    }

    public List<StockItemDTO> toAbnormalCommodity() {
        List<StockItemDTO> list = new ArrayList<>();
        for (StockReceiptItemDTO itemDTO : commodityList) {
            if(itemDTO.getAbnormalNumber() != null && itemDTO.getAbnormalNumber() > 0){
                list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getAbnormalNumber(), itemDTO.getAbnormalQuantity(),itemDTO.getDdStockInOutExtraVO()));
            }
        }
        return list;
    }
}