package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoodsAllocationPageIDTO extends Pagination<GoodsAllocationPageIDTO> {

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("区域")
    private Long areaId;

    @ApiModelProperty("档口")
    private Long stallId;

    @ApiModelProperty("货位号")
    private String goodsAllocationCode;

    /**
     * StorageAreaEnum
     */
    @ApiModelProperty("库区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty("1仅查询未分配档口的货位")
    private Integer undistributedStall;
}
