package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocationDTO {

    private Long shopId;

    /**
     *货位号
     */
    private String goodsAllocationCode;

    /**
     * 所属区域
     */
    private Long areaId;

    /**
     * 库区 1排面区 2拣货区 3存储区
     */
    private Integer storageArea;

    public void check() {
        QYAssert.isTrue(null != this.shopId, "门店不能为空");
        QYAssert.isTrue(!StringUtil.isNullOrEmpty(this.goodsAllocationCode), "货位号不能");
        QYAssert.isTrue(null != this.areaId, "所属区域不能");
        QYAssert.isTrue(null != this.storageArea, "库区不能为空");
    }
}
