package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.xd.cms.dto.XdBackSettingODTO;
import com.pinshang.qingyun.cms.service.XdBackSettingClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class XdBackSettingService {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private XdBackSettingClient xdBackSettingClient;

    public XdBackSettingODTO findXdBackSettingDetails(){
        RBucket<XdBackSettingODTO> bucket = redissonClient.getBucket(RedisKeyPrefixConst.XD_BACK_SETTING);
        if (bucket.isExists()){
            return bucket.get();
        }
       return xdBackSettingClient.findXdBackSettingDetails();
    }



}
