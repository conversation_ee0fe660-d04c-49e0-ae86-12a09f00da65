package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderMakeUpReqVo {

    @ApiModelProperty("订单编号，多个逗号分隔")
    private List<String> orderCodeList;
    @ApiModelProperty("送货日期范围：开始日期")
    private String startTime;
    @ApiModelProperty("送货日期范围：结束日期")
    private String endTime;
    @ApiModelProperty("比较的类型：1-包裹单，2-包裹单明细")
    private Integer packageFlag;

}
