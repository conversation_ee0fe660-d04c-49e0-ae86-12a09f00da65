package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
@ToString
@TableName("t_dd_pick_area_employee")
public class DdPickAreaEmployee implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 门店id */
    private Long shopId;

    /** 拣货分区id */
    private Long pickAreaId;

    /** 职员Id */
    private Long employeeId;

    /** 是否接单  0=否  1=是 */
    private Integer workStatus;

    /**
     * 开启分区拣货 0=否 1=是
     */
    private Integer partitionPickStatus;

    /**
     * 创建人ID
     */
    private Long createId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人ID
     */
    private Long updateId;
    /**
     * 修改时间
     */
    private Date updateTime;
}
