package com.pinshang.qingyun.xd.wms.mapper.bigShop;

import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoODTO;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Repository
public interface StallCommodityMapper extends BaseMapper<StallCommodity> {

    List<CommodityBaseInfoODTO> fuzzySearchByCommodityCode(Map<String, Object> sqlParams);

    List<StallCommodity> selectStallCommodityByCommodityIdList(@Param("shopId") Long shopId,
                                                                @Param("stallIdList") List<Long> stallIds,
                                                                @Param("commodityIdList") List<Long> commodityIdList);

    /**
     * 模糊搜索档口商品
     *
     * @param stallId
     * @param commodityQueryParam
     * @return
     */
    List<Long> listStallCommodityByNameOrBarCode(@Param("stallId") Long stallId, @Param("commodityQueryParam") String commodityQueryParam);
}