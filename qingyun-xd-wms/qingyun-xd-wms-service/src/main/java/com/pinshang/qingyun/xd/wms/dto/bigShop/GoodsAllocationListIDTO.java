package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询货位列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocationListIDTO {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    /**
     * storageArea
     * 1.库存不传，查询拣货区和存储区下面的货位
     * 2.传排面区，返回null
     * 3.传拣货区，查询该商品绑定的唯一拣货位
     * 4.传存储区，查询该商品虚拟绑定的存储货位
     */
    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private List<Integer> storageAreaList;

}