package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdPackingStationOrderODTO;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdPackingOrderMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPackingOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 订单打包口占用订单表  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Slf4j
@Service
public class DdPackingOrderService extends ServiceImpl<DdPackingOrderMapper, DdPackingOrder> {

    @Autowired
    private DdPackingOrderMapper ddPackingOrderMapper;

    /**
     * 释放打包口
     *
     * @param shopId
     * @param packingStationId
     * @param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer releasePackingStation(Long shopId, Long packingStationId, Long orderId) {
        return ddPackingOrderMapper.delete(
                new LambdaUpdateWrapper<DdPackingOrder>()
                        .eq(DdPackingOrder::getShopId, shopId)
                        .eq(DdPackingOrder::getPackingStationId, packingStationId)
                        .eq(DdPackingOrder::getOrderId, orderId)
        );
    }

    /**
     * 根据打包口ID获取占用订单列表
     *
     * @param shopId           门店ID
     * @param packingStationId 打包口ID
     */
    public List<DdPackingStationOrderODTO> listPackingOrderByPackingId(Long shopId, Long packingStationId) {
        return ddPackingOrderMapper.listPackingOrderByPackingId(shopId, packingStationId);
    }

    /**
     * 根据订单ID获取占用打包口
     * @param shopId
     * @param orderId
     * @return
     */
    public DdPackingOrder getByOrderId(Long shopId, Long orderId){
        return ddPackingOrderMapper.selectOne(
               new LambdaQueryWrapper<DdPackingOrder>()
                       .eq(DdPackingOrder::getShopId, shopId)
                       .eq(DdPackingOrder::getOrderId, orderId)
                       .last(" limit 1")
        );
    }

    public Integer packingPortOrderCount(Long shopId, Long packingStationId) {
        return ddPackingOrderMapper.selectCount(
                new LambdaQueryWrapper<DdPackingOrder>()
                        .eq(DdPackingOrder::getShopId, shopId)
                        .eq(DdPackingOrder::getPackingStationId, packingStationId)
        );
    }
}
