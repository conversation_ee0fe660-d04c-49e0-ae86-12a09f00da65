package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.EffectTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.FreezeStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.LimitTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdCommodityLimitMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdCommodityLimit;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodityStock;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.XdSendLogService;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Slf4j
@Service
public class DdCommodityLimitService extends ServiceImpl<DdCommodityLimitMapper, DdCommodityLimit> {

    @Autowired
    private DdCommodityLimitMapper ddCommodityLimitMapper;
    @Autowired
    private UserStallClient userStallClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;
    @Autowired
    private ShopCommodityService shopCommodityService;
    @Autowired
    private XdSendLogService xdSendLogService;
    @Autowired
    private StallMapper stallMapper;
    @Autowired
    private StallCommodityStockService stallCommodityStockService;



    /**
     * 限量销售设置列表
     * @param req
     * @return
     */
    public PageInfo<DdCommodityLimitODTO> ddCommodityLimitPageList(DdCommodityLimitPageIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, null));
        if(SpringUtil.isEmpty(shopIdList) || (null != req.getShopId() && !shopIdList.contains(req.getShopId()))){
            return new PageInfo<>();
        }

        if(tokenInfo.getShopId() != null && tokenInfo.getShopId() > 0) {
            SelectUserStallIdListIDTO idto = SelectUserStallIdListIDTO.init(FastThreadLocalUtil.getQY().getUserId(), req.getShopId());
            List<Long> stallIdList =  userStallClient.selectUserStallIdList(idto);
            if(SpringUtil.isEmpty(stallIdList)){
                return new PageInfo<>();
            }
            req.setStallIdList(stallIdList);
        }


        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
             ddCommodityLimitMapper.ddCommodityLimitPageList(req);
        });
    }


    /**
     *保存限量销售设置
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDdCommodityLimit(DdCommodityLimitSaveIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Date nowTime = new Date();
        Long userId = tokenInfo.getUserId();

        // 保存校验
        saveDdCommodityLimitCheck(req);

        // 判断档口不能为空、设置档口、校验权限
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());

        List<Long> comIds = req.getItemList().stream().map(DdCommodityLimitSaveItemIDTO::getCommodityId).collect(Collectors.toList());
        // 判断商品重复
        checkCommodity(comIds);

        List<DdCommodityODTO> commodityList = ddCommodityLimitMapper.queryDdStallCommodityList(req.getShopId(), req.getStallId(), null);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityList), "该档口不存在商品");
        List<Long> commodityIdList = commodityList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());

        req.getItemList().forEach(item ->{
            if(!commodityIdList.contains(item.getCommodityId())){
                List<Commodity> commList = commodityMapper.queryCommodityByIdList(Collections.singletonList(item.getCommodityId()));
                QYAssert.isFalse( commList.get(0).getCommodityCode() + "商品不属于当前档口");
            }
        });

        List<DdCommodityLimit> saveList = new ArrayList<>(comIds.size());
        List<DdCommodityLimit> updateList = new ArrayList<>(comIds.size());
        List<DdCommodityLimit> ddCommodityLimitList = queryDdCommodityLimitList(req.getShopId(), req.getStallId(), comIds);
        Map<Long, DdCommodityLimit> ddCommodityLimitMap = ddCommodityLimitList.stream().collect(Collectors.toMap(DdCommodityLimit::getCommodityId, Function.identity()));
        // 日志记录
        List<DdCommodityLimitLogDTO> logList = new ArrayList<>(comIds.size());

        // 组装新增、修改数据
        setSaveOrUpdateDdCommodityLimit(req, ddCommodityLimitMap, nowTime, userId, updateList, saveList,
                                       logList, tokenInfo.getEmployeeNumber(), tokenInfo.getRealName());

        // 批量新增、批量修改
        if(CollectionUtils.isNotEmpty(updateList)){
            this.updateBatchById(updateList);
        }

        if(CollectionUtils.isNotEmpty(saveList)){
            this.saveBatch(saveList);
        }

        // 如果是循环限量，则加入延迟队列
        if(EffectTypeEnum.DAY_LOOP.getCode().equals(req.getEffectType())){
            // 计算时间
            long delayTime = DdUtils.getDelayTime(req.getLoopTime());

            for (DdCommodityLimitSaveItemIDTO itemReqVo : req.getItemList()) {
                // 加入延迟队列
                DdCommodityLimitFeedDTO cloudCommodityFeedDTO = new DdCommodityLimitFeedDTO(
                        req.getShopId(),
                        req.getStallId(),
                        itemReqVo.getCommodityId(),
                        itemReqVo.getLimitNumber(),
                        req.getEffectType(),
                        req.getLoopTime(),
                        userId);
                DelayMsgIDTO idto = new DelayMsgIDTO(RedisDelayQueueEnum.DD_COMMODITY_LIMIT_SET.getCode(), JsonUtil.java2json(cloudCommodityFeedDTO), TimeUnit.MILLISECONDS.toSeconds(delayTime), TimeUnit.SECONDS);
                delayMsgClient.addDelayQueue(idto);
            }
        }

        // 记录日志
        if(CollectionUtils.isNotEmpty(logList)){
            sendDdCommodityLimitLog(logList);
        }



        if(CollectionUtils.isNotEmpty(updateList)){
            // 发送库存消息(限量修改)
//            sendStockChangeKafkaMsgCompare(oldList, updateList);
            this.dealStockMessage(req.getShopId(), updateList);
        }

        if(CollectionUtils.isNotEmpty(saveList)){
            // 发送库存消息(新增库存限量-- > 和实时库存对比，有没有库存以限量为主)
//            sendStockChangeKafkaMsg(saveList, true);
            this.dealStockMessage(req.getShopId(), saveList);
        }
        return Boolean.TRUE;
    }

    /**
     * 限量销售设置记录日志
     * @param logList
     */
    private void sendDdCommodityLimitLog(List<DdCommodityLimitLogDTO> logList) {
        Stall stall = stallMapper.selectById(logList.get(0).getStallId());
        logList.forEach(item -> {
            item.setStallCode(stall.getStallCode());
            item.setStallName(stall.getStallName());
        });

        renderService.render(logList, "/sendDdCommodityLimitLog");
        xdSendLogService.sendLog(logList, "t_log_dd_commodity_limit");
    }

    /**
     * 组装新增、修改数据
     */
    private void setSaveOrUpdateDdCommodityLimit(DdCommodityLimitSaveIDTO req, Map<Long, DdCommodityLimit> ddCommodityLimitMap,
                                                 Date nowTime, Long userId, List<DdCommodityLimit> updateList,
                                                 List<DdCommodityLimit> saveList, List<DdCommodityLimitLogDTO> logList,
                                                 String employeeNumber, String realName) {
        req.getItemList().forEach(item ->{
            // 日志信息
            DdCommodityLimitLogDTO logDTO = getDdCommodityLimitLogDTO(req.getShopId(), req.getStallId(), item.getCommodityId(), item.getLimitNumber(),
                                                   req.getLimitType(), req.getEffectType(), req.getLoopTime(), nowTime, userId, employeeNumber, realName);

            if(ddCommodityLimitMap.containsKey(item.getCommodityId())){
                DdCommodityLimit commodityLimit = ddCommodityLimitMap.get(item.getCommodityId());

                if (EffectTypeEnum.DAY_LOOP.getCode().equals(req.getEffectType())
                || LimitTypeEnum.FROM_ZERO.getCode().equals(req.getLimitType())){
                    commodityLimit.setPurchaseNumber(0);
                    commodityLimit.setEffectBeginTime(nowTime);
                }else {
                    //如果是累加的限量并且，不循环生效，获得以前的生效时间
                    commodityLimit.setEffectBeginTime(getEffectBeginTime(commodityLimit));
                }
                logDTO.setEffectBeginTime((DateUtil.getDateFormate(commodityLimit.getEffectBeginTime(), "yyyy-MM-dd HH:mm:ss")));
                commodityLimit.setEffectType(req.getEffectType());
                commodityLimit.setLoopTime(req.getLoopTime());
                commodityLimit.setLimitType(req.getLimitType());
                commodityLimit.setLimitNumber(item.getLimitNumber());
                commodityLimit.setUpdateId(userId);
                commodityLimit.setUpdateTime(nowTime);
                updateList.add(commodityLimit);
                logDTO.setOperationType(OperateTypeEnums.修改.getCode());
            }else {
                DdCommodityLimit commodityLimit = new DdCommodityLimit();
                commodityLimit.setShopId(req.getShopId());
                commodityLimit.setStallId(req.getStallId());
                commodityLimit.setEffectType(req.getEffectType());
                commodityLimit.setLoopTime(req.getLoopTime());
                commodityLimit.setLimitType(req.getLimitType());
                commodityLimit.setEffectBeginTime(nowTime);
                commodityLimit.setPurchaseNumber(0);
                commodityLimit.setCommodityId(item.getCommodityId());
                commodityLimit.setLimitNumber(item.getLimitNumber());
                commodityLimit.setCreateId(userId);
                commodityLimit.setCreateTime(nowTime);
                commodityLimit.setUpdateId(userId);
                commodityLimit.setUpdateTime(nowTime);
                saveList.add(commodityLimit);
                logDTO.setOperationType(OperateTypeEnums.新增.getCode());
            }
            logList.add(logDTO);
        });
    }

    public void dealStockMessage(Long shopId, List<DdCommodityLimit> sendList){
        List<Long> commodityIdList = sendList.stream().map(DdCommodityLimit::getCommodityId).collect(Collectors.toList());

        // 只有商品是上架才发送库存变动消息
        List<DdCommodityLimit> onlineList = ddCommodityLimitMapper.queryOnLineDdStallCommodityList(shopId, commodityIdList);
        if(CollectionUtils.isNotEmpty(onlineList)) {
            Map<String, String> onlineMap = new HashMap<>();
            onlineList.forEach(item -> {
                String key = item.getShopId() + "_" + item.getStallId() + "_" + item.getCommodityId();
                onlineMap.put(key, key);
            });

            List<StockItemDTO> dealList = new ArrayList<>();
            sendList.forEach(item ->{
                String key = item.getShopId() + "_" + item.getStallId() + "_" + item.getCommodityId();
                if(onlineMap.containsKey(key)){
                    dealList.add(new StockItemDTO(item.getCommodityId(), null, BigDecimal.ONE, null));
                }
            });

            if(CollectionUtils.isNotEmpty(dealList)) {
                shopCommodityService.dealStockMessage(shopId, dealList);
            }
        }
    }




    /**
     * 查询商品限购list
     * @param comIds
     * @return
     */
    public List<DdCommodityLimit> queryDdCommodityLimitList(Long shopId, Long stallId, List<Long> comIds) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<DdCommodityLimit>()
                .eq(DdCommodityLimit::getShopId, shopId)
                .eq(DdCommodityLimit::getStallId, stallId)
                .in(DdCommodityLimit::getCommodityId, comIds);
        List<DdCommodityLimit> ddCommodityLimitList = ddCommodityLimitMapper.selectList(query);
        return ddCommodityLimitList;
    }

    /**
     * 判断商品重复
     * @param comIds
     */
    private void checkCommodity(List<Long> comIds) {
        // 判断商品重复
        List<Long> commodityIdRepeatedList = comIds.stream()
                .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))// 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                .entrySet().stream().filter(entry -> entry.getValue() > 1)	// 过滤出元素出现次数大于 1 的 entry
                .map(Map.Entry::getKey)								// 获得 entry 的键（重复元素）对应的 Stream
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(commodityIdRepeatedList)){
            List<Commodity> commodityList = commodityMapper.queryCommodityByIdList(commodityIdRepeatedList);
            StringBuffer sb = new StringBuffer("以下商品编码重复 ");
            commodityList.forEach(entry -> {
                sb.append(entry.getCommodityCode()).append(" ");
            });
            QYAssert.isFalse(sb.toString());
        }
    }

    /**
     * 保存校验
     * @param req
     */
    private void saveDdCommodityLimitCheck(DdCommodityLimitSaveIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        QYAssert.notNull(req.getStallId(), "档口不能为空");
        QYAssert.notNull(req.getEffectType(), "生效不能为空");

        if(EffectTypeEnum.DAY_LOOP.getCode().equals(req.getEffectType())){
            QYAssert.notNull(req.getLoopTime(), "循环生效时间不能为空");
            req.setLimitType(LimitTypeEnum.FROM_ZERO.getCode());
        }else {
            QYAssert.notNull(req.getLimitType(), "限量计算方式不能为空");
            req.setLoopTime("");
        }
        QYAssert.isTrue(CollectionUtils.isNotEmpty(req.getItemList()), "商品清单不等你为空");
        req.getItemList().forEach(item ->{
            QYAssert.notNull(item.getCommodityId(), "商品不能为空");
            QYAssert.notNull(item.getLimitNumber(), "限量销售份数不能为空");
            QYAssert.isTrue(item.getLimitNumber() > 0 && item.getLimitNumber() <= 99999, "限量销售份数必须大于0,小于99999");

        });
    }


    /**
     * 查询档口商品
     *
     * @param req
     * @return
     */
    public List<DdCommodityODTO> queryDdCommodityList(DdCommodityQueryIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        QYAssert.notNull(req.getStallId(), "档口不能为空");
        return ddCommodityLimitMapper.queryDdStallCommodityList(req.getShopId(), req.getStallId(), req.getCommodityKey());
    }


    /**
     * 导入限量销售商品
     * @return
     */
    public ExcelResult importExcel(Workbook wb, Long stallId, Long shopId) {
        QYAssert.notNull(stallId, "档口不能为空");
        QYAssert.notNull(shopId, "门店不能为空");

        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");

        List<DdCommodityLimitSaveItemIDTO> importList = validateImportTemplate(sheet);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(importList),"导入数据不能为空");

        List<String> commodityCodes = importList.stream().map(DdCommodityLimitSaveItemIDTO::getCommodityCode).collect(Collectors.toList());
        List<Commodity> commodityList = commodityMapper.queryCommodityByCodeList(commodityCodes);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityList),"商品编码全部不存在");
        Map<String, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity()));


        List<DdCommodityODTO> ddCommodityList = ddCommodityLimitMapper.queryDdStallCommodityList(shopId, stallId, null);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(ddCommodityList),"门店档口下面没有商品数据");
        // 渲染
        renderService.render(ddCommodityList, "/importDdCommodityLimit");
        Map<String, DdCommodityODTO> ddCommodityMap = ddCommodityList.stream().collect(Collectors.toMap(DdCommodityODTO::getCommodityCode, Function.identity()));


        List<DdCommodityODTO> rusultList = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        int i = 2;
        for(DdCommodityLimitSaveItemIDTO item : importList) {
            if(!commodityMap.containsKey(item.getCommodityCode())) {
                errors.add("第" + i + "行,商品编码不存在" );
            }else {
                if(!ddCommodityMap.containsKey(item.getCommodityCode())) {
                    errors.add("第" + i + "行,商品不属于当前档口" );
                }
            }

            if(!isNumber(item.getLimitNumberStr())){
                errors.add("第" + i + "行,行请输入 > 0,<= 99999的整数" );
            }

            // 符合条件的数量
            if(ddCommodityMap.containsKey(item.getCommodityCode()) && isNumber(item.getLimitNumberStr())) {
                DdCommodityODTO ddCommodityODTO = ddCommodityMap.get(item.getCommodityCode());
                ddCommodityODTO.setLimitNumber(Integer.valueOf(item.getLimitNumberStr()));
                rusultList.add(ddCommodityODTO);
            }

            i ++;
        }


        if(CollectionUtils.isNotEmpty(errors)){
            return new ExcelResult(errors, null);
        }

        return  new ExcelResult(rusultList);
    }

    /**
     * 校验模板、获取导入数据
     * @param sheet
     * @return
     */
    private List<DdCommodityLimitSaveItemIDTO> validateImportTemplate(Sheet sheet){
        List<DdCommodityLimitSaveItemIDTO> list = new ArrayList<>();
        Map<String , String> commodityRepeatMap = new HashMap<>();
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(checkRowNumZero(row, 0, "商品编码"), "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 1, "限量销售份数"), "模板不正确");
            }

            //从第二行开始
            if (rowNum > 0) {
                DdCommodityLimitSaveItemIDTO importIDTO = new DdCommodityLimitSaveItemIDTO();
                Cell c0 = row.getCell(0);
                String val0 = isTextNotVoid(c0, "第" + (rowNum + 1) + "行,商品编码不能为空");
                importIDTO.setCommodityCode(val0.trim());

                Cell c1 = row.getCell(1);
                String val1 = isTextNotVoid(c1, "第" + (rowNum + 1) + "行,限量销售份数不能为空");
                importIDTO.setLimitNumberStr(val1.trim());
                list.add(importIDTO);

                if(commodityRepeatMap != null && commodityRepeatMap.get(importIDTO.getCommodityCode()) != null){
                    QYAssert.isFalse("第" + (rowNum + 1) + "行商品编码重复");
                }

                commodityRepeatMap.put(importIDTO.getCommodityCode(), importIDTO.getCommodityCode());
            }
        }
        return list;
    }

    private boolean isNumber(String str) {
        // 长度最多15位，允许2位小数
        Pattern pattern = Pattern.compile("[1-9]{1}\\d{0,4}");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 判断模板格式：根据Excel表头来判断
     * @param row
     * @param index
     * @param cellName
     * @return
     */
    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }

    private String isTextNotVoid(Cell c , String msg){
        QYAssert.notNull(c, msg);
        c.setCellType(CellType.STRING);
        String value = c.getStringCellValue();
        QYAssert.isTrue(StringUtils.isNotBlank(value),msg);
        return value;
    }

    /**
     * 批量删除限量设置
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeleteDdCommodityList(DdCommodityQueryIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(req.getShopId(), "门店ID不能为空");
        QYAssert.notNull(req.getStallId(), "档口ID不能为空");

        // 判断档口不能为空、设置档口、校验权限
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());

        QYAssert.isTrue(StringUtils.isNotBlank(req.getCommodityKey()), "商品清单不能为空!");
        List<String> codesList =  Arrays.asList(req.getCommodityKey().split("\n"));
        QYAssert.isTrue(SpringUtil.isNotEmpty(codesList), "请以回车分割商品编码!");
        QYAssert.isTrue(codesList.size() <= 500, "最多500个商品!");

        List<Commodity> commodityList = commodityMapper.queryCommodityByCodeList(codesList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityList), "商品编码全部不存在");
        Map<String, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity()));

        for(String code : codesList) {
            if(!commodityMap.containsKey(code)) {
                QYAssert.isFalse(code + " 商品编码不存在");
            }
        }

        List<Long> commodityIdList = commodityList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<DdCommodityLimitLogDTO> logList = new ArrayList<>(commodityIdList.size());

        List<DdCommodityLimit> ddCommodityLimitList = queryDdCommodityLimitList(req.getShopId(), req.getStallId(), commodityIdList);
        if(CollectionUtils.isNotEmpty(ddCommodityLimitList)) {
            List<Long> docIdList = ddCommodityLimitList.stream().map(item -> item.getId()).collect(Collectors.toList());
            ddCommodityLimitMapper.deleteBatchIds(docIdList);

            Date nowTime = new Date();
            ddCommodityLimitList.forEach(item -> {
                DdCommodityLimitLogDTO logDTO = getDdCommodityLimitLogDTO(item.getShopId(), item.getStallId(), item.getCommodityId(), item.getLimitNumber(),
                                                    item.getLimitType(), item.getEffectType(), item.getLoopTime(), nowTime,
                                                    tokenInfo.getUserId(), tokenInfo.getEmployeeNumber(), tokenInfo.getRealName());
                logDTO.setOperationType(OperateTypeEnums.删除.getCode());
                logDTO.setEffectBeginTime(DateUtil.getDateFormate(item.getEffectBeginTime(), "yyyy-MM-dd HH:mm:ss"));
                logList.add(logDTO);
            });
        }


        // 记录日志
        if(CollectionUtils.isNotEmpty(logList)){
            sendDdCommodityLimitLog(logList);
        }

        // 发送库存变动消息
//        sendStockChangeKafkaMsg(ddCommodityLimitList, false);
        this.dealStockMessage(req.getShopId(), ddCommodityLimitList);
        return ddCommodityLimitList.size();
    }

    /**
     * 组装商品销售限量日志
     */
    private DdCommodityLimitLogDTO getDdCommodityLimitLogDTO(Long shopId, Long stallId, Long commodityId,Integer limitNumber,
                                                             Integer limitType, Integer effectType, String loopTime,
                                                             Date nowTime, Long userId, String employeeNumber, String realName) {
        DdCommodityLimitLogDTO logDTO = new DdCommodityLimitLogDTO();
        logDTO.setShopId(shopId);
        logDTO.setStallId(stallId);
        logDTO.setCommodityId(commodityId);
        logDTO.setLimitNumber(limitNumber);
        logDTO.setLimitTypeName(LimitTypeEnum.getNameByCode(limitType));
        logDTO.setEffectTypeName(EffectTypeEnum.getNameByCode(effectType));
        logDTO.setEffectBeginTime(DateUtil.getDateFormate(nowTime, "yyyy-MM-dd HH:mm:ss"));
        logDTO.setLoopTime(loopTime);
        logDTO.setCreateId(userId);
        logDTO.setCreateUserNo(employeeNumber);
        logDTO.setCreateUserName(realName);
        logDTO.setCreateTime(DateUtil.getDateFormate(nowTime, "yyyy-MM-dd HH:mm:ss"));
        return logDTO;
    }

    /**
     * 删除限量设置
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDdCommodityList(DdCommodityQueryIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(req.getDocId(), "id不能为空");

        DdCommodityLimit dcl = ddCommodityLimitMapper.selectById(req.getDocId());
        QYAssert.notNull(dcl, "单据不存在");

        // 判断档口不能为空、设置档口、校验权限
        ddTokenShopIdService.processDdTokenShopId(dcl.getShopId(), dcl.getStallId());

        // 删除
        ddCommodityLimitMapper.deleteById(req.getDocId());

        Date nowTime = new Date();
        // 记录日志
        List<DdCommodityLimitLogDTO> logList = new ArrayList<>(1);
        DdCommodityLimitLogDTO logDTO = getDdCommodityLimitLogDTO(dcl.getShopId(), dcl.getStallId(), dcl.getCommodityId(), dcl.getLimitNumber(),
                                                dcl.getLimitType(), dcl.getEffectType(), dcl.getLoopTime(), nowTime,
                                                tokenInfo.getUserId(), tokenInfo.getEmployeeNumber(), tokenInfo.getRealName());
        logDTO.setOperationType(OperateTypeEnums.删除.getCode());
        logDTO.setEffectBeginTime(DateUtil.getDateFormate(dcl.getEffectBeginTime(), "yyyy-MM-dd HH:mm:ss"));
        logList.add(logDTO);

        // 记录日志
        if(CollectionUtils.isNotEmpty(logList)){
            sendDdCommodityLimitLog(logList);
        }

        // 发送库存变动消息
//        sendStockChangeKafkaMsg(Collections.singletonList(dcl), false);
        this.dealStockMessage(dcl.getShopId(), Collections.singletonList(dcl));
        return Boolean.TRUE;
    }

    /**
     * PDA保存限量销售设置
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaSaveDdCommodityLimit(DdCommodityLimitPdaSaveIDTO req) {
        DdCommodityLimitSaveIDTO ddCommodityLimitSaveIDTO = BeanCloneUtils.copyTo(req, DdCommodityLimitSaveIDTO.class);
        ddCommodityLimitSaveIDTO.setItemList(Collections.singletonList(req.getItem()));
        //统一调用后管保存方法
        return saveDdCommodityLimit(ddCommodityLimitSaveIDTO);
    }

    public DdPdaCommodityPdaLimitODTO pdaDdCommodityLimit(DdCommodityLimitPdaQueryIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        DdCommodityLimit oneDdCommodityLimit = getOneDdCommodityLimit(req.getShopId(), req.getStallId(), req.getCommodityId());
        DdPdaCommodityPdaLimitODTO odto = new DdPdaCommodityPdaLimitODTO();
        if (Objects.nonNull(oneDdCommodityLimit)) {
            odto = BeanCloneUtils.copyTo(oneDdCommodityLimit, DdPdaCommodityPdaLimitODTO.class);
            odto.setLimitId(String.valueOf(oneDdCommodityLimit.getId()));
        }
        // 查询大店库存
        List<StallCommodityStock> stallCommodityStocks = stallCommodityStockService
                .queryStallCommodityStock(req.getShopId(), req.getStallId(), Collections.singletonList(req.getCommodityId()));
        if (CollectionUtils.isNotEmpty(stallCommodityStocks)) {
            StallCommodityStock stallCommodityStock = stallCommodityStocks.get(0);
            odto.setStockQuantity(stripZeros(stallCommodityStock.getStockQuantity()));
            odto.setFreezeQuantity(stripZeros(BigDecimal.valueOf(stallCommodityStock.getFreezeNumber())));
            odto.setReserveStock(stripZeros(stallCommodityStock.getReserveStock()));
            odto.setStockProvisional(stripZeros(stallCommodityStock.getQualityQuantity()));
            odto.setWarehouseAreaStock(stripZeros(stallCommodityStock.getWarehouseAreaStock()));
            odto.setPickingAreaStock(stripZeros(stallCommodityStock.getPickingAreaStock()));
        }

        return odto;
    }

    private BigDecimal stripZeros(BigDecimal value) {
        return Objects.nonNull(value) ? new BigDecimal(value.stripTrailingZeros().toPlainString()) : null;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaDeleteDdCommodityList(DdCommodityLimitPdaDeleteIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        QYAssert.notNull(req.getStallId(), "档口不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品ID不能为空");
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());

        DdCommodityQueryIDTO idto = new DdCommodityQueryIDTO();
        if (StringUtils.isEmpty(req.getLimitId())){
            DdCommodityLimit oneDdCommodityLimit = getOneDdCommodityLimit(req.getShopId(), req.getStallId(), req.getCommodityId());
            QYAssert.notNull(oneDdCommodityLimit, "限量销售设置不存在");
            idto = BeanCloneUtils.copyTo(req, DdCommodityQueryIDTO.class);
            idto.setCommodityKey(String.valueOf(req.getCommodityId()));
            idto.setDocId(oneDdCommodityLimit.getId());
        }else {
            idto.setDocId(Long.valueOf(req.getLimitId()));
        }

        return this.deleteDdCommodityList(idto);
    }

    private DdCommodityLimit getOneDdCommodityLimit(Long shopId, Long stallId, Long commodityId) {
        LambdaQueryWrapper<DdCommodityLimit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DdCommodityLimit::getShopId, shopId)
                .eq(DdCommodityLimit::getStallId, stallId)
                .eq(DdCommodityLimit::getCommodityId, commodityId);
        return this.baseMapper.selectOne(wrapper);
    }


    /**
     * 查询商品限量销售设置
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<DdCommodityLimit> queryOnLineDdCommodityLimitList(DdCommodityLimitQueryIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getCommodityIdList(), "商品list不能为空");

        return  ddCommodityLimitMapper.queryOnLineDdCommodityLimitList(req.getShopId(),  req.getCommodityIdList());
    }

    /**
     * 冻结或者解冻商品限量
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> freezeOrUnFreezeCommodityLimit(DcCommodityLimitFreezeIDTO req) {
        log.info("冻结或者解冻商品限量，参数：{}", req);

        // 限量的商品
        List<Long> resultList = new ArrayList<>();

        QYAssert.notNull(req.getCreateTime(), "订单创建时间不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(req.getCommodityList()), "明细不能为空");

        req.getCommodityList().forEach(item -> {
            QYAssert.notNull(item.getCommodityId(), "商品id不能为空");
            QYAssert.notNull(item.getStockNumber(), "份数不能为空");
            QYAssert.notNull(item.getStallId(), "档口id不能为空");
        });

        // 获取商品基础信息
        List<Long> idList = req.getCommodityList().stream().distinct().map(DcCommodityLimitFreezeItemIDTO::getCommodityId).collect(Collectors.toList());
        List<Commodity> commList = commodityMapper.queryCommodityByIdList(idList);
        Map<Long, Commodity> commMap = commList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));

        // 根据档口分组，循环
        Map<Long, List<DcCommodityLimitFreezeItemIDTO>> reqMap = req.getCommodityList().stream().collect(Collectors.groupingBy(DcCommodityLimitFreezeItemIDTO::getStallId));
        reqMap.forEach((stallId,reqList)->{

            List<Long> commodityIdList = reqList.stream().map(DcCommodityLimitFreezeItemIDTO::getCommodityId).collect(Collectors.toList());

            List<DdCommodityLimit> ddCommodityLimitList = queryDdCommodityLimitList(req.getShopId(), stallId, commodityIdList);

            if(CollectionUtils.isNotEmpty(ddCommodityLimitList)) {
                // 原始的商品限量
                List<DdCommodityLimit> oldList = BeanCloneUtils.copyTo(ddCommodityLimitList, DdCommodityLimit.class);

                Map<Long, List<DcCommodityLimitFreezeItemIDTO>> commodityLimitFreezeMap = reqList.stream().collect(Collectors.groupingBy(DcCommodityLimitFreezeItemIDTO::getCommodityId));

                ddCommodityLimitList.forEach(item -> {
                    int leftNumber = (item.getLimitNumber() - (item.getPurchaseNumber() == null ? 0 : item.getPurchaseNumber()));

                    List<DcCommodityLimitFreezeItemIDTO> idtos = commodityLimitFreezeMap.get(item.getCommodityId());
                    // 此次商品需要冻结或者解冻数量
                    int stockNumber = idtos.stream().mapToInt(DcCommodityLimitFreezeItemIDTO::getStockNumber).sum();

                    // 冻结
                    if (FreezeStatusEnum.FREEZE.getCode().equals(req.getFrozenType())) {
                        if(leftNumber < stockNumber) {
                            // 冻结数量不足
                            QYAssert.isFalse(commMap.get(item.getCommodityId()).getCommodityName() + " 限量不足,剩余" + leftNumber);
                        }else {
                            item.setPurchaseNumber(item.getPurchaseNumber() + stockNumber);
                            ddCommodityLimitMapper.updateById(item);
                        }

                    }else {
                        // 解冻
                        boolean canUnfreezeStatus = canUnfreezeStatus(item, req.getCreateTime());
                        if(canUnfreezeStatus){
                            item.setPurchaseNumber(item.getPurchaseNumber() - stockNumber);
                            item.setPurchaseNumber(item.getPurchaseNumber() < 0 ? 0 : item.getPurchaseNumber());
                            ddCommodityLimitMapper.updateById(item);
                        }
                    }

                });

                // 发送库存变动消息
                List<Long> ddCommIdList = oldList.stream().map(DdCommodityLimit::getCommodityId).collect(Collectors.toList());
                List<DdCommodityLimit> newList = queryDdCommodityLimitList(req.getShopId(), stallId, ddCommIdList);;
                this.dealStockMessage(req.getShopId(), newList);
                resultList.addAll(ddCommIdList);
            }else {
                log.info("商品限量不存在, 门店:{},  商品:{}", req.getShopId(), idList);
            }
        });

        return resultList;
    }

    /**
     * 判断是否可以扣减已下单数量
     * @param cw
     * @param createTime
     * @return
     */
    private boolean canUnfreezeStatus(DdCommodityLimit cw, Date createTime) {
        QYAssert.notNull(createTime, "订单创建时间不能为空。");
        Date effectBeginTime = getEffectBeginTime(cw);
        return createTime.compareTo(effectBeginTime) > 0;
    }

    /**
     * 获取当前限量规则的生效时间
     * @param cw
     * @return
     */
    private Date getEffectBeginTime(DdCommodityLimit cw) {
        if (EffectTypeEnum.NO_LOOP.getCode().equals(cw.getEffectType())){
            return cw.getEffectBeginTime();
        }
        // 如果是循环的，则获取规则创建时间,和 循环时间
        //例如 ，循环时间每天是 08:00:00 创建时间是2019-01-01 10:00:00 如果当前时间是 2019-01-01 11:00:00 则返回 2019-01-01 10:00:00
        //如果当前时间是 2019-01-02 11:00:00 则返回 2019-01-02 08:00:00
        //如果当前时间是 2019-01-02 09:00:00 则返回 2019-01-02 08:00:00
        String yesterdayStr = LocalDateTime.now().plusDays(-1).toString("yyyy-MM-dd");
        Date yesterdayDate = DateUtil.parseDate(yesterdayStr + " " + cw.getLoopTime(), "yyyy-MM-dd HH:mm:ss");
        if(cw.getEffectBeginTime().compareTo(yesterdayDate)>0){
            return cw.getEffectBeginTime();
        }else {
            return yesterdayDate;
        }
    }

}
