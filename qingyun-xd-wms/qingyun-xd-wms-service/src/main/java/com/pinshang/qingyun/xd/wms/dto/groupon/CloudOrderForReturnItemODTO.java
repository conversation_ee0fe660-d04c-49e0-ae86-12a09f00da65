package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CloudOrderForReturnItemODTO
 * <AUTHOR>
 * @Date 2021/6/24 16:16
 * @Description CloudOrderForReturnItemODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudOrderForReturnItemODTO {
    @ApiModelProperty("子单id")
    private Long orderItemId;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("商品code")
    private String barCode;
    @ApiModelProperty("商品codeList-不包含主码")
    private List<String> barCodeList;
    @ApiModelProperty("商品名")
    private String commodityName;
    @ApiModelProperty("商品计量单位")
    private String commoditySpec;
    @ApiModelProperty("商品")
    private String commodityUnitName;
    @ApiModelProperty("件数")
    private BigDecimal returnNumber;
    @ApiModelProperty("商品数量:称重商品等于销售规格乘以件数")
    private BigDecimal quantity;
    @ApiModelProperty("金额")
    private BigDecimal amount;
    @ApiModelProperty("当前门店是否存在, 0-不存在, 1-存在")
    private Integer hasCommodity;
    @ApiModelProperty("是否称重,0-不称量,1-称重  ")
    private Integer isWeight;
    @ApiModelProperty("是否已经退货成功或者退货中,0-否,1-是")
    private Integer hasReturn;
    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;
}
