package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CloudOrderForReturnODTO
 * <AUTHOR>
 * @Date 2021/6/24 16:15
 * @Description CloudOrderForReturnODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudOrderForReturnODTO {
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("订单code")
    private String orderCode;
    @ApiModelProperty("下单人手机号")
    private String userMobile;
    @ApiModelProperty("提货日期")
    private Date arrivalTime;
    private List<CloudOrderForReturnItemODTO> itemList;
}
