package com.pinshang.qingyun.xd.wms.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.WeChatSendMessageClient;
import com.pinshang.qingyun.shop.dto.shopStatus.ShopStatusIDTO;
import com.pinshang.qingyun.shop.dto.shopStatus.ShopStatusODTO;
import com.pinshang.qingyun.shop.dto.xj.XJShopCommodityAPPStatusIDTO;
import com.pinshang.qingyun.shop.dto.xj.XJShopCommodityAPPStatusODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityUpDownManagerClient;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityDaliyReportMapper;
import com.pinshang.qingyun.xd.wms.model.ShopCommodityDaliyReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName ShopStockTrackService
 * <AUTHOR>
 * @Date 2022/11/9 10:40
 * @Description ShopStockTrackService
 * @Version 1.0
 */
@Slf4j
@Service
public class ShopStockTrackService {
    @Autowired
    private ShopStatusClient shopStatusClient;

    @Autowired
    private ShopCommodityUpDownManagerClient shopCommodityUpDownManagerClient;

    @Autowired
    private ShopCommodityDaliyReportMapper shopCommodityDaliyReportMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private WeChatSendMessageClient weChatSendMessageClient;

    public static final String YC_SHOP_CODE = "0843";

    /**
     * 及时达门店在线商品跟踪
     * @return
     */
    public Boolean insertStockList(){
        // 获取开启及时达的门店
        // 1取“云超和及时达开关 开通及时达的门店，即 及时达 =启用 #/xj/xjStoreRetailPrice/xjAPPSwitch
        ShopStatusIDTO shopStatusIDTO = new ShopStatusIDTO();
        shopStatusIDTO.setStatus(YesOrNoEnums.YES.getCode());
        shopStatusIDTO.setPageNo(1);
        shopStatusIDTO.setPageSize(Integer.MAX_VALUE);
        shopStatusIDTO.setShopStatus(YesOrNoEnums.YES.getCode());
        PageInfo<ShopStatusODTO> shopPage = shopStatusClient.selectShopStatusList(shopStatusIDTO);
        List<ShopStatusODTO> shopList = shopPage.getList();
        List<Long> shopIdList = shopList.stream().map(it -> it.getShopId()).collect(Collectors.toList());
        Map<Long, OrgAndParentInfoODTO> orgInfoMap = this.getOrgInformation(shopIdList);
        if(SpringUtil.isEmpty(shopList)){
            return true;
        }
        Date nowDate = new Date();

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 8, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopList.size())
        );

        for (ShopStatusODTO shopInfo : shopList) {
            Long shopId = shopInfo.getShopId();
            OrgAndParentInfoODTO orgInfo = orgInfoMap.get(shopId);
            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        insertShopStockTrack(shopInfo, nowDate, orgInfo);
                    }
                });
            } catch (Exception e) {
                // sendSmsMessageShopStockTrack(shopId);
                log.error("及时达门店在线商品跟踪生成异常，shopId={}", shopInfo.getShopId(), e);
            }
        }

        return true;
    }

    /**
     * 插入单门店信息
     * @param shopInfo
     * @param nowDate
     * @param orgInfo
     */
    public void insertShopStockTrack(ShopStatusODTO shopInfo, Date nowDate, OrgAndParentInfoODTO orgInfo){
        // 2不含：门店编码=0843 清美云超、不含测试门店(比如：包含但不限于'%测试%'，‘%ts%’,'%test%'等等) todo 补充测试门店
        if(YC_SHOP_CODE.equals(shopInfo.getShopCode())){
            return;
        }
        Long shopId = shopInfo.getShopId();
        // 获取商品  线上运营/线上商品上下架/线上商品上下架页面，门店类型= 鲜食店(shopType=2)，上架状态=已上架
        XJShopCommodityAPPStatusIDTO xjShopCommodityIDTO = new XJShopCommodityAPPStatusIDTO();
        xjShopCommodityIDTO.setShopId(shopId);
        //门店类型= 鲜食店(shopType=2)
        xjShopCommodityIDTO.setShopType(shopInfo.getShopType());
        //上架状态=已上架
        xjShopCommodityIDTO.setAppStatus(0);
        xjShopCommodityIDTO.setPageNo(1);
        xjShopCommodityIDTO.setPageSize(Integer.MAX_VALUE);
        //appStatus app状态：0-上架，1-下架 2-部分上架
        // saleStatusName-"必售" "非必售"
        PageInfo<XJShopCommodityAPPStatusODTO> commodityInfoPage = shopCommodityUpDownManagerClient.selectXjShopCommodityList(xjShopCommodityIDTO);
        List<XJShopCommodityAPPStatusODTO> commodityInfoList = commodityInfoPage.getList();

        List<Long> commodityIdList = commodityInfoList.stream().map(it -> Long.parseLong(it.getCommodityId())).collect(Collectors.toList());
        if(SpringUtil.isEmpty(commodityIdList)){
            return;
        }
        List<StockItemDTO> stocks = shopCommodityService.queryStockList(shopId, commodityIdList);
        Map<Long, BigDecimal> stocksMap = stocks.stream().collect(Collectors.toMap(StockItemDTO::getCommodityId, it -> null == it.getQuantity() ? BigDecimal.ZERO : it.getQuantity()));
        // 在线商品数
        int commodityCountOnline = 0;
        // 在线商品数_总部必售
        int commodityCountOnlineMustSell = 0;
        // 在线商品数_门店自选
        int commodityCountOnlineSelfSelect = 0;
        // 可售商品数
        int commodityCountOnsSale = 0;
        // 可售商品数_总部必售
        int commodityCountOnSaleMustSell = 0;
        // 可售商品数_门店自选
        int commodityCountOnSaleSelfSelect = 0;
        for(int j = 0; j<commodityInfoList.size(); j++){
            XJShopCommodityAPPStatusODTO commodityInfo = commodityInfoList.get(j);
            BigDecimal stock = stocksMap.get(Long.parseLong(commodityInfo.getCommodityId()));
            commodityCountOnline = commodityCountOnline + 1;
            boolean hasStock = null != stock && BigDecimal.ZERO.compareTo(stock) < 0;
            boolean mastSell = "必售".equals(commodityInfo.getSaleStatusName());
            if(mastSell){
                commodityCountOnlineMustSell = commodityCountOnlineMustSell + 1;
            }else{
                commodityCountOnlineSelfSelect = commodityCountOnlineSelfSelect + 1;
            }
            if(hasStock){
                commodityCountOnsSale = commodityCountOnsSale + 1;
                if(mastSell){
                    commodityCountOnSaleMustSell = commodityCountOnSaleMustSell + 1;
                }else{
                    commodityCountOnSaleSelfSelect = commodityCountOnSaleSelfSelect + 1;
                }
            }
        }

        ShopCommodityDaliyReport report = new ShopCommodityDaliyReport();
        report.setShopId(shopId);
        report.setShopName(shopInfo.getShopName());
        report.setCommodityCountOnline(commodityCountOnline);
        report.setCommodityCountOnlineMustSell(commodityCountOnlineMustSell);
        report.setCommodityCountOnlineSelfSelect(commodityCountOnlineSelfSelect);
        report.setCommodityCountOnSale(commodityCountOnsSale);
        report.setCommodityCountOnSaleMustSell(commodityCountOnSaleMustSell);
        report.setCommodityCountOnSaleSelfSelect(commodityCountOnSaleSelfSelect);
        report.setDeptId(orgInfo.getParentOrgId());
        report.setDeptName(orgInfo.getParentOrgName());
        report.setProcessTime(nowDate);
        report.setCreateId(-1L);
        report.setCreateTime(nowDate);
        shopCommodityDaliyReportMapper.insert(report);
    }

    /**
     * 获取门店部门信息
     * @param shopIdList
     * @return
     */
    private Map<Long, OrgAndParentInfoODTO> getOrgInformation(List<Long> shopIdList) {
        Map<Long, OrgAndParentInfoODTO> map = null;
        SelectShopOrgInfoListIDTO shopParentOrgInfoIDTO = new SelectShopOrgInfoListIDTO();
        shopParentOrgInfoIDTO.setShopIdList(shopIdList);
        List<OrgAndParentInfoODTO> orgInfoList = orgClient.selectShopOrgInfoList(shopParentOrgInfoIDTO);
        if (null != orgInfoList && orgInfoList.size() > 0) {
            map = orgInfoList.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId,it -> it));
        }
        return map;
    }

    /**
     * 发微信消息
     * @param shopId: 报错的门店di
     */
   /* public void sendSmsMessageShopStockTrack(Long shopId) {
        SmsMessageIDTO smsMessageIDTO = new SmsMessageIDTO();
        String weChartContent = "门店" + shopId + "追踪表生成失败";
        //content长度限制
        smsMessageIDTO.setContent(weChartContent);
        smsMessageIDTO.setMessageType(SmsMessageTypeEnums.XD_WMS_INFO_WARN);
        weChatSendMessageClient.sendWeChatMessage(smsMessageIDTO);
    }*/

}
