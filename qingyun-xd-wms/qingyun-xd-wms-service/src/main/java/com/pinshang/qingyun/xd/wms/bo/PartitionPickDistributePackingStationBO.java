package com.pinshang.qingyun.xd.wms.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PartitionPickDistributePackingStationBO implements Comparable<PartitionPickDistributePackingStationBO>, Serializable {

    private static final long serialVersionUID = -7348899083492097961L;

    /**
     * 打包口id
     */
    private Long packingStationId;

    /**
     * 打包口
     */
    private String packingPort;

    /**
     * 打包口拣货单数量
     */
    private Integer packingPortOrderCount;

    /**
     * 实现自然排序规则：
     * 1. 按 packingPortOrderCount 升序
     * 2. 再按 packingPort 自然排序（String.compareTo）
     */
    @Override
    public int compareTo(PartitionPickDistributePackingStationBO other) {
        if (other == null) {
            return -1; // null 排最后
        }
        // 先按订单数升序
        int cmp = Integer.compare(
                this.packingPortOrderCount != null ? this.packingPortOrderCount : 0,
                other.packingPortOrderCount != null ? other.packingPortOrderCount : 0
        );
        if (cmp != 0) return cmp;

        // 再按打包口字符串自然排序
        return this.packingPort.compareTo(other.packingPort);
    }
}
