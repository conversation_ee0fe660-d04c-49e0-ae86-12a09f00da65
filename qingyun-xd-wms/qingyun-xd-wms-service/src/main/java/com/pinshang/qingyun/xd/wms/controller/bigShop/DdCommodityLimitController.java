package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.FreezeStatusEnum;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdCommodityLimit;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdCommodityLimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.LocalDate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Slf4j
@Api(tags = "限量销售", description = "限量销售")
@RestController
@RequestMapping("/commodityLimit")
public class DdCommodityLimitController {

    @Autowired
    private DdCommodityLimitService ddCommodityLimitService;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private RedissonClient redissonClient;

    @PostMapping("/pageList")
    @MethodRender
    @ApiOperation(value = "限量销售设置列表")
    public PageInfo<DdCommodityLimitODTO> ddCommodityLimitPageList(@RequestBody DdCommodityLimitPageIDTO req) {
        return ddCommodityLimitService.ddCommodityLimitPageList(req);
    }


    @PostMapping("/exportDdCommodityLimit")
    @ApiOperation("限量销售设置列表 导出")
    public void exportDdCommodityLimit(@RequestBody DdCommodityLimitPageIDTO req, HttpServletResponse response) throws IOException {
        req.initExportPage();

        PageInfo<DdCommodityLimitODTO> page = ddCommodityLimitService.ddCommodityLimitPageList(req);
        List<DdCommodityLimitODTO> list = page.getList();
        if(CollectionUtils.isNotEmpty(list)){
            renderService.render(list, "/exportDdCommodityLimit");
        }

        try {
            ExcelUtil.setFileNameAndHead(response, "限量销售设置表" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream(), DdCommodityLimitODTO.class).autoCloseStream(Boolean.FALSE).sheet("限量销售设置表")
                    .doWrite(list);
        }catch (Exception e){
            log.error("限量销售设置表", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @PostMapping("/saveDdCommodityLimit")
    @ApiOperation(value = "保存限量销售设置")
    public Boolean saveDdCommodityLimit(@RequestBody DdCommodityLimitSaveIDTO req) {
        RLock lock = redissonClient.getLock("xsWms:saveDdCommodityLimit");
        if (lock.tryLock()) {
            try {
                return ddCommodityLimitService.saveDdCommodityLimit(req);
            } finally {
                lock.unlock();
            }
        }else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return false;
    }


    @ApiOperation(value = "查询档口商品", notes = "查询档口商品")
    @MethodRender
    @PostMapping("/queryDdCommodityList")
    public List<DdCommodityODTO> queryDdCommodityList(@RequestBody DdCommodityQueryIDTO req) {
        return ddCommodityLimitService.queryDdCommodityList(req);
    }

    @ApiOperation(value = "导入限量销售商品", notes = "导入限量销售商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public ExcelResult importExcel(@ApiParam(value = "待上传的文件") @RequestParam(value = "file", required = true) MultipartFile file,
                                   @ApiParam(value = "档口") @RequestParam(value = "stallId", required = true) Long stallId,
                                   @ApiParam(value = "门店") @RequestParam(value = "shopId", required = true) Long shopId) throws Exception {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            log.error("导入限量销售商品异常",e);
        }
        return ddCommodityLimitService.importExcel(wb, stallId, shopId);
    }

    @ApiOperation(value = "批量删除限量设置", notes = "批量删除限量设置")
    @PostMapping("/batchDeleteDdCommodityList")
    public Integer batchDeleteDdCommodityList(@RequestBody DdCommodityQueryIDTO req) {
        return ddCommodityLimitService.batchDeleteDdCommodityList(req);
    }

    @ApiOperation(value = "删除限量设置", notes = "删除限量设置")
    @PostMapping("/deleteDdCommodityList")
    public Boolean deleteDdCommodityList(@RequestBody DdCommodityQueryIDTO req) {
        return ddCommodityLimitService.deleteDdCommodityList(req);
    }

    @PostMapping("/pdaSaveDdCommodityLimit")
    @ApiOperation(value = "PDA保存限量销售设置", notes = "PDA保存限量销售设置")
    public Boolean pdaSaveDdCommodityLimit(@RequestBody DdCommodityLimitPdaSaveIDTO req) {
        RLock lock = redissonClient.getLock("xsWms:pdaSaveDdCommodityLimit");
        if (lock.tryLock()) {
            try {
                return ddCommodityLimitService.pdaSaveDdCommodityLimit(req);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return false;
    }

    @PostMapping("/pdaDdCommodityLimit")
    @MethodRender
    @ApiOperation(value = "PDA查询限量销售设置", notes = "PDA查询限量销售设置")
    public DdPdaCommodityPdaLimitODTO pdaDdCommodityLimit(@RequestBody DdCommodityLimitPdaQueryIDTO req) {
        return ddCommodityLimitService.pdaDdCommodityLimit(req);
    }

    @ApiOperation(value = "PDA删除限量设置", notes = "PDA删除限量设置")
    @PostMapping("/pdaDeleteDdCommodityList")
    public Boolean pdaDeleteDdCommodityList(@RequestBody DdCommodityLimitPdaDeleteIDTO req) {
        return ddCommodityLimitService.pdaDeleteDdCommodityList(req);
    }

    /**
     * 查询大店商品限量(默认app上架的档口)
     * @param req
     * @return
     */
    @ApiOperation(value = "查询商品限量销售设置", notes = "查询商品限量销售设置")
    @PostMapping("/queryDdCommodityLimitList")
    public List<DdCommodityLimit> queryDdCommodityLimitList(@RequestBody DdCommodityLimitQueryIDTO req) {
        return ddCommodityLimitService.queryOnLineDdCommodityLimitList(req);
    }

    /**
     * 冻结商品限量(默认app上架的档口)
     * @param req
     * @return
     */
    @ApiOperation(value = "冻结商品限量", notes = "冻结商品限量")
    @PostMapping("/freezeCommodityLimit")
    public List<Long> freezeCommodityLimit(@RequestBody DcCommodityLimitFreezeIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店id不能为空");

        RLock lock = redissonClient.getLock("WMS:FREEZE_COMMODITY_LIMIT:" + req.getShopId());
        try {
            if (lock.tryLock(100L, TimeUnit.SECONDS)) {
                req.setFrozenType(FreezeStatusEnum.FREEZE.getCode());
                return ddCommodityLimitService.freezeOrUnFreezeCommodityLimit(req);
            }
        } catch (InterruptedException e) {
            QYAssert.isFalse("冻结商品限量获取锁失败", ",reqVo={}", req);
        } finally {
            lock.unlock();
        }
        return new ArrayList<>();
    }

    /**
     * 解冻商品限量(默认app上架的档口)
     * @param req
     * @return
     */
    @ApiOperation(value = "解冻商品限量", notes = "解冻商品限量")
    @PostMapping("/unFreezeCommodityLimit")
    public List<Long> unFreezeCommodityLimit(@RequestBody DcCommodityLimitFreezeIDTO req) {
        QYAssert.notNull(req.getShopId(), "门店id不能为空");

        RLock lock = redissonClient.getLock("WMS:FREEZE_COMMODITY_LIMIT:" + req.getShopId());
        try {
            if (lock.tryLock(100L, TimeUnit.SECONDS)) {
                req.setFrozenType(FreezeStatusEnum.UN_FREEZE.getCode());
                return ddCommodityLimitService.freezeOrUnFreezeCommodityLimit(req);
            }
        } catch (InterruptedException e) {
            QYAssert.isFalse("解冻商品限量获取锁失败", ",reqVo={}", req);
        } finally {
            lock.unlock();
        }
        return new ArrayList<>();
    }
}
