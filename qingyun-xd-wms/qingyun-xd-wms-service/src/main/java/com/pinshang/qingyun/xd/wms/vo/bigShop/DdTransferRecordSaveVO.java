package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTransferRecordItemsSaveIDTO;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TransferTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@ApiModel("DdTransferRecordSaveVO")
public class DdTransferRecordSaveVO {

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 移出库区 1拣货区  2存储区 3排面区 4临时库
     */
    private Integer outStoreArea;

    /**
     * 移出货位code
     */
    private String outGoodsAllocationCode;


    /**
     * 移入库区 1拣货区  2存储区 3排面区 4临时库
     */
    private Integer inStoreArea;

    /**
     * 移入货位code
     */
    private String inGoodsAllocationCode;

    /**
     * 移库数量
     */
    private BigDecimal moveQuantity;

}
