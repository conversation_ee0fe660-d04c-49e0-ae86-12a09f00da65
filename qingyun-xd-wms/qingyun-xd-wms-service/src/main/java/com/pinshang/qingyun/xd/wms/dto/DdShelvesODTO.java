package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 排面货架管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@ApiModel("DdShelvesODTO")
public class DdShelvesODTO {

    private Long id;

    @ApiModelProperty("档口code")
    private String stallCode;

    @ApiModelProperty("档口名称")
    private String stallName;

    @ApiModelProperty("排面货架编码")
    private String shelveCode;

    @ApiModelProperty("排面货架名称")
    private String shelveName;

    @ApiModelProperty("陈列位数量")
    private Integer displayCount;

}