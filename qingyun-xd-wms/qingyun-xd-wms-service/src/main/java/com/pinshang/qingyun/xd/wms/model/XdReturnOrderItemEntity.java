package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 鲜食#退货单明细
 */
@Data
@TableName("t_xd_return_order_item")
public class XdReturnOrderItemEntity extends BaseEntity {

	/**退货单Id*/
	private Long returnOrderId;
	/**订单明细ID(t_xd_order_item表主键)*/
	private Long orderItemId;
    /**商品Id*/
    private Long commodityId;
    /**商品单价*/
	private BigDecimal price;
    /**退单数量*/
    private BigDecimal quantity;
    /**实际退单数量*/
    private BigDecimal realQuantity;
    /**退单份数*/
    private BigDecimal number;
    /**实际退货份数*/
    private BigDecimal realNumber;
    /**商品金额*/
    private BigDecimal totalAmount;
    /**实际退货金额*/
    private BigDecimal realAmount;
    /** 成本价*/
    private BigDecimal weightPrice;
    /**
     * 0普通商品 1满x元减y元 3第x件y折 4满x件y折 30任选x件组合价y元 33固定组合价  34享受赠品 35买满x元赠
     * 36每买满X元赠 37买满x件赠 38每买满x件赠
     */
    private Integer promotionStatus;
    /**
     * 促销id
     */
    private Long promotionId;

    private Date createTime;
    private Long createId;
    private Date updateTime;
    private Long updateId;
}