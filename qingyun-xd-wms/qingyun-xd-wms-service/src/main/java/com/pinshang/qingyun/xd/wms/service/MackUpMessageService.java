

package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.MqMessageListODTO;
import com.pinshang.qingyun.common.dto.MqMessageODTO;
import com.pinshang.qingyun.common.dto.MqMessageQueryListIDTO;
import com.pinshang.qingyun.common.service.MqMessageClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.xd.wms.dto.MackUpDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 消息补偿
 * Created by chenqi on 2020/02/19.
 */
@Service
@Slf4j
public class MackUpMessageService {

    @Autowired
    private MqMessageClient mqMessageClient;

//    @Autowired
//    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private IMqSenderComponent mqSenderComponent;


    public void makeUpMessage(String uuid) {
        MqMessageODTO mqMessageODTO = mqMessageClient.queryMqMessage(uuid);
        if(mqMessageODTO == null){
            mqMessageODTO = mqMessageClient.queryXdMqMessage(uuid);
        }

        sendMakeUpMessage(mqMessageODTO.getMessage(), mqMessageODTO.getTopic());
    }

    @Async
    public void makeUpMessageBatch(MackUpDTO dto) {
        MqMessageQueryListIDTO mqMessage = new MqMessageQueryListIDTO();
        mqMessage.setTopic(dto.getTopic());
        mqMessage.setTableName(dto.getTableName());
        mqMessage.setBeginTime(dto.getBeginTime());
        mqMessage.setEndTime(dto.getEndTime());
        List<MqMessageListODTO> list = mqMessageClient.queryMessageList(mqMessage);

        if(SpringUtil.isNotEmpty(list)){
            for (MqMessageListODTO msgDto : list) {
                if(StringUtils.isBlank(msgDto.getMessage()) || StringUtils.isBlank(msgDto.getTopic())){
                    continue;
                }
                sendMakeUpMessage(msgDto.getMessage(), msgDto.getTopic());
            }
        }
    }

    private void sendMakeUpMessage(String message, String topic) {
        Object obj = JsonUtil.json2java(message, LinkedHashMap.class);

        mqSenderComponent.send(
                topic,
                obj, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.MESSAGE_MAKE_UP.name(),
                KafkaMessageOperationTypeEnum.COPY.name());
    }
}