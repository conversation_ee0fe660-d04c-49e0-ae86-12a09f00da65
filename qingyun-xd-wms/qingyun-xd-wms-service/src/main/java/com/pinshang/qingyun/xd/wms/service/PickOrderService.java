

package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.msg.AppMessageTypeEnum;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.msg.dto.zsmd.MessageDTO;
import com.pinshang.qingyun.msg.service.RadioMsgClient;
import com.pinshang.qingyun.msg.service.zsmd.ZSMessageClient;
import com.pinshang.qingyun.shop.dto.ShopCommodityStockODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.*;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.*;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.model.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import com.pinshang.qingyun.xd.wms.service.platform.MeiTuanPickOrderService;
import com.pinshang.qingyun.xd.wms.service.platform.PlatFormPickOrderService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import com.pinshang.qingyun.xd.wms.service.state.PickOrderContext;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.util.XdWmsConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 拣货
 * 生成拣货单
 * 拣货单分配
 * 完成拣货
 * 取消拣货
 * Created by chenqi on 2019/12/02.
 */
@Service
@Slf4j
public class PickOrderService extends ServiceImpl<PickOrderMapper, PickOrder>{

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private PickOrderItemMapper pickOrderItemMapper;

    @Autowired
    private PickWorkOrderMapper pickWorkOrderMapper;

    @Autowired
    private WarehouseEmployeeService warehouseEmployeeService;

    @Autowired
    private WarehouseShelfService warehouseShelfService;

    @Autowired
    private WarehouseWorkCommodityService warehouseWorkCommodityService;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private BackSettingService backSettingService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;
    @Autowired
    private MeiTuanPickOrderService meiTuanPickOrderService;
    @Autowired
    private DictionaryClient dictionaryClient;

    @Value("${config.beginTime:60}")
    private int configBeginTime;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private ShopCommodityService shopCommodityService;

    public static final String INIT_SHELF_NO = "0001";

    @Autowired
    private WarehouseEmployeeMapper employeeMapper;

    @Autowired
    private StockOutRelationLogMapper stockOutRelationLogMapper;

    @Autowired
    private StockFreezeLogMapper stockFreezeLogMapper;

    @Autowired
    private ZSMessageClient zSMessageClient;

    @Autowired
    private RadioMsgClient radioMsgClient;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    @Autowired
    private PlatFormPickOrderService platFormPickOrderService;

    /**
     * 生成拣货单
     * 需求2732 改造为 绑定加工位就生成加工单
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void createPickOrder(OrderDTO orderDTO) {
        LockUtils.checkLock(LockUtils.PICK_CREATE, String.valueOf(orderDTO.getOrderId()), 1, TimeUnit.SECONDS);

        PickOrderResult pickOrderByOrderId = getPickOrderByOrderId(orderDTO.getOrderId());
        if (Objects.nonNull(pickOrderByOrderId)) {
            // 存在拣货单,消息处理过了
            return;
        }

        String code = codeClient.createCode("PICK_XD_CODE");
        //接收消息 调用创建拣货单
        PickOrder pickOrder = new PickOrder();
        pickOrder.setPickCode(code);
        pickOrder.setOrderId(orderDTO.getOrderId());
        pickOrder.setOrderCode(orderDTO.getOrderCode());
        pickOrder.setWarehouseId(orderDTO.getShopId());
        pickOrder.setOrderDeliveryBeginTime(orderDTO.getDeliveryBeginTime());
        pickOrder.setOrderDeliveryEndTime(orderDTO.getDeliveryEndTime());
        pickOrder.setSourceType(orderDTO.getSourceType());
        pickOrder.setOriginalOrderCode(orderDTO.getOriginalOrderCode());
        pickOrder.setOrderType(null == orderDTO.getOrderType()? YesOrNoEnums.NO.getCode(): orderDTO.getOrderType());

        //所有商品 绑定加工位就生成加工单
        List<Long> processList = orderDTO.getItems().stream()
//                .filter(item -> YesOrNoEnums.YES.getCode().equals(item.getIsProcess()))
                .map(item -> item.getCommodityId()).collect(Collectors.toList());

        Map<Long, WorkCommodityListResult> workCommodityMap = null;
        List<WorkCommodityListResult> workCommodities = warehouseWorkCommodityService.queryWorkCommodityList(processList, orderDTO.getShopId());
        if(SpringUtil.isNotEmpty(workCommodities)){
            workCommodityMap = workCommodities.parallelStream().collect(toMap(WorkCommodityListResult::getId, it -> it));
            pickOrder.setHasProcess(YesOrNoEnums.YES.getCode());
        }
        //pickOrder.setStoreSerialNum(orderDTO.getOrderNum());
        pickOrder.setOrderNum(orderDTO.getOrderNum());
        pickOrderMapper.insert(pickOrder);

        // 拣货单明细增加成本价
        List<Long> commodityIdList = orderDTO.getItems().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, ShopCommodityStockODTO> shopCommodityMap = shopCommodityClient.queryShopCommodityValidStock(orderDTO.getShopId(),commodityIdList);
        for (OrderItemDTO orderItem : orderDTO.getItems()) {
            PickOrderItem pickItem = new PickOrderItem();
            pickItem.setId(IdWorker.getId());
            pickItem.setOrderId(orderDTO.getOrderId());
            pickItem.setStallId(orderItem.getStallId());
            pickItem.setOrderItemId(orderItem.getItemId());
            pickItem.setPickOrderId(pickOrder.getId());
            pickItem.setCommodityId(orderItem.getCommodityId());
            pickItem.setStockNumber(orderItem.getStockNumber());
            pickItem.setQuantity(orderItem.getQuantity());
            pickItem.setIsProcess(orderItem.getIsProcess());
            pickItem.setIsWeight(orderItem.getIsWeight());
            pickItem.setOriginSubBizId(orderItem.getOriginSubBizId());
            if(null != shopCommodityMap && shopCommodityMap.get(orderItem.getCommodityId()) != null){
                pickItem.setWeightPrice(shopCommodityMap.get(orderItem.getCommodityId()).getWeightPrice());
            }
            pickOrderItemMapper.insert(pickItem);

            if(workCommodityMap == null || workCommodityMap.get(orderItem.getCommodityId()) == null){
                continue;
            }
            PickWorkOrder workOrder = new PickWorkOrder();
            workOrder.setOrderId(orderDTO.getOrderId());
            workOrder.setPickOrderId(pickOrder.getId());
            workOrder.setPickOrderItemId(pickItem.getId());
            workOrder.setCommodityId(orderItem.getCommodityId());
            workOrder.setProcessId(orderItem.getProcessId());
            workOrder.setProcessName(orderItem.getProcessName());

            //商品加工点
            workOrder.setWorkId(workCommodityMap.get(workOrder.getCommodityId()).getWorkId());
            workOrder.setWorkName(workCommodityMap.get(workOrder.getCommodityId()).getWorkName());

            pickWorkOrderMapper.insert(workOrder);
        }

        //修改冻结逻辑,根据冻结（需冻结、已冻结）处理状态
        updateStockOutStatus(orderDTO.getOrderId(), orderDTO.getOrderCode(), orderDTO.getItems());

        if (XdOrderTypeEnum.ORDINARY.getCode() == pickOrder.getOrderType()
                || XdOrderTypeEnum.XD_WAREHOUSE.getCode() == pickOrder.getOrderType()
                || XdOrderTypeEnum.FARM.getCode() == pickOrder.getOrderType()) {
            radioMsgClient.broadcast(orderDTO.getShopId(), RadioTemplateEnum.NEW_ORDER.getCode());
        }
        if (XdOrderTypeEnum.THIRD.getCode() == pickOrder.getOrderType()) {
            radioMsgClient.sendDynamicMsgByShopId(orderDTO.getShopId(), RadioTemplateEnum.NEW_THIRD_ORDER.getName());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelPickOrder(Long orderId, Long userId) {
        LockUtils.checkLock(LockUtils.PICK_OPERATE, orderId.toString());

        LambdaQueryWrapper query = new LambdaQueryWrapper<PickOrder>()
                .eq(PickOrder::getOrderId, orderId);
        PickOrder pickOrder = pickOrderMapper.selectOne(query);

        // 分区拣货
        if (Objects.nonNull(pickOrder) && Objects.equals(pickOrder.getPickId(), XdWmsConstantUtil.SYSTEM_PICK_ID)) {
            ddPickPartitionOrderService.cancelPickOrder(pickOrder,orderId, userId);
            return;
        }

        cancelPickOrder(pickOrder, orderId,userId, Boolean.TRUE);
    }

    /**
     * 取消拣货单
     * @param pickOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelPickOrder(PickOrder pickOrder,Long orderId, Long userId,Boolean completeShelfWorkAndDelivery) {

        Order order = orderMapper.selectById(orderId);

        //团购单 取消时[未提货完成]无拣货单
        if(!(pickOrder == null && OrderTypeEnum.GROUP.getCode().equals(order.getOrderType()))){
            QYAssert.isTrue(pickOrder != null, "无效拣货单");
            QYAssert.isTrue(PickStatusEnum.CANCEL.getCode() != pickOrder.getPickStatus(), "拣货单已取消");
            QYAssert.isTrue(PickStatusEnum.FINISH.getCode() != pickOrder.getPickStatus(), "拣货单已完成");

            //京东到家 无法后台拣货取消
            if(userId != null && OrderSourceTypeEnum.JDDJ.getCode().equals(pickOrder.getSourceType())){
                throw new BizLogicException("无法取消拣货，请联系顾客取消订单。");
            }

            //取消拣货单
            PickOrderContext pickOrderContext = new PickOrderContext();
            pickOrderContext.setPickOrder(pickOrder);
            pickOrderContext.doCancel(mqSenderComponent, meiTuanPickOrderService, userId);
            pickOrderMapper.updateById(pickOrder);
            //饿了么的订单后台取消拣货，订单那边不处理，先这边处理，分库再说
            if((OrderSourceTypeEnum.ELM.getCode().equals(pickOrder.getSourceType()) || OrderSourceTypeEnum.MTSG.getCode().equals(pickOrder.getSourceType()))
                    && userId != null){
                pickOrderMapper.orderCancelPickOrder(pickOrder.getOrderId());
            }
            //完成配送取货位、加工点取货位
            if (completeShelfWorkAndDelivery) {
                //分区拣货自己去完成
                warehouseShelfService.completeShelfDelivery(orderId, pickOrder.getShelfNo());
                completeShelfWork(pickOrder, false);
            }
        }
        //解冻库存 冻结多少 解冻多少
        shopCommodityService.stockUnFreeze(orderId, order.getOrderCode(), order.getShopId());
    }

    /**
     * 完成拣货
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void completePickOrder(PickCompleteDTO pickCompleteDTO) {
        PickOrder pickOrder = pickOrderMapper.selectById(pickCompleteDTO.getPickOrderId());
        QYAssert.isTrue(pickOrder != null, "无效拣货单");
        if(PickStatusEnum.CANCEL.getCode() == pickOrder.getPickStatus()){
            throw new BizLogicException("拣货单已取消，请将商品放回原处", ApiErrorCodeEnum.XD_PICK_CANCEL);
        }

        LockUtils.checkLock(LockUtils.PICK_OPERATE, pickOrder.getOrderId().toString());

        LambdaQueryWrapper query = new LambdaQueryWrapper<PickOrderItem>()
                .eq(PickOrderItem::getPickOrderId, pickOrder.getId());
        List<PickOrderItem> pickOrderItems = pickOrderItemMapper.selectList(query);
        QYAssert.isTrue(pickCompleteDTO.getItems().size() == pickOrderItems.size(), "拣货数据异常");

        Map<Long, PickCompleteItemDTO> completeItemMap = pickCompleteDTO.getItems().parallelStream().collect(toMap(PickCompleteItemDTO::getPickOrderItemId, it -> it));

        pickOrderItems.forEach(item -> {
            PickCompleteItemDTO pickCompleteItemDTO = completeItemMap.get(item.getId());
            processPickOrderItemPickQuantityAndNum(item, pickCompleteItemDTO, pickOrder);
        });

        PickOrderContext pickOrderContext = new PickOrderContext();
        pickOrderContext.setPickOrder(pickOrder);
        pickOrderContext.setPickOrderItems(pickOrderItems);
        //京东到家没有commodityId
        if(OrderSourceTypeEnum.JDDJ.getCode().equals(pickOrder.getSourceType())){
            List<Long> commodityIdList = pickCompleteDTO.getItems().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            LambdaQueryWrapper commodityQuery = new LambdaQueryWrapper<Commodity>()
                    .in(Commodity::getId, commodityIdList);
            List<Commodity> commodityList = commodityMapper.selectList(commodityQuery);
            Map<Long, Commodity> commodityMap = commodityList.parallelStream().collect(toMap(Commodity::getId, it -> it));
            pickOrderContext.setCommodityMap(commodityMap);
        }
        //团购订单不发送拣货完成的消息
        if (OrderTypeEnum.GROUP.getCode().equals(pickOrder.getOrderType())
                || OrderTypeEnum.QUICK_GROUPON.getCode().equals(pickOrder.getOrderType())) {
            pickOrder.setPickStatus(PickStatusEnum.FINISH.getCode());
        } else {
            pickOrderContext.doComplete(mqSenderComponent, meiTuanPickOrderService, platFormPickOrderService);
        }
        pickOrder.setPickEndTime(new Date());
        pickOrderMapper.updateById(pickOrder);

        //完成加工点取货位
        completeShelfWork(pickOrder, true);

        //处理库存  出入库给订单单据号
        ImmutablePair idAndCode = new ImmutablePair(pickOrder.getOrderId(), pickOrder.getOrderCode());
        if (!pickOrder.getOrderType().equals(XdOrderTypeEnum.CLOUDXJ.getCode())
                && !pickOrder.getSourceType().equals(OrderSourceTypeEnum.JIULANG.getCode())
                && !pickOrder.getSourceType().equals(OrderSourceTypeEnum.LIQING.getCode())) {
            stockServiceAdapter.stockCompletePick(pickOrder.getOrderId(), pickOrder.getOrderCode(), pickOrder.getWarehouseId(), idAndCode, pickOrderItems, pickOrder.getOrderType());
        }
    }

    @Transactional
    public void processPickOrderItemPickQuantityAndNum(PickOrderItem item, PickCompleteItemDTO pickCompleteItemDTO, PickOrder pickOrder) {

        if(YesOrNoEnums.YES.getCode().equals(item.getIsWeight())){
            int realPickNumber = pickCompleteItemDTO.getPickQuantity().divide(item.getQuantity(), 2, BigDecimal.ROUND_DOWN)
                    .multiply(BigDecimal.valueOf(item.getStockNumber())).setScale( 0, BigDecimal.ROUND_UP ).intValue();
            item.setPickNumber(realPickNumber > item.getStockNumber() ? item.getStockNumber() : realPickNumber);
            item.setPickQuantity(pickCompleteItemDTO.getPickQuantity());
            if(pickOrder.getOrderType() != null && !pickOrder.getOrderType().equals(XdOrderTypeEnum.CLOUDXJ.getCode()) &&
                    !pickOrder.getSourceType().equals(OrderSourceTypeEnum.JIULANG.getCode()) &&
                    !pickOrder.getSourceType().equals(OrderSourceTypeEnum.LIQING.getCode()) &&
                    pickCompleteItemDTO.getPickQuantity().compareTo(item.getQuantity().multiply(new BigDecimal("2"))) >= 0){
                Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "请注意称重品的计量单位为kg,0≤称重品拣货量<订货数量*2");
            }
        }else{
            if(pickCompleteItemDTO.getPickQuantity().compareTo(item.getQuantity()) > 0){
                Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "0≤标品拣货量≤订货数量");
            }
            item.setPickNumber(pickCompleteItemDTO.getPickQuantity().intValue());
            item.setPickQuantity(pickCompleteItemDTO.getPickQuantity());
        }
        pickOrderItemMapper.updateById(item);
    }

    /**
     * 完成加工点取货位
     * @param pickOrder
     * @param ifComplete   true 完成 false 取消
     */
    private void completeShelfWork(PickOrder pickOrder, Boolean ifComplete) {
        //完成加工点取货位
        if(YesOrNoEnums.YES.getCode().equals(pickOrder.getHasProcess())){
            //加工点取货位
            LambdaQueryWrapper workQuery = new LambdaQueryWrapper<PickWorkOrder>()
                    .eq(PickWorkOrder::getPickOrderId, pickOrder.getId());
            List<PickWorkOrder> workOrderList = pickWorkOrderMapper.selectList(workQuery);
            if(SpringUtil.isEmpty(workOrderList)){
                log.warn("订单号 [{}] 处理拣货单时存在加工商品未设置加工点情况 ", pickOrder.getOrderCode());
                return;
            }

            doCompleteShelfWork(ifComplete, workOrderList);
        }
    }

    public void doCompleteShelfWork(Boolean ifComplete, List<PickWorkOrder> workOrderList) {
        if (SpringUtil.isEmpty(workOrderList)) {
            return;
        }

        List<PickWorkOrder> unDoneWorkOrders = workOrderList.stream()
                .filter(item -> WorkOrderStatusEnum.FINISH.getCode() != item.getWorkStatus())
                .collect(Collectors.toList());
        if (ifComplete) {
            QYAssert.isTrue(SpringUtil.isEmpty(unDoneWorkOrders), "加工单还未完成，无法完成");

            String shelfNo = workOrderList.get(0).getShelfNo();
            warehouseShelfService.completeShelfWork(shelfNo);
        } else {
            //把加工单取消  已处理的加工单不管，
            for (PickWorkOrder unDoWorkOrder : unDoneWorkOrders) {
                unDoWorkOrder.setWorkStatus(WorkOrderStatusEnum.CANCEL.getCode());
                pickWorkOrderMapper.updateById(unDoWorkOrder);
            }
            List<PickWorkOrder> todoWorkOrders = workOrderList.stream()
                    .filter(item -> WorkOrderStatusEnum.WAIT.getCode() != item.getWorkStatus())
                    .collect(Collectors.toList());
            if (SpringUtil.isNotEmpty(todoWorkOrders)) {
                String shelfNo = todoWorkOrders.get(0).getShelfNo();
                warehouseShelfService.completeShelfWork(shelfNo);
            }
        }
    }

    /**
     * 拣货单分配  -2D为了，防止仓内无人上班
     */
    @Transactional
    public void distributePickOrderList() {
        try {
            DictionaryODTO dictionary = dictionaryClient.getDictionaryById(DictionaryEnums.WAREHOUSE_DISTRIBUTE.getId());
            configBeginTime = Integer.valueOf(dictionary.getOptionValue());
        } catch (Exception e) {
            log.error("获取分配提前时间失败");
        }
        List<PickOrder> pickOrderList = listWaitDistributePickOrderList(PickingMethodEnum.WHOLE_ORDER_PICKING, configBeginTime, null);

        log.info("拣货单分配：查询到返回内容 -> {} ", JsonUtil.java2json(pickOrderList));
        pickOrderList.forEach(this::distributePickOrder);
    }

    /**
     * 非大店获取WAREHOUSE_DISTRIBUTE提前时间
     * 大店获取BIG_WAREHOUSE_DISTRIBUTE提前时间
     * 大店 尽快配送 特殊逻辑[下单时用户选择尽快配送
     *                      落收货时间=当前时间+(通用设置拣货时长+通用设置配送时长)
     *                      拣货提前时间=收货时间-(通用设置拣货时长+通用设置配送时长)]
     */
    public List<PickOrder> listWaitDistributePickOrderList(PickingMethodEnum pickingMethodEnum, int configBeginTime, String shopIdStr) {
        Date date = new Date();
        Date beginDate = DateUtils.addDays(date, -2);
        Date endDate = DateUtils.addMinutes(date, configBeginTime);
        String beginTime = DateUtil.get4yMdHms(beginDate);
        String endTime = DateUtil.get4yMdHms(endDate);

        BackSetting backSetting = backSettingService.select();
        Date soonEndDate = DateUtils.addMinutes(date, backSetting.getDeliveryTime() + backSetting.getPickingTime());
        String soonEndTime = DateUtil.get4yMdHms(soonEndDate);


        LambdaQueryWrapper<PickOrder> query = new LambdaQueryWrapper<PickOrder>()
                .eq(PickOrder::getPickStatus, PickStatusEnum.WAIT.getCode());

        if (PickingMethodEnum.WHOLE_ORDER_PICKING.equals(pickingMethodEnum)) {
            query.isNull(PickOrder::getPickId);
            query.between(PickOrder::getOrderDeliveryBeginTime, beginTime, endTime)
                    .between(PickOrder::getOrderDeliveryEndTime, beginTime, DateUtil.get4yMdHms(DateUtils.addDays(endDate, 1)));
            return pickOrderMapper.selectList(query);
        } else {
            Long shopId = null;
            if (SpringUtil.hasText(shopIdStr)) {
                shopId = Long.valueOf(shopIdStr);
            }
            return pickOrderMapper.listZoneWaitDistributePickOrderList(beginTime, endTime, soonEndTime, DateUtil.get4yMdHms(DateUtils.addDays(endDate, 1)), shopId);
        }
    }

    /**
     * 拣货单分配
     */
    void distributePickOrder(PickOrder pickOrder) {
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(pickOrder.getWarehouseId());
        FastThreadLocalUtil.setQY(tokenInfo);

        //分配拣货人
        Long picker = warehouseEmployeeService.distributePicker();
        if(picker == null){
            log.warn("拣货单分配: 没有可用的拣货人员");
            return;
        }

        distributePickOrderById(pickOrder, picker);
    }

    /**
     * 查询拣货单
     * @param pickOrderDTO
     * @return
     */
    public MPage<PickOrderListResult> pickOrderList(PickOrderDTO pickOrderDTO) {
//        if (null != pickOrderDTO.getOrderType() && XdOrderTypeEnum.ORDINARY.getCode() == pickOrderDTO.getOrderType()) {
//            pickOrderDTO.setOrderTypeList(Arrays.asList(XdOrderTypeEnum.ORDINARY.getCode(), XdOrderTypeEnum.XD_WAREHOUSE.getCode()));
//            pickOrderDTO.setOrderType(null);
//        }
        return pickOrderMapper.pickOrderList(pickOrderDTO);
    }


    /**
     * 根据id查询拣货单详情
     * @param pickOrderId
     * @return
     */
    public PickOrderListResult pickOrderById(Long pickOrderId) {
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        PickOrderListResult pickOrderListResult = pickOrderMapper.pickOrderById(pickOrderId, warehouseId);
        if (null != pickOrderListResult) {
            Order order = orderMapper.getOneById(pickOrderListResult.getOrderId());
            if (null != order) {
                pickOrderListResult.setReceiveMan(order.getReceiveMan());
                pickOrderListResult.setReceiveMobile(order.getReceiveMobile());
            }
        }
        return pickOrderListResult;
    }

    /**
     * 改派拣货人
     * @param pickChangeDTO
     * @return
     */
    public void changePicker(PickChangeDTO pickChangeDTO) {
        PickOrder pickOrder = pickOrderMapper.selectById(pickChangeDTO.getPickOrderId());
        // (待拣货 或者 拣货中 状态) 且 已分配了拣货人 才允许改派
        boolean canChange = (pickOrder.getPickStatus().equals(PickStatusEnum.MIDDLE.getCode()) || pickOrder.getPickStatus().equals(PickStatusEnum.WAIT.getCode()))
                && null != pickOrder.getPickId();
        QYAssert.isTrue( canChange , "拣货单不在拣货中，无需改派拣货人");

        log.warn("改派拣货人,订单号{}, 从{} --> 改为{}", pickOrder.getOrderCode(), pickOrder.getPickId(), pickChangeDTO.getPickId());
        pickOrder.setPickId(pickChangeDTO.getPickId());
        pickOrderMapper.updateById(pickOrder);
    }

    /**
     * 根据订单ids查询拣货单
     * @param orderIds
     * @return
     */
    public List<PickOrderResult> selectPickByOrderId(List<Long> orderIds) {
        return pickOrderMapper.selectPickByOrderId(orderIds);
    }

    public PickOrderResult getPickOrderByOrderId(Long orderId) {
        return pickOrderMapper.getPickOrderByOrderId(orderId);
    }

    /**
     * 根据订单id查询拣货单
     * @param orderId
     * @return
     */
    public PickOrderMqDTO pickOrderByOrderId(Long orderId) {
        return pickOrderMapper.selectPickOrderByOrderId(orderId);
    }

    /**
     * 根据拣货人查询待件货和拣货中的数量
     * @param list
     * @return
     */
    public List<TaskNumDTO> pickNum(List<Long> list) {
        return pickOrderMapper.pickNum(list);
    }

    /**
     * 获取拣货单预警时间
     * @return
     */
    public PickSettingDTO queryPickSetting() {
        PickSettingDTO res = null;
        BackSetting backSetting = backSettingService.select();
        if (null != backSetting) {
            res = new PickSettingDTO();
            res.setDistributionBeginWarning(backSetting.getDistributionBeginWarning());
            res.setDistributionEndWarning(backSetting.getDistributionEndWarning());
        }

        // 是否显示"开始接单/暂停接单"
        Long employeeId = FastThreadLocalUtil.getQY().getEmployeeId();
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseEmployee>()
                .eq(WarehouseEmployee::getWarehouseId, shopId)
                .eq(WarehouseEmployee::getEmployeeId, employeeId)
                .eq(WarehouseEmployee::getType, WarehouseEmployeeTypeEnum.PICK.getCode());
        WarehouseEmployee warehouseEmployee = employeeMapper.selectOne(query);
        boolean isShowOrderButton = false;
        if (warehouseEmployee != null) {
            isShowOrderButton = true;
        }
        res.setIsShowOrderButton(isShowOrderButton);
        return res;
    }

    public Boolean stockOutRelationLog(Long orderId) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<PickOrder>()
                .eq(PickOrder::getOrderId, orderId);
        PickOrder pickOrder = pickOrderMapper.selectOne(query);
        if (null != pickOrder) {
            QYAssert.isTrue(pickOrder.getPickStatus() == PickStatusEnum.MIDDLE.getCode(), "状态异常");

            LambdaQueryWrapper stockOutRelationQuery = new LambdaQueryWrapper<StockOutRelationLog>()
                    .eq(StockOutRelationLog::getOrderCode, pickOrder.getOrderCode());
            Integer count = stockOutRelationLogMapper.selectCount(stockOutRelationQuery);
            QYAssert.isTrue(count <= 0, "请不要重复点击");

            StockOutRelationLog stockOutRelationLog = new StockOutRelationLog();
            stockOutRelationLog.setOrderId(pickOrder.getOrderId());
            stockOutRelationLog.setOrderCode(pickOrder.getOrderCode());
            stockOutRelationLog.setCreate_id(StockUtils.INSTANCE.userId());
            stockOutRelationLogMapper.insert(stockOutRelationLog);
        }
        return Boolean.TRUE;
    }

    public void updateStockOutStatus(Long orderId, String orderCode, List<OrderItemDTO> commodityList){
        //查询冻结数量
        LambdaQueryWrapper query = new LambdaQueryWrapper<StockFreezeLog>()
                .eq(StockFreezeLog::getReferCode, orderCode)
                .ge(StockFreezeLog::getQuantity, 0);
        List<StockFreezeLog> freezeList = stockFreezeLogMapper.selectList(query);

        int commodityReduce = commodityList.stream().map(OrderItemDTO::getStockNumber).reduce(0, (a, b) -> a + b);
        BigDecimal freezeReduceDec = freezeList.stream().map(StockFreezeLog::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        int freezeReduce = freezeReduceDec.intValue();
        if(commodityReduce == freezeReduce){
           return;
        }

        PickOrder pickOrder = new PickOrder();
        pickOrder.setStockOutStatus(YesOrNoEnums.YES.getCode());
        LambdaUpdateWrapper<PickOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(PickOrder::getOrderId, orderId);
        pickOrderMapper.update(pickOrder, updateWrapper);
    }

    /**
     * 分配拣货
     * @param pickOrderId
     * @param picker
     * @return
     */
    public void distributePickOrderSingle(Long pickOrderId, Long picker){
        PickOrder pickOrder = pickOrderMapper.selectById(pickOrderId);
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(pickOrder.getWarehouseId());
        FastThreadLocalUtil.setQY(tokenInfo);
        distributePickOrderById(pickOrder, picker);
    }

    private void distributePickOrderById(PickOrder pickOrder, Long picker){

        String pickShelfNo = warehouseShelfService.distributeShelfDelivery();
        if(pickShelfNo == null){
            pickShelfNo = INIT_SHELF_NO;
        }
        PickOrder checkPickOrder = pickOrderMapper.selectById(pickOrder.getId());
        if(PickStatusEnum.CANCEL.getCode() == checkPickOrder.getPickStatus()){
            return;
        }

        pickOrder.setPickId(picker);
        pickOrder.setPickBeginTime(new Date());
        pickOrder.setShelfNo(pickShelfNo);
        // 开始拣货状态才更新为拣货中
//        PickOrderContext pickOrderContext = new PickOrderContext();
//        pickOrderContext.setPickOrder(pickOrder);
//        pickOrderContext.doPick(kafkaTemplate);
          pickOrderMapper.updateById(pickOrder);
        //激光推送新订单提醒消息
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTypeEnum(AppMessageTypeEnum.ORDER);
        messageDTO.setSendScope(1);
        messageDTO.setTitle("来新订单了，请注意查收！");
        messageDTO.setContent("NEW-PICK-ORDER");
        Long userId = employeeMapper.getUserId(picker);
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        messageDTO.setUserIds(userIds);
        messageDTO.setTargetUrl("来新订单了，请注意查收！");
        zSMessageClient.sendMessage(messageDTO);

        //分配加工点取货位
        LambdaQueryWrapper query = new LambdaQueryWrapper<PickWorkOrder>()
                .eq(PickWorkOrder::getPickOrderId, pickOrder.getId());
        List<PickWorkOrder> workOrderList = pickWorkOrderMapper.selectList(query);

        if(SpringUtil.isNotEmpty(workOrderList)){
            String workShelfNo = warehouseShelfService.distributeShelfWork();
            if(workShelfNo == null){
                workShelfNo = INIT_SHELF_NO;
            }
            pickWorkOrderMapper.distributeShelfNo(pickOrder.getId(), workShelfNo);
        }
    }

    @Transactional
    public Boolean beginPickOrder(Long pickOrderId) {

        redisLockService.lock(RedisLockEnums.BEGIN_PICK_ORDER, pickOrderId.toString(), () -> {
            PickOrder pickOrder = pickOrderMapper.selectById(pickOrderId);
            QYAssert.isTrue(null != pickOrder, "拣货单不存在");
            Order order = orderMapper.selectById(pickOrder.getOrderId());
            if (null != order && order.getOrderStatus().equals(XdOrderStatusEnum.CANCEL.getCode())) {
                throw new BizLogicException("订单已取消", ApiErrorCodeEnum.XD_PICK_CANCEL);
            }
            //只有待拣货的才能开始拣货(发消息变更订单状态，更新拣货单)
            if (pickOrder.getPickStatus().equals(PickStatusEnum.WAIT.getCode())) {
                PickOrderContext pickOrderContext = new PickOrderContext();
                Date pickBeginTime = new Date();
                pickOrder.setPickBeginTime(pickBeginTime);
                pickOrderContext.setPickOrder(pickOrder);
                pickOrderContext.doPick(mqSenderComponent, meiTuanPickOrderService);

                LambdaUpdateWrapper<PickOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(PickOrder::getPickBeginTime, pickBeginTime)
                        .set(PickOrder::getPickStatus, pickOrder.getPickStatus())
                        .eq(PickOrder::getId, pickOrder.getId())
                        .eq(PickOrder::getPickStatus, PickStatusEnum.WAIT.getCode());
                pickOrderMapper.update(null, updateWrapper);
            }
        });

        return Boolean.TRUE;
    }

    /**
     * 去除换行符
     * @param code
     * @return
     */
    private String replace(String code){
        Pattern p = Pattern.compile("\\s*|\t|\r|\n");
        Matcher m = p.matcher(code);
        code = m.replaceAll("");
        return code;
    }

//    /**
//     * 本地查询新订单
//     * @return
//     */
//    public Integer queryNewOrderCount(String shopCode) {
//        shopCode = replace(shopCode);
//        Shop shop = shopMapper.selectOne(new LambdaQueryWrapper<Shop>().select(Shop::getId).eq(Shop::getShopCode, shopCode));
//        QYAssert.isTrue(shop != null, "本地查询新订单，门店编码不存在" + shopCode);
//        return pickOrderMapper.queryNewOrderCount(shop.getId(), DateUtil.getDateFormate(new Date(),"yyyy-MM-dd"));
//    }

//    /**
//     * 本地exe查询拣货单:预约到货时间=T t1~t2，且t≥t1-1hour——说明订单已进入该拣货的时间点
//     * 本地exe查询待配送单
//     * @param shopCode
//     * @return
//     */
//    public Integer queryWaitDealOrderCount(String shopCode) {
//        shopCode = replace(shopCode);
//        Shop shop = shopMapper.selectOne(new LambdaQueryWrapper<Shop>().select(Shop::getId).eq(Shop::getShopCode, shopCode));
//        QYAssert.isTrue(shop != null, "本地查询拣货单配送单，门店编码不存在" + shopCode);
//
//
//        String yyyyMMdd = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
//        Integer pickCount = pickOrderMapper.queryPickOrderCount(shop.getId(), yyyyMMdd + " 00:00:00", yyyyMMdd + " 23:59:59");
//
//        Date now = new Date();
//        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(now, -7), "yyyy-MM-dd");
//        String endTime = DateUtil.getDateFormate(DateUtil.addDay(now, 1), "yyyy-MM-dd");
//        Integer deliveryCount =  pickOrderMapper.queryDeliveryOrderCount(shop.getId(), beginTime, endTime, Arrays.asList(XdOrderTypeEnum.ORDINARY.getCode(), XdOrderTypeEnum.XD_WAREHOUSE.getCode()));
//        return pickCount + deliveryCount;
//    }

    public List<Long> queryWaitDealJsdOrder(List<Long> shopIds) {
        String yyyyMMdd = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        List<Long> pickShopIds = pickOrderMapper.queryPickOrderList(shopIds, yyyyMMdd + " 00:00:00", yyyyMMdd + " 23:59:59", Arrays.asList(XdOrderTypeEnum.ORDINARY.getCode(),XdOrderTypeEnum.FARM.getCode(), XdOrderTypeEnum.XD_WAREHOUSE.getCode()));

        Date now = new Date();
        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(now, -7), "yyyy-MM-dd");
        String endTime = DateUtil.getDateFormate(DateUtil.addDay(now, 1), "yyyy-MM-dd");
        List<Long> deliveryShopIds =  pickOrderMapper.queryDeliveryOrder(shopIds, beginTime, endTime, Arrays.asList(XdOrderTypeEnum.ORDINARY.getCode(),XdOrderTypeEnum.FARM.getCode(), XdOrderTypeEnum.XD_WAREHOUSE.getCode()), null);

        List<Long> jsdShopIds = new ArrayList<>();
        for (Long shopId : shopIds) {
            if (pickShopIds.contains(shopId) || deliveryShopIds.contains(shopId)) {
                jsdShopIds.add(shopId);
            }
        }
        return jsdShopIds;
    }

    public List<Long> queryThirdOrder(List<Long> shopIds) {
        String yyyyMMdd = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        List<Long> pickShopIds = pickOrderMapper.queryPickOrderList(shopIds, yyyyMMdd + " 00:00:00", yyyyMMdd + " 23:59:59", Arrays.asList(XdOrderTypeEnum.THIRD.getCode()));

        //美团，饿了么，京东到家不需要我们自己配送  玖琅需要我们自己配送
        Date now = new Date();
        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(now, -7), "yyyy-MM-dd");
        String endTime = DateUtil.getDateFormate(DateUtil.addDay(now, 1), "yyyy-MM-dd");
        List<Integer> sourceTypeList = Arrays.asList(OrderSourceTypeEnum.JIULANG.getCode(), OrderSourceTypeEnum.LIQING.getCode());
        List<Long> deliveryShopIds =  pickOrderMapper.queryDeliveryOrder(shopIds, beginTime, endTime, Arrays.asList(XdOrderTypeEnum.THIRD.getCode()),sourceTypeList);

        List<Long> thirdShopIds = new ArrayList<>();
        for (Long shopId : shopIds) {
            if (pickShopIds.contains(shopId) || deliveryShopIds.contains(shopId)) {
                thirdShopIds.add(shopId);
            }
        }
        return thirdShopIds;
    }

    public List<Long> waitCloudOrder(List<Long> shopIds) {
        String receiveBeginTime =  DateUtil.get4yMdHms(DateUtil.addHour(new Date(), 1));
        String date = DateUtil.get4yMd(new Date());
        //查询待处理的云超订单
        List<Order> cloudOrders = orderMapper.waitCloudOrder(date, shopIds, receiveBeginTime);
        List<Long> cloudShopIds = new ArrayList<>();
        if (null != cloudOrders && cloudOrders.size() > 0) {
            cloudShopIds = cloudOrders.stream().map(Order::getShopId).distinct().collect(Collectors.toList());
        }
        //查询云超配送单
        Date now = new Date();
        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(now, -7), "yyyy-MM-dd");
        String endTime = DateUtil.getDateFormate(DateUtil.addDay(now, 1), "yyyy-MM-dd");
        List<Long> deliveryShopIds = pickOrderMapper.queryDeliveryOrder(shopIds, beginTime, endTime, Arrays.asList(OrderTypeEnum.CLOUD.getCode()), null);

        List<Long> waitCloudShopIds = new ArrayList<>();
        for (Long shopId : shopIds) {
            if (deliveryShopIds.contains(shopId) || cloudShopIds.contains(shopId)) {
                waitCloudShopIds.add(shopId);
            }
        }
        return waitCloudShopIds;
    }

    public Map<Long, Boolean> getShopPickOrderExistsMap(QueryPickOrderDTO queryPickOrderDTO) {
        QYAssert.notNull(queryPickOrderDTO,"参数不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(queryPickOrderDTO.getWarehouseIdList()),"门店List不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(queryPickOrderDTO.getPickStatusList()),"拣货单状态List不能为空");

        Date now = new Date();
        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(now,-30), DateTimeUtil.YYYY_MM_DD)+ " 00:00:00";
        String endTime = DateUtil.getDateFormate(now, DateTimeUtil.YYYY_MM_DD)+ " 23:59:59";

        LambdaQueryWrapper<PickOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PickOrder::getWarehouseId,queryPickOrderDTO.getWarehouseIdList());
        queryWrapper.in(PickOrder::getPickStatus,queryPickOrderDTO.getPickStatusList());
        queryWrapper.between(PickOrder::getCreateTime,beginTime,endTime);
        List<PickOrder> pickOrders = pickOrderMapper.selectList(queryWrapper);
        return pickOrders.stream()
                .collect(Collectors.toMap(
                        PickOrder::getWarehouseId,
                        pickOrder -> true,
                        (existing, replacement) -> true
                ));

    }

    public void orderCancelPickOrder(Long orderId) {
        this.baseMapper.orderCancelPickOrder(orderId);
    }


    /**
     * 修改订单的打包人id和打包结束时间
     *
     * @param orderId
     * @param packingId
     */
    public void updatePackingIdAndEndTimeByOrderId(Long orderId, Long packingId) {
        this.baseMapper.updatePackingIdAndEndTimeByOrderId(orderId, packingId);
    }
}