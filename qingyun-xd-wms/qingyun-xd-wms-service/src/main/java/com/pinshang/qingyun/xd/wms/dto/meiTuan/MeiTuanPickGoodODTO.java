package com.pinshang.qingyun.xd.wms.dto.meiTuan;

import lombok.Data;

/**
 * @Author: sk
 * @Date: 2023/7/17
 */
@Data
public class MeiTuanPickGoodODTO {

    /** 原app_food_code字段）APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符 */
    private String app_spu_code;

    /** 商品sku唯一标识码。 如为单规格商品，可以不传此参数；如为多规格商品，必须上传要退款商品sku的sku_id。 */
    private String sku_id;

    /**
     * 订单内商品行维度的商品标识id。 注意：若商品参加活动，对商品发起退差价时，
     * 请注意一定要上传item_id参数（可通过非称重商品：order/getPartRefundFoods
     * 或称重商品：order/getUnitPartRefundFoods接口获取）。
     * 关于item_id参数的重要作用，请参考发起部分退款接口优化的说明。
     * */
    private String item_id;

    /**
     * 本次退款的商品数量：(1)当按件部分退款时，此字段必填，传入需退款商品sku的数量，为大于0的整数。
     * (2)当part_refund_type=3时，即按克重退差价，则此字段非必填(如传了也不会生效)。
     */
    private float count;

    /**
     * 商品sku的实拣重量，单位是克/g，支持1位小数。
     * (1)此字段信息上传本次退差价商品sku单件的实际拣货重量。
     * (2)上传的实拣重量需小于商品sku的标价重量(同步商品时维护的重量信息)，且不能为负数。
     * (3)如有多件商品sku需要退差价，需分别调用此接口录入每件商品sku的实拣重量信息分别发起退差价流程。
     * (4)当part_refund_type=3时，即按克重退差价，actual_weight字段必填；如part_refund_type不传或所传信息不为3时，即按件部分退款，不传actual_weight字段。
     */
    private double actual_weight;
}
