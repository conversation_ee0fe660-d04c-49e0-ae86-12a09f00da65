package com.pinshang.qingyun.xd.wms.dto.volcano;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 门店库存处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VolcanoStockIDTO {
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "商品list")
    private List<StockItemDTO> commodityList;

    @ApiModelProperty(value = "销售类型:SaleTypeEnum 1 销售 2退货")
    private Integer saleType;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    public void checkData() {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        QYAssert.isTrue(referId != null, "关联单号id不能为空");
        QYAssert.isTrue(referCode != null, "关联单号code不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");
    }
}