package com.pinshang.qingyun.xd.wms.vo.bigShop;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
public class PickPartitionOrderPageVO {

    /**
     * 分区拣货单id
     */
    private Long pickPartitionOrderId;

    /**
     * 拣货分区id
     */
    private Long pickAreaId;


    /**
     * 拣货子单编码
     */
    private String pickPartitionOrderCode;


    /**
     * 订单短号
     */
    private String orderNum;

    /**
     * 打包口id
     */
    private Long packingStationId;

    /**
     * 门店拣货单状态  0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消
     */
    private Integer pickOrderStatus;

    private Integer pickPartitionOrderStatus;

    /**
     * 拣货人id
     */
    private Long pickId;

    /**
     * 客户要求配送开始时间
     */
    private Date orderDeliveryBeginTime;

    /**
     * 缺货处理方式
     */
    private Integer lackProcessMode;

    /**
     * 收货人手机
     */
    private String receiveMobile;

}
