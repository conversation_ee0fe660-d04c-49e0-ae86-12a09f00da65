package com.pinshang.qingyun.xd.wms.mapper.bigShop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaIDTO;
import com.pinshang.qingyun.xd.wms.model.bigShop.Area;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdReplenishmentTask;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Repository
public interface AreaMapper extends BaseMapper<Area> {


    MPage<AreaDTO> areaList(@Param("dto") AreaIDTO dto);

    List<Area> getAreaListByKeyWord(@Param("shopId") Long shopId, @Param("keyWord") String keyWord);

}