package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * 仓库货位表
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseShelfDTO extends BaseEntity {

    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "货位属性:1-拣货位,2-取货位")
    private Integer type;

    @ApiModelProperty(value = "货位状态:0-停用(没有绑定货可停用),1-启用")
    private Integer status;

    public void checkData() {
        QYAssert.isTrue(!StringUtils.isEmpty(shelfNo.trim()), "货位编号不能为空");
        QYAssert.isTrue(status != null, "货位状态不能为空");
        QYAssert.isTrue(type != null, "货位属性不能为空");
        shelfNo = shelfNo.trim();
    }

}
