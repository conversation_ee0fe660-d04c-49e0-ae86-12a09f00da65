package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.RegExpUtil;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseEmployeeDTO {

    @ApiModelProperty("员工id，不能为空")
    private Long employeeId;

    @ApiModelProperty("员工类型,不能为空")
    private WarehouseEmployeeTypeEnum type;

    @ApiModelProperty("手机号码，配送员不能为空")
    private String employeePhone;

    public void checkData() {
        QYAssert.isTrue(employeeId != null, "员工id不能为空");
        QYAssert.isTrue(type != null, "员工类型不能为空");
        if (WarehouseEmployeeTypeEnum.DELIVERY.compareTo(type) == 0) {
            QYAssert.isTrue(!StringUtils.isEmpty(employeePhone), "配送员手机号不能为空");
//            QYAssert.isTrue(RegExpUtil.checkPhone(employeePhone), "配送员手机号不合法");
        }
    }
}
