package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.shop.dto.MdShopStatusODTO;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.xd.wms.bo.PartitionPickDistributePackingStationBO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdPackingStationMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPackingOrder;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPackingStation;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RPriorityQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单打包口管理表  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Slf4j
@Service
public class DdPackingStationService extends ServiceImpl<DdPackingStationMapper, DdPackingStation> {

    @Autowired
    private DdPackingOrderService ddPackingOrderService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ShopStatusClient shopStatusClient;

    private static final String PACKING_PORT_REGEX = "^[a-zA-Z0-9]{1,20}$";

    private static final String ACQUIRE_PACKING_PORT_V2 = "WMS:ACQUIRE_PACKING_PORT_V2:";

    @Autowired
    private RedisLockService redisLockService;


    public DdPackingStation queryPackingStationById(Long shopId, Long id) {
        return this.getOne(new LambdaQueryWrapper<DdPackingStation>().eq(DdPackingStation::getShopId, shopId).eq(DdPackingStation::getId, id));
    }

    /**
     * 订单打包口管理表 列表
     */
    public PageInfo<DdPackingStationODTO> page(DdPackingStationPageIDTO req) {
        req.setShopId(FastThreadLocalUtil.getQY().getShopId());
        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> baseMapper.list(req));
    }

    /**
     * 保存 订单打包口管理表
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(List<DdPackingStationSaveIDTO> req) {
        // 参数校验
        validateRequest(req);

        // 批量保存
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        List<DdPackingStation> list = req.stream().map(item -> {
            DdPackingStation ddPackingStation = new DdPackingStation();
            ddPackingStation.setShopId(shopId);
            ddPackingStation.setPackingPort(item.getPackingPort());
            ddPackingStation.setStatus(item.getStatus());
            ddPackingStation.setCreateId(FastThreadLocalUtil.getQY().getUserId());
            return ddPackingStation;
        }).collect(Collectors.toList());
        this.saveBatch(list);

        // 设置启用的到队列中
        for (DdPackingStation ddPackingStation : list) {
            if (Objects.equals(YesOrNoEnums.YES.getCode(), ddPackingStation.getStatus())) {
                addOrUpdatePackingStation(shopId, ddPackingStation.getId(), ddPackingStation.getPackingPort());
            }
        }

        return Boolean.TRUE;
    }

    private void validateRequest(List<DdPackingStationSaveIDTO> req) {
        QYAssert.notEmpty(req, "参数不能为空");
        req.forEach(idto -> {
            QYAssert.isTrue(Pattern.compile(PACKING_PORT_REGEX).matcher(idto.getPackingPort()).matches(), "打包口只能输入字母或数字");
            QYAssert.notNull(idto.getStatus(), "状态不能为空");
        });
        // 校验入参中有没有重复的打包口名称
        List<String> duplicatePackingPort = findDuplicates(req.stream().map(DdPackingStationSaveIDTO::getPackingPort).collect(Collectors.toList()));
        QYAssert.isTrue(CollectionUtils.isEmpty(duplicatePackingPort), "打包口" + String.join(",", duplicatePackingPort) + "重复");

        // 校验与数据库中有没有重复的打包口名称
        List<DdPackingStation> ddPackingStations = listByPackingPorts(FastThreadLocalUtil.getQY().getShopId(), null, req.stream().map(DdPackingStationSaveIDTO::getPackingPort).collect(Collectors.toList()));
        ddPackingStations.forEach(packingStation -> QYAssert.isTrue(false, "打包口" + packingStation.getPackingPort() + "重复"));
    }

    /**
     * 更新 订单打包口管理表
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(DdPackingStationUpdateIDTO req) {
        QYAssert.notNull(req.getId(), "id不能为空");
        QYAssert.notNull(req.getStatus(), "状态不能为空");
        DdPackingStation station = this.getById(req.getId());
        QYAssert.notNull(station, "打包口不存在");
        QYAssert.isTrue(!Objects.equals(station.getStatus(), req.getStatus()), "打包口已" + XdPickOrderStatusEnum.getByCode(req.getStatus()).getRemark());

        // 当状态为停用时，校验打包口是否为空闲
        if (Objects.equals(YesOrNoEnums.NO.getCode(), req.getStatus())) {
            List<DdPackingStationOrderODTO> orderList = ddPackingOrderService.listPackingOrderByPackingId(FastThreadLocalUtil.getQY().getShopId(), req.getId());
            QYAssert.isTrue(CollectionUtils.isEmpty(orderList), "占用状态的打包口不可操作停用");
        }

        // 更新状态
        DdPackingStation packingStation = new DdPackingStation();
        packingStation.setId(req.getId());
        packingStation.setStatus(req.getStatus());
        this.updateById(packingStation);

        // 获取队列
        if (Objects.equals(YesOrNoEnums.YES.getCode(), req.getStatus())) {
            // 设置到队列中
            addOrUpdatePackingStation(FastThreadLocalUtil.getQY().getShopId(), req.getId(), station.getPackingPort());
        } else {
            // 移除队列
            removePackingStation(FastThreadLocalUtil.getQY().getShopId(), req.getId());
        }

        return Boolean.TRUE;
    }

    /**
     * 打包口 订单占用明细
     */
    public List<DdPackingStationOrderODTO> detail(Long packingStationId) {
        return ddPackingOrderService.listPackingOrderByPackingId(FastThreadLocalUtil.getQY().getShopId(), packingStationId);
    }

    /**
     * 查询门店所有打包口
     *
     * @param shopId       门店ID
     * @param packingPorts 打包口编码列表
     */
    public List<DdPackingStation> listByPackingPorts(Long shopId, Integer status, List<String> packingPorts) {
        LambdaQueryWrapper<DdPackingStation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DdPackingStation::getShopId, shopId)
                .eq(Objects.nonNull(status), DdPackingStation::getStatus, status)
                .in(CollectionUtils.isNotEmpty(packingPorts), DdPackingStation::getPackingPort, packingPorts);
        return this.list(wrapper);
    }

    public Map<Long, DdPackingStation> mapByIds(Collection<Long> packingStationIds) {
        if (SpringUtil.isEmpty(packingStationIds)) {
            return Collections.emptyMap();
        }

        Collection<DdPackingStation> ddPackingStations = this.listByIds(packingStationIds);

        if (SpringUtil.isEmpty(ddPackingStations)) {
            return Collections.emptyMap();
        }

        return ddPackingStations.stream().collect(Collectors.toMap(DdPackingStation::getId, Function.identity()));
    }

    /**
     * 获取指定门店的优先队列
     */
    private RPriorityQueue<PartitionPickDistributePackingStationBO> getQueue(Long shopId) {
        String redisKey = ACQUIRE_PACKING_PORT_V2 + shopId;
        return redissonClient.getPriorityQueue(redisKey);
    }

    /**
     * 新增或更新打包口
     */
    public void addOrUpdatePackingStation(Long shopId, Long packingStationId, String packingPort) {
        log.warn("新增或更新打包口队列：shopId={}, packingStationId={}, packingPort={}", shopId, packingStationId, packingPort);
        redisLockService.lock(RedisLockEnums.ACQUIRE_PACKING_PORT_V2, shopId.toString(), () -> {
            RPriorityQueue<PartitionPickDistributePackingStationBO> queue = getQueue(shopId);

            PartitionPickDistributePackingStationBO station = new PartitionPickDistributePackingStationBO();
            station.setPackingStationId(packingStationId);
            station.setPackingPort(packingPort);

            Integer packingPortOrderCount = ddPackingOrderService.packingPortOrderCount(shopId, packingStationId);
            station.setPackingPortOrderCount(packingPortOrderCount);

            // 移除所有相同 packingPort 的对象
            queue.removeIf(item -> packingStationId.equals(item.getPackingStationId()));
            // 重新入队
            queue.add(station);
            log.warn("新增或更新打包口队列成功：shopId={}, packingStationId={}, packingPort={}", shopId, packingStationId, packingPort);
        });
    }

    /**
     * 分配订单给最优打包口（取出 -> 更新 -> 重新入队）
     */
    public PartitionPickDistributePackingStationBO allocateOrderToBestStation(Long shopId) {
        MdShopStatusODTO shopStatusByShopId = shopStatusClient.getShopStatusByShopId(shopId);

        return redisLockService.lock(RedisLockEnums.ACQUIRE_PACKING_PORT_V2, shopId.toString(), () -> {
            RPriorityQueue<PartitionPickDistributePackingStationBO> queue = getQueue(shopId);
            PartitionPickDistributePackingStationBO best = queue.peek();

            if (Objects.nonNull(best)) {
                // 打包口不能被多个订单占用
                if (!YesOrNoEnums.YES.getCode().equals(shopStatusByShopId.getPackingPortMultipleOrder()) && best.getPackingPortOrderCount() > 0) {
                    QYAssert.isFalse("打包口已被其他订单占用");
                }

                // 从队列中弹出头元素
                queue.poll();
                // 更新状态
                best.setPackingPortOrderCount(best.getPackingPortOrderCount() + 1);

                // 更新后重新入队，维持优先级排序
                queue.offer(best);
            }
            return best;
        });
    }

    /**
     * 删除指定打包口
     */
    public void removePackingStation(Long shopId, Long packingStationId) {
        RPriorityQueue<PartitionPickDistributePackingStationBO> queue = getQueue(shopId);
        queue.removeIf(item -> packingStationId.equals(item.getPackingStationId()));
        log.warn("删除打包口队列成功：shopId={}, packingStationId={}", shopId, packingStationId);
    }

    /**
     * 释放打包口
     *
     * @param shopId
     * @param packingStationId
     */
    public void releasePackingStation(Long shopId, Long packingStationId) {
        log.warn("释放打包口队列：shopId={}, packingStationId={}", shopId, packingStationId);
        redisLockService.lock(RedisLockEnums.ACQUIRE_PACKING_PORT_V2, shopId.toString(), () -> {

            RPriorityQueue<PartitionPickDistributePackingStationBO> queue = getQueue(shopId);
            PartitionPickDistributePackingStationBO target = null;
            for (PartitionPickDistributePackingStationBO item : queue) {
                if (Objects.equals(item.getPackingStationId(), packingStationId)) {
                    target = item;
                    break;
                }
            }
            if (Objects.nonNull(target)) {
                queue.remove(target);
                int latestOrderCount = target.getPackingPortOrderCount() - 1;
                if (latestOrderCount < 0) {
                    log.warn("释放打包口队列完成,无任务,shopId:[{}],packingStationId:[{}],packingPort:[{}],packingPortOrderCount:[{}],latestOrderCount:[{}]", shopId, packingStationId, target.getPackingPort(), target.getPackingPortOrderCount(), latestOrderCount);
                    return;
                }
                target.setPackingPortOrderCount(target.getPackingPortOrderCount() - 1);
                queue.add(target);
                log.warn("释放打包口队列成功：shopId={}, packingStationId={},最新任务数量:[{}]", shopId, packingStationId, target.getPackingPortOrderCount());
            }
        });
    }


    @Transactional
    public Long distributePackingStation(Long shopId, Long orderId) {
        QYAssert.notNull(shopId, "门店id不能为空");
        QYAssert.notNull(orderId, "订单id不能为空");

        // 空的话先初始化
        initPackingPortQueueIfEmpty(shopId);

        // 分配最佳打包口
        PartitionPickDistributePackingStationBO station = allocateOrderToBestStation(shopId);

        // 占用打包口
        Long packingStationId = station.getPackingStationId();
        DdPackingOrder save = new DdPackingOrder();
        save.setShopId(shopId);
        save.setPackingStationId(packingStationId);
        save.setOrderId(orderId);
        ddPackingOrderService.save(save);
        return packingStationId;
    }

    private void initPackingPortQueueIfEmpty(Long shopId) {
        RPriorityQueue<PartitionPickDistributePackingStationBO> queue = getQueue(shopId);
        if (Objects.isNull(queue) || queue.isEmpty()) {
            log.warn("=======initPackingPortQueueIfEmpty============shopId=" + shopId);
            List<DdPackingStation> ddPackingStationList = this.list(
                    new LambdaQueryWrapper<DdPackingStation>()
                            .eq(DdPackingStation::getShopId, shopId)
                            .eq(DdPackingStation::getStatus, YesOrNoEnums.YES.getCode())
                            .orderByAsc(DdPackingStation::getId));

            QYAssert.notEmpty(ddPackingStationList, "没有可用打包口");

            ddPackingStationList.forEach(it -> {
                addOrUpdatePackingStation(shopId, it.getId(), it.getPackingPort());
            });
            log.warn("=======initPackingPortQueueIfEmpty============shopId=" + shopId + "  size=" + ddPackingStationList.size());
        }
    }

    public List<DdPackingStation> list(List<Long> ids) {
        if (SpringUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return new ArrayList<>(this.listByIds(ids));
    }

    /**
     * 查找集合中的重复元素
     *
     * @param list 待检查的集合
     * @return 重复元素列表
     */
    public static <T> List<T> findDuplicates(List<T> list) {
        return list.stream()
                // 按元素分组计数
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()))
                .entrySet().stream()
                // 找出计数大于 1 的元素
                .filter(entry -> entry.getValue() > 1)
                // 提取重复的元素
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
}

