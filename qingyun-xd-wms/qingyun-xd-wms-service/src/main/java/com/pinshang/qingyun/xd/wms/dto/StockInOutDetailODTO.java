package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 库存出入库详情DTO
 */
@NoArgsConstructor
@AllArgsConstructor
public class StockInOutDetailODTO {

    @ApiModelProperty(value = "详情主体")
    private StockInOutItemODTO item;

    @ApiModelProperty(value = "详情列表")
    private List<StockInOutDetailListDTO> itemList;

    public StockInOutItemODTO getItem() {
        return item;
    }

    public void setItem(StockInOutItemODTO item) {
        this.item = item;
    }

    public List<StockInOutDetailListDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<StockInOutDetailListDTO> itemList) {
        this.itemList = itemList;
    }

    @Override
    public String toString() {
        return "StockInOutDetailODTO{" +
                "item=" + item +
                ", itemList=" + itemList +
                '}';
    }
}