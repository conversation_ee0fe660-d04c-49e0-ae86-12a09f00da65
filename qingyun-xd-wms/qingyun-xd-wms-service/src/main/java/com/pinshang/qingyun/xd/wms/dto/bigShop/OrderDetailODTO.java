package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("OrderDetailODTO")
public class OrderDetailODTO implements Serializable {

    private static final long serialVersionUID = 5704399123828956162L;

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "订单短号")
    private Long orderNum;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "打包口")
    private String packingPort;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStatusName;

    @ApiModelProperty(value = "分区拣货子单详情列表")
    private List<PartitionPickOrderDetailODTO> pickOrderDetails;

    @ApiModelProperty(value = "是否显示打包完成")
    private Boolean showPackingComplete = Boolean.FALSE;

    @ApiModelProperty(value = "是否显示打印小票")
    private Boolean showPrintTicket = Boolean.FALSE;

    public String getOrderStatusName() {
        return XdOrderStatusEnum.getName(orderStatus);
    }
}
