package com.pinshang.qingyun.xd.wms.event;

import com.pinshang.qingyun.xd.wms.model.bigShop.DdPickPartitionOrder;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString(callSuper = true)
public class HandoverDeliveryEvent extends ApplicationEvent {

    private static final long serialVersionUID = -5249347061933453479L;

    private List<DdPickPartitionOrder> ddPickPartitionOrders;

    public HandoverDeliveryEvent(Object source) {
        super(source);
    }

    public List<DdPickPartitionOrder> getDdPickPartitionOrders() {
        return ddPickPartitionOrders;
    }

    public void setDdPickPartitionOrders(List<DdPickPartitionOrder> ddPickPartitionOrders) {
        this.ddPickPartitionOrders = ddPickPartitionOrders;
    }

    public Long getPickOrderId() {
        return (Long) this.getSource();
    }

}
