package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 调拨申请商品信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderItemApplyDTO {
    @ApiModelProperty(value = "商品id")
    private Long commodityId;
    @ApiModelProperty(value = "申请份数")
    private Integer applyNumber;
    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyQuantity;
}
