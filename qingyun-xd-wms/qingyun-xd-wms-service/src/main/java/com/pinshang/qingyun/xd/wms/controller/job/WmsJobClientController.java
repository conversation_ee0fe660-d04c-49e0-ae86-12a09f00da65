package com.pinshang.qingyun.xd.wms.controller.job;


import com.pinshang.qingyun.xd.wms.service.job.InventoryCompService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/10 15:00
 */
@RestController
@RequestMapping("/wmsJob")
@RequiredArgsConstructor
public class WmsJobClientController {

    private final InventoryCompService inventoryCompService;

    /**
     * POS香烟/酒类/进口商品 库存进销存比对告警
     * @return
     */
    @PostMapping("/inventoryComp")
    public Boolean amountFlowComp(@RequestParam(value = "dateTime",required = false) String dateTime) {
        return inventoryCompService.doInventoryComp(dateTime);
    }


}
