package com.pinshang.qingyun.xd.wms.util;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.enums.ExcelSheetTitleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.web.servlet.ModelAndView;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 报表工具.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/7/20
 */
@Slf4j
public class ReportUtil {


    public static <T> void  buildData(List<T> list,Map<String, List<String>> data){
        buildData(list,data,3,DateUtil.DEFAULT_DATE_FORMAT);
    }

    /**
     * 构建报表数据
     *
     * 注意：T 中的属性的声明属性必须和要导出的报表的顺序完全一致，字段不能多也不能少
     * @param list 要导出的数据列表
     * @param <T> 报表导出的ODTO类型
     * @return
     */
    public static <T> void  buildData(List<T> list,Map<String, List<String>> data,int rowNum,String dateFormate){
        if (SpringUtil.isEmpty(list)) {
            return;
        }
        int row = rowNum;
        for (T object : list) {
            if(object == null){
                continue;
            }
            List<String> dataList = new ArrayList<>();
            Field[] fields = object.getClass().getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                field.setAccessible(true);
                Object value = null;
                try{
                    PropertyDescriptor pd = new PropertyDescriptor(field.getName(),object.getClass());
                    Method readMethod = pd.getReadMethod();
                    if(readMethod != null){
                        value = readMethod.invoke(object);
                    }
                } catch (IllegalAccessException | IntrospectionException | InvocationTargetException e) {
                    try{
                        value = field.get(object);
                    }catch (IllegalAccessException e1) {
                        log.error("buildRows error, filed:{}", field);
                    }
                }
                if (value instanceof Date) {
                    value = DateUtil.getDateFormate((Date)value, dateFormate);
                }
                String valueStr = value != null ? value.toString() : "";
                dataList.add(valueStr);
            }
            data.put("key_"+ (row++), dataList);
        }
    }

    /**
     * 构建ExcelView
     * @param sheetTitle sheet类型
     * @param data 报表数据
     * @return
     */
    public static ModelAndView buildModelAndView(ExcelSheetTitleEnum sheetTitle, Map<String, List<String>> data, String tableHeader){
        String fileName = sheetTitle.getName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName +"_"+ sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        map.put("tableHeader", tableHeader);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    /**
     * 构建ExcelView
     * @param sheetTitle sheet类型
     * @param data 报表数据
     * @return
     */
    public static ModelAndView buildModelAndView(ExcelSheetTitleEnum sheetTitle, Map<String, List<String>> data){
        String fileName = sheetTitle.getName();


        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = fileName +"_"+ sdf.format(new Date());
        Map<String, Object> map = new HashMap<>();
        map.put("filename", filename);
        map.put("sheetTitle", sheetTitle);
        map.put("data", data);
        map.put("title", fileName);
        map.put("titleCells", (short)3);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    public static boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }

}
