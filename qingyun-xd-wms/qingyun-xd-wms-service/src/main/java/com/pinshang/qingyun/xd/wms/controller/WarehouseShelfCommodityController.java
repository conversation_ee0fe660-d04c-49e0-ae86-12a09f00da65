package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfResult;
import com.pinshang.qingyun.xd.wms.dto.ShelfCommodityBatchInsertDTO;
import com.pinshang.qingyun.xd.wms.dto.WarehouseShelfCommodityDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfCommodityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/warehouse/shelf/commodity")
@Api(value = "仓库货位商品绑定关系", tags = "WarehouseShelfCommodityController")
public class WarehouseShelfCommodityController {

    @Autowired
    private WarehouseShelfCommodityService warehouseShelfCommodityService;

    @PostMapping("/insertWarehouseShelfCommodity")
    @ApiOperation(value = "添加商品货位管理", notes = "添加商品货位管理")
    public Integer insertWarehouseShelfCommodity(@RequestBody WarehouseShelfCommodityDTO warehouseShelfCommodityDTO) {
        return warehouseShelfCommodityService.insertWarehouseShelfCommodity(warehouseShelfCommodityDTO);
    }

    @PostMapping("/updateShelfidByCommodityid")
    @ApiOperation(value = "修改绑定关系", notes = "修改绑定关系")
    public Integer updateShelfidByCommodityid(@RequestBody WarehouseShelfCommodityDTO warehouseShelfCommodityDTO) {
        return warehouseShelfCommodityService.updateShelfidByCommodityid(warehouseShelfCommodityDTO);
    }

    @PostMapping("/deleteBycommodityId")
    @ApiOperation(value = "解绑绑定关系", notes = "解绑绑定关系")
    public Integer deleteByCommodityId(@RequestBody WarehouseShelfCommodityDTO dto){
        return warehouseShelfCommodityService.deleteByCommodityId(dto);
    }

    @PostMapping(value = "/queryCommodityByWarehouseShelf")
    @ApiModelProperty(value = "查询商品列表(分页)", notes = "查询商品列表")
    public MPage<QueryCommodityByShelfResult> queryCommodityByWarehouseShelf(@RequestBody QueryCommodityByShelfDTO dto) {
        return warehouseShelfCommodityService.queryCommodityByWarehouseShelf(dto);
    }

    @PostMapping(value = "/queryCommodityNotPage")
    @ApiModelProperty(value = "查询商品列表(不分页)", notes = "查询商品列表")
    public List<QueryCommodityByShelfResult> queryCommodityNotPage(@RequestBody QueryCommodityByShelfDTO dto) {
        return warehouseShelfCommodityService.queryCommodityNotPage(dto);
    }

    @ApiModelProperty(value = "批量添加商品和货位关系", notes = "批量添加商品和货位关系")
    @PostMapping(value = "/batchInsert")
    public Boolean batchInsert(@RequestBody ShelfCommodityBatchInsertDTO dto) {
        warehouseShelfCommodityService.batchInsert(dto);
        return true;
    }

    @ApiModelProperty(value = "根据货位id查询绑定商品", notes = "根据货位id查询绑定商品")
    @GetMapping(value = "/queryCommodityIdByShelf/{shelfId}")
    public Long queryCommodityIdByShelf(@PathVariable("shelfId") Long shelfId) {
        return warehouseShelfCommodityService.queryCommodityIdByShelf(shelfId);
    }


}
