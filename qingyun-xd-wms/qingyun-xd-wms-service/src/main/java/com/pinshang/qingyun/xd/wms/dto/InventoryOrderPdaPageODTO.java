package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOrderPdaPageODTO  extends BaseEntity {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "盘点单号")
    private String orderCode;
    @ApiModelProperty(value = "进度已盘")
    private Integer inventoryQuantity;
    @ApiModelProperty(value = "进度总数")
    private Integer totalQuantity;
}
