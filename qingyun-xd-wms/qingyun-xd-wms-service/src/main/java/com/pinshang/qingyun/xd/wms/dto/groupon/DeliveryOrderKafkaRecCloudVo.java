package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DeliveryOrderKafkaRecCloudVo
 * <AUTHOR>
 * @Date 2021/7/7 19:46
 * @Description DeliveryOrderKafkaRecCloudVo
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderKafkaRecCloudVo {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderCode;
    /**
     * 订单类型
     * @see XdOrderTypeEnum#getCode()
     */
    private Integer orderType;

    /**
     * @see XdDeliveryOrderStatusEnum#getCode()
     */
    private Integer deliveryStatus;

    /**
     * @see XdDeliveryOrderTypeEnum#getCode()
     */
    private Integer deliveryType;

    /**
     * @see OrderSourceTypeEnum
     */
    private OrderSourceTypeEnum sourceType;

    private Long warehouseId;
}
