package com.pinshang.qingyun.xd.wms.vo.bigShop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogDdGoodsAllocationVO {

    private Long id;

    private Long shopId;

    private Integer type;

    private Long goodsAllocationId;

    private String goodsAllocationCode;

    /**
     * 库区 1排面区 2拣货区 3存储区
     */
    private Integer storageArea;

    private Long stallId;

    /**
     * 档口信息
     */
    private String stallName;

    /**
     * 状态 1启用 2停用
     */
    private Integer status;

    private Long createId;

    private String createCode;

    private String createName;

    private String createTime;


}
