package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName GetEmployeeByTypeAndShopIDTO
 * <AUTHOR>
 * @Date 2022/1/19 15:18
 * @Description GetEmployeeByTypeAndShopIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetEmployeeByTypeAndShopIDTO {
    @ApiModelProperty("员工类型")
    private WarehouseEmployeeTypeEnum typeEnum;
    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("员工id列表")
    private List<Long> employeeIdList;
    @ApiModelProperty("类型code-后台处理")
    private Integer type;
}
