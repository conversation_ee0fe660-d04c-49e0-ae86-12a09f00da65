package com.pinshang.qingyun.xd.wms.vo.bigShop;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class GroupPickDetailVO {

    private String commodityId;

    private Long stallId;

    private String orderNum;

    private BigDecimal quantity;

    private BigDecimal pickQuantity;

    private Integer stockNumber;

    private Integer pickNumber;

    private Long pickOrderId;

    private Long pickOrderItemId;

    private Long pickPartitionOrderId;

    private Integer isProcess;

    private Integer lackProcessMode;

    private String receiveMobile;

    private String receiveMan;

    private String orderCode;

    private String commodityPackageKind;

    private Integer isComplete;

    private Integer pickOrderStatus;

    private Integer pickPartitionOrderStatus;

    private Integer orderStatus;

}
