package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindQueryReqBO;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdPickAreaEmployeeODTO;
import com.pinshang.qingyun.xd.wms.model.PickOrder;
import com.pinshang.qingyun.xd.wms.model.PrinterBind;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindQueryRspVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PrinterBindMapper extends BaseMapper<PrinterBind> {

    MPage<PrinterBindQueryRspVO>
    bindPage(@Param("dto") PrinterBindQueryReqBO printerBindQueryReqBO);

}
