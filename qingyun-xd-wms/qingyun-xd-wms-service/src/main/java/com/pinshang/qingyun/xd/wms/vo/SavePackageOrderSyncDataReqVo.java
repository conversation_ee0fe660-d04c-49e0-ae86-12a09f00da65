package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SavePackageOrderSyncDataReqVo {
    @ApiModelProperty("比较的类型：1-包裹单，2-包裹单明细")
    private Integer packageFlag;

    private List<DcShopPackageOrderReqVo> insertSyncOrderDataList;

    private List<DcShopPackageOrderReqVo> updateSyncOrderDataList;

    private List<DcShopPackageOrderItemReqVo> insertPackageOrderItemList;

    private List<DcShopPackageOrderItemReqVo> updatePackageOrderItemList;
}
