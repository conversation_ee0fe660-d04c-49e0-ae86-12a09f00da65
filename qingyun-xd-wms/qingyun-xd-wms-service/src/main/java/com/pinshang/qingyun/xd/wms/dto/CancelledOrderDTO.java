package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderDTO  {

    /** 订单来源 **/
    private Integer sourceType;

     /** 订单号 */
     private String orderCode;

    /** 门店id */
     private Long shopId;

    /** 订单时间 */
     private Date orderTime;

    /** 配送员id */
     private Long deliveryId;

    /** 配送员姓名 */
     private String deliveryMan;

    /** 配送员电话 */
     private String deliveryMobile;

    /** 取货号 */
     private String pickCode;

     private List<CancelledOrderItemDTO> itemList;

}
