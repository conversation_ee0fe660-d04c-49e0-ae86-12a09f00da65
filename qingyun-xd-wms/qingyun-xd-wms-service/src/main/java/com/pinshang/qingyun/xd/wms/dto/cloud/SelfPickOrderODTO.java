package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SelfPickOrderODTO {
//
//    @ApiModelProperty("错误码 001核销码或订单号不存在 002此单已核销提货，不能再提货 003订单已核销提货" +
//            "004提示大仓未进行出库")
    @ApiModelProperty("错误码 001核销码或订单号不存在 002此单非自提订单，不能提货 003此单已提货核销，不能再提货" +
            "004此单已取消，不能提货 005大仓未进行出库，不能提货 006此单预约的门店不是当前门店")
    private String code;

    private Long orderId;

    @ApiModelProperty("2云超普通单子  6云超团购")
    private Integer orderType;

    @ApiModelProperty("订单短号")
    private Long orderNum;

    @ApiModelProperty("提货人")
    private String receiveMan;

    @ApiModelProperty("提货人手机号码")
    private String receiveMobile;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("提货开始时间")
    private Date receiveTimeBegin;

    @ApiModelProperty("提货结束时间")
    private Date receiveTimeEnd;

    @ApiModelProperty("总包裹数")
    private Integer sumPackages;

    @ApiModelProperty("已拣包裹数")
    private Integer pickPackages;

    @ApiModelProperty("日期状态 1已过期  2未到期")
    private Integer dateStatus;

    @ApiModelProperty("0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败")
    private Integer orderStatus;

    @ApiModelProperty("6云超团购 商品列表")
    private List<CloudGroupCommodityDTO> commodityList;

    @ApiModelProperty("2云超普通单子 包裹列表")
    private List<PackageListDTO> packages;
}
