package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16
 * @Version 1.0
 */
@Data
@ExcelIgnoreUnannotated
public class StallCommodityStockForPDAItemODTO {

    private Integer storageAreaType;

    @ExcelProperty("存储区名称")
    private String name;

    @ExcelProperty("库存最大")
    private BigDecimal stockMax;

    @ExcelProperty("库存最小")
    private BigDecimal stockMin;

    @ExcelProperty("库存")
    private BigDecimal stock;

    @ExcelProperty("库存-仅限区域")
    private List<StallCommodityStockForPDAItemODTO> stockList;
}
