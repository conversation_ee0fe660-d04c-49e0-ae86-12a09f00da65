package com.pinshang.qingyun.xd.wms.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.cms.service.CommodityEverydayFreshClient;
import com.pinshang.qingyun.order.dto.store.StoreODTO;
import com.pinshang.qingyun.order.service.StoreClient2;
import com.pinshang.qingyun.shop.admin.dto.PrepostionWarehouseGroupODTO;
import com.pinshang.qingyun.shop.admin.service.PrepostionWarehouseGroupClient;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserDepartmentShopIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.mapper.CommodityStockMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockOutCollectMapper;
import com.pinshang.qingyun.xd.wms.model.StockOutCollect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/31 11:26
 * @Description
 */
@Service
@Slf4j
public class StrockOutService {
    @Autowired
    private StoreClient2 storeClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private PrepostionWarehouseGroupClient prepostionWarehouseGroupClient;
    @Autowired
    private StockLogMapper stockLogMapper;
    @Autowired
    private StockOutCollectMapper stockOutCollectMapper;
    @Autowired
    private CommodityStockMapper commodityStockMapper;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private ShopCommodityClient shopCommodityClient;
    
    /**
     * 缺货统计
     */
    @Async
    @Transactional
    public void strockOutStatistics() {
        StockOutLogIDTO deleteIdto = new StockOutLogIDTO();
        deleteIdto.setBeginTime( LocalTime.parse("00:00", DateTimeFormat.forPattern("HH:mm")).toDateTimeToday().plusDays(-1).toDate());
        deleteIdto.setEndTime(LocalTime.parse("00:00", DateTimeFormat.forPattern("HH:mm")).toDateTimeToday().toDate());
        //所有前置仓已开业的门店
       List<ShopDto> shopODTOList= shopClient.listShopByTypeAndOpen(ShopTypeEnums.XD.getCode());
        //获取日日鲜分组时间，和分组商品
        List<PrepostionWarehouseGroupODTO> groupODTOS = prepostionWarehouseGroupClient.getGroupCommodityList(new ArrayList<>());
        Map<String, List<PrepostionWarehouseGroupODTO>> groupMap = groupODTOS.stream().collect(Collectors.groupingBy(PrepostionWarehouseGroupODTO::getCheckTime));
        //获取日日鲜上架商品
        for (ShopDto shop : shopODTOList) {
            //删除门店这个时间之前统计的商品
            deleteIdto.setShopId(shop.getId());
            stockOutCollectMapper.deleteByShopIdAndCollectTime(deleteIdto);
            //获取门店所有上架商品
            List<Long> commodityIdList= shopCommodityClient.findAllCommodityIdByShopIdAndAppStatus(shop.getId());
            for (Map.Entry<String, List<PrepostionWarehouseGroupODTO>> entry : groupMap.entrySet()) {
                List<Long> commodities = entry.getValue().stream().map(PrepostionWarehouseGroupODTO::getCommodityId).collect(Collectors.toList());
                //取交集-->门店上架的日日鲜商品列表
                commodities.retainAll(commodityIdList);
                //取差集，移除掉已经统计过缺货的日日鲜商品
                commodityIdList.removeAll(commodities);
                stockCollect(entry.getKey(), commodities, shop.getId());
            }
            //获取除了日日鲜商品的分组 时间固定 22:00
            stockCollect("22:00",commodityIdList,shop.getId());
        }
    }

    /**
     * 库存处理
     */
    private void stockCollect(String end,List<Long> commodities,Long shopId){
        if (commodities.isEmpty()){
            return;
        }
        Date endTime = LocalTime.parse(end, DateTimeFormat.forPattern("HH:mm")).toDateTimeToday().plusDays(-1).toDate();
        List<StockOutLogODTO> outLogODTOS=  searchStockOut("00:00",endTime,shopId,commodities);
        //找到缺货的
        List<StockOutLogODTO> outLogODTOList= outLogODTOS.stream().filter(item ->item.getNormalChangeQuantity().compareTo(BigDecimal.ZERO)<1).collect(Collectors.toList());
        saveStockCollect(outLogODTOList,endTime,shopId);
        //找到不存在的
        List<Long> commdityIds=outLogODTOS.stream().map(StockOutLogODTO::getCommodityId).collect(Collectors.toList());
        commodities.removeAll(commdityIds);
        if (!CollectionUtils.isEmpty(commodities)){
            List<StockItemDTO> stockItemDTOList= searchStockOutByStockAndLog(endTime,shopId,commodities);
            //找到缺货的
            List<StockItemDTO> stockItemDTOList1= stockItemDTOList.stream().filter(item ->item.getQuantity().compareTo(BigDecimal.ZERO)<1).collect(Collectors.toList());
            saveStockCollectByStockItem(stockItemDTOList1,endTime,shopId);
        }
    }

    /**
     * 查询某个时间点商品数量
     * @param begin
     * @param endTime
     * @param shopId
     * @param commodities
     * @return
     */
    private List<StockOutLogODTO> searchStockOut(String begin, Date endTime, Long shopId, List<Long> commodities) {
        Date beginTime = LocalTime.parse(begin, DateTimeFormat.forPattern("HH:mm")).toDateTimeToday().plusDays(-2).toDate();
        StockOutLogIDTO idto = new StockOutLogIDTO();
        idto.setShopId(shopId);
        idto.setCommodityIds(commodities);
        idto.setBeginTime(beginTime);
        idto.setEndTime(endTime);
        return stockLogMapper.selectStockOutLog(idto);
    }

    /**
     * 根据当前商品数量和商品变化数量推出某个时间点商品数量
     * @param endTime
     * @param shopId
     * @param commodities
     * @return
     */
    private List<StockItemDTO> searchStockOutByStockAndLog(Date endTime, Long shopId, List<Long> commodities) {
        //查询当前商品数量 +日期后变化数量
        List<StockItemDTO> stockItemDTOList = commodityStockMapper.queryStockListNoTQ(shopId, commodities);
        StockOutLogIDTO  idto = new StockOutLogIDTO();
        idto.setShopId(shopId);
        idto.setCommodityIds(commodities);
        idto.setEndTime(endTime);
        List<StockOutLogODTO> outLogODTOS1=stockLogMapper.selectStockOutLogByEndTime(idto);
        Map<Long, StockOutLogODTO> maps = outLogODTOS1.stream().collect(Collectors.toMap(StockOutLogODTO::getCommodityId,Function.identity()));
        for (StockItemDTO stockItemDTO : stockItemDTOList) {
            StockOutLogODTO outLogODTO=  maps.get(stockItemDTO.getCommodityId());
            if (null!=outLogODTO){
                stockItemDTO.setQuantity(stockItemDTO.getQuantity().subtract(outLogODTO.getChangeQuantity()));
            }
        }
        return stockItemDTOList;
    }

    /**
     * 保存缺货
     * @param outLogODTOList
     * @param endTime
     * @param shopId
     */
    private void saveStockCollect(List<StockOutLogODTO> outLogODTOList,Date endTime,Long shopId){
        if (CollectionUtils.isEmpty(outLogODTOList)){
            return;
        }
        List<StockOutCollect> list = new ArrayList<>();
        for (StockOutLogODTO stockOutLogODTO : outLogODTOList) {
            StockOutCollect collect = new StockOutCollect();
            collect.setCollectTime(endTime);
            collect.setId(IdWorker.getId());
            collect.setQuantity(stockOutLogODTO.getNormalChangeQuantity());
            collect.setCommodityId(stockOutLogODTO.getCommodityId());
            collect.setCreateTime(new Date());
            collect.setShopId(shopId);
            list.add(collect);
        }
        stockOutCollectMapper.saveBatch(list);
    }

    /**
     * 保存缺货
     * @param outLogODTOList
     * @param endTime
     * @param shopId
     */
    private void saveStockCollectByStockItem(List<StockItemDTO> outLogODTOList,Date endTime,Long shopId){
        if (CollectionUtils.isEmpty(outLogODTOList)){
            return;
        }
        List<StockOutCollect> list = new ArrayList<>();
        for (StockItemDTO stockOutLogODTO : outLogODTOList) {
            StockOutCollect collect = new StockOutCollect();
            collect.setCollectTime(endTime);
            collect.setId(IdWorker.getId());
            collect.setQuantity(stockOutLogODTO.getQuantity());
            collect.setCommodityId(stockOutLogODTO.getCommodityId());
            collect.setCreateTime(new Date());
            collect.setShopId(shopId);
            list.add(collect);
        }
        stockOutCollectMapper.saveBatch(list);
    }


    public PageInfo<StockOutCollectODTO> page(StockOutCollectIDTO idto) {
       Long userId= FastThreadLocalUtil.getQY().getUserId();

        SelectUserDepartmentShopIdListIDTO departmentShopIdListIDTO = new SelectUserDepartmentShopIdListIDTO();
        departmentShopIdListIDTO.setUserId(userId);
        departmentShopIdListIDTO.setDepartmentOrgId(idto.getDeptId());
        //查询用户部门下的门店列表
        List<Long> shopIdList = smmUserClient.selectUserDepartmentShopIdList(departmentShopIdListIDTO);
        idto.setShopIdList(shopIdList);
        PageInfo<StockOutCollectODTO> pageInfo= PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> stockOutCollectMapper.findAllByIdto((idto)));

        if (pageInfo != null && SpringUtil.isNotEmpty(pageInfo.getList())) {
            List<StockOutCollectODTO> list = pageInfo.getList();
            List<Long> shopIds = list.stream().map(StockOutCollectODTO::getShopId).collect(Collectors.toList());
            //部门
            SelectShopOrgInfoListIDTO idto1 = new SelectShopOrgInfoListIDTO();
            idto1.setShopIdList(shopIds);
            List<OrgAndParentInfoODTO> orgAndParentInfoODTOS = orgClient.selectShopOrgInfoList(idto1);
            Map<Long, OrgAndParentInfoODTO> OrgMaps = orgAndParentInfoODTOS.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, Function.identity()));
            //客户编码
            List<StoreODTO> storeList = storeClient.findAllShopStoreList();
            Map<Long, StoreODTO> storeODTOMap = storeList.stream().collect(Collectors.toMap(StoreODTO::getShopId, Function.identity()));
            //条形码
            List<Long> commoditys = list.stream().map(StockOutCollectODTO::getCommodityId).collect(Collectors.toList());
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commoditys);

            for (StockOutCollectODTO stockOutCollectODTO : list) {
                OrgAndParentInfoODTO org = OrgMaps.get(stockOutCollectODTO.getShopId());
                if (null != org) {
                    //部门
                    stockOutCollectODTO.setDeptName(org.getParentOrgName());
                }
                StoreODTO storeODTO = storeODTOMap.get(stockOutCollectODTO.getShopId());
                if (null != storeODTO) {
                    // 客户编码
                    stockOutCollectODTO.setStoreCode(storeODTO.getStoreCode());
                }
                //条形码
                stockOutCollectODTO.setBarCodeList(longListMap.get(stockOutCollectODTO.getCommodityId()));
            }
        }
        return pageInfo;


    }
}
