package com.pinshang.qingyun.xd.wms.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.common.dto.DelayMsgDTO;
import com.pinshang.qingyun.common.dto.DelayMsgUpDTO;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: liu zhen
 * @DateTime: 2022/10/9 14:55
 * @Description
 */
@Component
@Slf4j
@OnlineSwitchWatcher
public class DelayMsgKafkaListener extends BaseKafkaOnlineSwitchProcessor {
    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.DELAY_MESSAGE_TOPIC+ "xdWmsKafkaListenerContainerFactory",
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.DELAY_MESSAGE_UP_TOPIC+ "xdWmsKafkaListenerContainerFactory"
                );
    }

    @KafkaListener(topics = "${application.name.switch}" + KafkaTopicConstant.DELAY_MESSAGE_TOPIC,
            containerFactory = "kafkaListenerContainerFactory",errorHandler ="kafkaConsumerErrorHandler")
    public void delayQueueMessage(String message) {
        String msg = "topic:" + KafkaTopicConstant.DELAY_MESSAGE_TOPIC + " ====================message==" + message;
        log.warn(msg);

        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            DelayMsgDTO idto = JsonUtil.json2java(messageWrapper.getData().toString(), DelayMsgDTO.class);
            if(idto.getRedisDelayQueueEnum() != null && RedisDelayQueueEnum.CLOUD_PICK_OCCUPY_FREED.getBeanId().equals(idto.getRedisDelayQueueEnum().getBeanId())) {
                RedisDelayQueueHandle redisDelayQueueHandle = (RedisDelayQueueHandle) SpringBeanFinder.getBean(idto.getRedisDelayQueueEnum().getBeanId());
                if (redisDelayQueueHandle != null){
                    redisDelayQueueHandle.execute(idto.getCode());
                }
            }
        } catch (Exception e) {
            log.error(msg + "延时消息异常:{}", e);
        }

    }


    @KafkaListener(topics = "${application.name.switch}" + KafkaTopicConstant.DELAY_MESSAGE_UP_TOPIC,
            containerFactory = "kafkaListenerContainerFactory",errorHandler ="kafkaConsumerErrorHandler")
    public void delayQueueMessageUp(String message) {
        String msg = "topic:" + KafkaTopicConstant.DELAY_MESSAGE_UP_TOPIC + " ====================message==" + message;
        log.warn(msg);

        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            DelayMsgUpDTO idto = JsonUtil.json2java(messageWrapper.getData().toString(), DelayMsgUpDTO.class);
            if(idto != null){
                RedisDelayQueueEnum redisDelayQueueEnum = RedisDelayQueueEnum.getByCode(idto.getDelayQueueCode());
                if(redisDelayQueueEnum != null && RedisDelayQueueEnum.CLOUD_PICK_OCCUPY_FREED.getBeanId().equals(redisDelayQueueEnum.getBeanId())){
                    RedisDelayQueueHandle redisDelayQueueHandle = (RedisDelayQueueHandle) SpringBeanFinder.getBean(redisDelayQueueEnum.getBeanId());
                    if (redisDelayQueueHandle != null){
                        redisDelayQueueHandle.execute(idto.getCode());
                    }
                }

                if(redisDelayQueueEnum != null && RedisDelayQueueEnum.DD_COMMODITY_LIMIT_SET.getBeanId().equals(redisDelayQueueEnum.getBeanId())){
                    RedisDelayQueueHandle redisDelayQueueHandle = (RedisDelayQueueHandle) SpringBeanFinder.getBean(redisDelayQueueEnum.getBeanId());
                    if (redisDelayQueueHandle != null){
                        redisDelayQueueHandle.execute(idto.getCode());
                    }
                }
            }
        } catch (Exception e) {
            log.error(msg + "延时消息异常:{}", e);
        }

    }
}
