package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickNumReportDTO extends Pagination<PickNumReportListDTO> {

    @ApiModelProperty(value = "前置仓id")
    private Long warehouseId;

    @ApiModelProperty(value = "时间")
    private String time;

    @ApiModelProperty(value = "拣货员id")
    private Long pickId;

    private String startDate;

    private String endDate;

    private List<Long> shopIdList;

    @ApiModelProperty(value = "经营模式：1-直营、2-外包")
    private Integer managementMode;

    @ApiModelProperty(value = "销售模式")
    private Integer orderType;

    private String orderDeliveryEndTime;

    private String orderDeliveryBeginTime;

    public void checkData() {
        QYAssert.isTrue(!StringUtils.isEmpty(time.trim()), "报表统计时间不能为空");
        startDate = time.trim() + "-01 00:00:00";
        endDate = time.trim() + "-31 23:59:59";
        Date a = DateUtil.addDay(DateUtil.parseDateFullYear(startDate), -1);
        orderDeliveryBeginTime = DateUtil.get4yMdHms(a);
        Date b = DateUtil.addDay(DateUtil.parseDateFullYear(endDate), 1);
        orderDeliveryEndTime = DateUtil.get4yMdHms(b);
    }
}
