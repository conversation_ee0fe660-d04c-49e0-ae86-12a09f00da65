package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.DdDisplayPositionODTO;
import com.pinshang.qingyun.xd.wms.dto.DdDisplayPositionPageIDTO;
import com.pinshang.qingyun.xd.wms.model.DdDisplayPosition;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdDisplayPositionVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Repository
public interface DdDisplayPositionMapper extends BaseMapper<DdDisplayPosition> {

    Integer countDisplay(@Param("shelveId") Long shelveId);

    List<DdDisplayPositionODTO> page(DdDisplayPositionPageIDTO req);

    List<DdDisplayPositionODTO> list(@Param("shopId") Long shopId ,@Param("displayPosition") String displayPosition, @Param("stallId") Long stallId);

    Integer isRepeat(@Param("shopId") Long shopId, @Param("displayPositionName") String displayPositionName, @Param("id") Long id);

    List<LogDdDisplayPositionVO> selectLog(@Param("list") List<Long> id);

}