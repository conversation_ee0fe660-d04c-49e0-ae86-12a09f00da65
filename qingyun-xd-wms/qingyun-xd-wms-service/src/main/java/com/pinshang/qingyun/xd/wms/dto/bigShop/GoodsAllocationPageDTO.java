package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoodsAllocationPageDTO {

    @ExcelIgnore
    private Long goodsAllocationId;

    @ApiModelProperty("区域")
    @ExcelProperty("区域")
    private String areaName;

    @ApiModelProperty("货位号")
    @ExcelProperty("货位号")
    private String goodsAllocationCode;

    @ApiModelProperty("库区")
    @ExcelIgnore
    private Integer storageArea;

    @ApiModelProperty("库区名称")
    @ExcelProperty("所属库区")
    private String storageAreaName;

    @ApiModelProperty("所属档口")
    @ExcelProperty("所属档口")
    private String stallName;

    @ApiModelProperty("状态")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty("状态")
    @ExcelProperty("货位状态")
    private String statusName;

    public String getStorageAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.storageArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }

    public String getStatusName() {
        StatusEnum statusEnum = StatusEnum.fromCode(this.status);
        return null == statusEnum ? null : statusEnum.getName();
    }


}
