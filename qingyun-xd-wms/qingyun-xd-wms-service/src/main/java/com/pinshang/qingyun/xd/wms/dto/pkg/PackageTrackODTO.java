package com.pinshang.qingyun.xd.wms.dto.pkg;

import com.pinshang.qingyun.base.enums.shop.PackageStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.DeliveryBatchEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageTrackODTO {
    private Long packageOrderId;

    @ApiModelProperty(value = "箱码(多个)")
    private String boxCodeList;

    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    @ApiModelProperty(value = "门店名称")
    private String shopName;


    @ApiModelProperty(value = "云超订单编码")
    private String orderCode;
    @ApiModelProperty(value = "送货日期")
    private Date orderTime;


    @ApiModelProperty(value = "配送批次 0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次(装箱用)")
    private Integer deliveryBatch;

    @ApiModelProperty(value = "预约到货时间")
    private String arriveTime;

    @ApiModelProperty(value = "包裹号")
    private String packageOrderCode;
    @ApiModelProperty(value = "包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货")
    private Integer packageStatus;

    @ApiModelProperty(value = "筐码/箱码")
    private String boxCode;

    @ApiModelProperty(value = "出库时间")
    private Date outTime;
    @ApiModelProperty(value = "出库人ID")
    private Long outId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "outId")
    private String outUserName;

    @ApiModelProperty(value = "装箱时间")
    private Date loadBoxTime;
    @ApiModelProperty(value = "装箱人ID")
    private Long loaderBoxId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "loaderBoxId")
    private String loaderBoxUserName;


    @ApiModelProperty(value = "揽收时间")
    private Date takeBoxTime;
    @ApiModelProperty(value = "揽收人ID")
    private Long takerBoxId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "takerBoxId")
    private String takerBoxUserName;

    @ApiModelProperty(value = "卸货时间")
    private Date unloadBoxTime;
    @ApiModelProperty(value = "卸货人ID")
    private Long unloadBoxId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "unloadBoxId")
    private String unloadBoxUserName;

    @ApiModelProperty(value = "收货时间")
    private Date receiveBoxTime;
    @ApiModelProperty(value = "收货人ID")
    private Long receiverBoxId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "receiverBoxId")
    private String receiverBoxUserName;


    @ApiModelProperty(value = "拣货时间")
    private Date pickBoxTime;
    @ApiModelProperty(value = "拣货人ID")
    private Long pickerBoxId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "pickerBoxId")
    private String pickerBoxUserName;

    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;
    @ApiModelProperty(value = "取消人ID")
    private Long cancelUserId;
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "cancelUserId")
    private String cancelUseName;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;
    public String getOrderStatusName(){
        return XdOrderStatusEnum.getName(orderStatus);
    }

    public String getOrderTimeStr(){
        return DateUtil.getDateFormate(orderTime,"yyyy-MM-dd");
    }
    public String getDeliveryBatchName(){
        return DeliveryBatchEnum.getName(deliveryBatch);
    }
    public String getPackageStatusName(){
        return PackageStatusEnum.getValue(packageStatus);
    }


    public String getLoadBoxTimeStr(){
        return DateUtil.getDateFormate(loadBoxTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getTakeBoxTimeStr(){
        return DateUtil.getDateFormate(takeBoxTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getUnloadBoxTimeStr(){
        return DateUtil.getDateFormate(unloadBoxTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getReceiverBoxTimeStr(){
        return DateUtil.getDateFormate(receiveBoxTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getPickerBoxTimeStr(){
        return DateUtil.getDateFormate(pickBoxTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getOutTimeStr(){
        return DateUtil.getDateFormate(outTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getCancelTimeStr(){
        return DateUtil.getDateFormate(cancelTime,"yyyy-MM-dd HH:mm:ss");
    }
}
