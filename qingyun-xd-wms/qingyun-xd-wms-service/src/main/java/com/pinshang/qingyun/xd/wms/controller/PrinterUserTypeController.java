package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xd.wms.bo.PrinterUserTypeQueryReqBO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.PrinterUserTypeService;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryReqVO;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryRspVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 使用方类型
 */
@RestController
@RequestMapping("/printerUserType")
@Api(value = "使用方类型", tags = "PrinterUserTypeController")
@RequiredArgsConstructor
public class PrinterUserTypeController {

    private final PrinterUserTypeService printerUserTypeService;

    /**
     * 使用方查询
     * @param
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "使用方查询", notes = "使用方查询")
    public MPage<PrinterUserTypeQueryRspVO> page(@RequestBody PrinterUserTypeQueryReqVO reqVO){
        reqVO.check();
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token),"请登录后操作");
        Long tokenShopId = token.getShopId();
        QYAssert.isTrue(Objects.nonNull(tokenShopId),"不允许总部操作");
        PrinterUserTypeQueryReqBO printerUserTypeQueryReqBO = BeanCloneUtils.copyTo(reqVO, PrinterUserTypeQueryReqBO.class);
        printerUserTypeQueryReqBO.setShopId(tokenShopId);
        return printerUserTypeService.page(printerUserTypeQueryReqBO);
    }
}
