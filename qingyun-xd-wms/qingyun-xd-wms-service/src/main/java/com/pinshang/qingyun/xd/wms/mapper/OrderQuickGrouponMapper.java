package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickOrderItemPickUpDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickOrderPickUpDTO;
import com.pinshang.qingyun.xd.wms.model.OrderQuickGroupon;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface OrderQuickGrouponMapper extends BaseMapper<OrderQuickGroupon> {

    /**
     * 根据提货码查询订单信息
     * @param pickupCode
     * @return
     */
    QuickOrderPickUpDTO quickOrderByPickupCode(@Param("pickupCode") String pickupCode);

    /**
     * 根据订单id查询商品信息
     * @param orderId
     * @return
     */
    List<QuickOrderItemPickUpDTO> commodityInfoList(@Param("orderId") Long orderId, @Param("grouponId") Long grouponId);

    /**
     * 更新已下单量
     * @param grouponId
     * @param commodityId
     * @param number
     * @return
     */
    Integer updateStock(@Param("grouponId") Long grouponId, @Param("commodityId") Long commodityId, @Param("number") Integer number);
}
