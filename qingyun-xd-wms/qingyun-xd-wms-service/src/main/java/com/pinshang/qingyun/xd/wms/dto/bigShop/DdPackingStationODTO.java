package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.enums.xd.XdPopupMsgStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 * 订单打包口管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@ToString
@ApiModel("DdPackingStationODTO")
public class DdPackingStationODTO {

    @ApiModelProperty("打包口ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("打包口编号")
    private String packingPort;

    private Integer status;

    @ApiModelProperty("状态，1-启用，0-停用")
    private String statusName;

    @ApiModelProperty("占用订单数量")
    private Integer orderCount;

    @ApiModelProperty("创建人ID")
    private Long createId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    public String getStatusName() {
        if (null != status) {
            return XdPopupMsgStatusEnums.getName(this.status);
        }
        return statusName;
    }

}