package com.pinshang.qingyun.xd.wms.util;

import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Created by honway on 2017/2/20.
 * excel导出工具类
 */
public class ExcelExportUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelExportUtils.class);

    /**
     * 导出2007格式Excel
     * @param response
     * @param fileName
     * @param xb
     */
    public static void exportExcel(HttpServletResponse response, String fileName, XSSFWorkbook xb) {
        ServletOutputStream outputStream = null;
        try {
            fileName = new String(fileName.getBytes(),"UTF-8");
            setDownloadHeader(response, fileName);
            outputStream = response.getOutputStream();
            xb.write(outputStream);
            outputStream.flush();
        } catch (Exception ex) {
            LOGGER.error("设置下载头信息时异常:{}", ex.getMessage(),ex);
        }finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    //
                }finally {
                    outputStream = null;
                }
            }
        }
    }

    /**
     * 导出2007格式Excel
     * @param response
     * @param fileName
     * @param xb
     */
    public static void exportExcelS(HttpServletResponse response, String fileName, SXSSFWorkbook xb) {
        ServletOutputStream outputStream = null;
        try {
            fileName = new String(fileName.getBytes(),"UTF-8");
            setDownloadHeader(response, fileName);
            outputStream = response.getOutputStream();
            xb.write(outputStream);
            outputStream.flush();
        } catch (Exception ex) {
            LOGGER.error("设置下载头信息时异常:{}", ex.getMessage(),ex);
        }finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    //
                }finally {
                    outputStream = null;
                }
            }
        }
    }


    public static void setDownloadHeader(HttpServletResponse response, String fileName) {
        try {
            fileName = new String(fileName.getBytes(),"ISO8859-1");
            response.setContentType("application/octet-stream;charset=ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename="+ fileName);
            response.addHeader("Pragma", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            LOGGER.error("设置下载头信息时异常:{}", ex.getMessage());
        }
    }


    /**
     * 获取2007格式以上的excel对象. 能生成excel的数据最多行为 1048576行
     * @param sheetName sheet名称
     * @param title 表头
     * @param values 值
     * @param xb
     * @return
     */
    public static XSSFWorkbook getXSSFWorkbook(String sheetName, List<String> title, List<List<String>> values, XSSFWorkbook xb){
        // 第一步，创建一个webbook，对应一个Excel文件
        if(xb == null){
            xb = new XSSFWorkbook();
        }
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        XSSFSheet sheet = xb.createSheet(sheetName);
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        XSSFRow row = sheet.createRow(0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        XSSFCellStyle style = xb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        XSSFCell cell = null;
        //创建标题
        for (int i = 0; i < title.size(); i++) {
            cell = row.createCell(i);
            cell.setCellValue(title.get(i));
            cell.setCellStyle(style);
        }
        if(values == null || values.isEmpty()){
            return xb;
        }
        //创建内容
        for (int i = 0; i < values.size(); i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values.get(i).size(); j++) {
                row.createCell(j).setCellValue(values.get(i).get(j));
            }
        }

        return xb;
    }

    /**
     * 获取2007格式以上的excel对象. 能生成excel的数据最多行为 1048576行
     * @param sheetName sheet名称
     * @param title 表头
     * @param values 值
     * @param xb
     * @return
     */
    public static SXSSFWorkbook getXSSFWorkbookS(String sheetName, List<String> title, List<List<String>> values, SXSSFWorkbook xb){
        // 第一步，创建一个webbook，对应一个Excel文件
        if(xb == null){
            xb = new SXSSFWorkbook();
        }
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        SXSSFSheet sheet = xb.createSheet(sheetName);
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        SXSSFRow row = sheet.createRow(0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        XSSFCellStyle style = (XSSFCellStyle)xb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        SXSSFCell cell = null;
        //创建标题
        for (int i = 0; i < title.size(); i++) {
            cell = row.createCell(i);
            cell.setCellValue(title.get(i));
            cell.setCellStyle(style);
        }
        if(values == null || values.isEmpty()){
            return xb;
        }
        //创建内容
        for (int i = 0; i < values.size(); i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values.get(i).size(); j++) {
                row.createCell(j).setCellValue(values.get(i).get(j));
            }
        }

        return xb;
    }
}
