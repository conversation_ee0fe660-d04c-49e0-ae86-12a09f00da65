package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoodsAllocationCommodityPageIDTO extends Pagination<GoodsAllocationCommodityPageIDTO> {

    private Long shopId;

    @ApiModelProperty("档口")
    private Long stallId;

    @ApiModelProperty("货位号")
    private Long goodsAllocationId;

    @ApiModelProperty("商品")
    private Long commodityId;

    @ApiModelProperty("品类")
    private Long cateId;

    @ApiModelProperty("货位状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty("1未绑定商品")
    private Integer unBindCommodity;

    @ApiModelProperty("1未设置安全库存")
    private Integer unSetSecureStock;


}
