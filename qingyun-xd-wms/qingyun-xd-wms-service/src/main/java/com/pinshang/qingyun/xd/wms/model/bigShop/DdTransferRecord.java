package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 移库单主表
 * </p>
 *
 */
@Data
@ToString
@TableName("t_dd_transfer_record")
public class DdTransferRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 移库单号
     */
    private String transferCode;

    /**
     * 类型1-排面补货、2-拣货位补货、3-后仓上架、4-移库
     */
    private Integer type;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

    /**
     * create_id
     */
    private Long createId;

    /**
     * update_id
     */
    private Long updateId;

}
