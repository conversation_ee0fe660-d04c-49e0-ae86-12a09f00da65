
package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.plus.MyPage;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import com.pinshang.qingyun.xd.wms.util.XdWmsConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 拣货
 * Created by chenqi on 2019/12/03.
 * 下单(同时生成拣货单，待拣货)->自动或手动分配拣货人->点击开始拣货(更改状态拣货中，待出库)->完成拣货
 */
@RestController
@RequestMapping("/pick")
@Api(value = "鲜到拣货", tags = "PickOrderController")
public class PickOrderController {
    @Autowired
    private PickOrderService pickOrderService;

    @PostMapping("/cancel/{orderId}")
    @ApiOperation(value = "取消拣货", notes = "取消拣货")
    public Boolean cancel(@PathVariable("orderId") Long orderId) {
        pickOrderService.cancelPickOrder(orderId,StockUtils.INSTANCE.userId());
        return Boolean.TRUE;
    }

    @PostMapping("/distribute")
    @ApiOperation(value = "分配拣货单", notes = "分配拣货单")
    public Boolean distributePickOrder(){
        pickOrderService.distributePickOrderList();
        return Boolean.TRUE;
    }

    @PostMapping("/complete")
    @ApiOperation(value = "手持、后端：完成拣货单", notes = "完成拣货单")
    public Boolean completePickOrder(@RequestBody PickCompleteDTO pickCompleteDTO){
        pickCompleteDTO.checkData();
        pickOrderService.completePickOrder(pickCompleteDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/changePicker")
    @ApiOperation(value = "改派拣货人", notes = "改派拣货人")
    public Boolean changePicker(@RequestBody PickChangeDTO pickChangeDTO){
        pickChangeDTO.checkData();
        pickOrderService.changePicker(pickChangeDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/pickOrderList")
    @ApiOperation(value = "后端：查询拣货单", notes = "查询拣货单")
    public MPage<PickOrderListResult> pickOrderList(@RequestBody PickOrderDTO pickOrderDTO) {
        pickOrderDTO.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        if (!StringUtils.isEmpty(pickOrderDTO.getOrderDeliveryBeginTime())) {
            pickOrderDTO.setOrderDeliveryBeginTime(pickOrderDTO.getOrderDeliveryBeginTime()+" 00:00:00");
        }
        if (!StringUtils.isEmpty(pickOrderDTO.getOrderDeliveryEndTime())) {
            pickOrderDTO.setOrderDeliveryEndTime(pickOrderDTO.getOrderDeliveryEndTime()+" 23:59:59");
        }

        MPage<PickOrderListResult> pickOrderListResultMPage = pickOrderService.pickOrderList(pickOrderDTO);

        if (Objects.nonNull(pickOrderListResultMPage) && SpringUtil.isNotEmpty(pickOrderListResultMPage.getList())) {
            List<PickOrderListResult> list = pickOrderListResultMPage.getList();
            list.forEach(data -> {
                if (Objects.equals(XdWmsConstantUtil.SYSTEM_PICK_ID, data.getPickId())) {
                    data.setPickingMethod(PickingMethodEnum.ZONE_ORDER_PICKING.getCode());
                    data.setEmployeeName(XdWmsConstantUtil.SYSTEM_PICK_NAME);
                } else {
                    data.setPickingMethod(PickingMethodEnum.WHOLE_ORDER_PICKING.getCode());
                }

                if (Objects.equals(XdWmsConstantUtil.SYSTEM_PICK_ID, data.getPackingId())) {
                    data.setPackingName(XdWmsConstantUtil.SYSTEM_PICK_NAME);
                }
            });
        }

        return pickOrderListResultMPage;
    }

    /**
     * 待拣货列表查询，待拣货和拣货中的订单
     * @param pickOrderDTO
     * @return
     */
    @PostMapping("/pickOrderList4App")
    @ApiOperation(value = "手持：查询拣货单", notes = "查询拣货单")
    public MyPage<PickOrderListResult> pickOrderList4App(@RequestBody PickOrder4AppDTO pickOrderDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        pickOrderDTO.setWarehouseId(tokenInfo.getShopId());
        PickOrderDTO dto = new PickOrderDTO();
        BeanUtils.copyProperties(pickOrderDTO, dto);
        dto.setPickId(tokenInfo.getEmployeeId());
        //手持端拣货完成只查询当天的
        dto.setType(1);
        //手持只需要查询普通得订单，团购和云超得不需要查询
        dto.setOrderTypeList(Arrays.asList(XdOrderTypeEnum.FARM.getCode(),XdOrderTypeEnum.ORDINARY.getCode(), XdOrderTypeEnum.XD_WAREHOUSE.getCode(), XdOrderTypeEnum.THIRD.getCode()));
        MPage<PickOrderListResult> mpage = pickOrderService.pickOrderList(dto);
        return MyPage.toMyPage(mpage);
    }

    @GetMapping("/pickOrderById/{pickOrderId}")
    @ApiOperation(value = "根据id查询拣货单详情")
    public PickOrderListResult pickOrderById(@PathVariable("pickOrderId") Long pickOrderId) {
        return pickOrderService.pickOrderById(pickOrderId);
    }

    @ApiOperation("根据orderIds查询拣货单")
    @PostMapping("/selectPickByOrderId")
    public List<PickOrderResult> selectPickByOrderId(@RequestBody List<Long> orderIds){
        return pickOrderService.selectPickByOrderId(orderIds);
    }

    @GetMapping("/pickOrderByOrderId/{orderId}")
    @ApiOperation(value = "根据订单id查询拣货单")
    public PickOrderMqDTO pickOrderByOrderId(@PathVariable("orderId") Long orderId) {
        return pickOrderService.pickOrderByOrderId(orderId);
    }

    /**
     * 获取拣货单配置信息
     * 例：客户下单12:00~13:00 , 配置10~30分钟， 那么 12:30~12:50 即将超时 12：50 ~    超时
     * @return
     */
    @GetMapping("/queryPickSetting")
    @ApiOperation(value = "查询拣货配置")
    public PickSettingDTO queryPickSetting() {
        return pickOrderService.queryPickSetting();
    }

    @GetMapping("/stockOutRelationLog")
    @ApiOperation(value = "缺货联系客户记录")
    public Boolean stockOutRelationLog(@RequestParam(value = "orderId",required = false) Long orderId) {
        return pickOrderService.stockOutRelationLog(orderId);
    }

    /**
     * 分配拣货
     * */
    @PostMapping("/distributePickOrderSingle")
    @ApiOperation(value = "分配拣货", notes = "分配拣货")
    public Boolean distributePickOrderSingle(@RequestParam(value = "pickOrderId",required = false) Long pickOrderId,@RequestParam(value = "employeeId",required = false) Long employeeId){
        pickOrderService.distributePickOrderSingle(pickOrderId,employeeId);
        return true;
    }

    @PostMapping("/beginPickOrder")
    @ApiOperation(value = "开始拣货", notes = "开始拣货")
    public Boolean beginPickOrder(@RequestParam(value = "pickOrderId",required = false) Long pickOrderId) {
        return pickOrderService.beginPickOrder(pickOrderId);
    }

    @PostMapping("/shopPickOrderExistsMap")
    @ApiOperation(value = "查询指定状态门店拣货单是否存在的map", notes = "查询指定状态门店拣货单是否存在的map")
    public Map<Long,Boolean> getShopPickOrderExistsMap(@RequestBody QueryPickOrderDTO queryPickOrderDTO) {
        return pickOrderService.getShopPickOrderExistsMap(queryPickOrderDTO);
    }

}
