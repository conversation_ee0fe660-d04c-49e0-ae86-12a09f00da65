package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderReceiveIDTO {

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty("收货人id")
    private Long receiveId;

    @ApiModelProperty("收货人id")
    private String receiveMan;

    private List<ReceiveItem> list;

    @Data
    public static class ReceiveItem{

        private Long  cancelledOrderItemId;

        @ApiModelProperty(value = "商品id")
        private String commodityId;

        @ApiModelProperty(value = "价格")
        private BigDecimal price;

        @ApiModelProperty(value = "包装规格")
        private BigDecimal commodityPackageSpec;

        @ApiModelProperty(value = "订单数")
        private Integer orderNumber;

        @ApiModelProperty(value = "发货数")
        private Integer deliverNumber;

        @ApiModelProperty(value = "正常品入库份数")
        private Integer normalNumber;

        @ApiModelProperty(value = "异常品入库份数")
        private Integer abnormalNumber;
    }
}
