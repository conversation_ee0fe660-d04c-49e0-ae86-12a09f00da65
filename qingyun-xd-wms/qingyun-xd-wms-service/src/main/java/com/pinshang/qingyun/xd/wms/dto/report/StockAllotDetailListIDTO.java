package com.pinshang.qingyun.xd.wms.dto.report;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotDetailListIDTO extends Pagination<StockAllotDetailListODTO> {

    @ApiModelProperty(value = "出库门店")
    private Long outShop;

    @ApiModelProperty(value = "入库门店")
    private Long inShop;

    @ApiModelProperty(value = "调拨单号")
    private String orderCode;

    @ApiModelProperty(value = "状态 10待审核 20审核通过 30出库完成 40入库完成 50驳回")
    private Integer status;

    @ApiModelProperty(value = "开始出库日期")
    private String startOutTime;

    @ApiModelProperty(value = "结束出库日期")
    private String endOutTime;

    @ApiModelProperty(value = "开始入库日期")
    private String startInTime;

    @ApiModelProperty(value = "结束入库日期")
    private String endInTime;

    @ApiModelProperty(value = "商品关键字")
    private String commodityKey;

    @ApiModelProperty(value = "调入档口ID")
    private Long inStallId;

    @ApiModelProperty(value = "调出档口ID")
    private Long outStallId;

    private List<Long> outStallIdList;

    private List<Long> inStallIdList;




}
