package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.model.QualityReturnOrderPic;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface QualityReturnOrderPicMapper extends BaseMapper<QualityReturnOrderPic> {

    List<QualityReturnOrderPic> queryByReturnOrderList(@Param("idList") List<Long> idList);
}
