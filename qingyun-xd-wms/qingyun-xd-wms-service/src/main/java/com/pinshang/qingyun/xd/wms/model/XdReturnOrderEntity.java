package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 鲜食#退货单
 */
@TableName("t_xd_return_order")
@Data
public class XdReturnOrderEntity extends BaseEntity {
	
	/**门店Id*/
	private Long shopId;
    /**订单Id*/
    private Long orderId;
    /**用户ID*/
    private Long userId;
    /**退货单编号*/
    private String returnCode;
    /**订单来源 0=后台，1=App，3=小程序, 4饿了么*/
    private Integer sourceType;
	/**退货单日期*/
	private Date returnDate;
    /**状态:0-取消退货(app=取消退货),1-待审核(app=申请中),2-审核通过(app=待取货)，3取货中(app=取货中)，4-待退款 (app=待退款 ),5-已退货(app=已退款),6=退货失败(取货失败)*/
    private Integer status;
    /**退货总金额*/
    private BigDecimal totalAmount;
    /**商品份数*/
    private BigDecimal totalNumber;
    /**用户使用的积分*/
    private Integer integral;
    /**积分兑换比率*/
    private Integer integralRatio;
    /**退货原因id,字典数据*/
    private Long returnReasonId;
    /**退货原因*/
    private String returnReason;
    /**退货单类型:0-非整单,1-整单*/
    private Integer returnOrderType;
    /**备注(C端用户退款理由)*/
    private String remark;
    /**审核说明*/
    private String checkRemark;
    /**
     * 审核结果
     */
    private Integer checkType;
    /**
     * 预约收货起始时间
     */
    private Date receiveTimeBegin;
    /**
     * 预约收货结束时间
     */
    private Date receiveTimeEnd;
    /**
     * 操作人员(线下)
     */
    private String createName;

    private Integer cancelReason;

    /**
     * 第三方退单号
     */
    private String originalOrderCode;

    /**
     * 售后申请类型(1=退货退款, 2=仅退款)
     */
    private Integer refundType;

    /**
     *审核方(1- 京东客服, 可为空, 默认鲜到)
     */
    private Integer auditor;
}