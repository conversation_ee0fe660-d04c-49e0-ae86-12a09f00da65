package com.pinshang.qingyun.xd.wms.service.redislock;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class RedisLockService {
    @Autowired
    private RedissonClient redissonClient;

    @Value("${application.name.switch}")
    private String applicationNameSwitch;

    @Value("${pinshang.application-name}")
    private String applicationName;

    private String buildLockKey(RedisLockEnums redisLockEnums, String lockKey) {
        return StringUtils.joinWith(":", applicationNameSwitch, applicationName, "lock", redisLockEnums.getPrefix(), lockKey);
    }

    private RLock acquireLock(RedisLockEnums redisLockEnums, String lockKey) {
        QYAssert.notNull(redisLockEnums, "redisLockEnums不能为空");
        QYAssert.hasText(lockKey, "lockKey不能为空");

        String fullLockKey = buildLockKey(redisLockEnums, lockKey);
        RLock lock = redissonClient.getLock(fullLockKey);

        boolean tryLock;
        try {
            tryLock = lock.tryLock(redisLockEnums.getWaitTime(), redisLockEnums.getTtl(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new BizLogicException(StringUtils.join("尝试获取", redisLockEnums.getDescription(), "失败，请稍后重试！"));
        }
        if (!tryLock) {
            throw new BizLogicException(ApiErrorCodeEnum.REPEAT_SUBMIT);
        }
        return lock;
    }

    public void lock(RedisLockEnums redisLockEnums, String lockKey, Runnable runnable) {
        RLock lock = acquireLock(redisLockEnums, lockKey);
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("执行业务逻辑出现异常", e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    public void multiLock(RedisLockEnums redisLockEnums, Collection lockKeys, Runnable runnable) {
        RLock lock = acquireMultiLock(redisLockEnums, lockKeys);
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("执行业务逻辑出现异常", e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    private RLock acquireMultiLock(RedisLockEnums redisLockEnums, Collection<Object> lockKeys) {
        QYAssert.notNull(redisLockEnums, "redisLockEnums不能为空");
        QYAssert.notEmpty(lockKeys, "lockKey不能为空");

        RLock[] rLocks = buildRLocks(redisLockEnums, lockKeys);
        RLock lock = redissonClient.getMultiLock(rLocks);

        boolean tryLock;
        try {
            tryLock = lock.tryLock(redisLockEnums.getWaitTime(), redisLockEnums.getTtl(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            throw new BizLogicException(StringUtils.join("尝试获取", redisLockEnums.getDescription(), "失败，请稍后重试！"));
        }
        if (!tryLock) {
            throw new BizLogicException(ApiErrorCodeEnum.REPEAT_SUBMIT);
        }
        return lock;
    }

    private RLock[] buildRLocks(RedisLockEnums redisLockEnums, Collection<Object> lockKeys) {

        return lockKeys.stream()
                .distinct()
                .map(lockKey -> {
                    String lockKeyFull = buildLockKey(redisLockEnums, lockKey.toString());
                    return redissonClient.getLock(lockKeyFull);
                })
                .toArray(RLock[]::new);
    }

    public <T> T lock(RedisLockEnums redisLockEnums, String lockKey, Supplier<T> supplier) {
        RLock lock = acquireLock(redisLockEnums, lockKey);
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("执行业务逻辑出现异常", e);
            throw e;
        } finally {
            try {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                } else {
                    log.warn("当前线程不是锁的持有者，跳过 unlock，lockKey: {}", lockKey);
                }
            } catch (IllegalMonitorStateException e) {
                log.error("尝试解锁失败：当前线程未持有锁，lockKey: {}", lockKey, e);
            } catch (Exception e) {
                log.error("解锁失败，lockKey: {}", lockKey, e);
            }
        }
    }

    public <T> T multiLock(RedisLockEnums redisLockEnums, Collection lockKeys, Supplier<T> supplier) {
        RLock lock = acquireMultiLock(redisLockEnums, lockKeys);
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("执行业务逻辑出现异常", e);
            throw e;
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.error("多锁解锁异常，lockKeys: {}", lockKeys, e);
            }
        }
    }
}
