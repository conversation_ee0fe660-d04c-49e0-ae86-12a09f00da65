package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_xd_warehouse_work")
public class WarehouseWork extends BaseEntity {

    @ApiModelProperty(value = "加工点编号")
    private String workNo;

    @ApiModelProperty(value = "加工点名字")
    private String workName;

    @ApiModelProperty(value = "仓库Id")
    private Long warehouseId;

    @ApiModelProperty(value = "状态，0-停用(没有绑定货可停用),1-启用")
    private Integer status;
}
