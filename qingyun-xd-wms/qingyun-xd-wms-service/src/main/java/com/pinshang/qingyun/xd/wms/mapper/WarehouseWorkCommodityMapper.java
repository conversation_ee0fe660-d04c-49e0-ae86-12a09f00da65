package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListResult;
import com.pinshang.qingyun.xd.wms.model.WarehouseWorkCommodity;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseWorkCommodityMapper extends BaseMapper<WarehouseWorkCommodity> {

    /**
     * 查询加工点有多少商品
     * @param workId
     * @param warehouseId
     * @return
     */
    Integer countWarehouseWorkByWorkid(@Param("workId") Long workId, @Param("warehouseId") Long warehouseId);

    /**
     * 根据加工点查询商品信息
     * @param workCommodityListDTO
     * @return
     */
    MPage<WorkCommodityListResult> workCommodityList(@Param("dto") WorkCommodityListDTO workCommodityListDTO);

    /**
     * 查询加工点商品
     * @param commodityIds
     * @param warehouseId
     * @return
     */
    List<WorkCommodityListResult> queryWorkCommodityByIds(@Param("commodityIds") List<Long> commodityIds, @Param("warehouseId") Long warehouseId);

}
