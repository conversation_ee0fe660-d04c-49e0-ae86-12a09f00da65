package com.pinshang.qingyun.xd.wms.dto.groupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName CloudOrderForReturnDTO
 * <AUTHOR>
 * @Date 2021/6/25 17:32
 * @Description CloudOrderForReturnDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudOrderForReturnDTO {
    private Long orderId;
    private String orderCode;
    private Integer orderStatus;
    private String userMobile;
    private Date arrivalTime;
    private Long shopId;
    private String shopName;
}
