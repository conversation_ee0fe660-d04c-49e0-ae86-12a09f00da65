package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.service.DdShelvesService;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 排面货架管理  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Api(tags = "排面货架管理", description = "排面货架管理")
@RestController
@RequestMapping("/ddShelves")
public class DdShelvesController {

    @Autowired
    private DdShelvesService ddShelvesService;

    /**
    * 排面货架管理 列表
    * @param req
    * @return
    */
    @PostMapping("/page")
    @ApiOperation(value = "排面货架管理 列表")
    public PageInfo<DdShelvesODTO> page(@RequestBody DdShelvesPageIDTO req) {
        return ddShelvesService.page(req);
    }

    /**
    * 保存 排面货架管理
    * @param req
    */
    @PostMapping("/save")
    @ApiOperation(value = "保存 排面货架管理")
    public Boolean save(@RequestBody DdShelvesSaveIDTO req) {
        return ddShelvesService.save(req);
    }

    /**
    * 更新 排面货架管理
    * @param req
    */
    @PostMapping("/update")
    @ApiOperation(value = "更新 排面货架管理")
    public Boolean update(@RequestBody DdShelvesUpdateIDTO req) {
        return ddShelvesService.update(req);
    }

    /**
    * 删除 排面货架管理
    * @param req
    */
    @PostMapping("/delete")
    @ApiOperation(value = "更新 排面货架管理")
    public Boolean delete(@RequestBody DdShelvesDeleteIDTO req) {
        return ddShelvesService.delete(req);
    }

    @GetMapping("/list")
    @ApiOperation(value = "货架编码/名称查询")
    public List<DdShelvesODTO> list(@RequestParam("shelves") String shelves, @RequestParam(value = "stallId", required = false) Long stallId){
        return ddShelvesService.list(shelves, stallId);
    }

    @ApiOperation(value = "导入排面货架", notes = "导入排面货架", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public ExcelResult importExccel(@ApiParam(value = "待上传的文件") @RequestParam(value = "file", required = true) MultipartFile file,
                                    @ApiParam(value = "档口") @RequestParam(value = "stallId", required = true) Long stallId,
                                    @ApiParam(value = "门店") @RequestParam(value = "shopId", required = true) Long shopId) throws Exception {
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        Assert.notNull(wb, "工作簿不能为空");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "表单不能为空");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<DdShelvesSaveIDTO> shelveList = new ArrayList<>();
        QYAssert.isTrue(sheet.getLastRowNum() < 501, "每次最多导入500行");
        List<String> errorList = new ArrayList<>();
        sheet.forEach(row ->{
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货架编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "货架名称*"), "模板不正确");
            }
            // 第二行开始
            if (rowNum > 0) {
                DdShelvesSaveIDTO  shelve = new DdShelvesSaveIDTO();
                Cell codeCell = row.getCell(0);
                if(null == codeCell){
                    shelve.setShelveCode("");
                }else{
                    shelve.setShelveCode(codeCell.getStringCellValue().trim());
                }
                Cell nameCell = row.getCell(1);
                if(null == nameCell){
                    shelve.setShelveName("");
                }else {
                    shelve.setShelveName(nameCell.getStringCellValue().trim());
                }

                if (StringUtils.isEmpty(shelve.getShelveCode()) || StringUtils.isEmpty(shelve.getShelveName())){
                    errorList.add(rowNum + "行信息填写不完整");
                }
                if(shelve.getShelveCode().length() > 20){
                    errorList.add(rowNum + "行货架编码最长20个字");
                }
                if(shelve.getShelveName().length() > 50){
                    errorList.add(rowNum + "行货架编码最长50个字");
                }
                shelveList.add(shelve);
            }
        });
        if(SpringUtil.isNotEmpty(errorList)){
            return new ExcelResult(errorList, null);
        }
        QYAssert.isTrue(SpringUtil.isNotEmpty(shelveList), "导入数据不能为空");
        return ddShelvesService.importExcel(shelveList, stallId, shopId);
    }

}
