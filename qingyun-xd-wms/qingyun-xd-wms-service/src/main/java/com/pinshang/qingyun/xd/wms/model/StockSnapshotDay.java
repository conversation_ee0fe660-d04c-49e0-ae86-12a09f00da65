package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_xd_stock_snapshot_day")
public class StockSnapshotDay {

    private Long id;

    /**
     * 门店
     */
    private Long shopId;

    /**
     * 库存品项
     */
    private Integer stockItem;

    /**
     * 库存成本金额
     */
    private BigDecimal stockWeightPrice;

    /**
     * 库存成本日期
     */
    private Date stockDate;

    /**
     * 类型：1-负库存以0计算、2-负库存原样计算
     */
    private Integer stockStatus;

    private Date createTime;
}
