package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickPartitionOrderListResult extends BaseEntity {

    @ApiModelProperty(value = "拣货单code")
    private String pickOrderCode;

    @ApiModelProperty(value = "分区拣货单Id")
    private String pickPartitionOrderId;

    @ApiModelProperty(value = "分区拣货单code")
    private String pickPartitionOrderCode;

    @ApiModelProperty(value = "拣货分区Id")
    private Long pickAreaId;

    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA, fieldName = RenderFieldHelper.PickArea.pickAreaName, keyName = "pickAreaId")
    private String pickAreaName;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "打包口id")
    private Long packingStationId;

    @ApiModelProperty(value = "订单打包口名称")
    private String packingStationName;

    @ApiModelProperty(value = "分区拣货单状态(0=待拣货，1＝拣货中，2＝待交接，3＝已取消)")
    private Integer pickPartitionOrderStatus;

    @ApiModelProperty(value = "分区拣货单状态名称(0=待拣货，1＝拣货中，2＝待交接，3＝已取消)")
    private String pickPartitionOrderStatusName;

    @ApiModelProperty(value = "拣货人")
    private Long pickId;

    @ApiModelProperty(value = "拣货人姓名")
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "pickId")
    private String pickEmployeeName;

    @ApiModelProperty(value = "拣货完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickEndTime;

    @ApiModelProperty(value = "拣货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickBeginTime;

    @ApiModelProperty(value = "交接完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handoverEndTime;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "客户要求配送开始时间")
    private Date orderDeliveryBeginTime;

    @ApiModelProperty(value = "客户要求配送完成时间")
    private Date orderDeliveryEndTime;

    @ApiModelProperty(value = "分区拣货预警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date pickWarnTime;

    @ApiModelProperty(value = "创建人")
    private Long createId;

    @ApiModelProperty(value = "创建人姓名")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.realName, keyName = "createId")
    private String createName;

    /**
     * 分区拣货单明细使用
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiveMan;

    /**
     * 分区拣货单明细使用
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiveMobile;

    @ApiModelProperty("拣货单状态")
    private Integer pickOrderStatus;

    @ApiModelProperty("品项数")
    private Integer commodityCount;

    @ApiModelProperty("拣货单id")
    private String pickOrderId;


    public String getPickPartitionOrderStatusName() {
        return Optional.ofNullable(XdPickOrderStatusEnum.getByCode(pickPartitionOrderStatus)).map(XdPickOrderStatusEnum::getRemark).orElse("");
    }
}
