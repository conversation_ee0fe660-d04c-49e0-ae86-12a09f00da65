package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("PickPartitionOrderPageIDTO")
public class PickPartitionOrderPageIDTO/* extends Pagination<PickPartitionOrderPageODTO> */implements Serializable {

    private static final long serialVersionUID = -252533740514428100L;

    @ApiModelProperty("拣货中 1  待拣货 2 待交接 3")
    private Integer queryType;

}
