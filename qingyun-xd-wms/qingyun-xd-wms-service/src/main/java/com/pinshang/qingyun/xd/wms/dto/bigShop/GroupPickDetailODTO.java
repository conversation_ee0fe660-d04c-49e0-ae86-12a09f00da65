package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@ApiModel("GroupPickDetailODTO")
public class GroupPickDetailODTO implements Serializable {

    private static final long serialVersionUID = 5404417297796309662L;

    @ApiModelProperty("商品Id")
    private String commodityId;

    @ApiModelProperty("商品条码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品条码集合")
    private List<String> barCodeList;

    @ApiModelProperty("商品名")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("型号规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commoditySpec, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("称重")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.isWeight, keyName = "commodityId")
    private Integer isWeight;

    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityUnit, keyName = "commodityId")
    private String commodityUnit;

    @ApiModelProperty("包装规格")
    private String commodityPackageKind;

    @ApiModelProperty("拣货区货位编码")
    private String goodsAllocationCode;

    @ApiModelProperty("陈列位")
    private String displayPositionName;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty(value = "档口")
    @FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
    private String stallName;

    @ApiModelProperty("订购数量")
    private BigDecimal totalPickQuantity;

    @ApiModelProperty("订购份数")
    private Integer totalPickNumber;

    @ApiModelProperty("已拣货数量")
    private BigDecimal pickedQuantity;

    @ApiModelProperty("已拣货份数")
    private Integer pickedNumber;

    @ApiModelProperty("待拣货数量")
    private BigDecimal waitPickQuantity;

    @ApiModelProperty("待拣货份数")
    private Integer waitPickNumber;

    @ApiModelProperty("合单拣货商品明细")
    private List<GroupPickOrderDetailODTO> groupPickOrderDetailList;

    @ApiModelProperty("订单状态 0-已取消  1-待付款  2-待拣货  3-出库中 4-待配送  5-配送中  6-配送成功  7-配送失败  8-订单锁定")
    private Integer orderStatus;

    @JsonIgnore
    private Integer categorySortNum;

    @JsonIgnore
    private Integer goodsAllocationSortNum;

    @ApiModelProperty("单个分区拣货单时参数：表示该商品已经拣货")
    private Boolean alreadyPicked;

    @ApiModelProperty("拼接之后的商品位置")
    private String goodsPosition;

    @ApiModelProperty("收货人手机")
    private String receiveMobile;

    @ApiModelProperty("收货人")
    private String receiveMan;

    @ApiModelProperty("订单号")
    private String orderCode;

    public String getGoodsPosition() {
        if (SpringUtil.hasText(goodsAllocationCode)) {
            return goodsAllocationCode + "拣";
        }

        return displayPositionName;
    }
}
