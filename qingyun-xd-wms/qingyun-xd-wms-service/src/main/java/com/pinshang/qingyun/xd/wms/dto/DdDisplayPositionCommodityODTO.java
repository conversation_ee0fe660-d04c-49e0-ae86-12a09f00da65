package com.pinshang.qingyun.xd.wms.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 门店商品绑定陈列位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@ToString
@ApiModel("DdDisplayPositionCommodityODTO")
public class DdDisplayPositionCommodityODTO {

    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Long stallId;

    @ApiModelProperty("档口code")
    @ExcelProperty("档口编码")
    @FieldRender(fieldType = FieldTypeEnum.STALL, keyName = "stallId", fieldName = RenderFieldHelper.Stall.stallCode)
    private String stallCode;

    @ApiModelProperty("档口名称")
    @ExcelProperty("档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL, keyName = "stallId", fieldName = RenderFieldHelper.Stall.stallName)
    private String stallName;

    @ExcelProperty("商品编码")
    @ApiModelProperty("商品编码")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityCode,keyName = "commodityId")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("规格")
    @ExcelProperty( "规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commoditySpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @ExcelProperty( "计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityUnit;

    @ExcelIgnore
    @ApiModelProperty("pos是否可售, 1-是, 0-否")
    private Integer commoditySaleStatus;

    @ExcelProperty("pos可售")
    @ApiModelProperty("pos是否可售, 1-是, 0-否")
    private String commoditySaleStatusStr;

    @ExcelProperty("品类")
    @ApiModelProperty("品类")
    private String commodityKind;

    @ApiModelProperty("陈列位")
    @ExcelProperty("陈列位")
    private String displayPositionName;

    @ExcelProperty("陈列位最小数量")
    @ApiModelProperty("陈列位最小数量")
    private BigDecimal minStock;

    @ExcelProperty("陈列位最大数量")
    @ApiModelProperty("陈列位最大数量")
    private BigDecimal maxStock;

    @ExcelProperty("货架编码")
    @ApiModelProperty("排面货架编码")
    private String shelveCode;

    @ExcelProperty("货架名称")
    @ApiModelProperty("排面货架名称")
    private String shelveName;

    @ExcelIgnore
    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("一级品类")
    @ExcelIgnore
    @FieldRender(fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName, fieldType = FieldTypeEnum.COMMODITY_KIND, keyName = "commodityId")
    private String commodityFirstKindName;

    @ApiModelProperty("二级品类")
    @ExcelIgnore
    @FieldRender(fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName, fieldType = FieldTypeEnum.COMMODITY_KIND, keyName = "commodityId")
    private String commoditySecondKindName;

    @ApiModelProperty("三级品类")
    @ExcelIgnore
    @FieldRender(fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName, fieldType = FieldTypeEnum.COMMODITY_KIND, keyName = "commodityId")
    private String commodityThirdKindName;

    @ExcelIgnore
    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    public String getCommoditySaleStatusStr() {
        return 1 == this.commoditySaleStatus? "可售" : "不可售";
    }

    public String getCommodityKind() {
        String kindName =  null ==  this.commodityFirstKindName ? "" :  this.commodityFirstKindName;
        kindName = kindName + "-" + (null == this.commoditySecondKindName? "" :this.commoditySecondKindName);
        kindName = kindName + "-" + (null == this.commodityThirdKindName? "" :this.commodityThirdKindName);
        return kindName;
    }
}