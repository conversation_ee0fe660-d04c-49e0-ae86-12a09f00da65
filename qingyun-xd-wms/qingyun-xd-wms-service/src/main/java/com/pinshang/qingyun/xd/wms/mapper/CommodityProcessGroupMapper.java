package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.CommodityProcessName;
import com.pinshang.qingyun.xd.wms.model.CommodityProcessGroup;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommodityProcessGroupMapper extends BaseMapper<CommodityProcessGroup> {

    List<CommodityProcessName> commodityProcessNameList(@Param("list") List<Long> list);
}
