package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityBatchBindDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListResult;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseWorkCommodityService;
import com.pinshang.qingyun.xd.wms.util.ExcelExportUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/warehouse/work/commodity")
@Api(value = "加工点商品管理", tags = "WarehouseWorkCommodityController")
public class WarehouseWorkCommodityController {

    @Autowired
    private WarehouseWorkCommodityService warehouseWorkCommodityService;

    @PostMapping("/batchBind")
    @ApiOperation(value = "批量绑定", notes = "批量绑定")
    public void batchBind(@RequestBody WorkCommodityBatchBindDTO dto) {
        warehouseWorkCommodityService.batchBind(dto);
    }

    @PostMapping("/deleteBind")
    @ApiOperation(value = "批量解绑", notes = "批量解绑")
    public void deleteBind(@RequestBody List<Long> list) {
        warehouseWorkCommodityService.deleteBind(list);
    }

    @PostMapping("/workCommodityList")
    @ApiOperation(value = "加工点商品查询", notes = "加工点商品查询")
    public MPage<WorkCommodityListResult> workCommodityList(@RequestBody WorkCommodityListDTO dto) {
        return warehouseWorkCommodityService.workCommodityList(dto);
    }

    @GetMapping("/workCommodityListExport")
    @ApiOperation(value = "加工点商品导出", notes = "加工点商品导出")
    @FileCacheQuery(bizCode = "WAREHOUSE_WORK_COMMODITY")
    public void workCommodityListExport(WorkCommodityListDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        MPage<WorkCommodityListResult> res = warehouseWorkCommodityService.workCommodityList(dto);
        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "加工点商品列表" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("加工点");
        tableHeader.add("商品编码");
        tableHeader.add("商品名称");
        tableHeader.add("商品条码");
        tableHeader.add("规格");
        tableHeader.add("前台加工方式");
        StringBuilder stringBuilder = new StringBuilder();
        List<List<String>> dataList = new ArrayList<>();
        if(res!=null && SpringUtil.isNotEmpty(res.getList())) {
            for (WorkCommodityListResult e : res.getList()) {
                stringBuilder.setLength(0);
                List<String> row = new ArrayList<>();
                row.add(e.getWorkName());
                row.add(e.getCommodityCode());
                row.add(e.getCommodityName());
                row.add(e.getBarCode());
                row.add(e.getCommoditySpec());
                row.add(e.getProcessName());
                dataList.add(row);
            }
        }

        //覆盖文件名, 无需扩展名
        fileName = "加工点商品列表" + sdf.format(new Date());
        ExcelUtil.setFileNameAndHead(response,  fileName);
        List<List<String>> excelHead = tableHeader.stream().map(Collections::singletonList).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet("数据").doWrite(dataList);

        /* 已重构, 后续稳定后删除
        XSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbook(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcel(response,fileName,xb);
        */
    }
}
