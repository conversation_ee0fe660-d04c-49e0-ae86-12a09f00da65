package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.StallCommodityEntry;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoSearchIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.SelectStallCommodityByCommodityBarCodeIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.SelectStallCommodityByCommodityCodeIDTO;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 档口商品  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Api(tags = "档口商品", description = "档口商品")
@RestController
@RequestMapping("/bigShop/stallCommodity")
public class StallCommodityController {

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private StallCommodityService stallCommodityService;


    /**
     * 根据商品编号模糊搜索商品基本信息
     *
     * @param req 搜索条件
     * @return 分页返回商品基本信息
     */
    @PostMapping(value = "/fuzzySearchByCommodityCode")
    @ApiOperation(value = "PDA搜索档口商品")
    @MethodRender
    public List<CommodityBaseInfoODTO> fuzzySearchByCommodityCode(@RequestBody CommodityBaseInfoSearchIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return stallCommodityService.fuzzySearchByCommodityCode(req);
    }

    @PostMapping(value = "/listByCommodity")
    @ApiOperation(value = "搜索档口商品 目前用于品鲜后台 ")
    public List<CommodityBaseInfoODTO> listByCommodity(@RequestBody CommodityBaseInfoSearchIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return stallCommodityService.listByCommodity(req);
    }

    @GetMapping(value = "/searchByCommodityIdAndStall")
    @ApiOperation(value = "根据商品id和档口id查询商品信息")
    public List<StallCommodity> searchByCommodityIdAndStall(@RequestParam("commodityId") Long commodityId, @RequestParam("stallId") Long stallId) {
        return stallCommodityService.searchByCommodityIdAndStall(commodityId, stallId);
    }


}
