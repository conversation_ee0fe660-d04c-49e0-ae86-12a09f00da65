package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@ApiModel("DdDisplayPositionCommodityDeleteIDTO")
@AllArgsConstructor
@NoArgsConstructor
public class DdDisplayPositionCommodityDeleteODTO {
    @ApiModelProperty("是否成功, 0-成功, 1-失败")
    private Integer code;

    @ApiModelProperty("失败时候错误提示")
    private String msg;
}