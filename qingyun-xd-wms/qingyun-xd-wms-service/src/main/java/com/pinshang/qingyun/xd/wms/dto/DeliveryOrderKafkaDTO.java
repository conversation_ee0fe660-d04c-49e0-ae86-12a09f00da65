package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderKafkaDTO {

    /**
     * 配送流程-订单号，退货流程-退货单号
     * */
    private Long orderId;

    /**
     * 配送流程-订单编号，退货流程-退货单编号
     */
    private String orderCode;

    /**
     * @see XdDeliveryOrderStatusEnum#getCode()
     */
    private Integer deliveryStatus;
    /**
     * 仓库id (门店id)
     */
    private Long warehouseId;

    /**
     * 配送取货位
     */
    private String shelfNo;

    /**
     * @see XdDeliveryOrderTypeEnum#getCode()
     */
    private Integer deliveryType;

    /**
     * 配送员id
     */
    private Long deliveryUserId;
    /**
     * 配送员姓名
     */
    private String deliveryUserName;
    /**
     * 配送员电话
     */
    private String deliveryUserPhone;
    /**
     * 配送失败原因
     */
    private Long reasonId;
    /**
     * 是否超时,0-未超时，1-超时
     */
    private Integer ifOvertime;
    /**
     * 没有和后台配置比对过的超时时间(毫秒值),若超时则为负值
     */
    private Long deliveryTimeOut;
    /**
     * 实际配送完成时间
     */
    private Date deliveryEndTime;
    /**
     * 客户要求配送完成时间
     */
    private Date orderDeliveryEndTime;

    private OrderSourceTypeEnum sourceType;

    private List<PickOrderItemMqDTO> items;
}
