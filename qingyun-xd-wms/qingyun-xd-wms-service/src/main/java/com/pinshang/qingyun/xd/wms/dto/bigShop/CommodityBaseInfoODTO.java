package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@NoArgsConstructor
@AllArgsConstructor
public class CommodityBaseInfoODTO {

    @ApiModelProperty(value = "商品id ")
    private String commodityId;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;


    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "商品后台名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位")
    private String commodityUnitName;

    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("pos是否可售, 1-是, 0-否")
    private Integer commoditySaleStatus;

    @ApiModelProperty("上下架状态：0-上架，1-下架")
    private Integer appStatus;

    @ApiModelProperty(value = "包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty(value = "临时库存数量")
    private BigDecimal qualityQuantity;

    private String isWeightStr;
    @ApiModelProperty(value = "商品重量")
    private BigDecimal weight;

    public String getIsWeightStr() {
        return YesOrNoEnums.getName(isWeight);
    }

    public String getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(String commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommoditySpec() {
        return commoditySpec;
    }

    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }

    public String getCommodityUnitName() {
        return commodityUnitName;
    }

    public void setCommodityUnitName(String commodityUnitName) {
        this.commodityUnitName = commodityUnitName;
    }

    public String getCommodityPackageKind() {
        return commodityPackageKind;
    }

    public void setCommodityPackageKind(String commodityPackageKind) {
        this.commodityPackageKind = commodityPackageKind;
    }

    public Integer getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(Integer isWeight) {
        this.isWeight = isWeight;
    }

    public Integer getCommoditySaleStatus() {
        return commoditySaleStatus;
    }

    public void setCommoditySaleStatus(Integer commoditySaleStatus) {
        this.commoditySaleStatus = commoditySaleStatus;
    }

    public Integer getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(Integer appStatus) {
        this.appStatus = appStatus;
    }

    public BigDecimal getCommodityPackageSpec() {
        return commodityPackageSpec;
    }

    public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
        this.commodityPackageSpec = commodityPackageSpec;
    }

    public BigDecimal getQualityQuantity() {
        return qualityQuantity;
    }

    public void setQualityQuantity(BigDecimal qualityQuantity) {
        this.qualityQuantity = qualityQuantity;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }
}