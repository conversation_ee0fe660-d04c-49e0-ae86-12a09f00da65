package com.pinshang.qingyun.xd.wms.enums;

/**
 * @ClassName CloudTakeGoodErroCodeEnum
 * <AUTHOR>
 * @Date 2023/8/4 18:22
 * @Description CloudTakeGoodErroCodeEnum
 * @Version 1.0
 */
public enum CloudTakeGoodErrorCodeEnum {
    NO_PICK(2, "还未拣货, 请先拣货"),
    ERROR_SHOP(4, "此订单预约的提货门店不是当前门店"),
    ERROR_CODE(5, "核销码或订单号不存在"),
    ERROR_RETURN_CODE(6, "退货码或退货单号不存在"),
    ERROR_RETURN_STATUS(7, "退货单不是待退货到店状态, 不允许入库"),
    ERROR_RETURN_SHOP(8, "此退货单退货门店不是当前门店"),;

    private Integer code;

    private String desc;

    CloudTakeGoodErrorCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
