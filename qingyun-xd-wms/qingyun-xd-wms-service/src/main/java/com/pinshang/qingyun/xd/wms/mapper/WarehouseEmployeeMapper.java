package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface WarehouseEmployeeMapper extends BaseMapper<WarehouseEmployee>{

    int batchInsert(@Param("list") List<WarehouseEmployee> list);

    List<WarehouseEmployee> getByEmployeeIds(@Param("list") Set<Long> empIds);

    /**
     * 根据员工id和类型查询员工工作状态
     * @param employee
     * @return
     */
    WarehouseEmployee getByEmployeeIdAndType(DelWarehouseEmployee employee);

    List<WarehouseEmployeeInfoDTO> searchEmployeeByType(SearchWarehouseEmployee warehouseEmployee);

    int deleteByEmployeeId(DelWarehouseEmployee delWarehouseEmployee);

    int deleteByEmployeeIds(@Param("ids") List<Long> ids);

    int doWorkStatus(@Param("employeeId") long employeeId, @Param("workStatus") Integer workStatus);

    /**
     * 通过employeeId更新仓库员工信息
     * @param warehouseEmployee
     * @return
     */
    int updateByEmployeeId(WarehouseEmployee warehouseEmployee);

    MPage<WarehouseEmployeeResult> employeeList(@Param("dto") QueryWarehouseEmployee dto);

    Long getUserId(@Param("employeeId") Long employeeId);

    List<WarehouseEmployeeInfoDTO> getEmployeeUserByIdList(@Param("userIdList") List<Long> userIdList);

    List<WarehouseEmployeeInfoDTO> searchEmployeeByTypeForCloud(GetEmployeeByTypeAndShopIDTO idto);
}
