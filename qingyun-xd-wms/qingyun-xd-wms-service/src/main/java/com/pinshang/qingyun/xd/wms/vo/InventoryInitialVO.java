package com.pinshang.qingyun.xd.wms.vo;

import com.pinshang.qingyun.base.enums.shop.ShopCommodityBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName InventoryInitialVO
 * <AUTHOR>
 * @Date 2021/7/20 18:37
 * @Description InventoryInitialVO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryInitialVO {
    private Long referOrderId;
    private String referOrderCode;
    private Long shopId;
    private Long storeId;
    private Long userId;
    private StockInOutTypeEnums stockInOutTypeEnums;
    private List<InventoryInitialItemVO> itemList;
    private List<CommodityPriceVO> commodityPriceList;
    private ShopCommodityBusinessTypeEnum businessTypeEnum;

    private String orderCode;
}
