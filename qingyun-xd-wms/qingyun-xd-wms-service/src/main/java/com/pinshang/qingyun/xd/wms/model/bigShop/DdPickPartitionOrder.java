package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 大店分区拣货单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@ToString
@TableName("t_dd_pick_partition_order")
public class DdPickPartitionOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 拣货单id
     */
    private Long pickOrderId;

    /**
     * 分区拣货单code
     */
    private String pickPartitionOrderCode;

    /**
     * 拣货分区id
     */
    private Long pickAreaId;

    /**
     * 拣货人id
     */
    private Long pickId;

    /**
     * 分区拣货单状态(0=待拣货，1＝拣货中，2＝待交接，3＝已取消)
     * XdPickOrderStatusEnum
     */
    private Integer pickStatus;

    /**
     * 子单拣货开始时间
     */
    private Date pickBeginTime;

    /**
     * 拣货完成时间
     */
    private Date pickEndTime;

    /**
     * 交接完成时间
     */
    private Date handoverEndTime;

    /**
     * 修改人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    private Date updateTime;

}
