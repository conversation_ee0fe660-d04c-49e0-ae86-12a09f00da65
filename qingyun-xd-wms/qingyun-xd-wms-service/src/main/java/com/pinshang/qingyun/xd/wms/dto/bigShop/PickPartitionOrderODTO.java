package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@ToString
@ApiModel("PickPartitionOrderODTO")
public class PickPartitionOrderODTO implements Serializable {

    private static final long serialVersionUID = -8189740490682526920L;

    @ApiModelProperty(value = "分区拣货子单列表")
    private List<PickPartitionOrderPageODTO> pickPartitionOrderList;

    @ApiModelProperty(value = "取消单数量")
    private Integer cancelPickOrderNum;

    @ApiModelProperty(value = "拣货中数量")
    private Integer pickingOrderNum;

    @ApiModelProperty(value = "待拣货数量")
    private Integer waitingPickOrderNum;

    @ApiModelProperty(value = "待交接数量")
    private Integer waitingHandoverOrderNum;

    public Integer getPickingOrderNum() {
        return Objects.isNull(pickingOrderNum) ? 0 : pickingOrderNum;
    }

    public Integer getWaitingPickOrderNum() {
        return Objects.isNull(waitingPickOrderNum) ? 0 : waitingPickOrderNum;
    }

    public Integer getWaitingHandoverOrderNum() {
        return Objects.isNull(waitingHandoverOrderNum) ? 0 : waitingHandoverOrderNum;
    }
}
