package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderSyncReqVo {

    @ApiModelProperty("订单信息")
    private List<PackageOrderSyncStatusReqVo> orderList;
    @ApiModelProperty("比较的类型：1-包裹单，2-包裹单明细")
    private Integer packageFlag;

}
