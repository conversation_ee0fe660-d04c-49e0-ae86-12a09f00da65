package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("PartitionPickOrderPageIDTO")
public class PartitionPickOrderPageIDTO extends Pagination<PartitionPickOrderPageODTO> implements Serializable {

    private static final long serialVersionUID = 2390904577506232380L;

    @ApiModelProperty(value = "拣货子单号")
    private String pickPartitionOrderCode;

    @ApiModelProperty("未完成 false  已完成 true")
    private Boolean completed;

    @JsonIgnore
    private Long pickAreaId;

    @JsonIgnore
    private List<Integer> pickStatusList;
}
