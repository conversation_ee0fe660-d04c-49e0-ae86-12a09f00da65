package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@ApiModel("GroupPickOrderDetailODTO")
public class GroupPickOrderDetailODTO implements Serializable {

    private static final long serialVersionUID = -3141980364032132275L;

    @ApiModelProperty("订单短号")
    private String orderNum;

    @ApiModelProperty("订购数量")
    private BigDecimal quantity;

    @ApiModelProperty("拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty("订购份数")
    private Integer stockNumber;

    @ApiModelProperty("拣货份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单明细id")
    private Long pickOrderItemId;

    @ApiModelProperty("分区拣货子单id")
    private Long pickPartitionOrderId;

    @ApiModelProperty("加工名称")
    private String processName;

    @ApiModelProperty("缺货处理方式 1-缺货时与我电话沟通 2-退款其他商品继续配送（缺货商品直接)")
    private Integer lackProcessMode;

    @ApiModelProperty("收货人手机")
    private String receiveMobile;

    @ApiModelProperty("收货人")
    private String receiveMan;

    @ApiModelProperty("订单号")
    private String orderCode;

}
