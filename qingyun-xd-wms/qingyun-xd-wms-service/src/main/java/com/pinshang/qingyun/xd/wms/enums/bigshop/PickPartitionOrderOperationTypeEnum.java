package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * <AUTHOR>
 */
public enum PickPartitionOrderOperationTypeEnum {

    PARTION_PICK_ORDER_CREATE(1, "拣货子单创建"),
    DISTRIBUTE_PICKER(2, "分配拣货员"),
    CHANGE_PICKER(3, "改派拣货员"),
    BEGIN_PICK_ORDER(4, "开始拣货"),
    COMPLETE_PICK_ORDER(5, "拣货完成"),
    HANDLOVER_DELIVERY(6, "交接完成"),
    CANCEL_PICK_ORDER(7, "取消拣货"),
    CANCEL_ORDER_RECYCLE(8, "取消单回收"),
    ;
    private Integer code;
    private String name;

    PickPartitionOrderOperationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
