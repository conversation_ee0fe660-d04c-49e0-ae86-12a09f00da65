package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.PickCompleteItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@ToString
@ApiModel("PrinterBindSaveVO")
public class PrinterBindSaveVO {

    /**
     * 打印机编码
     */
    @ApiModelProperty(value = "打印机编码")
    private String printerCode;

    /**
     * 使用方类型
     * @see com.pinshang.qingyun.xd.wms.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "使用方类型（1-加工点 2-打包口）")
    private Integer userType;

    /**
     * 使用方
     */
    @ApiModelProperty(value = "使用方Id")
    private Long realUserId;

    public void checkData() {
        QYAssert.isTrue(StringUtils.isNotEmpty(printerCode), "打印机编码不能为空");
        QYAssert.isTrue(Objects.nonNull(userType), "使用方类型不能为空");
        QYAssert.isTrue(Objects.nonNull(realUserId), "使用方不能为空");
    }
}
