package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.PickStatusEnum;
import com.pinshang.qingyun.xd.wms.mapper.CancelledOrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.CancelledOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.OrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import com.pinshang.qingyun.xd.wms.model.CancelledOrder;
import com.pinshang.qingyun.xd.wms.model.CancelledOrderItem;
import com.pinshang.qingyun.xd.wms.model.Order;
import com.pinshang.qingyun.xd.wms.model.PickOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */

@Service
@Slf4j
public class CancelledOrderService {

    @Autowired
    private CancelledOrderMapper cancelledOrderMapper;

    @Autowired
    private CancelledOrderItemMapper cancelledOrderItemMapper;

    @Autowired
    private WarehouseShelfService shelfService;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 查询取消单列表，分页
     * @param idto
     * @return
     */
    public MPage<CancelledOrderODTO> queryCancelledOrderPage(CancelledOrderIDTO idto) {
        if(StringUtils.isNotBlank(idto.getOrderTimeBegin()) && StringUtils.isNotBlank(idto.getOrderTimeEnd())){
            idto.setOrderTimeBegin(idto.getOrderTimeBegin() + " 00:00:00");
            idto.setOrderTimeEnd(idto.getOrderTimeEnd() + " 23:59:59");
        }

        if(StringUtils.isNotBlank(idto.getCancelTimeBegin()) && StringUtils.isNotBlank(idto.getCancelTimeEnd())){
            idto.setCancelTimeBegin(idto.getCancelTimeBegin() + " 00:00:00");
            idto.setCancelTimeEnd(idto.getCancelTimeEnd() + " 23:59:59");
        }

        if(StringUtils.isNotBlank(idto.getStockInTimeBegin()) && StringUtils.isNotBlank(idto.getStockInTimeEnd())){
            idto.setStockInTimeBegin(idto.getStockInTimeBegin() + " 00:00:00");
            idto.setStockInTimeEnd(idto.getStockInTimeEnd() + " 23:59:59");
        }
        return cancelledOrderMapper.queryCancelledOrderPage(idto);
    }

    /**
     *
     * @param orderCode
     * @return
     */
    public List<CancelledOrderItemODTO> queryCancelledOrderItemList(String orderCode,String userName) {
        QYAssert.isTrue(StringUtils.isNotBlank(orderCode), "订单编码不能为空");

        List<CancelledOrderItemODTO> cancelledOrderItemList = cancelledOrderMapper.queryCancelledOrderItemList(orderCode);
        if(CollectionUtils.isNotEmpty(cancelledOrderItemList)){
            Map<Long,String> shelfMap = new HashMap<>();
            List<Long> commodityIdList = cancelledOrderItemList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());

            ShopShelfReqDTO reqDTO = new ShopShelfReqDTO();
            reqDTO.setShopId(cancelledOrderItemList.get(0).getShopId());
            reqDTO.setCommdityIdList(commodityIdList);
            List<ShopShelfResDTO> shelfList = shelfService.queryShopShelf(reqDTO);
            if(CollectionUtils.isNotEmpty(shelfList)){
                shelfMap = shelfList.stream().collect(Collectors.toMap(ShopShelfResDTO::getCommodityId,ShopShelfResDTO::getShelfNo,(key1 , key2)-> key2));
            }

            Map<Long, List<String>> barCodeMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIdList);

            for(CancelledOrderItemODTO dto : cancelledOrderItemList){
                dto.setShelfNo(shelfMap.get(Long.valueOf(dto.getCommodityId())));

                if (barCodeMap.containsKey(Long.valueOf(dto.getCommodityId()))) {
                    dto.setBarCodes(String.join(",", barCodeMap.get(Long.valueOf(dto.getCommodityId()))));
                }
                //dto.setBarCode(StringUtils.isNotBlank(dto.getBarCodes()) ? dto.getBarCodes().split(",")[0]:"");
                if(StringUtils.isBlank(dto.getReceiveMan())){
                    dto.setReceiveMan(userName);
                }
            }
        }
        return cancelledOrderItemList;
    }

    /**
     * 取消单收货操作
     * @param idto
     * @return
     */
    @Transactional
    public Boolean cancelledOrderReceive(CancelledOrderReceiveIDTO idto) {
        // 更新主单
        LambdaQueryWrapper query = new LambdaQueryWrapper<CancelledOrder>()
                .eq(CancelledOrder::getOrderCode, idto.getOrderCode());
        CancelledOrder  order = cancelledOrderMapper.selectOne(query);
        QYAssert.isTrue(null != order, "取消单信息不存在");
        QYAssert.isTrue(order.getStockInStatus() != 1, "取消单已入库");


        CancelledOrder cancelledOrder = new CancelledOrder();
        cancelledOrder.setReceiveId(idto.getReceiveId());
        cancelledOrder.setReceiveMan(idto.getReceiveMan());
        cancelledOrder.setUpdateId(idto.getReceiveId());
        cancelledOrder.setUpdateTime(new Date());
        cancelledOrder.setStockInTime(new Date());
        cancelledOrder.setStockInStatus(1);
        cancelledOrder.setId(order.getId());
        cancelledOrderMapper.updateById(cancelledOrder);

        // 更新明细
        for(CancelledOrderReceiveIDTO.ReceiveItem item : idto.getList()){
            Integer number = item.getNormalNumber() + (item.getAbnormalNumber() == null ? 0 : item.getAbnormalNumber());
            QYAssert.isTrue(number <= item.getDeliverNumber(), "正常品入库数加异常品数不能超过实发数");
            CancelledOrderItem cancelledOrderItem = new CancelledOrderItem();
            cancelledOrderItem.setUpdateId(idto.getReceiveId());
            cancelledOrderItem.setUpdateTime(new Date());
            cancelledOrderItem.setNormalNumber(item.getNormalNumber());
            cancelledOrderItem.setAbnormalNumber(item.getAbnormalNumber());
            cancelledOrderItem.setId(item.getCancelledOrderItemId());
            cancelledOrderItemMapper.updateById(cancelledOrderItem);
        }

        //执行收货操作
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setIfReturnReceipt(true);
        stockReceiptIDTO.setUserId(idto.getReceiveId());
        stockReceiptIDTO.setReferId(order.getId());
        stockReceiptIDTO.setReferCode(order.getCancelledOrderCode());
        stockReceiptIDTO.setWarehouseId(order.getShopId());
        List<StockReceiptItemDTO> commodityList = new ArrayList<>();
        for(CancelledOrderReceiveIDTO.ReceiveItem item : idto.getList()){
            StockReceiptItemDTO receiptItem = new StockReceiptItemDTO();

            receiptItem.setCommodityId(Long.valueOf(item.getCommodityId()));
            receiptItem.setNormalQuantity(new BigDecimal(item.getNormalNumber()).multiply(item.getCommodityPackageSpec()));
            receiptItem.setAbnormalQuantity(item.getAbnormalNumber() == null ? BigDecimal.ZERO : new BigDecimal(item.getAbnormalNumber()).multiply(item.getCommodityPackageSpec()));
            receiptItem.setQuantity(receiptItem.getNormalQuantity().add(receiptItem.getAbnormalQuantity()));
            receiptItem.setNormalNumber(item.getNormalNumber());
            receiptItem.setAbnormalNumber(item.getAbnormalNumber() == null ? 0 : item.getAbnormalNumber());
            receiptItem.setNumber(receiptItem.getNormalNumber()+receiptItem.getAbnormalNumber());
            receiptItem.setPrice(item.getPrice());
            receiptItem.setTotalPrice(receiptItem.getPrice().multiply(receiptItem.getQuantity()));

            if(receiptItem.getQuantity().compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            commodityList.add(receiptItem);
        }
        stockReceiptIDTO.setCommodityList(commodityList);
        stockServiceAdapter.stockReceipt(stockReceiptIDTO);
        return true;
    }

    /**
     * 饿了么取消订单接受消息
     * @param cancelledOrderDTO
     */
    @Transactional
    public void elemCancelledOrder(CancelledOrderDTO cancelledOrderDTO) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<CancelledOrder>()
                .eq(CancelledOrder::getOrderCode, cancelledOrderDTO.getOrderCode());
        Integer count = cancelledOrderMapper.selectCount(query);
        if (count > 0) {
            return;
        }

        //判断拣货单 一定是拣货完成 才确定是正常取消单
        LambdaQueryWrapper orderQuery = new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderCode, cancelledOrderDTO.getOrderCode());
        Order order = orderMapper.selectOne(orderQuery);

        LambdaQueryWrapper pickQuery = new LambdaQueryWrapper<PickOrder>()
                .eq(PickOrder::getOrderId, order.getId());
        PickOrder pickOrder = pickOrderMapper.selectOne(pickQuery);

        if(pickOrder == null || pickOrder.getPickStatus() != PickStatusEnum.FINISH.getCode()){
            return;
        }

        CancelledOrder cancelledOrder = new CancelledOrder();
        BeanUtils.copyProperties(cancelledOrderDTO,cancelledOrder);

        String code = "";
        if(cancelledOrderDTO.getSourceType().equals(OrderSourceTypeEnum.ELM.getCode())){
            code = codeClient.createCode("ELM_XD_CODE");
            cancelledOrder.setSourceType(OrderSourceTypeEnum.ELM.getCode());
        }
        if(cancelledOrderDTO.getSourceType().equals(OrderSourceTypeEnum.JDDJ.getCode())){
            code = codeClient.createCode("JDDJ_XD_CODE");
        }
        if(cancelledOrderDTO.getSourceType().equals(OrderSourceTypeEnum.MTSG.getCode())){
            code = codeClient.createCode("MTSG_XD_CODE");
        }

        cancelledOrder.setCancelledOrderCode(code);
        cancelledOrder.setCancelTime(new Date());
        cancelledOrder.setCreateId(1L);
        cancelledOrder.setCreateTime(new Date());
        cancelledOrder.setUpdateId(1L);
        cancelledOrder.setUpdateTime(new Date());
        cancelledOrderMapper.insert(cancelledOrder);
        for(CancelledOrderItemDTO item : cancelledOrderDTO.getItemList()){
            CancelledOrderItem cancelledOrderItem = new CancelledOrderItem();
            BeanUtils.copyProperties(item,cancelledOrderItem);
            cancelledOrderItem.setCancellecOrderId(cancelledOrder.getId());
            cancelledOrderItem.setCreateId(1L);
            cancelledOrderItem.setCreateTime(new Date());
            cancelledOrderItem.setUpdateId(1L);
            cancelledOrderItem.setUpdateTime(new Date());
            cancelledOrderItemMapper.insert(cancelledOrderItem);
        }
    }
}
