package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.enums.EmployeeStateEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/11/5
 */
@Data
public class DdPickAreaEmployeeODTO {

    @ApiModelProperty("id")
    private String idStr;

    @ApiModelProperty("门店id")
    private Long shopId;

    private Long pickAreaId;

    @ApiModelProperty("拣货分区")
    private String pickAreaName;

    private String employeeId;

    @ApiModelProperty("职员编码")
    private String employeeCode;

    @ApiModelProperty("职员名称")
    private String employeeName;

    @ApiModelProperty("职员状态：1-在职、4-离职(smm.t_smm_employee 字段)")
    private Integer employeeState;

    @ApiModelProperty("是否接单  0=否  1=是")
    private Integer workStatus;

    @ApiModelProperty("合并拣货状态  0=否  1=是")
    private Integer partitionPickStatus;

    @ApiModelProperty("任务数")
    private Integer taskCount;

    public String getWorkStatusName() {
       if(YesOrNoEnums.YES.getCode().equals(workStatus)) {
           return "是";
       }else {
           return "否";
       }
    }

    public String getEmployeeStateName() {
        if(employeeState != null) {
            return EmployeeStateEnums.getName(employeeState);
        }
        return "";
    }
}
