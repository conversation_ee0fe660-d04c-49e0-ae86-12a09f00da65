package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWorkStatusDTO {

    @ApiModelProperty(value = "操作类型")
    private Integer workStatus;

    @ApiModelProperty(value = "warehouse_employee_id")
    private List<Long> list;

    public void checkData() {
        QYAssert.notNull(workStatus,"操作类型不能为空");
        QYAssert.notEmpty(list, "员工编号不能为空");
    }
}
