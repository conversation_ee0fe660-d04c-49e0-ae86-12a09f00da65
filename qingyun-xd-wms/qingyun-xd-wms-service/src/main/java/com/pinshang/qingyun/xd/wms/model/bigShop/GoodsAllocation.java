package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.ToString;

@ToString
@TableName("t_dd_goods_allocation")
public class GoodsAllocation extends BaseEntity {

    private Long shopId;

    /**
     *货位号
     */
    private String goodsAllocationCode;

    /**
     * 所属区域
     */
    private Long areaId;

    /**
     * 所属档口
     */
    private Long stallId;

    /**
     * 库区 1排面区 2拣货区 3存储区 4临时库
     */
    private Integer storageArea;

    /**
     *排序值：值越小越靠前
     */
    private Integer sortNum;

    /**
    * 状态
     */
    private Integer status;

    @TableField(exist = false)
    private String storageAreaName;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getGoodsAllocationCode() {
        return goodsAllocationCode;
    }

    public void setGoodsAllocationCode(String goodsAllocationCode) {
        this.goodsAllocationCode = goodsAllocationCode;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Integer getStorageArea() {
        return storageArea;
    }

    public void setStorageArea(Integer storageArea) {
        this.storageArea = storageArea;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStorageAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.storageArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }
}
