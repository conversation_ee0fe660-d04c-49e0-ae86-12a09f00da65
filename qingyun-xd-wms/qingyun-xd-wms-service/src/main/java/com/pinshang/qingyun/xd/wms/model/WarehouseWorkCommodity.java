package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_xd_warehouse_work_commodity")
public class WarehouseWorkCommodity extends BaseEntity {

    @ApiModelProperty(value = "加工点id")
    private Long workId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
}
