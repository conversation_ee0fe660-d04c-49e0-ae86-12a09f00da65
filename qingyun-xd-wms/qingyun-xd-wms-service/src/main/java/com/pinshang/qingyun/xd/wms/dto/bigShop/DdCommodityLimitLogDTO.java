package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/10/17
 */
@Data
public class DdCommodityLimitLogDTO {

    private Long shopId;
    private Long stallId;
    private String stallCode;
    private String stallName;

    private Long commodityId;
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityCode, keyName = "commodityId")
    private String commodityCode;
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barCode;

    private Integer limitNumber;
    private String limitTypeName;
    private String effectTypeName;
    private String effectBeginTime;
    private String loopTime;

    private Long createId;
    private String createUserNo;
    private String createUserName;
    private String createTime;

    /** 操作类型 1=新增  2=删除  3=修改 */
    private Integer operationType;
}
