package com.pinshang.qingyun.xd.wms.service;

import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.DdDisplayPositionCommodityBatchSaveIDTO.DdDisplayPositionCommodityIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdDisplayPositionCommoditySaveODTO;
import com.pinshang.qingyun.xd.wms.enums.bigshop.BigShopOperateTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.DdDisplayPositionMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdDisplayPositionCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.DdDisplayPosition;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdDisplayPositionCommodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdDisplayPositionCommodityVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 门店商品绑定陈列位  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@Service
public class DdDisplayPositionCommoditySettingService extends ServiceImpl<DdDisplayPositionCommodityMapper, DdDisplayPositionCommodity> {

    @Autowired
    private DdDisplayPositionCommodityMapper ddDisplayPositionCommodityMapper;

    @Autowired
    private DdDisplayPositionService ddDisplayPositionService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private StallCommodityMapper stallCommodityMapper;

    @Autowired
    private DdDisplayPositionMapper ddDisplayPositionMapper;

    @Autowired
    private StallMapper stallMapper;

    @Autowired
    private StallCommodityService stallCommodityService;

    @Autowired
    private XdSendLogService xdSendLogService;

    @Autowired
    private RedisLockService redisLockService;

    /**
     * 门店商品绑定陈列位 列表
     *
     * @param req
     * @return
     */
    public PageInfo<DdDisplayPositionCommodityODTO> page(DdDisplayPositionCommodityPageIDTO req) {
        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
            ddDisplayPositionCommodityMapper.page(req);
        });
    }

    /**
     * 保存 门店商品绑定陈列位
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(DdDisplayPositionCommoditySaveIDTO req) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        Long userId = token.getUserId();
        Date now = new Date();
        String employeeCode = token.getEmployeeNumber();
        DdDisplayPositionCommodity ddDisplayPositionCommodity = BeanCloneUtils.copyTo(req, DdDisplayPositionCommodity.class);
        ddDisplayPositionCommodity.setUpdateId(userId);
        ddDisplayPositionCommodity.setUpdateTime(now);
        if (null == req.getId()) {
            List<StallCommodity> stallCommodity = stallCommodityMapper.selectList(new LambdaQueryWrapper<StallCommodity>().eq(StallCommodity::getStallId, req.getStallId())
                    .eq(StallCommodity::getCommodityId, req.getCommodityId()));
            QYAssert.isTrue(SpringUtil.isNotEmpty(stallCommodity), "商品不属于档口");
        }
        LogDdDisplayPositionCommodityVO vo;
        int operateType;
        // 更新最大最小数量
        if (null != req.getId()) {
            ddDisplayPositionCommodity.setId(req.getId());
            ddDisplayPositionCommodity.setMaxStock(req.getMaxStock());
            ddDisplayPositionCommodity.setMinStock(req.getMinStock());
            ddDisplayPositionCommodityMapper.updateSecureStock(req.getMinStock(), req.getMaxStock(), userId, req.getId());
            List<LogDdDisplayPositionCommodityVO> voList = ddDisplayPositionCommodityMapper.selectLogInfoById(Collections.singletonList(req.getId()));
            vo = voList.get(0);
            operateType = BigShopOperateTypeEnum.SET_SAFE_STOCK.getCode();
        } else {
            // 绑定
            DdDisplayPositionCommodity positionRecord = ddDisplayPositionCommodityMapper.selectOne(new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                    .eq(DdDisplayPositionCommodity::getCommodityId, req.getCommodityId())
                    .eq(DdDisplayPositionCommodity::getStallId, req.getStallId()));
            if (null != positionRecord) {
                ddDisplayPositionCommodity.setId(positionRecord.getId());
                ddDisplayPositionCommodityMapper.updateById(ddDisplayPositionCommodity);
                operateType = BigShopOperateTypeEnum.BIND.getCode();
            } else {
                ddDisplayPositionCommodity.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                ddDisplayPositionCommodity.setCreateId(userId);
                ddDisplayPositionCommodity.setCreateTime(now);
                ddDisplayPositionCommodityMapper.insert(ddDisplayPositionCommodity);
                operateType = BigShopOperateTypeEnum.BIND.getCode();
            }
            vo = ddDisplayPositionCommodityMapper.selectLogInfo(req.getDisplayPositionId(), req.getCommodityId());
        }
        vo.setOperateType(operateType);
        vo.setOperateTime(DateUtil.get4yMdHms(now));
        vo.setOperateUserName(token.getRealName());
        vo.setOperateUserId(userId);
        vo.setOperateUserCode(employeeCode);
        this.sendDdDisplayPositionCommodityLogMessage(Collections.singletonList(vo));
        return Boolean.TRUE;
    }

    public DdDisplayPositionCommoditySaveODTO checkRepeat(DdDisplayPositionCommoditySaveIDTO req) {
        DdDisplayPositionCommoditySaveODTO odto = new DdDisplayPositionCommoditySaveODTO();
        String dispositionName = ddDisplayPositionCommodityMapper.isRepeat(req);
        if (null == dispositionName) {
            return new DdDisplayPositionCommoditySaveODTO(0, "");
        } else {
            return new DdDisplayPositionCommoditySaveODTO(1, "商品已绑定陈列位" + dispositionName + "，是否继续？");
        }
    }

    /**
     * 删除 门店商品绑定陈列位
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DdDisplayPositionCommodityDeleteODTO delete(DdDisplayPositionCommodityDeleteIDTO req) {
        Date now = new Date();
        String nowStr = DateUtil.get4yMdHms(now);
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String userName = tokenInfo.getRealName();
        String userCode = tokenInfo.getEmployeeNumber();
        // 商品的所属档口≠所选档口，则不做解绑处理
        List<Commodity> commodityList = commodityMapper.selectList(new LambdaQueryWrapper<Commodity>()
                .in(Commodity::getCommodityCode, req.getCommodityCodes()));
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品不存在");
        List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        List<DdDisplayPositionCommodity> oldRecord = ddDisplayPositionCommodityMapper.selectList(new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                .eq(DdDisplayPositionCommodity::getStallId, req.getStallId())
                .in(DdDisplayPositionCommodity::getCommodityId, commodityIdList));
//        List<StallCommodity> stallCommodityList = stallCommodityMapper.selectList(new LambdaQueryWrapper<StallCommodity>()
//                .eq(StallCommodity::getStallId, req.getStallId())
//                .in(StallCommodity::getCommodityId, commodityIdList));
//        QYAssert.isTrue(commodityIdList.size() == stallCommodityList.size(), "商品的所属档口≠所选档口");
        if (SpringUtil.isEmpty(oldRecord)) {
            return new DdDisplayPositionCommodityDeleteODTO(YesOrNoEnums.YES.getCode(), "解绑成功0个，失败" + req.getCommodityCodes().size() + "个");
        }
        List<Long> idList = oldRecord.stream().map(DdDisplayPositionCommodity::getId).collect(Collectors.toList());
        List<LogDdDisplayPositionCommodityVO> voList = ddDisplayPositionCommodityMapper.selectLogInfoById(idList);
        voList = voList.stream().peek(vo -> {
            vo.setOperateType(BigShopOperateTypeEnum.BATCH_DELETE.getCode());
            vo.setOperateTime(nowStr);
            vo.setOperateUserCode(userCode);
            vo.setOperateUserName(userName);
            vo.setOperateUserId(userId);
        }).collect(Collectors.toList());
        int successCount = ddDisplayPositionCommodityMapper.deleteBatchIds(idList);
        int failCount = req.getCommodityCodes().size() - successCount;
        this.sendDdDisplayPositionCommodityLogMessage(voList);
        return new DdDisplayPositionCommodityDeleteODTO(YesOrNoEnums.YES.getCode(), "解绑成功" + successCount + "个，失败" + failCount + "个");
    }


    /**
     * 删除 门店商品绑定陈列位
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        Date now = new Date();
        String nowStr = DateUtil.get4yMdHms(now);
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String userName = tokenInfo.getRealName();
        String employeeCode = tokenInfo.getEmployeeNumber();
        List<LogDdDisplayPositionCommodityVO> voList = ddDisplayPositionCommodityMapper.selectLogInfoById(Collections.singletonList(id));
        ddDisplayPositionCommodityMapper.deleteById(id);
        LogDdDisplayPositionCommodityVO vo = voList.get(0);
        vo.setOperateType(BigShopOperateTypeEnum.UNBIND.getCode());
        vo.setOperateUserId(userId);
        vo.setOperateUserName(userName);
        vo.setOperateTime(nowStr);
        vo.setOperateUserCode(employeeCode);
        this.sendDdDisplayPositionCommodityLogMessage(voList);
        return true;
    }

    /**
     * 发送 门店商品绑定陈列位 日志
     *
     * @param vo
     */
    public void sendDdDisplayPositionCommodityLogMessage(List<LogDdDisplayPositionCommodityVO> vo) {
        xdSendLogService.sendLog(vo, "t_log_dd_display_position_commodity");
    }


    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importExcelUpdate(List<DdDisplayPositionCommoditySaveIDTO> positionList, Long stallId) {
        List<String> commodityCodeList = positionList.stream().map(DdDisplayPositionCommoditySaveIDTO::getCommodityCode).collect(Collectors.toList());
        List<Commodity> commodityList = commodityMapper.selectList(new LambdaQueryWrapper<Commodity>().in(Commodity::getCommodityCode, commodityCodeList));
        List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        Map<String, Commodity> commodityIdMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity()));
        Stall stall = stallMapper.selectById(stallId);

        Map<Long, DdDisplayPositionCommodity> positionCommodityMap = new HashMap<>();
        if (SpringUtil.isNotEmpty(commodityIdList)) {
            // 一个档口下排面只有一个商品
            List<DdDisplayPositionCommodity> positionCommodityList = ddDisplayPositionCommodityMapper.selectList(new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                    .eq(DdDisplayPositionCommodity::getStallId, stallId).in(DdDisplayPositionCommodity::getCommodityId, commodityIdList));
            positionCommodityMap = positionCommodityList.stream().collect(Collectors.toMap(DdDisplayPositionCommodity::getCommodityId, Function.identity()));
        }

        TokenInfo token = FastThreadLocalUtil.getQY();

        // 查询档口下面的商品
        List<StallCommodity> stallCommodities = stallCommodityService.getStallByCommodityIds(token.getShopId(), stallId, commodityIdList);
        List<Long> stallCommodityIds = stallCommodities.stream().map(StallCommodity::getCommodityId).distinct().collect(Collectors.toList());

        List<DdDisplayPosition> positionInfoList = ddDisplayPositionMapper.selectList(new LambdaQueryWrapper<DdDisplayPosition>()
                .eq(DdDisplayPosition::getStallId, stallId));
        Map<Long, DdDisplayPosition> positionInfoMap = positionInfoList.stream()
                .collect(Collectors.toMap(DdDisplayPosition::getId, Function.identity(), (t1, t2) -> t2));
        List<String> errorList = new ArrayList<>();

        Long userId = token.getUserId();
        String userName = token.getRealName();
        String employeeCode = token.getEmployeeNumber();
        Date now = new Date();
        String nowStr = DateUtil.get4yMdHms(now);
        boolean flag = true;
        List<LambdaUpdateWrapper<DdDisplayPositionCommodity>> updateList = new ArrayList<>(positionList.size());

        Map<String, Integer> codeRepeat = new HashMap<>();

        for (int i = 0; i < positionList.size(); i++) {
            DdDisplayPositionCommoditySaveIDTO item = positionList.get(i);

            if (codeRepeat.containsKey(item.getCommodityCode())) {
                if (1 == codeRepeat.get(item.getCommodityCode())) {
                    errorList.add(item.getCommodityCode() + "商品编码重复");
                }
                codeRepeat.put(item.getCommodityCode(), 2);
                flag = false;
                continue;
            }

            if (!commodityIdMap.containsKey(item.getCommodityCode())) {
                errorList.add(item.getCommodityCode() + "商品编码不存在");
                flag = false;
                continue;
            }
            Commodity commodity = commodityIdMap.get(item.getCommodityCode());
            Long commodityId = commodity.getId();

            if (!stallCommodityIds.contains(commodityId)) {
                errorList.add("无" + item.getCommodityCode() + "商品编码的档口权限");
                flag = false;
                continue;
            }

            if (!positionCommodityMap.containsKey(commodityId)) {
                errorList.add(item.getCommodityCode() + "商品编码未绑定陈列位");
                flag = false;
                continue;
            }

            DdDisplayPositionCommodity positionCommodity = positionCommodityMap.get(commodityId);

            codeRepeat.put(item.getCommodityCode(), 1);

            LambdaUpdateWrapper<DdDisplayPositionCommodity> updateWrapper = new LambdaUpdateWrapper<DdDisplayPositionCommodity>()
                    .eq(DdDisplayPositionCommodity::getId, positionCommodity.getId())
                    .set(DdDisplayPositionCommodity::getUpdateId, userId)
                    .set(DdDisplayPositionCommodity::getUpdateTime, now);

            if (Objects.isNull(item.getMinStock())) {
                updateWrapper.set(DdDisplayPositionCommodity::getMinStock, null);
            } else {
                updateWrapper.set(DdDisplayPositionCommodity::getMinStock, item.getMinStock());
            }
            if (Objects.isNull(item.getMaxStock())) {
                updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, null);
            } else {
                updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, item.getMaxStock());
            }
            updateList.add(updateWrapper);

            DdDisplayPosition positionInfo = positionInfoMap.get(positionCommodity.getDisplayPositionId());

            LogDdDisplayPositionCommodityVO logVo = new LogDdDisplayPositionCommodityVO();
            logVo.setCommodityId(commodityId);
            logVo.setDisplayPositionName(positionInfo.getDisplayPositionName());
            logVo.setDisplayPositionId(positionInfo.getId());
            logVo.setCommodityCode(commodity.getCommodityCode());
            logVo.setCommodityName(commodity.getCommodityName());
            logVo.setCommoditySpec(commodity.getCommoditySpec());
            logVo.setCommodityUnit(commodity.getCommodityUnitName());
            logVo.setShopId(positionInfo.getShopId());
            logVo.setStallId(stallId);
            logVo.setStallCode(stall.getStallCode());
            logVo.setStallName(stall.getStallName());
            logVo.setMinStock(item.getMinStock());
            logVo.setMaxStock(item.getMaxStock());
            logVo.setOperateUserId(userId);
            logVo.setOperateUserName(userName);
            logVo.setOperateTime(nowStr);
            logVo.setOperateUserCode(employeeCode);
            logVo.setOperateType(BigShopOperateTypeEnum.IMPORT_SAFE_STOCK.getCode());

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendDdDisplayPositionCommodityLogMessage(Collections.singletonList(logVo));
                }
            });
        }
        if (flag) {
            updateList.forEach(this::update);
        }
        return new ExcelResult(errorList, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importExcel(List<DdDisplayPositionCommoditySaveIDTO> positionList, Long stallId, Long shopId) {
        TokenInfo token = FastThreadLocalUtil.getQY();

        List<String> commodityCodeList = positionList.stream().map(DdDisplayPositionCommoditySaveIDTO::getCommodityCode).collect(Collectors.toList());
        List<Commodity> commodityList = commodityMapper.selectList(new LambdaQueryWrapper<Commodity>().in(Commodity::getCommodityCode, commodityCodeList));
        List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        Map<String, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Function.identity()));

        List<StallCommodity> stallCommodityList = stallCommodityService.getStallByCommodityIds(token.getShopId(), stallId, commodityIdList);

//        List<StallCommodity> stallCommodityList = stallCommodityMapper.selectList(new LambdaQueryWrapper<StallCommodity>()
//                .eq(StallCommodity::getStallId, stallId).in(StallCommodity::getCommodityId, commodityIdList));

        Stall stall = stallMapper.selectById(stallId);
        Map<Long, StallCommodity> stallCommodityMap = stallCommodityList.stream().collect(Collectors.toMap(StallCommodity::getCommodityId, Function.identity()));
        List<String> positionNameList = positionList.stream().map(DdDisplayPositionCommoditySaveIDTO::getDisplayPositionName).collect(Collectors.toList());
        List<DdDisplayPosition> ddDisplayPositionList = ddDisplayPositionMapper.selectList(new LambdaQueryWrapper<DdDisplayPosition>()
                .eq(DdDisplayPosition::getStallId, stallId)
                .in(DdDisplayPosition::getDisplayPositionName, positionNameList));
        Map<String, DdDisplayPosition> ddDisplayPositionMap = ddDisplayPositionList.stream().collect(Collectors.toMap(DdDisplayPosition::getDisplayPositionName, Function.identity()));
        // 一个档口下排面只有一个商品
        Map<Long, DdDisplayPositionCommodity> positionCommodityMap = new HashMap<>();
        if (SpringUtil.isNotEmpty(commodityIdList)) {
            List<DdDisplayPositionCommodity> positionCommodityList = ddDisplayPositionCommodityMapper.selectList(new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                    .eq(DdDisplayPositionCommodity::getStallId, stallId).in(DdDisplayPositionCommodity::getCommodityId, commodityIdList));
            positionCommodityMap = positionCommodityList.stream().collect(Collectors.toMap(DdDisplayPositionCommodity::getCommodityId, Function.identity()));
        }

        List<String> errorList = new ArrayList<>();

        Long userId = token.getUserId();
        String userName = token.getRealName();
        String employeeCode = token.getEmployeeNumber();

        Map<String, Integer> codeRepeat = new HashMap<>();

        Date now = new Date();
        String nowStr = DateUtil.get4yMdHm(now);
        boolean flag = true;
        List<LambdaUpdateWrapper<DdDisplayPositionCommodity>> updateList = new ArrayList<>(positionList.size());
        List<DdDisplayPositionCommodity> insertList = new ArrayList<>(positionList.size());
        for (int i = 0; i < positionList.size(); i++) {
            DdDisplayPositionCommoditySaveIDTO item = positionList.get(i);

            if (codeRepeat.containsKey(item.getCommodityCode())) {
                if (1 == codeRepeat.get(item.getCommodityCode())) {
                    errorList.add(item.getCommodityCode() + "商品编码重复");
                }
                codeRepeat.put(item.getCommodityCode(), 2);
                flag = false;
                continue;
            }

            if (!commodityMap.containsKey(item.getCommodityCode())) {
                errorList.add(item.getCommodityCode() + "商品编码不存在");
                flag = false;
                continue;
            }
            Commodity commodity = commodityMap.get(item.getCommodityCode());
            Long commodityId = commodity.getId();

            if (!stallCommodityMap.containsKey(commodityId)) {
                errorList.add(item.getCommodityCode() + "商品编码的所属档口不是当前所选档口");
                flag = false;
                continue;
            }
            if (!ddDisplayPositionMap.containsKey(item.getDisplayPositionName())) {
                errorList.add(item.getDisplayPositionName() + "陈列位不存在");
                flag = false;
                continue;
            }
            DdDisplayPosition ddDisplayPosition = ddDisplayPositionMap.get(item.getDisplayPositionName());
            if (null == ddDisplayPosition) {
                errorList.add(item.getDisplayPositionName() + "陈列位的档口不是当前所选档口");
                flag = false;
                continue;
            }

            codeRepeat.put(item.getCommodityCode(), 1);

            // 已绑定过则update
            if (positionCommodityMap.containsKey(commodityId)) {
                DdDisplayPositionCommodity positionCommodity = positionCommodityMap.get(commodityId);
                LambdaUpdateWrapper<DdDisplayPositionCommodity> updateWrapper = new LambdaUpdateWrapper<DdDisplayPositionCommodity>()
                        .eq(DdDisplayPositionCommodity::getId, positionCommodity.getId())
                        .set(DdDisplayPositionCommodity::getDisplayPositionId, ddDisplayPosition.getId())
                        .set(DdDisplayPositionCommodity::getUpdateId, userId)
                        .set(DdDisplayPositionCommodity::getUpdateTime, now);

                if (Objects.isNull(item.getMinStock())) {
                    updateWrapper.set(DdDisplayPositionCommodity::getMinStock, null);
                } else {
                    updateWrapper.set(DdDisplayPositionCommodity::getMinStock, item.getMinStock());
                }
                if (Objects.isNull(item.getMaxStock())) {
                    updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, null);
                } else {
                    updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, item.getMaxStock());
                }
                updateList.add(updateWrapper);
            } else {
                DdDisplayPositionCommodity insertItem = new DdDisplayPositionCommodity();
                insertItem.setUpdateId(userId);
                insertItem.setUpdateTime(now);
                insertItem.setMinStock(item.getMinStock());
                insertItem.setMaxStock(item.getMaxStock());
                insertItem.setDisplayPositionId(ddDisplayPosition.getId());
                insertItem.setShopId(shopId);
                insertItem.setCommodityId(commodityId);
                insertItem.setStallId(stallId);
                insertItem.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                insertItem.setCreateTime(now);
                insertItem.setCreateId(userId);
                insertList.add(insertItem);
            }
            LogDdDisplayPositionCommodityVO logVo = new LogDdDisplayPositionCommodityVO();
            logVo.setCommodityId(commodityId);
            logVo.setDisplayPositionName(ddDisplayPosition.getDisplayPositionName());
            logVo.setDisplayPositionId(ddDisplayPosition.getId());
            logVo.setCommodityCode(commodity.getCommodityCode());
            logVo.setCommodityName(commodity.getCommodityName());
            logVo.setCommoditySpec(commodity.getCommoditySpec());
            logVo.setCommodityUnit(commodity.getCommodityUnitName());
            logVo.setShopId(shopId);
            logVo.setStallId(stallId);
            logVo.setStallCode(stall.getStallCode());
            logVo.setStallName(stall.getStallName());
            logVo.setMinStock(item.getMinStock());
            logVo.setMaxStock(item.getMaxStock());
            logVo.setOperateUserId(userId);
            logVo.setOperateUserName(userName);
            logVo.setOperateTime(nowStr);
            logVo.setOperateType(BigShopOperateTypeEnum.BATCH_BIND.getCode());
            logVo.setOperateUserCode(employeeCode);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendDdDisplayPositionCommodityLogMessage(Collections.singletonList(logVo));
                }
            });
        }
        if (flag) {
            if (SpringUtil.isNotEmpty(updateList)) {
                updateList.forEach(this::update);
            }
            if (SpringUtil.isNotEmpty(insertList)) {
                this.saveBatch(insertList);
            }
        }
        return new ExcelResult(errorList, null);
    }

    /**
     * pda 商品绑定陈列位 - 商品查询接口
     *
     * @param req
     * @return
     */
    public List<DdDisplayPositionCommodityQueryODTO> listCommodity(DdDisplayPositionCommodityQueryIDTO req) {
        req.checkData();
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        // 获取陈列位信息
        DdDisplayPosition ddDisplayPosition = ddDisplayPositionMapper.selectById(req.getDdDisplayPositionId());
        QYAssert.notNull(ddDisplayPosition, "陈列位不存在");
        Long stallId = ddDisplayPosition.getStallId();

        // 处理商品查询参数
        String commodityQueryParam = req.getCommodityQueryParam();
        if (isWeighOrCountCode(commodityQueryParam)) {
            req.setCommodityQueryParam(commodityQueryParam.substring(1, 7));
        }

        // 查询陈列商品 ID
        List<Long> commodityIdList = stallCommodityService.listStallCommodityByNameOrBarCode(stallId, req.getCommodityQueryParam());
        if (SpringUtil.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }

        // 查询陈列位商品记录
        List<DdDisplayPositionCommodity> ddDisplayPositionCommodities = ddDisplayPositionCommodityMapper.selectList(
                new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                        .eq(DdDisplayPositionCommodity::getShopId, tokenInfo.getShopId())
                        .eq(DdDisplayPositionCommodity::getStallId, stallId)
                        .in(DdDisplayPositionCommodity::getCommodityId, commodityIdList)
        );

        List<Commodity> commodities = commodityMapper.queryCommodityByIdList(commodityIdList);
        Map<Long, String> commodityIdPackageNameMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityPackageName));

        if (SpringUtil.isEmpty(ddDisplayPositionCommodities)) {
            return commodityIdList.stream().map(data -> {
                DdDisplayPositionCommodityQueryODTO dto = new DdDisplayPositionCommodityQueryODTO();
                dto.setCommodityId(data.toString());
                dto.setCommodityPackageKind(commodityIdPackageNameMap.get(data));
                return dto;
            }).collect(Collectors.toList());
        }

        // 构建映射
        Map<Long, DdDisplayPositionCommodity> commodityMap = ddDisplayPositionCommodities.stream()
                .collect(Collectors.toMap(DdDisplayPositionCommodity::getCommodityId, Function.identity()));

        Set<Long> displayPositionIds = ddDisplayPositionCommodities.stream()
                .map(DdDisplayPositionCommodity::getDisplayPositionId)
                .collect(Collectors.toSet());

        Map<Long, String> displayPositionNameMap = ddDisplayPositionService.listByIds(displayPositionIds).stream()
                .collect(Collectors.toMap(DdDisplayPosition::getId, DdDisplayPosition::getDisplayPositionName));

        // 构建返回结果
        List<DdDisplayPositionCommodityQueryODTO> result = new ArrayList<>(commodityIdList.size());
        for (Long commodityId : commodityIdList) {
            DdDisplayPositionCommodityQueryODTO dto = new DdDisplayPositionCommodityQueryODTO();
            dto.setCommodityId(commodityId.toString());

            DdDisplayPositionCommodity dd = commodityMap.get(commodityId);
            if (dd != null) {
                Long displayPositionId = dd.getDisplayPositionId();
                dto.setDisplayPositionId(displayPositionId);
                dto.setDisplayPositionName(displayPositionNameMap.get(displayPositionId));
                dto.setMinStock(dd.getMinStock());
                dto.setMaxStock(dd.getMaxStock());
            }

            dto.setCommodityPackageKind(commodityIdPackageNameMap.get(commodityId));
            result.add(dto);
        }

        return result;
    }

    private boolean isWeighOrCountCode(String param) {
        return StringUtils.isNumeric(param)
                && param.length() == 18
                && (param.startsWith("2") || param.startsWith("3"));
    }

    /**
     * pda 批量商品绑定陈列位
     *
     * @param req
     */
    @Transactional
    public void pdaBatchSave(DdDisplayPositionCommodityBatchSaveIDTO req) {
        req.checkData();
        Long shopId = req.getShopId();
        Long displayPositionId = req.getDisplayPositionId();
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        DdDisplayPosition ddDisplayPosition = ddDisplayPositionService.getByIdAndShopId(displayPositionId, shopId);
        QYAssert.notNull(ddDisplayPosition, "陈列位不存在");
        Long stallId = ddDisplayPosition.getStallId();

        List<DdDisplayPositionCommodityIDTO> displayPositionCommodityList = req.getDisplayPositionCommodityList();
        Map<String, DdDisplayPositionCommodityIDTO> commodityIdMap = displayPositionCommodityList.stream()
                .collect(Collectors.toMap(DdDisplayPositionCommodityIDTO::getCommodityId, Function.identity(), (key1, key2) -> key1));

        List<Commodity> commodityList = commodityMapper.selectBatchIds(commodityIdMap.keySet());
        Map<Long, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));

        Stall stall = stallMapper.selectById(stallId);
        String nowStr = DateUtil.get4yMdHm(new Date());
        Long userId = tokenInfo.getUserId();
        String userName = tokenInfo.getRealName();
        String employeeCode = tokenInfo.getEmployeeNumber();

        redisLockService.lock(RedisLockEnums.PDA_BATCH_SAVE_DD_DISPLAY, shopId.toString(), () -> {

            List<Long> commodityIdList = commodityIdMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
            List<DdDisplayPositionCommodity> ddDisplayPositionCommodityList = listByShopIdAndCommodityIdList(shopId, stallId, commodityIdList);
            Map<Long, DdDisplayPositionCommodity> existMapByCommodityId = ddDisplayPositionCommodityList.stream()
                    .collect(Collectors.toMap(DdDisplayPositionCommodity::getCommodityId, Function.identity()));
            Date operateDate = new Date();
            for (DdDisplayPositionCommodityIDTO ddDisplayPositionCommodityIDTO : displayPositionCommodityList) {
                Long commodityId = Long.valueOf(ddDisplayPositionCommodityIDTO.getCommodityId());
                DdDisplayPositionCommodity exist = existMapByCommodityId.get(commodityId);
                LogDdDisplayPositionCommodityVO logDdDisplayPositionCommodityVO;

                if (Objects.nonNull(exist)) {
                    LambdaUpdateWrapper<DdDisplayPositionCommodity> updateWrapper = new LambdaUpdateWrapper<DdDisplayPositionCommodity>()
                            .eq(DdDisplayPositionCommodity::getId, exist.getId())
                            .set(DdDisplayPositionCommodity::getDisplayPositionId, displayPositionId)
                            .set(DdDisplayPositionCommodity::getStallId, stallId)
                            .set(DdDisplayPositionCommodity::getUpdateId, userId)
                            .set(DdDisplayPositionCommodity::getUpdateTime, operateDate);
                    if (Objects.isNull(ddDisplayPositionCommodityIDTO.getMinStock())) {
                        updateWrapper.set(DdDisplayPositionCommodity::getMinStock, null);
                    } else {
                        updateWrapper.set(DdDisplayPositionCommodity::getMinStock, ddDisplayPositionCommodityIDTO.getMinStock());
                    }
                    if (Objects.isNull(ddDisplayPositionCommodityIDTO.getMaxStock())) {
                        updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, null);
                    } else {
                        updateWrapper.set(DdDisplayPositionCommodity::getMaxStock, ddDisplayPositionCommodityIDTO.getMaxStock());
                    }
                    ddDisplayPositionCommodityMapper.update(null, updateWrapper);

                    if (Objects.equals(exist.getDisplayPositionId(), displayPositionId)) {
                        // 设置安全库存
                        List<LogDdDisplayPositionCommodityVO> voList = ddDisplayPositionCommodityMapper.selectLogInfoById(Collections.singletonList(exist.getId()));
                        logDdDisplayPositionCommodityVO = voList.get(0);
                        logDdDisplayPositionCommodityVO.setOperateType(BigShopOperateTypeEnum.SET_SAFE_STOCK.getCode());
                        logDdDisplayPositionCommodityVO.setOperateTime(nowStr);
                        logDdDisplayPositionCommodityVO.setOperateUserName(userName);
                        logDdDisplayPositionCommodityVO.setOperateUserId(userId);
                        logDdDisplayPositionCommodityVO.setOperateUserCode(employeeCode);
                    } else {
                        logDdDisplayPositionCommodityVO = pdaBatchBindLog(ddDisplayPositionCommodityIDTO, commodityId, ddDisplayPosition, commodityMap, shopId, stallId, stall, userId, userName, nowStr, employeeCode);
                    }

                } else {
                    DdDisplayPositionCommodity save = new DdDisplayPositionCommodity();
                    save.setShopId(shopId);
                    save.setStallId(stallId);
                    save.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
                    save.setDisplayPositionId(displayPositionId);
                    save.setCommodityId(commodityId);
                    save.setMinStock(ddDisplayPositionCommodityIDTO.getMinStock());
                    save.setMaxStock(ddDisplayPositionCommodityIDTO.getMaxStock());
                    save.setCreateId(userId);
                    save.setCreateTime(operateDate);
                    save.setUpdateId(userId);
                    save.setUpdateTime(operateDate);
                    this.save(save);

                    logDdDisplayPositionCommodityVO = pdaBatchBindLog(ddDisplayPositionCommodityIDTO, commodityId, ddDisplayPosition, commodityMap, shopId, stallId, stall, userId, userName, nowStr, employeeCode);
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        sendDdDisplayPositionCommodityLogMessage(Collections.singletonList(logDdDisplayPositionCommodityVO));
                    }
                });

            }
        });
    }

    private static LogDdDisplayPositionCommodityVO pdaBatchBindLog(DdDisplayPositionCommodityIDTO ddDisplayPositionCommodityIDTO, Long commodityId, DdDisplayPosition ddDisplayPosition, Map<Long, Commodity> commodityMap, Long shopId, Long stallId, Stall stall, Long userId, String userName, String nowStr, String employeeCode) {
        LogDdDisplayPositionCommodityVO logVo = new LogDdDisplayPositionCommodityVO();
        logVo.setCommodityId(commodityId);
        logVo.setDisplayPositionName(ddDisplayPosition.getDisplayPositionName());
        logVo.setDisplayPositionId(ddDisplayPosition.getId());
        Commodity commodity = commodityMap.get(commodityId);
        logVo.setCommodityCode(commodity.getCommodityCode());
        logVo.setCommodityName(commodity.getCommodityName());
        logVo.setCommoditySpec(commodity.getCommoditySpec());
        logVo.setCommodityUnit(commodity.getCommodityUnitName());
        logVo.setShopId(shopId);
        logVo.setStallId(stallId);
        logVo.setStallCode(stall.getStallCode());
        logVo.setStallName(stall.getStallName());
        logVo.setMinStock(ddDisplayPositionCommodityIDTO.getMinStock());
        logVo.setMaxStock(ddDisplayPositionCommodityIDTO.getMaxStock());
        logVo.setOperateUserId(userId);
        logVo.setOperateUserName(userName);
        logVo.setOperateTime(nowStr);
        logVo.setOperateType(BigShopOperateTypeEnum.BATCH_BIND.getCode());
        logVo.setOperateUserCode(employeeCode);

        return logVo;
    }

    private List<DdDisplayPositionCommodity> listByShopIdAndCommodityIdList(Long shopId, Long stallId, List<Long> commodityIdList) {
        return this.baseMapper.selectList(
                new LambdaQueryWrapper<DdDisplayPositionCommodity>()
                        .eq(DdDisplayPositionCommodity::getShopId, shopId)
                        .eq(DdDisplayPositionCommodity::getStallId, stallId)
                        .in(DdDisplayPositionCommodity::getCommodityId, commodityIdList)
        );
    }

}
