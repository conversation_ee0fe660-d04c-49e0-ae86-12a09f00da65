package com.pinshang.qingyun.xd.wms.service.groupon;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.*;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.order.dto.XdOrderItemDTO;
import com.pinshang.qingyun.xd.order.dto.XdOrderODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.enums.GrouponOrderStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.PickStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.WorkOrderStatusEnum;
import com.pinshang.qingyun.xd.wms.mapper.*;
import com.pinshang.qingyun.xd.wms.model.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.CommodityBarCodeService;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RDeque;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

/**
 * @Author: sk
 * @Date: 2020/12/18
 */
@Service
@Slf4j
public class GrouponService {

    @Autowired
    private GrouponMapper grouponMapper;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private XdOrderClient xdOrderClient;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private PickWorkOrderMapper pickWorkOrderMapper;

    @Autowired
    private PickOrderItemMapper pickOrderItemMapper;

    @Autowired
    private DcShopPackageOrderMapper dcShopPackageOrderMapper;

    @Autowired
    private CloudService cloudService;

    @Autowired
    private GroupPurchaseService groupPurchaseService;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommodityMapper commodityMapper;
    private static final String PICKUP_CODE = "WMS:PICKUP_CODE:";

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 查询社区团购商品信息
     * @param ito
     * @return
     */
    public List<GrouponCommodityODTO> queryGrouponCommodityList(GrouponCommodityIDTO ito){
        QYAssert.notNull(ito,"参数为空");
        QYAssert.notNull(ito.getBeginTime(),"开始时间不能为空");
        QYAssert.notNull(ito.getEndTime(),"结束时间不能为空");
        return grouponMapper.queryGrouponCommodityList(ito.getGrouponIdList(),ito.getBeginTime(),ito.getEndTime());
    }

    /**
     * 根据手机号查询提货列表  如果有订单就呈现订单里面第一个人的姓名和电话，
     * 如果没有，就返回一个电话号码
     * @param receiveMobile
     * @return
     */
    public GrouponOrderCommodityDTO grouponOrderByMobileList(String receiveMobile) {
        QYAssert.isTrue(false,"系统已升级，需重启PDA");
        GrouponOrderCommodityDTO res = new GrouponOrderCommodityDTO();
        return res;
    }

    /**
     * 根据取货码查询提货列表，
     * @param pickupCode
     * @return
     */
    public GrouponOrderCommodityDTO grouponOrderListByPickupCode(String pickupCode) {
        QYAssert.isTrue(false,"系统已升级，需重启PDA");
        GrouponOrderCommodityDTO res = new GrouponOrderCommodityDTO();
        return res;
    }

    /**
     * 团购收货
     * 团购收货，商品团购已预定，库存不允许被别处销售，收货+冻结
     * @param stockIDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void groupStockReceipt(StockReceiptIDTO stockIDTO) {
        stockServiceAdapter.stockReceipt(stockIDTO);

        //处理冻结 order收货操作已经添加了防重复校验
        //LockUtils.checkLock(LockUtils.FREEZE_OPERATE, stockIDTO.getReferCode());

        List<StockItemDTO> stockItemList = new ArrayList<>();
        List<StockReceiptItemDTO> commodityList = stockIDTO.getCommodityList();
        for (StockReceiptItemDTO itemDTO : commodityList) {
            StockItemDTO stockItem = new StockItemDTO();
            stockItem.setCommodityId(itemDTO.getCommodityId());
            stockItem.setStockNumber(itemDTO.getNumber());
            stockItem.setQuantity(itemDTO.getQuantity());
            stockItemList.add(stockItem);
        }
        shopCommodityService.stockFreeze(stockIDTO.getReferId(),stockIDTO.getReferCode(),stockIDTO.getWarehouseId(), stockItemList, stockIDTO.getUserId(),null);
    }

    /**
     * 团购完成提货
     * @param completeDeliveryDTO
     * @return
     */
    @Transactional
    public Boolean completeDelivery(CompleteDeliveryDTO completeDeliveryDTO) {
        QYAssert.isTrue(false,"系统已升级，需重启PDA");
        return Boolean.TRUE;
    }


    /**
     * 团购提货页面
     * @param idto
     * @return
     */
    public TablePageInfo<GrouponOrderPageODTO> grouponOrderPage(GrouponOrderPageIDTO idto){
        idto.checkData();

        PageInfo<GrouponOrderPageODTO> pageInfo;
        Integer total;
        TablePageInfo tablePageInfo;

        boolean hasSuccTime = StringUtils.isNoneBlank(idto.getSuccReceiveBeginTime()) || StringUtils.isNoneBlank(idto.getSuccReceiveBeginTime());

        // 如果搜索条件有完成时间 且状态是空, 则将状态置为已完成 再进行查询
        if(hasSuccTime && null == idto.getStatus()) {
            idto.setStatus(GrouponOrderStatusEnum.complete.getCode());
        }

        // 如果搜索条件有完成时间 且 状态非已完成, 直接返回空结果
        if(hasSuccTime && idto.getStatus() != 1) {
            PageHelper.startPage(idto.getPageNo(), idto.getPageSize());
            pageInfo = new PageInfo<>(new ArrayList<>());
            tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
            tablePageInfo.setHeader(0);
            return tablePageInfo;
        }

        pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(
                ()->grouponMapper.orderGrouponPage(idto).stream().peek(
                        e->{
                            GrouponTypeEnums type = GrouponTypeEnums.getByCode(e.getTypeCode());
                            e.setTypeName(type == null ? null : type.getDesc());
                            GrouponOrderStatusEnum statusEnum = GrouponOrderStatusEnum.getEnumByOrderStatus(e.getOrderStatus());
                            e.setStatus(statusEnum.getCode());
                            e.setStatusDesc(statusEnum.getDesc());
                            // 如果是非已完成的订单, 设置完成时间和操作人为空
                            if(e.getStatus() != GrouponOrderStatusEnum.complete.getCode()){
                                e.setOrderCompleteDate(null);
                                e.setEmployeeName(null);
                            }
                        }
                ).collect(toList())
        );

        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);

        // 查询所有记录的合(无分页)
        total = grouponMapper.sumGrouopon(idto) ;
        if(null == total){
            tablePageInfo.setHeader(0);
        }else{
            tablePageInfo.setHeader(total);
        }
        return tablePageInfo;
    }

    /**
     * 生成取货码
     * @param orderId,shopId
     * @return
     */
    public boolean generatePickupCode(Long orderId){
        OrderGroupon orderGroupon = new OrderGroupon();
        orderGroupon.setPickupCode(DateUtil.getDateFormate(new Date(),"yyMMdd") + generateRandomCode());
        LambdaUpdateWrapper<OrderGroupon> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrderGroupon::getOrderId, orderId);
        grouponMapper.update(orderGroupon, updateWrapper);
        return true;
    }

    /**
     * 生成6位随机数
     * @param
     * @return
     */
    public String generateRandomCode(){
        RDeque<Integer> randomCodes = redissonClient.getDeque(PICKUP_CODE);
        if(randomCodes.size() == 0){
            List<Integer> randomList = IntStream.range(100000, 999999).boxed().collect(toList());
            Collections.shuffle(randomList);
            randomCodes.addAll(randomList);
        }

        Integer firstCode = randomCodes.removeFirst();
        String randomCode = firstCode.toString();
//        String pickupCode = StringUtils.leftPad(firstCode.toString(), 6, '0');
        return randomCode;
    }


    /**
     * 根据提货码查询提货列表(团购退货)
     * @param pickupCode
     * @return
     */
    public GrouponOrderReturnODTO getReturnGrouponOrder(String pickupCode) {
        GrouponOrderReturnODTO res = new GrouponOrderReturnODTO();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");

        List<GrouponOrderReturnODTO> pickCodeList = grouponMapper.getGrouponOrderListByPickupCode(pickupCode, df.format(new Date()));

        if(CollectionUtils.isEmpty(pickCodeList)){
            res.setStatus("9999");
            res.setMsg("提货码不存在！");
        }else {
            GrouponOrderReturnODTO grouponOrder = pickCodeList.get(0);
            BeanUtils.copyProperties(grouponOrder,res);
            res.setRefundNum(res.getRefundNum() + "份");
            Integer orderStatus = Integer.valueOf(grouponOrder.getStatus());

            if(orderStatus.equals(XdOrderStatusEnum.DELIVERED.getCode())){
                PickOrderResult pickOrder = pickOrderService.getPickOrderByOrderId(grouponOrder.getOrderId());
                if(pickOrder != null){
                    res.setPickTime(DateUtil.getDateFormate(pickOrder.getPickEndTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                res.setMsg("此单已提货，如需退货，\n请使用PDA退货功能！");
            }else if(orderStatus.equals(XdOrderStatusEnum.CANCEL.getCode())){
                XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(grouponOrder.getOrderId());
                res.setPickTime(DateUtil.getDateFormate(xdOrderODTO.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
                res.setMsg("此单已取消，不能二次退款！");
            }
        }

        return res;
    }

    /**
     * 团购退货
     * @param pickupCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean returnGrouponOrder(String pickupCode) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        List<GrouponOrderReturnODTO> pickCodeList = grouponMapper.getGrouponOrderListByPickupCode(pickupCode, df.format(new Date()));

        QYAssert.isTrue(CollectionUtils.isNotEmpty(pickCodeList),"提货码不存在！");

        GrouponOrderReturnODTO grouponOrder = pickCodeList.get(0);
        Integer orderStatus = Integer.valueOf(grouponOrder.getStatus());
        QYAssert.isTrue(!orderStatus.equals(XdOrderStatusEnum.DELIVERED.getCode()),"此单已提货，如需退货，\n请使用PDA退货功能！");
        QYAssert.isTrue(!orderStatus.equals(XdOrderStatusEnum.CANCEL.getCode()),"此单已取消，不能二次退款！");

        // 更新拣货单状态为取消
        pickOrderMapper.updateByOrderId(grouponOrder.getOrderId());

        XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(grouponOrder.getOrderId());
        shopCommodityService.stockUnFreeze(grouponOrder.getOrderId(), xdOrderODTO.getOrderCode(), xdOrderODTO.getShopId());

        //取消拣货单
        PickOrderMqDTO mqDTO = new PickOrderMqDTO();
        mqDTO.setOrderId(grouponOrder.getOrderId());
        mqDTO.setPickStatus(PickStatusEnum.CANCEL.getCode());
        mqDTO.setCancelType(XdOrderCancelReasonEnum.USER_CANCEL.getCode());
//        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_PICK_ORDER_CHANGE_TYPE
//                , mqDTO, KafkaMessageOperationTypeEnum.UPDATE);
//        kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XD_PICK_ORDER_CHANGE_TOPIC, JsonUtil.java2json(message));

        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_PICK_ORDER_CHANGE_TOPIC.getTopic(),
                mqDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_PICK_ORDER_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
        return true;
    }

    public AbstractGroupOrder grouponOrder(String pickupCode) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<OrderGroupon>()
                .eq(OrderGroupon::getPickupCode, pickupCode);
        OrderGroupon orderGroupon = grouponMapper.selectOne(query);
        QYAssert.isTrue(null != orderGroupon, "提货码不存在！");
        if (orderGroupon.getOrderType().equals(XdOrderTypeEnum.CLOUDXJ.getCode())) {
            return cloudService;
        } else {
            return groupPurchaseService;
        }
    }

    /**
     * 获取未发货商品列表
     * @param orderId
     */
    public List<ShopPackageDetailItemODTO> getUnShipped(Long orderId) {
        List<Long> shipped = dcShopPackageOrderMapper.getShipped(orderId);
        return dcShopPackageOrderMapper.getUnShippedCommodity(orderId, shipped);
    }

    /**
     * 团购提货(每次只提一单)
     * @param pickupCode
     * @return
     */
    public GrouponOrderReturnODTO getGrouponOrderByPickupCode(String pickupCode) {
        List<GrouponOrderReturnODTO> pickCodeList = grouponMapper.getGrouponOrderByPickupCode(pickupCode);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(pickCodeList),"提货码不存在！");

        GrouponOrderReturnODTO returnODTO = pickCodeList.get(0);
        returnODTO.setRefundNum(returnODTO.getRefundNum() + "份");
        Integer orderStatus = Integer.valueOf(returnODTO.getStatus());

        if(orderStatus.equals(XdOrderStatusEnum.DELIVERED.getCode())){
            PickOrderResult pickOrder = pickOrderService.getPickOrderByOrderId(returnODTO.getOrderId());
            if(pickOrder != null){
                returnODTO.setPickTime(DateUtil.getDateFormate(pickOrder.getPickEndTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            returnODTO.setMsg("此单已提货，不能再提货！");
        }else if(orderStatus.equals(XdOrderStatusEnum.CANCEL.getCode())){
            XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(returnODTO.getOrderId());
            returnODTO.setPickTime(DateUtil.getDateFormate(xdOrderODTO.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
            returnODTO.setMsg("此单已取消，不能再提货！");
        }
        return returnODTO;
    }

    /**
     * 团购提货(每次只提一单) 完成提货
     * @param pickupCode
     * @param barCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GrouponOrderReturnODTO completeTakeGood(String pickupCode, String barCode) {
        List<GrouponOrderReturnODTO> pickCodeList = grouponMapper.getGrouponOrderByPickupCode(pickupCode);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(pickCodeList),"提货码不存在！");

        GrouponOrderReturnODTO order = pickCodeList.get(0);
        Integer orderStatus = Integer.valueOf(order.getStatus());
        QYAssert.isTrue(!orderStatus.equals(XdOrderStatusEnum.DELIVERED.getCode()),"此单已提货，不能再提货！");
        QYAssert.isTrue(!orderStatus.equals(XdOrderStatusEnum.CANCEL.getCode()),"此单已取消，不能再提货！");

        Commodity commodity = commodityMapper.getCommodityIdByBarCode(barCode);
        Boolean isCommodity = commodity != null && order.getCommodityId().equals(commodity.getId());
        QYAssert.isTrue(isCommodity,"商品条码有误！");

        // 提货核销
        completeTakeGood(order.getOrderId(),new BigDecimal(order.getRefundNum()));

        GrouponOrderReturnODTO grouponOrderReturnODTO = new GrouponOrderReturnODTO();
        grouponOrderReturnODTO.setCompleteMsg("核销成功!\n提货数量：" + order.getRefundNum() + "份");
        return grouponOrderReturnODTO;
    }

    /**
     * 完成提货公用方法
     * @param orderId
     * @param refundNum
     */
    private void completeTakeGood(Long orderId,BigDecimal refundNum) {
        PickOrderResult pickOrderResult = pickOrderService.getPickOrderByOrderId(orderId);
        QYAssert.isTrue(pickOrderResult == null,"此单已生成拣货单!");
        //原来提货完成是前端传的数量[考虑了称重商品问题，称重分行问题]
        //团购8期提货完成数量是后端数量[不适用称重商品，目前团购已拦截称重商品设置]
        XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(orderId);
        QYAssert.notNull(xdOrderODTO,"订单不存在");
        QYAssert.isTrue(xdOrderODTO.getOrderStatus().getCode() != XdOrderStatusEnum.CANCEL.getCode(),"订单不存在");
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(orderId);
        orderDTO.setOrderCode(xdOrderODTO.getOrderCode());
        orderDTO.setShopId(xdOrderODTO.getShopId());
        orderDTO.setStatus(xdOrderODTO.getOrderStatus());
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        orderDTO.setDeliveryBeginTime(sdf1.format(xdOrderODTO.getReceiveTimeBegin()));
        orderDTO.setDeliveryEndTime(sdf1.format(xdOrderODTO.getReceiveTimeEnd()));
        orderDTO.setSourceType(xdOrderODTO.getSourceType().getCode());
        orderDTO.setOriginalOrderCode(xdOrderODTO.getOriginalOrderCode());
        orderDTO.setOrderType(YesOrNoEnums.YES.getCode());

        List<XdOrderItemDTO> items = xdOrderClient.queryItemsByOrderId(orderId);
        if (items != null && !items.isEmpty()) {
            OrderItemDTO orderItemDTO = null;
            List<OrderItemDTO> list = new ArrayList<>();
            for (XdOrderItemDTO item : items) {
                orderItemDTO = new OrderItemDTO();
                orderItemDTO.setOrderId(orderId);
                orderItemDTO.setItemId(item.getId());
                orderItemDTO.setCommodityId(item.getCommodityId());
                orderItemDTO.setQuantity(item.getQuantity());
                orderItemDTO.setStockNumber(item.getNumber());
                orderItemDTO.setIsWeight(item.getIsWeight().getCode());
                orderItemDTO.setIsProcess(item.getIsProcess().getCode());
                orderItemDTO.setProcessId(null != item.getProcessId() ?item.getProcessId().longValue(): null );
                orderItemDTO.setProcessName(item.getProcessName());
                orderItemDTO.setOriginSubBizId(item.getOriginSubBizId());
                list.add(orderItemDTO);
            }
            orderDTO.setItems(list);
        }
        //生成拣货单
        pickOrderService.createPickOrder(orderDTO);
        //更新拣货单
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        PickOrderMqDTO pickOrderMqDTO = pickOrderService.pickOrderByOrderId(orderId);
        PickOrder pickOrder = new PickOrder();
        pickOrder.setId(pickOrderMqDTO.getPickOrderId());
        pickOrder.setPickBeginTime(new Date());
        pickOrder.setPickId(tokenInfo.getEmployeeId());
        pickOrder.setPickStatus(PickStatusEnum.MIDDLE.getCode());
        pickOrderMapper.updateById(pickOrder);
        //完成加工单
        LambdaQueryWrapper query = new LambdaQueryWrapper<PickWorkOrder>()
                .eq(PickWorkOrder::getPickOrderId, pickOrder.getId());
        List<PickWorkOrder> workOrderList = pickWorkOrderMapper.selectList(query);
        if (null != workOrderList && workOrderList.size() > 0) {
            PickWorkOrder one = null;
            for (PickWorkOrder pickWorkOrder : workOrderList) {
                one = new PickWorkOrder();
                one.setId(pickWorkOrder.getId());
                one.setCompleteTime(new Date());
                one.setWorkStatus(WorkOrderStatusEnum.FINISH.getCode());
                one.setWorkUserId(tokenInfo.getUserId());
                pickWorkOrderMapper.updateById(one);
            }
        }

        //完成拣货单
        PickCompleteDTO pickCompleteDTO = new PickCompleteDTO();
        pickCompleteDTO.setPickOrderId(pickOrder.getId());
        LambdaQueryWrapper queryItem = new LambdaQueryWrapper<PickOrderItem>()
                .eq(PickOrderItem::getPickOrderId, pickOrder.getId());
        List<PickOrderItem> pickOrderItems = pickOrderItemMapper.selectList(queryItem);
        if (null != pickOrderItems && !pickOrderItems.isEmpty()) {
            List<PickCompleteItemDTO> pickCompleteList = new ArrayList<>();
            for (int i = 0; i < pickOrderItems.size(); i++) {
                PickCompleteItemDTO pickCompleteItemDTO = new PickCompleteItemDTO();
                pickCompleteItemDTO.setPickOrderId(pickOrderItems.get(i).getPickOrderId());
                pickCompleteItemDTO.setPickOrderItemId(pickOrderItems.get(i).getId());
                pickCompleteItemDTO.setCommodityId(pickOrderItems.get(i).getCommodityId());
                pickCompleteItemDTO.setPickQuantity(refundNum);
                pickCompleteItemDTO.setIsWeight(pickOrderItems.get(i).getIsWeight());
                pickCompleteList.add(pickCompleteItemDTO);
            }
            pickCompleteDTO.setItems(pickCompleteList);
        }
        pickOrderService.completePickOrder(pickCompleteDTO);

        //模拟 发送配送完成的单子
        DeliveryOrderKafkaDTO deliveryOrderKafkaDTO = new DeliveryOrderKafkaDTO();
        deliveryOrderKafkaDTO.setOrderId(orderId);
        deliveryOrderKafkaDTO.setOrderCode(xdOrderODTO.getOrderCode());
        deliveryOrderKafkaDTO.setDeliveryStatus(XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode());
        deliveryOrderKafkaDTO.setWarehouseId(xdOrderODTO.getShopId());
        deliveryOrderKafkaDTO.setDeliveryType(XdDeliveryOrderTypeEnum.DELIVERY.getCode());
        deliveryOrderKafkaDTO.setDeliveryUserId(tokenInfo.getUserId());
        deliveryOrderKafkaDTO.setDeliveryUserName(tokenInfo.getRealName());
        deliveryOrderKafkaDTO.setDeliveryUserPhone("");
        deliveryOrderKafkaDTO.setDeliveryEndTime(new Date());
        deliveryOrderKafkaDTO.setOrderDeliveryEndTime(new Date());
        deliveryOrderKafkaDTO.setSourceType(OrderSourceTypeEnum.APP);
        deliveryOrderKafkaDTO.setIfOvertime(0);
        List<PickOrderItem> pickCompleteOrderItems = pickOrderItemMapper.selectList(queryItem);
        List<PickOrderItemMqDTO> itemMqs = new ArrayList<>();
        if (null != pickCompleteOrderItems) {
            PickOrderItemMqDTO mqDto = null;
            for (PickOrderItem pickOrderItem : pickCompleteOrderItems) {
                mqDto = new PickOrderItemMqDTO();
                BeanUtils.copyProperties(pickOrderItem, mqDto);
                itemMqs.add(mqDto);
            }
        }
        deliveryOrderKafkaDTO.setItems(itemMqs);


        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_DELIVERY_CHANGE_TOPIC.getTopic(),
                deliveryOrderKafkaDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_DELIVERY_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }

    /**
     * 云超发货查询
     * @param dto
     * @return
     */
    public MPage<CloudDeliverListDTO> cloudDeliverList(CloudDeliverDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        dto.setShopIdList(shopIdList);
        if (StringUtils.isEmpty(dto.getPickupCode()) && StringUtils.isEmpty(dto.getOrderCode())
            && StringUtils.isEmpty(dto.getStartArrivalTime()) && StringUtils.isEmpty(dto.getEndArrivalTime())) {
            QYAssert.isTrue(false, "预约提货日期、订单编号、提货码不能都为空！");
        }
        dto.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
        return grouponMapper.cloudDeliverList(dto);
    }

    /**
     * 云超发货订单详情
     * @param orderId
     * @return
     */
    public CloudCommodityDetailODTO cloudOrderDetail(Long orderId) {
        CloudCommodityDetailODTO cloudCommodityDetailODTO =  grouponMapper.cloudOrder(orderId);
        List<CloudCommodityDetailDTO> list = grouponMapper.cloudCommodityDetail(orderId);
        if (null != list && list.size() > 0) {
            //获取副码
            List<Long> commodityList = list.stream().map(CloudCommodityDetailDTO::getCommodityId).collect(Collectors.toList());
            Map<Long, List<String>> map = commodityBarCodeService.queryCommodityBarCodeList(commodityList);
            for (CloudCommodityDetailDTO dto : list) {
                dto.setBarCodeList(map.get(dto.getCommodityId()));
            }
            cloudCommodityDetailODTO.setDetails(list);
        }
        return cloudCommodityDetailODTO;
    }
}
