package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

/**
 * 门店库存处理IDTO
 */
@AllArgsConstructor
public class CommodityStockItemDTO {
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "是否特价折扣码 1是 0否")
    private Integer ifCouponCode;

    @ApiModelProperty(value = "是否母商品 1是 0否")
    private Integer ifMotherCommodity;

    @ApiModelProperty("档口id")
    private Long stallId;

    public CommodityStockItemDTO() {
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(Integer stockNumber) {
        this.stockNumber = stockNumber;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getIfCouponCode() {
        return ifCouponCode;
    }

    public void setIfCouponCode(Integer ifCouponCode) {
        this.ifCouponCode = ifCouponCode;
    }

    public Integer getIfMotherCommodity() {
        return ifMotherCommodity;
    }

    public void setIfMotherCommodity(Integer ifMotherCommodity) {
        this.ifMotherCommodity = ifMotherCommodity;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }
}