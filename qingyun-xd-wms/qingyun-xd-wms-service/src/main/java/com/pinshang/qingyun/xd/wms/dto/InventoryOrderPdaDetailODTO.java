package com.pinshang.qingyun.xd.wms.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class InventoryOrderPdaDetailODTO extends BaseEntity {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "盘点单号")
    private String orderCode;
    @ApiModelProperty(value = "盘点类型（1：全场盘点，2：品类盘点，3：单品盘点）")
    private Integer inventoryType;
    @ApiModelProperty(value = "进度已盘")
    private Integer inventoryQuantity;
    @ApiModelProperty(value = "进度总数")
    private Integer totalQuantity;
    @ApiModelProperty(hidden = true,value = "计划盘点日期")
    private Date planInventorDate;

    @ApiModelProperty(hidden = true,value = "订单状态(0已取消 1=未确认,2=已确认)")
    @JsonIgnore
    private Integer orderStatus;
    @ApiModelProperty(hidden = true,value = "是否已锁库（0：未锁库，1：已锁库）")
    @JsonIgnore
    private Integer locked;

    public InventoryOrderPdaDetailODTO(Long id, String orderCode, Integer inventoryType, Integer inventoryQuantity, Integer totalQuantity, Date planInventorDate, Integer orderStatus, Integer locked) {
        this.id = id;
        this.orderCode = orderCode;
        this.inventoryType = inventoryType;
        this.inventoryQuantity = inventoryQuantity;
        this.totalQuantity = totalQuantity;
        this.planInventorDate = planInventorDate;
        this.orderStatus = orderStatus;
        this.locked = locked;
    }

    public InventoryOrderPdaDetailODTO() {
    }
}
