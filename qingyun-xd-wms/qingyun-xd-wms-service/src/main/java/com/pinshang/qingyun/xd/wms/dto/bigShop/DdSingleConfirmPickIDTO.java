package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString
@ApiModel("DdSingleConfirmPickIDTO")
public class DdSingleConfirmPickIDTO implements Serializable {

    private static final long serialVersionUID = 380767773864220650L;

    @ApiModelProperty("分区拣货子单id")
    private Long pickPartitionOrderId;

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单明细id")
    private Long pickOrderItemId;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty(value = "拣货份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    public void checkData() {
        QYAssert.isTrue(Objects.nonNull(pickPartitionOrderId), "分区拣货子单id不能为空");
        QYAssert.isTrue(Objects.nonNull(pickOrderId), "拣货单id不能为空");
        QYAssert.isTrue(Objects.nonNull(pickOrderItemId), "拣货单明细id不能为空");
        QYAssert.hasText(commodityId, "商品id不能为空");
    }
}
