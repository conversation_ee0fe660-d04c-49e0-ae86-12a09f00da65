package com.pinshang.qingyun.xd.wms.dto;

import lombok.Data;

/**
 * @ClassName QueryOrderInfoForCloudTakeGoodItemODTO
 * <AUTHOR>
 * @Date 2023/7/31 18:20
 * @Description QueryOrderInfoForCloudTakeGoodItemODTO
 * @Version 1.0
 */
@Data
public class QueryOrderInfoForCloudTakeGoodItemODTO {
    private Long packageId;

    private String packageCode;

    /**
     * 包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货( 一期时状态  1= 门店未验证  4=待顾客提货  7＝顾客已提货）
     */
    private Integer packageStatus;

}
