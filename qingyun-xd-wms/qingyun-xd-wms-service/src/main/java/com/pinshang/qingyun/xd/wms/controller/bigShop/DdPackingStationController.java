package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPackingStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 订单打包口管理表  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Api(tags = "订单打包口管理表", description = "订单打包口管理表")
@RestController
@RequestMapping("/ddPackingStation")
public class DdPackingStationController {

    @Autowired
    private DdPackingStationService ddPackingStationService;

    /**
     * 订单打包口管理表 列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "订单打包口管理表 列表", notes = "订单打包口管理表 列表")
    public PageInfo<DdPackingStationODTO> page(@RequestBody DdPackingStationPageIDTO req) {
        return ddPackingStationService.page(req);
    }

    /**
     * 保存 订单打包口管理表
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存 订单打包口管理表", notes = "保存 订单打包口管理表")
    public Boolean save(@RequestBody List<DdPackingStationSaveIDTO> req) {
        return ddPackingStationService.save(req);
    }

    /**
     * 启用/停用 打包口设置
     */
    @PostMapping("/updateStatus")
    @ApiOperation(value = "启用/停用 打包口设置", notes = "启用/停用 打包口设置")
    public Boolean update(@RequestBody DdPackingStationUpdateIDTO req) {
        return ddPackingStationService.updateStatus(req);
    }

    /**
     * 打包口 订单占用明细
     */
    @MethodRender
    @ApiOperation(value = "打包口 订单占用明细", notes = "打包口 订单占用明细")
    @GetMapping(value = "/detail")
    public List<DdPackingStationOrderODTO> detail(@RequestParam("packingStationId") Long packingStationId) {
        return ddPackingStationService.detail(packingStationId);
    }

}
