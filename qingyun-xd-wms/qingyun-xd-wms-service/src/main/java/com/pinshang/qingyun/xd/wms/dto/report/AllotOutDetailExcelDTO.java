package com.pinshang.qingyun.xd.wms.dto.report;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AllotOutDetailExcelDTO {

    @ApiModelProperty(value = "出库门店")
    @ExcelProperty("门店")
    private String outShopName;

    @ApiModelProperty(value = "入库门店")
    @ExcelProperty("调入门店")
    private String inShopName;

    @ApiModelProperty(value = "订单编号")
    @ExcelProperty("调拨单号")
    private String orderCode;

    @ApiModelProperty(value = "商品编号")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty(value = "条形码")
    @ExcelProperty("条形码")
    private String barCode;

    @ApiModelProperty(value = "包装规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位")
    @ExcelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "申请数量")
    @ExcelProperty("申请数量")
    private BigDecimal applyQuantity;

    @ApiModelProperty(value = "出库数量")
    @ExcelProperty("出库数量")
    private BigDecimal outQuantity;

    @ApiModelProperty(value = "成本价")
    @ExcelProperty("单价")
    private BigDecimal weightPrice;

    @ApiModelProperty(value = "出库商品金额")
    @ExcelProperty("出库商品金额")
    private BigDecimal sumWeightPrice;

    @ApiModelProperty(value = "状态")
    @ExcelProperty("状态")
    private String statusName;

    @ApiModelProperty(value = "出库时间")
    @ExcelProperty("出库时间")
    private Date outTime;

    @ApiModelProperty(value = "入库时间")
    @ExcelProperty("入库时间")
    private Date inTime;

    @ApiModelProperty(value = "调出档口")
    @ExcelProperty("调出档口")
    private String outStallName;

    @ApiModelProperty(value = "调入档口")
    @ExcelProperty("调入档口")
    private String inStallName;

}
