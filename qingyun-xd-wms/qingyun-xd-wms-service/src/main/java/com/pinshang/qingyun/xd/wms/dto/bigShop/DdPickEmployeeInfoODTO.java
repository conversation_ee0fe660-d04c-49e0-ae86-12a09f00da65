package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店职员信息
 */
@Data
@NoArgsConstructor
public class DdPickEmployeeInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "职员ID")
    private Long employeeId;
	@ApiModelProperty(position = 12, required = true, value = "职员编码")
    private String employeeCode;
	@ApiModelProperty(position = 13, required = true, value = "职员名称")
    private String employeeName;
	@ApiModelProperty(position = 14, required = true, value = "职员类型：1-内部职员、2-外部职员	—— 参见 EmployeeTypeEnums")
    private Integer employeeType;
	@ApiModelProperty(position = 15, required = true, value = "职员状态：1-正常、4-离职	—— 参见 EmployeeStateEnums")
    private Integer employeeState;
	@ApiModelProperty(position = 16, required = true, value = "职员账号状态：0-未开通、1-已开通、2-已关闭	 —— 参见 EmployeeAccountStateEnums")
    private Integer employeeAccountState;
	
	@ApiModelProperty(position = 11, required = true, value = "职员IDStr")
    private String employeeIdStr;
	public String getEmployeeIdStr() {
		return null == this.employeeId? "": this.employeeId + "";
	}
}
