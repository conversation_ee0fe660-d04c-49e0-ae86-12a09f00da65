package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16
 * @Version 1.0
 */
@Data
public class StallCommodityStockForPDAODTO {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityCode;

    @ApiModelProperty("条形码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.barCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityName, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("包装规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commoditySpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("是否称重")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.isWeight, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String isWeight;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityUnit;

    @ApiModelProperty("是否可售：1-是,0-否 和t_xs_shop_commodity统一")
    private Integer commoditySaleStatus;

    @ApiModelProperty("APP状态：0-上架、1-下架")
    private Integer appStatus;

    @ApiModelProperty("冻结库存/待拣货库存")
    private BigDecimal freezeQuantity;

    @ApiModelProperty("线下预留库存")
    private BigDecimal reserveStock;

    @ApiModelProperty("临时库存")
    private BigDecimal qualityQuantity;

    @ApiModelProperty("排面库存")
    private StallCommodityStockForPDAItemODTO displayPositionStock;

    @ApiModelProperty("拣货区总库存")
    private BigDecimal totalPickStock;

    @ApiModelProperty("拣货区库存")
    private List<StallCommodityStockForPDAItemODTO> pickingAreaStock;

    @ApiModelProperty("存储区总库存")
    private BigDecimal totalWarehouseStock;

    @ApiModelProperty("临时库总库存")
    private BigDecimal totalTempStock;

    @ApiModelProperty("存储区库存")
    private List<StallCommodityStockForPDAItemODTO> warehouseAreaStock;

    @ApiModelProperty("临时区库存")
    private List<StallCommodityStockForPDAItemODTO> tempStock;

    @ApiModelProperty("排面库存,拣货区库存, 存储区总库存 ")
    private List<StallCommodityStockForPDAItemODTO> integration;
}
