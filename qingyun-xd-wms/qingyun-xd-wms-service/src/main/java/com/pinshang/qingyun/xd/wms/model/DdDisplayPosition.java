package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 排面陈列位管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@TableName("t_dd_display_position")
public class DdDisplayPosition implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 货架id
     */
    private Long shelveId;

    /**
     * 陈列位
     */
    private String displayPositionName;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    private Date updateTime;

}
