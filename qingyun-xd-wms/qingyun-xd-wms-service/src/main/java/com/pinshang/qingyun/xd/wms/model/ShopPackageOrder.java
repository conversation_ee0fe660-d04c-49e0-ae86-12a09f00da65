package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/15 14:10
 */
//TODO DcShopPackageOrder
@Data
@NoArgsConstructor
@TableName("t_dc_shop_package_order")
@ApiModel(value = "ShopPackageOrder对象", description = "商品包裹订单")
public class ShopPackageOrder {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    private Date orderTime;

    private Long shopId;

    /**
     * 包裹单号
     */
    private String orderCode;
    /**
     * 状态(大仓使用) 0=异常 1=正常
     */
    private Integer status;
    /**
     * 包裹状态(门店使用)　1= 门店未验证  4=待顾客提货  7＝顾客已提货
     */
    private Integer packageStatus;
    /**
     * 子单主键(t_xd_sub_order.id)
     */
    private Long subOrderId;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 相关单号(t_xd_order.order_code)
     */
    private String referCode;
    /**
     * 相关单据ID(t_xd_order.id)
     */
    private Long referId;
    /**
     * 打包时间
     */
    private Date packageTime;
    /**
     * package_name
     */
    private String packageName;
    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;


}
