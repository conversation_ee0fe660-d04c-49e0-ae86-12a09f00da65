package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/11/5
 */
@Data
public class DdPickAreaEmployeePageIDTO extends Pagination {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("拣货分区id")
    private Long pickAreaId;

    @ApiModelProperty("职员id")
    private Long employeeId;

    @ApiModelProperty("是否接单  0=否  1=是")
    private Integer workStatus;

    @ApiModelProperty("职员状态  1=在职  4=离职")
    private Integer employeeState;

    @ApiModelProperty("职员编码")
    private String employeeCode;
}
