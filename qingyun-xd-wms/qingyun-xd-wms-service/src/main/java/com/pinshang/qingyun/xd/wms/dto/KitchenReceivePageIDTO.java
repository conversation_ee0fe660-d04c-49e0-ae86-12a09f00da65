package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @ClassName KitchenReceiveIDTO
 * <AUTHOR>
 * @Date 2021/10/20 17:18
 * @Description KitchenReceiveIDTO
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KitchenReceivePageIDTO extends Pagination{
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("后台一级品类ID")
    private Long firstCategoryId;
    @ApiModelProperty("后台二级品类ID")
    private Long secondCategoryId;
    @ApiModelProperty("后台三级品类ID")
    private Long thirdCategoryId;
    @ApiModelProperty("领用开始时间 yyyy-MM-dd")
    private String beginTime;
    @ApiModelProperty("领用结束时间 yyyy-MM-dd")
    private String endTime;
}
