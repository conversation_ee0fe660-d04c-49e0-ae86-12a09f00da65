package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DelWarehouseEmployee {

    @ApiModelProperty(value = "员工id，必填", required = true)
    private Long employeeId;

    @ApiModelProperty(value = "员工类型，1:拣货员 2:配送员，必填", required = true)
    private Integer type;

    public void checkData(){
        QYAssert.isTrue(employeeId != null, "员工id不能为空");
        QYAssert.isTrue(type != null, "员工类型不能为空");
        QYAssert.isTrue(WarehouseEmployeeTypeEnum.getTypeEnumByCode(type) != null, "员工类型不合法");
    }
}
