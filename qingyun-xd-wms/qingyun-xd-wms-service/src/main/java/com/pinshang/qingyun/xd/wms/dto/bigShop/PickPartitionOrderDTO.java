package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickPartitionOrderDTO extends Pagination {

    @ApiModelProperty(value = "拣货单code")
    private String pickOrderCode;

    @ApiModelProperty(value = "拣货单状态(0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消)")
    private Integer pickOrderStatus;

    @ApiModelProperty(value = "拣货分区Id")
    private Long pickAreaId;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "打包口")
    private String packingPort;

    @ApiModelProperty(value = "门店id")
    private Long warehouseId;

    @ApiModelProperty(value = "分区拣货单状态(0=待拣货，1＝拣货中，2＝待交接，3＝已取消)")
    private List<Integer> pickPartitionOrderStatusList;

    @ApiModelProperty(value = "分区拣货单code")
    private String pickPartitionOrderCode;

    @ApiModelProperty(value = "创建开始时间")
    private String createBeginTime;

    @ApiModelProperty(value = "创建结束时间")
    private String createEndTime;

    @ApiModelProperty(value = "客户要求配送开始时间")
    private String orderDeliveryBeginTime;

    @ApiModelProperty(value = "客户要求配送完成时间")
    private String orderDeliveryEndTime;

    @ApiModelProperty("是否分配拣货员 false-未分配 true-已分配")
    private Boolean distributePicker;

    @ApiModelProperty(value = "拣货员id")
    private Long pickId;

}
