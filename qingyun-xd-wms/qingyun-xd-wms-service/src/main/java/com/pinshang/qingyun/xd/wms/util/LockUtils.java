package com.pinshang.qingyun.xd.wms.util;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * Created by chenqi on 2020/4/20.
 * lock工具类
 */
@Slf4j
@Component
public class LockUtils {

    public static int lockWaitTime = 2;

    public static final String FREEZE_OPERATE = "WMS:LOCK:FREEZE_OPERATE:";
    public static final String UN_FREEZE_OPERATE = "WMS:LOCK:UN_FREEZE_OPERATE:";
    public static final String STOCK_INOUT = "WMS:LOCK:STOCK_INOUT:";
    public static final String PICK_OPERATE = "WMS:LOCK:PICK_OPERATE:";
    public static final String PICK_CREATE = "WMS:LOCK:PICK_CREATE:";
    public static final String ELM_CANCELLED = "WMS:LOCK:ELM_CANCELLED:";
    public static final String STOCK_ALLOT = "WMS:LOCK:STOCK_ALLOT:";
    public static final String STOCK_ALLOT_APPLY = "WMS:LOCK:STOCK_ALLOT_APPLY:";

    @Autowired
    private RedissonClient redissonClient;

    private static LockUtils lock;

    @PostConstruct
    public void init() {
        lock = this;
        lock.redissonClient = this.redissonClient;
    }

    public static void checkLock(String key, String code) {
        checkLock(key, code, lockWaitTime, TimeUnit.SECONDS);
    }

    public static void checkLock(String key, String code, int time, TimeUnit timeUnit) {
        RAtomicLong operateLock = lock.redissonClient.getAtomicLong(key + code);
        long lock = operateLock.incrementAndGet();
        if(lock == 1) {
            operateLock.expire(time, timeUnit);
        }
        else if(lock > 1){
            throw new BizLogicException(ApiErrorCodeEnum.REPEAT_SUBMIT);
        }
    }

    public static Boolean checkLockNoEx(String key, String code) {
        return checkLockNoEx(key, code, lockWaitTime, TimeUnit.SECONDS);
    }

    public static Boolean checkLockNoEx(String key, String code, int time, TimeUnit timeUnit) {
        RAtomicLong operateLock = lock.redissonClient.getAtomicLong(key + code);
        long lock = operateLock.incrementAndGet();
        if(lock == 1) {
            operateLock.expire(time, timeUnit);
            return true;
        }
        else if(lock > 1){
            return false;
        }
        return false;
    }
}
