package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Objects;


/**
 * 已上货位 返回值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocatedODTO {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("货位编码")
    private String goodsAllocationCode;

    @ApiModelProperty("拣货/存储区 库存")
    private BigDecimal stock;

    @ApiModelProperty("库区")
    private Integer storageArea;

    @ApiModelProperty("库区名称")
    private String storageAreaName;

    public String getStorageAreaName() {
        if (Objects.equals(storageArea, 2)) {
            return "拣货位";
        } else if (Objects.equals(storageArea, 3)) {
            return "存储位";
        } else {
            return "";
        }
    }
}