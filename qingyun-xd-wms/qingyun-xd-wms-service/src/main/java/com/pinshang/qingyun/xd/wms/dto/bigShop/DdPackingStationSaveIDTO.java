package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;


@Data
@ToString
@ApiModel("DdPackingStationSaveIDTO")
public class DdPackingStationSaveIDTO {

    private Long shopId;

    @ApiModelProperty("打包口")
    private String packingPort;

    @ApiModelProperty("状态，1-启用，0-停用")
    private Integer status;


}