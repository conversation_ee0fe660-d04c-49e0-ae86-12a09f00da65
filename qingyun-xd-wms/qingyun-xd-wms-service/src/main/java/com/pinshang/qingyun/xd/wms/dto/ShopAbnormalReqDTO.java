package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("查询门店损耗请求对象")
@NoArgsConstructor
@AllArgsConstructor
public class ShopAbnormalReqDTO {

    @ApiModelProperty(value = "查询日期,格式：yyyy-MM-dd", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    public void checkData() {
        QYAssert.isTrue(date != null, "查询日期不能为空");
    }

}
