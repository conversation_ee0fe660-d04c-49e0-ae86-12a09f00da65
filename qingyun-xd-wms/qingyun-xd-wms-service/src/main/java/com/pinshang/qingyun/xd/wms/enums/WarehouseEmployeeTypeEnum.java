package com.pinshang.qingyun.xd.wms.enums;

/**
 * 仓内职员类型
 * <AUTHOR>
 */
public enum WarehouseEmployeeTypeEnum {
    PICK(1,"拣货员"),
    DELIVERY(2,"配送员")
    ;
    private Integer code;
    private String name;

    WarehouseEmployeeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static WarehouseEmployeeTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (WarehouseEmployeeTypeEnum typeEnum : WarehouseEmployeeTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
