package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.TaskNumDTO;
import com.pinshang.qingyun.xd.wms.model.DeliveryOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeliveryOrderMapper extends BaseMapper<DeliveryOrder> {

    /**
     * 配送取货位是否占用
     * @param shelfNo
     * @param warehouseId
     * @return
     */
    Integer countShelf(@Param("shelfNo") String shelfNo, @Param("warehouseId") Long warehouseId);

    /**
     * 代配送和配送中的数量
     * @param list
     * @return
     */
    List<TaskNumDTO> deliveryNum(@Param("list") List<Long> list);

    /**
     * 检测 状态异常的配送单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectAbnormalDeliveryOrder(@Param("beginTime") String beginTime, @Param("endTime") String endTime);


    /**
     * 检测 配送单  货位为空的订单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectNullShelfDeliveryOrder(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 检测配送单状态异常订单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectAbnormalDeliveryStatusOrder(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

}
