package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.price.PriceTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.price.dto.CommodityPriceListIDTO;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptIDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptItemIDTO;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseDefaultDTO;
import com.pinshang.qingyun.xd.wms.enums.OrderTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.StockFreezeLog;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import com.pinshang.qingyun.xd.wms.vo.ShopStockReceiptItemKfkVO;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Slf4j
@Transactional(rollbackFor = Exception.class)
public abstract class AbstractStockService {

    @Autowired
    private EveryDayFreshSwitchService everyDayFreshSwitchService;

    @Autowired
    private ShopStockClient shopStockClient;

    @Autowired
    private CommodityPriceClient commodityPriceClient;

    @Autowired
    private StockLogMapper stockLogMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 出入库核心，子类个性化实现
     *
     * @param stockInOutVO
     * @return
     */
    public abstract Pair<Long, String> stockInOut(StockInOutVO stockInOutVO);


    /**
     * 查询库存，大店非大店逻辑不同，必须各自实现
     *
     * @param warehouseId
     * @param commodityIdList
     * @param ddStockInOutExtraVO
     * @return
     */
    public abstract List<ShopStockDTO> queryShopStock(Long warehouseId, List<Long> commodityIdList, List<DdStockInOutExtraVO> ddStockInOutExtraVO);


    public abstract void buildStockCompletePickOutList(Long warehouseId, List<PickOrderItem> pickOrderItems, List<StockItemDTO> outList);

    /**
     * 仓库收货处理 预订单收货
     *
     * @param stockReceiptIDTO
     */
    public void stockReceipt(StockReceiptIDTO stockReceiptIDTO) {
        everyDayFreshSwitchService.transportQuantitySubtract(stockReceiptIDTO);

        StockInOutTypeEnums inType = null;
        StockInOutTypeEnums qualityInType;

        Long warehouseId = stockReceiptIDTO.getWarehouseId();
        Long userId = stockReceiptIDTO.getUserId();
        List<ShopStockDTO> stockList = new ArrayList<>();

        if (stockReceiptIDTO.getIfReturnReceipt() != null && stockReceiptIDTO.getIfReturnReceipt() == true) {
            inType = StockInOutTypeEnums.IN_CANCEL_RECEIPT_NORMAL;
            qualityInType = StockInOutTypeEnums.IN_CANCEL_RECEIPT_QUALITY;
        } else {
//            shopCommodityService.processReceipt(stockReceiptIDTO.getWarehouseId(), stockReceiptIDTO.getCommodityList());
            List<Long> commodityIdList = stockReceiptIDTO.getCommodityList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<DdStockInOutExtraVO> ddStockInOutExtraVOList = stockReceiptIDTO.getCommodityList().stream().map(StockReceiptItemDTO::getDdStockInOutExtraVO).collect(Collectors.toList());
            stockList = queryShopStock(warehouseId, commodityIdList, ddStockInOutExtraVOList);
            // 收货stockEnums传空，预订单收货传enums
            if (stockReceiptIDTO.getStockEnums() == null) {
                inType = StockInOutTypeEnums.IN_RECEIPT_NORMAL;
                stockReceiptIDTO.setStockEnums(StockInOutTypeEnums.IN_RECEIPT_NORMAL);
            }
            qualityInType = StockInOutTypeEnums.IN_RECEIPT_QUALITY;

            // 退货审核入库
            if (StockInOutTypeEnums.IN_RETURN_CHECK.equals(stockReceiptIDTO.getStockEnums())) {
                inType = StockInOutTypeEnums.IN_RETURN_CHECK;
            }

            // 门店审核入库
            if (StockInOutTypeEnums.AUDIT_RETURN_CHECK.equals(stockReceiptIDTO.getStockEnums())) {
                inType = StockInOutTypeEnums.AUDIT_RETURN_CHECK;
            }

            //门店审核入库
            if (StockInOutTypeEnums.IN_RETURN_CHECK.equals(stockReceiptIDTO.getStockEnums())) {
                inType = StockInOutTypeEnums.IN_RETURN_CHECK;
            }

        }

        //预订单 入库 只传 数量份数 无正常品、异常品
        if (StockInOutTypeEnums.IN_PREORDER_NORMAL.equals(stockReceiptIDTO.getStockEnums())) {
            stockPreOrderReceipt(stockReceiptIDTO);
        } else {
            //非预定单收货
            //正常品 入库
            ImmutablePair pair = new ImmutablePair(stockReceiptIDTO.getReferId(), stockReceiptIDTO.getReferCode());
            List<StockItemDTO> normalList = stockReceiptIDTO.toNormalCommodity();
            if (SpringUtil.isNotEmpty(normalList)) {
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, inType, normalList, warehouseId, userId);
                stockInOut(stockInOutVO);
            }

            //异常品 入库
            List<StockItemDTO> abnormalList = stockReceiptIDTO.toAbnormalCommodity();
            if (SpringUtil.isNotEmpty(abnormalList)) {
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, qualityInType, abnormalList, warehouseId, userId);
                stockInOut(stockInOutVO);
            }
        }

        //收货 处理成本价
        if (stockReceiptIDTO.getIfReturnReceipt() == null || stockReceiptIDTO.getIfReturnReceipt() == false) {
            Map<Long, BigDecimal> collect = stockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

            ShopStockReceiptIDTO stockIDTO = new ShopStockReceiptIDTO();
            stockIDTO.setShopId(warehouseId);
            List<ShopStockReceiptItemIDTO> receiptList = new ArrayList<>();
            Long stallId = null;
            for (StockReceiptItemDTO receiptItemDTO : stockReceiptIDTO.getCommodityList()) {
                ShopStockReceiptItemIDTO dto = new ShopStockReceiptItemIDTO();
                dto.setCommodityId(receiptItemDTO.getCommodityId());
                dto.setQuantity(receiptItemDTO.getQuantity());
                dto.setStockNumber(receiptItemDTO.getNumber());
                dto.setPrice(receiptItemDTO.getPrice());
                dto.setTotalPrice(receiptItemDTO.getTotalPrice());
                dto.setExistStockQuantity(collect.get(receiptItemDTO.getCommodityId()));
                receiptList.add(dto);

                DdStockInOutExtraVO ddStockInOutExtraVO = receiptItemDTO.getDdStockInOutExtraVO();
                stallId = Objects.nonNull(ddStockInOutExtraVO) ? ddStockInOutExtraVO.getStallId() : null;
            }
            stockIDTO.setCommodityList(receiptList);
            stockIDTO.setStallId(stallId);
            if (SpringUtil.isNotEmpty(receiptList.stream().filter(it -> it.getQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()))) {
                shopStockClient.stockReceipt(stockIDTO);
            }
        }
    }

    /**
     * 预订单 入库
     *
     * @param stockReceiptIDTO
     */
    void stockPreOrderReceipt(StockReceiptIDTO stockReceiptIDTO) {
        StockIDTO stockIDTO = new StockIDTO();
        stockIDTO.setReferId(stockReceiptIDTO.getReferId());
        stockIDTO.setReferCode(stockReceiptIDTO.getReferCode());
        stockIDTO.setWarehouseId(stockReceiptIDTO.getWarehouseId());
        List<StockItemDTO> commodityList = new ArrayList<>();
        for (StockReceiptItemDTO dto : stockReceiptIDTO.getCommodityList()) {
            StockItemDTO item = new StockItemDTO();
            item.setCommodityId(dto.getCommodityId());
            item.setStockNumber(dto.getNumber());
            item.setQuantity(dto.getQuantity());
            item.setDdStockInOutExtraVO(dto.getDdStockInOutExtraVO());
            commodityList.add(item);
        }
        stockIDTO.setCommodityList(commodityList);
        stockIDTO.setUserId(stockReceiptIDTO.getUserId());
        stockIDTO.setStockEnums(stockReceiptIDTO.getStockEnums());
        modifyStock(stockIDTO);
    }

    /**
     * 处理处理 调整 报损
     *
     * @param stockIDTO
     */
    public void modifyStock(StockIDTO stockIDTO) {
        ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());

        List<StockItemDTO> positiveCommodityList = new ArrayList<>();
        List<StockItemDTO> minusCommodityList = new ArrayList<>();

        for (StockItemDTO stockItemDTO : stockIDTO.getCommodityList()) {
            BigDecimal quantity = stockItemDTO.getQuantity();

            if (quantity.compareTo(BigDecimal.ZERO) >= 0) {
                positiveCommodityList.add(stockItemDTO);
            } else {
                stockItemDTO.setQuantity(quantity.abs());

                if (Objects.isNull(stockIDTO.getCommodityList().get(0).getDdStockInOutExtraVO())
                        && Objects.nonNull(stockItemDTO.getStockNumber()) && stockItemDTO.getStockNumber() < 0) {
                    stockItemDTO.setStockNumber(Math.abs(stockItemDTO.getStockNumber()));
                }

                minusCommodityList.add(stockItemDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(positiveCommodityList)) {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, stockIDTO.getStockEnums(), positiveCommodityList
                    , stockIDTO.getWarehouseId(), stockIDTO.getUserId());
            stockInOut(stockInOutVO);
        }

        if (CollectionUtils.isNotEmpty(minusCommodityList)) {
            if (stockIDTO.getStockEnums().equals(StockInOutTypeEnums.IN_PREORDER_NORMAL)) {
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_PREORDER_NORMAL
                        , minusCommodityList, stockIDTO.getWarehouseId(), stockIDTO.getUserId());
                stockInOut(stockInOutVO);
            } else if (stockIDTO.getStockEnums().equals(StockInOutTypeEnums.IN_ADJUST_NORMAL)) {
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_ADJUST_NORMAL
                        , minusCommodityList, stockIDTO.getWarehouseId(), stockIDTO.getUserId());
                stockInOut(stockInOutVO);
            } else {
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, stockIDTO.getStockEnums(), minusCommodityList
                        , stockIDTO.getWarehouseId(), stockIDTO.getUserId());
                stockInOut(stockInOutVO);
            }
        }
    }

    /**
     * 仓库退货处理(批量)
     *
     * @param stockIDTOList
     */
    public Boolean stockShopReturnList(List<StockIDTO> stockIDTOList) {
        for (StockIDTO stockIDTO : stockIDTOList) {
            //检验数据
            stockIDTO.checkData();
            checkStockReturn(stockIDTO);
            stockShopReturn(stockIDTO);
        }
        return Boolean.TRUE;
    }

    public void checkStockReturn(StockIDTO stockIDTO) {
        if (stockIDTO.getOutTypeEnum() != null) {
            List<Long> commodityIdList = stockIDTO.getCommodityList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<DdStockInOutExtraVO> ddStockInOutExtraVOList = stockIDTO.getCommodityList().stream().map(item -> item.getDdStockInOutExtraVO()).collect(Collectors.toList());

            List<ShopStockDTO> stockList = this.queryShopStock(stockIDTO.getWarehouseId(), commodityIdList, ddStockInOutExtraVOList);
            Map<Long, BigDecimal> collect = stockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

            for (StockItemDTO item : stockIDTO.getCommodityList()) {
                if (item.getQuantity().compareTo(collect.get(item.getCommodityId())) > 0) {
                    throw new BizLogicException("库存数量不足，无法退货");
                }
            }
        }
    }

    /**
     * 仓库退货处理
     *
     * @param stockIDTO
     */
    public void stockShopReturn(StockIDTO stockIDTO) {
        Long warehouseId = stockIDTO.getWarehouseId();
        Long userId = stockIDTO.getUserId();
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(stockIDTO.getWarehouseId());
        tokenInfo.setUserId(stockIDTO.getUserId());
        FastThreadLocalUtil.setQY(tokenInfo);

        ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());
        if (stockIDTO.getInTypeEnum() != null) {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_SHOP_QUALITY
                    , stockIDTO.getCommodityList(), warehouseId, userId);

            // 初始化 临时库 门店退货取消 货位
            processProvisionalAreaGoodsAllocation(stockInOutVO);

            stockInOut(stockInOutVO);
        } else if (stockIDTO.getOutTypeEnum() != null) {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_SHOP_NORMAL
                    , stockIDTO.getCommodityList(), warehouseId, userId);
            stockInOut(stockInOutVO);
        }
    }

    private void processProvisionalAreaGoodsAllocation(StockInOutVO stockInOutVO) {
        List<StockItemDTO> commodityList = stockInOutVO.getCommodityList();
        Set<Long> stallIds = commodityList.stream()
                .map(StockItemDTO::getDdStockInOutExtraVO)
                .filter(Objects::nonNull)
                .map(DdStockInOutExtraVO::getStallId)
                .collect(Collectors.toSet());
        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap =
                ddTempWarehouseAllocationService.getStallIdProvisionalAreaGoodsAllocationMap(stallIds,TempOperationTypeEnum.STORE_RETURN_CANCEL);
        if (SpringUtil.isEmpty(stallIdProvisionalAreaGoodsAllocationMap)) {
           return;
        }

        for (StockItemDTO stockItemDTO : commodityList) {
            DdTempWarehouseDefaultDTO ddTempWarehouseDefaultDTO = stallIdProvisionalAreaGoodsAllocationMap.get(stockItemDTO.getDdStockInOutExtraVO()
                    .getStallId());
            if (Objects.isNull(ddTempWarehouseDefaultDTO)) {
                continue;
            }
            DdStockInOutExtraVO ddStockInOutExtraVO = stockItemDTO.getDdStockInOutExtraVO();
            ddStockInOutExtraVO.setGoodsAllocationId(ddTempWarehouseDefaultDTO.getGoodsAllocationId());
            ddStockInOutExtraVO.setGoodsAllocationCode(ddTempWarehouseDefaultDTO.getGoodsAllocationCode());
        }
    }

    /**
     * 加工单
     * 某加工商品的成本金额 x = (某加工商品零售金额 * 加工后数量 / ∑ 加工商品零售金额 * 加工商品数量 ) * (∑ 原材料成本 * 原材料数量)
     *
     * @param stockProcessIDTO
     */
    public void stockProcessing(StockProcessIDTO stockProcessIDTO) {
        //加工单 处理成本价
        List<StockProcessItemDTO> originList = stockProcessIDTO.getOriginList();
        List<StockProcessItemDTO> finishedList = stockProcessIDTO.getFinishedList();

        List<Long> originIdList = new ArrayList<>(originList.size());
        List<DdStockInOutExtraVO> originDdStockInOutExtraVOList = new ArrayList<>(originList.size());
        extractLists(originList, originIdList, originDdStockInOutExtraVOList);

        List<Long> finishedIdList = new ArrayList<>(finishedList.size());
        List<DdStockInOutExtraVO> finishedDdStockInOutExtraVOList = new ArrayList<>(finishedList.size());
        extractLists(finishedList, finishedIdList, finishedDdStockInOutExtraVOList);

        List<ShopStockDTO> finishedStockList = this.queryShopStock(stockProcessIDTO.getShopId(), finishedIdList, finishedDdStockInOutExtraVOList);
        Map<Long, BigDecimal> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

        //出入库
        ImmutablePair idAndCode = new ImmutablePair(stockProcessIDTO.getReferId(), stockProcessIDTO.getReferCode());
        StockInOutVO outStockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_PROCESS_NORMAL
                , stockProcessIDTO.toOriginList(), stockProcessIDTO.getShopId(), stockProcessIDTO.getUserId());
        stockInOut(outStockInOutVO);

        StockInOutVO inStockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_PROCESS_NORMAL
                , stockProcessIDTO.toFinishedList(), stockProcessIDTO.getShopId(), stockProcessIDTO.getUserId());
        stockInOut(inStockInOutVO);

        //获取所有原材料成本价
        CommodityPriceListIDTO priceIDTO = new CommodityPriceListIDTO();
        priceIDTO.setAllotType(PriceTypeEnums.WEIGHT_PRICE.getCode());
        priceIDTO.setCommodityIdList(originIdList);
        priceIDTO.setShopId(stockProcessIDTO.getShopId());
        Map<Long, BigDecimal> originPriceMap = commodityPriceClient.getShopCommodityPriceByShopId(priceIDTO);
        Assert.isTrue(originIdList.size() == originPriceMap.size(), "获取所有原材料成本价失败");
        //获取所有产成品零售价
        priceIDTO.setAllotType(PriceTypeEnums.RETAIL_PRICE.getCode());
        priceIDTO.setCommodityIdList(finishedIdList);
        priceIDTO.setShopId(stockProcessIDTO.getShopId());
        Map<Long, BigDecimal> retailPriceMap = commodityPriceClient.getShopCommodityPriceByShopId(priceIDTO);
        Assert.isTrue(finishedIdList.size() == retailPriceMap.size(), "获取所有产成品零售价失败");

        //∑ 加工商品零售金额 * 加工商品数量
        BigDecimal originWeightSum = originList.stream().map(item -> item.getQuantity().multiply(originPriceMap.get(item.getCommodityId()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        originWeightSum = originWeightSum.setScale(4, BigDecimal.ROUND_HALF_UP);
        //∑ 加工商品零售金额 * 加工商品数量
        BigDecimal finishedPriceSum = finishedList.stream().map(item -> item.getQuantity().multiply(retailPriceMap.get(item.getCommodityId()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        finishedPriceSum = finishedPriceSum.setScale(4, BigDecimal.ROUND_HALF_UP);
        Assert.isTrue(finishedPriceSum.compareTo(BigDecimal.ZERO) > 0, "产成品零售价异常");

        ShopStockReceiptIDTO stockIDTO = new ShopStockReceiptIDTO();
        stockIDTO.setShopId(stockProcessIDTO.getShopId());
        List<ShopStockReceiptItemIDTO> receiptList = new ArrayList<>();
        /*for (StockProcessItemDTO processItemDTO : finishedList) {
            ShopStockReceiptItemIDTO dto = new ShopStockReceiptItemIDTO();
            dto.setCommodityId(processItemDTO.getCommodityId());
            dto.setQuantity(processItemDTO.getQuantity());
            dto.setStockNumber(processItemDTO.getNumber());
            //本次加工出来的成本价 以及 总价
            //本次成本价公式
            BigDecimal weightTotal = processItemDTO.getQuantity().multiply(retailPriceMap.get(processItemDTO.getCommodityId()))
                    .divide(finishedPriceSum, 4, BigDecimal.ROUND_HALF_UP).multiply(originWeightSum).setScale(2, BigDecimal.ROUND_HALF_UP);
            dto.setPrice(weightTotal.divide(processItemDTO.getQuantity(), 2, BigDecimal.ROUND_HALF_UP));
            dto.setTotalPrice(weightTotal);
            dto.setExistStockQuantity(stockCollect.get(processItemDTO.getCommodityId()));
            receiptList.add(dto);
        }*/

        List<ShopStockReceiptItemKfkVO> kfkVOList = new ArrayList<>();
        // 分摊比例
        BigDecimal commodityTotalRate = BigDecimal.ZERO;

        // 方法调整，最后产成品的分摊比例 用减法
        int size = finishedList.size() - 1;
        BigDecimal countedWeightPrice = BigDecimal.ZERO;
        for (int i = 0; i < finishedList.size(); i++) {
            StockProcessItemDTO processItemDTO = finishedList.get(i);
            ShopStockReceiptItemIDTO dto = new ShopStockReceiptItemIDTO();
            dto.setCommodityId(processItemDTO.getCommodityId());
            dto.setQuantity(processItemDTO.getQuantity());
            dto.setStockNumber(processItemDTO.getNumber());
            dto.setExistStockQuantity(stockCollect.get(processItemDTO.getCommodityId()));
            BigDecimal weightTotal = null;
            if (i == size) {
                weightTotal = originWeightSum.subtract(countedWeightPrice);
            } else {
                weightTotal = processItemDTO.getQuantity().multiply(retailPriceMap.get(processItemDTO.getCommodityId()))
                        .divide(finishedPriceSum, 4, BigDecimal.ROUND_HALF_UP).multiply(originWeightSum).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            dto.setPrice(weightTotal.divide(processItemDTO.getQuantity(), 2, BigDecimal.ROUND_HALF_UP));
            dto.setTotalPrice(weightTotal);

            countedWeightPrice = countedWeightPrice.add(weightTotal);
            receiptList.add(dto);

            ShopStockReceiptItemKfkVO kfkVO = BeanCloneUtils.copyTo(dto, ShopStockReceiptItemKfkVO.class);
            kfkVO.setJgId(processItemDTO.getJgId());
            // 计算分摊比例,最后一个减
            BigDecimal commodityRate = null;
            if (i == size) {
                commodityRate = new BigDecimal("100").subtract(commodityTotalRate);
            } else {
                commodityRate = finishedList.get(i).getQuantity().multiply(retailPriceMap.get(finishedList.get(i).getCommodityId()))
                        .divide(finishedPriceSum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
            }
            kfkVO.setCommodityRate(commodityRate);
            commodityTotalRate = commodityTotalRate.add(commodityRate);
            kfkVOList.add(kfkVO);

            log.info("分摊比例小店: jgId={} commodityId={} retailPrice={} finishedPriceSum={} commodityRate={}",
                    kfkVO.getJgId(), dto.getCommodityId(),
                    finishedList.get(i).getQuantity().multiply(retailPriceMap.get(finishedList.get(i).getCommodityId())),
                    finishedPriceSum, commodityRate);
        }
        stockIDTO.setCommodityList(receiptList);
        shopStockClient.stockReceipt(stockIDTO);

        // 门店加工单记录加工入库商品的分摊比例
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.JG_SAVE_COMMODITY_PRICE_TOPIC.getTopic(),
                kfkVOList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.JG_SAVE_COMMODITY_PRICE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }

    /**
     * 提取 CommodityId 和 DdStockInOutExtraVO 的通用方法
     *
     * @param sourceList
     * @param idList
     * @param extraVOList
     */
    public void extractLists(List<StockProcessItemDTO> sourceList, List<Long> idList, List<DdStockInOutExtraVO> extraVOList) {
        for (StockProcessItemDTO stockProcessItemDTO : sourceList) {
            idList.add(stockProcessItemDTO.getCommodityId());
            extraVOList.add(stockProcessItemDTO.getDdStockInOutExtra());
        }
    }

    /**
     * 库存冻结解冻记录日志
     */
    public void addStockFreezeLog(Long referId, String referCode, Long shopId, List<StockItemDTO> commodityList) {
        List<StockFreezeLog> stockFreezeLogList = new ArrayList<>();
        for (StockItemDTO stockItemDTO : commodityList) {
            StockFreezeLog stockFreezeLog = new StockFreezeLog();
            stockFreezeLog.setReferId(referId);
            stockFreezeLog.setReferCode(referCode);
            stockFreezeLog.setShopId(shopId);

            if (Objects.nonNull(stockItemDTO.getDdStockInOutExtraVO())) {
                stockFreezeLog.setStallId(stockItemDTO.getDdStockInOutExtraVO().getStallId());
            }

            stockFreezeLog.setCommodityId(stockItemDTO.getCommodityId());
            stockFreezeLog.setQuantity(new BigDecimal(stockItemDTO.getStockNumber()));
            stockFreezeLogList.add(stockFreezeLog);
        }
        if (CollectionUtils.isNotEmpty(stockFreezeLogList)) {
            stockLogMapper.batchInsertStockFreezeLog(stockFreezeLogList);
        }
    }

    public void stockCompletePick(Long orderId, String orderCode, Long warehouseId, Pair<Long, String> idAndCode, List<PickOrderItem> pickOrderItems, Integer orderType) {
        int listInitSize = pickOrderItems.size();
        List<StockItemDTO> outList = new ArrayList<>(listInitSize);

        buildStockCompletePickOutList(warehouseId,pickOrderItems, outList);

        //解冻库存
        //冻结多少直接解冻多少 需求4072
        if (OrderTypeEnum.GROUP.getCode().equals(orderType)) {
            int groupSwitch = 0;
            try {
                DictionaryODTO dictionary = dictionaryClient.getDictionaryById(DictionaryEnums.GROUP_SWITCH.getId());
                groupSwitch = Integer.valueOf(dictionary.getOptionValue());
            } catch (Exception e) {
                log.error("获取分配提前时间失败");
            }
            if (YesOrNoEnums.YES.getCode() == groupSwitch) {
                //清美团购 东西已到门店并且门店收货 冻结的意义是假如不冻结 可能被其他人其他渠道买走，客人无法提到货
                shopCommodityService.groupStockUnFreeze(orderId, orderCode, warehouseId, pickOrderItems);
            }
            //出库
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_CLIENT_GROUP_NORMAL
                    , outList, warehouseId, StockUtils.INSTANCE.userId());
            stockInOut(stockInOutVO);
        } else if (OrderTypeEnum.QUICK_GROUPON.getCode().equals(orderType)) {
            shopCommodityService.stockUnFreeze(orderId, orderCode, warehouseId);
            //出库
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_QUICK_STOCK
                    , outList, warehouseId, StockUtils.INSTANCE.userId());
            stockInOut(stockInOutVO);
        } else if (OrderTypeEnum.CLOUD_GROUP.getCode().equals(orderType)) {
            //出库，不需要解冻库存
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_CLIENT_NORMAL
                    , outList, warehouseId, StockUtils.INSTANCE.userId());
            stockInOut(stockInOutVO);
        } else {
            shopCommodityService.stockUnFreeze(orderId, orderCode, warehouseId);
            //出库
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_CLIENT_NORMAL
                    , outList, warehouseId, StockUtils.INSTANCE.userId());
            stockInOut(stockInOutVO);
        }

    }

    public Boolean stockInventory(CommodityXsStockIDTO stockIDTO, ImmutablePair idAndCode) {
        List<StockItemDTO> positiveCommodityList = new ArrayList<>();
        List<StockItemDTO> minusCommodityList = new ArrayList<>();
        for (StockItemDTO stockItemDTO : stockIDTO.getCommodityList()) {
            // 1. 数量 > 0  或者 (数量 == 0 且 份数 > 0)  为盘盈
            // 2.数量 < 0, 盘亏 , 数量取绝对值, 份数取反 ( 如果份数 < 0 则份数取正, 如果 份数 > 0, 取负数, 等于0则还是0 )
            // 3. 数量 == 0 且份数 < 0 , 则盘亏, 份数取绝对值
            // 不在 数量和份数同时为0的情况.
            if (stockItemDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0 ||
                    (stockItemDTO.getQuantity().compareTo(BigDecimal.ZERO) == 0 && stockItemDTO.getStockNumber() > 0)) {
                positiveCommodityList.add(stockItemDTO);
            } else if (stockItemDTO.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
                stockItemDTO.setStockNumber(-stockItemDTO.getStockNumber());
                stockItemDTO.setQuantity(stockItemDTO.getQuantity().abs());
                minusCommodityList.add(stockItemDTO);
            } else if (stockItemDTO.getQuantity().compareTo(BigDecimal.ZERO) == 0 && stockItemDTO.getStockNumber() < 0) {
                stockItemDTO.setStockNumber(Math.abs(stockItemDTO.getStockNumber()));
                minusCommodityList.add(stockItemDTO);
            }
        }
        //盘点 数据量太大分摊压力
        int subListSize = 50;
        if (positiveCommodityList.size() > subListSize) {
            int times = positiveCommodityList.size() / subListSize;

            for (int i = 0; i <= times; i++) {
                List<StockItemDTO> subList = positiveCommodityList.subList(i * subListSize, ((i + 1) * subListSize) < positiveCommodityList.size() ? ((i + 1) * subListSize) : positiveCommodityList.size());
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_INVENTORY_PLUS
                        , subList, stockIDTO.getShopId(), stockIDTO.getUserId());
                this.stockInOut(stockInOutVO);
            }
        } else {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_INVENTORY_PLUS
                    , positiveCommodityList, stockIDTO.getShopId(), stockIDTO.getUserId());
            this.stockInOut(stockInOutVO);
        }

        if (minusCommodityList.size() > subListSize) {
            int times = minusCommodityList.size() / subListSize;

            for (int i = 0; i <= times; i++) {
                List<StockItemDTO> subList = minusCommodityList.subList(i * subListSize, ((i + 1) * subListSize) < minusCommodityList.size() ? ((i + 1) * subListSize) : minusCommodityList.size());
                StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_INVENTORY_LOSS
                        , subList, stockIDTO.getShopId(), stockIDTO.getUserId());
                this.stockInOut(stockInOutVO);
            }
        } else {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_INVENTORY_LOSS
                    , minusCommodityList, stockIDTO.getShopId(), stockIDTO.getUserId());
            this.stockInOut(stockInOutVO);
        }
        return Boolean.TRUE;
    }

}
