package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 调拨申请列表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderListDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "申请类型")
    private Integer allotType;

    @ApiModelProperty(value = "调拨单号")
    private String orderCode;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    //入库单列表
    @ApiModelProperty(value = "入库时间")
    private Date inTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "入库人")
    private String inPersonName;

    @ApiModelProperty(value = "调出门店")
    private String outShopName;

    private Long outStallId;

    @ApiModelProperty(value = "调出档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "outStallId")
    private String outStallName;

    //出库单
    @ApiModelProperty(value = "出库时间")
    private Date outTime;

    @ApiModelProperty(value = "出库人")
    private String outPersonName;

    @ApiModelProperty(value = "调入门店")
    private String inShopName;

    private Long inStallId;

    @ApiModelProperty(value = "调入档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "inStallId")
    private String inStallName;

    //总部列表
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    @ApiModelProperty(value = "审核人")
    private String auditName;

}
