package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShelfCommodityDTO {

    @ApiModelProperty(value = "货位id")
    private Long shelfId;

    @ApiModelProperty(value = "货位编码")
    private String shelfNo;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;
}
