package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.pinshang.qingyun.base.service.UserStallServiceInterface ;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserStallServiceImpl implements UserStallServiceInterface  {

    @Autowired
    private UserStallClient userStallClient;

    @Override
    public List<Long> selectUserStallIdList(Long userId, Long shopId) {
        SelectUserStallIdListIDTO selectUserStallIdListIDTO = new SelectUserStallIdListIDTO();
        selectUserStallIdListIDTO.setUserId(userId);
        selectUserStallIdListIDTO.setShopId(shopId);
        selectUserStallIdListIDTO.setStallStatus(1);
        return userStallClient.selectUserStallIdList(selectUserStallIdListIDTO);
    }
}
