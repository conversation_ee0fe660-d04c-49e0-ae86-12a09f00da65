package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.StockAllotOrderStatusEnums;
import com.pinshang.qingyun.base.enums.StockAllotTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.XsStockTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.shop.admin.dto.ConsignmentSupplierInfoODTO;
import com.pinshang.qingyun.shop.admin.service.ConsignmentSupplierClient;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.dto.ShopStockCommodityIDto;
import com.pinshang.qingyun.shop.dto.ShopStockIDto;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptIDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptItemIDTO;
import com.pinshang.qingyun.shop.service.ShopAllocateWhiteClient;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.OrgRefClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.price.dto.CommodityPriceListIDTO;
import com.pinshang.qingyun.xd.price.service.XdShopCommodityPriceClient;
import com.pinshang.qingyun.xd.product.dto.XdShopCommodityListIDTO;
import com.pinshang.qingyun.xd.product.service.XdCommodityClient;
import com.pinshang.qingyun.xd.wms.bo.StockAllotOrderApplyBO;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdCheckShopAllotStallAuthorityDTO;
import com.pinshang.qingyun.xd.wms.dto.message.AllotItemMessage;
import com.pinshang.qingyun.xd.wms.dto.message.AllotMessage;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.ShopMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockAllotOrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockAllotOrderMapper;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.Shop;
import com.pinshang.qingyun.xd.wms.model.StockAllotOrder;
import com.pinshang.qingyun.xd.wms.model.StockAllotOrderItem;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityService;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.StallUtils;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 所有库存调拨操作
 * 调拨申请
 * 调拨审核
 * 调拨出库
 * 调拨入库
 * Created by chenqi on 2020/5/11.
 */
@Service
@Slf4j
public class StockAllotOrderService {

    @Autowired
    private StockAllotOrderMapper stockAllotOrderMapper;
    @Autowired
    private StockAllotOrderItemMapper stockAllotOrderItemMapper;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private StockServiceAdapter stockServiceAdapter;
    @Autowired
    private XdShopCommodityPriceClient xdShopCommodityPriceClient;
    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private ShopStockClient shopStockClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private XdCommodityClient xdCommodityClient;
    @Autowired
    private ShopCommodityClient shopCommodityClient;
    @Autowired
    private OrgRefClient orgRefClient;
    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private ShopAllocateWhiteClient shopAllocateWhiteClient;

    @Autowired
    private ConsignmentSupplierClient consignmentSupplierClient;

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private StallCommodityService stallCommodityService;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private StallAuthorityCheckService stallAuthorityCheckService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private StockAllotOrderApplyInitService  stockAllotOrderApplyInitService;


    @Autowired
    private UserStallClient userStallClient;

    public Map<Long, OrgAndParentInfoODTO> getOrgMap(List<Long> shopIdList){
        Map<Long,OrgAndParentInfoODTO> orgMap = new HashMap<>();
        SelectShopOrgInfoListIDTO selectShopOrgInfoListIDTO = new SelectShopOrgInfoListIDTO();
        selectShopOrgInfoListIDTO.setShopIdList(shopIdList);
        List<OrgAndParentInfoODTO> orgList = orgClient.selectShopOrgInfoList(selectShopOrgInfoListIDTO);
        if(CollectionUtils.isNotEmpty(orgList)){
            orgMap = orgList.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, e -> e));
        }
        return orgMap;
    }

    /**
     * http://192.168.0.213/zentao/story-view-12131.html
     * 同一门店类型之间调拨，调入价格=调出方成本价
     * 同一门店类型分组并且同部门，调入价格=调出方成本价
     * @return
     */
    private Boolean isSameShopTypeOrGroup(Long inShopId, Long outShopId){
        Shop inShop= shopMapper.selectById(inShopId);
        Shop outShop= shopMapper.selectById(outShopId);

        // 门店类型相同
        Boolean sameShopType = inShop.getShopType().equals(outShop.getShopType());
        Boolean sameShopGroup = false;

        // 判断组内门店类型和部门是否都相同
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("allotInPriceGroup");
        if(dictionaryODTO != null && org.apache.commons.lang3.StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {
            List<String> groupList = new ArrayList<>(Arrays.asList(dictionaryODTO.getOptionValue().split(",")));
            // 组内门店类型相同
            if(groupList.contains(inShop.getShopType().toString()) && groupList.contains(outShop.getShopType().toString())) {
                List<Long> shopIdList = new ArrayList<>();
                shopIdList.add(inShopId);
                shopIdList.add(outShopId);
                Map<Long,OrgAndParentInfoODTO> orgMap = getOrgMap(new ArrayList<>(shopIdList));

                OrgAndParentInfoODTO inOrg = orgMap.get(inShopId);
                OrgAndParentInfoODTO outOrg = orgMap.get(outShopId);
                if(inOrg != null && outOrg != null && inOrg.getParentOrgCode().equals(outOrg.getParentOrgCode())) {
                    sameShopGroup = true;
                }
            }
        }

        if(sameShopType || sameShopGroup) {
            return true;
        }
        return false;
    }

    @Transactional
    public void apply(StockAllotOrderApplyDTO dto) {
        //1校验数据
        StockAllotOrderApplyBO bo = this.checkDataBeforeApply(dto);
        Shop shop = shopMapper.selectById(dto.getShopId());
        QYAssert.isTrue(shop != null, "门店不存在");

        // 调出门店可以是营业中，暂停营业，开业前 ；调入门店依然是：开业前，营业中
        if (StockAllotTypeEnums.ALLOT_OUT.getCode() == dto.getAllotType().intValue()) { //调出判断调入门店状态
            QYAssert.isTrue(shop.getShopStatus() != 0 && shop.getShopStatus() != 3, "调拨申请不支持暂停营业或永久停业门店");
        }
        //2处理申请单信息
        StockAllotOrder sao = new StockAllotOrder();
        BeanUtils.copyProperties(dto, sao);
        Long shopIdToken = StockUtils.INSTANCE.warehouseId();
        Shop shopToken = shopMapper.selectById(shopIdToken);
        //调入申请单
        if (StockAllotTypeEnums.ALLOT_IN.getCode() == dto.getAllotType().intValue()) {
            sao.setInShopId(shopToken.getId());
            sao.setInShopType(shopToken.getShopType());
            sao.setInStallId(dto.getInStallId());
            sao.setOutShopId(shop.getId());
            sao.setOutShopType(shop.getShopType());
            sao.setOutStallId(dto.getOutStallId());
        } else {//调出申请单
            sao.setInShopId(shop.getId());
            sao.setInShopType(shop.getShopType());
            sao.setInStallId(dto.getInStallId());
            sao.setOutShopId(shopToken.getId());
            sao.setOutShopType(shopToken.getShopType());
            sao.setOutStallId(dto.getOutStallId());
        }

        // 同类型或者同组同部门
        Boolean isSameShopTypeOrGroup = isSameShopTypeOrGroup(sao.getInShopId(), sao.getOutShopId());

        //生成申请单号
        String code = codeClient.createCode("DBD_STOCK_ALLOT_APPLY_CODE");
        sao.setOrderCode(code);
        sao.setStatus(StockAllotOrderStatusEnums.WAIT_AUDIT.getCode());
        Long userId = StockUtils.INSTANCE.userId();
        sao.setCreateId(userId);
        //设置是否店内调拨0否-1是
        sao.setIsInstoreAllot(bo.getIsInstoreAllot());
        stockAllotOrderMapper.insert(sao);
        Long stockAllotId = sao.getId();
        List<StockAllotOrderItem> itemEntitys = new ArrayList<>();

        boolean isBigShop = bo.isBigShop();
        List<Long> commodityIdList = dto.getItems().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        if(!isBigShop){
            //1调用价格接口获取成本价、进价 保存价格 审核那一刻的 调出取成本 调入取进价
            CommodityPriceListIDTO priceDto = new CommodityPriceListIDTO();
            priceDto.setShopId(sao.getOutShopId());
            priceDto.setAllotType(StockAllotTypeEnums.ALLOT_OUT.getCode());
            priceDto.setCommodityIdList(commodityIdList);
            Map<Long, BigDecimal> weightMap = xdShopCommodityPriceClient.getShopCommodityPrice(priceDto);
            priceDto.setShopId(sao.getInShopId());
            priceDto.setAllotType(StockAllotTypeEnums.ALLOT_IN.getCode());
            Map<Long, BigDecimal> priceMap = xdShopCommodityPriceClient.getShopCommodityPrice(priceDto);
            //3处理商品信息
            StockAllotOrderItem saoi = null;
            for (StockAllotOrderItemApplyDTO item : dto.getItems()) {
                if (weightMap.get(item.getCommodityId()) == null) {
                    Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                    throw new BizLogicException(commodity.getCommodityCode() + "无效的商品成本");
                }

                // 同类型或者同组同部门不校验调入门店商品信息了
                if(!isSameShopTypeOrGroup) {
                    if (priceMap.get(item.getCommodityId()) == null) {
                        Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                        throw new BizLogicException(commodity.getCommodityCode() + "无效的商品进价");
                    }
                }

                saoi = new StockAllotOrderItem();
                saoi.setStockAllotId(stockAllotId);
                BeanUtils.copyProperties(item, saoi);
                saoi.setCreateId(userId);
                itemEntitys.add(saoi);
            }
        }else{
            //大店之间调拨：价格取移动平均价 取调出档口的移动平均价
            StockAllotOrderItem saoi = null;
            for (StockAllotOrderItemApplyDTO item : dto.getItems()) {
                saoi = new StockAllotOrderItem();
                saoi.setStockAllotId(stockAllotId);
                BeanUtils.copyProperties(item, saoi);
                saoi.setCreateId(userId);
                itemEntitys.add(saoi);
            }

        }

        stockAllotOrderItemMapper.batchInsert(itemEntitys);

        if(isSameShopTypeOrGroup) {
            // 异步调用初始化调入门店商品信息
            stockAllotOrderApplyInitService.initShopCommodityTransferInventory(code,userId, sao.getInShopId(), sao.getOutShopId(), commodityIdList);
        }
    }

    /**
     * 申请前校验
     */
    private StockAllotOrderApplyBO checkDataBeforeApply(StockAllotOrderApplyDTO dto) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token), "请登录后操作");
        QYAssert.isTrue(dto != null, "无效调拨申请单");
        QYAssert.isTrue(dto.getAllotType() != null && (StockAllotTypeEnums.ALLOT_IN.getCode() == dto.getAllotType().intValue() || StockAllotTypeEnums.ALLOT_OUT.getCode() == dto.getAllotType().intValue()), "调拨类型不合法");
        QYAssert.isTrue(!CollectionUtils.isEmpty(dto.getItems()), "商品信息必填");

        Long shopIdToken = StockUtils.INSTANCE.warehouseId();
        QYAssert.isTrue(null != shopIdToken, "总部不可新增调拨申请");

        //判断是否大店
        boolean isBigShop = StallUtils.isBigShop(token.getManagementMode());
        Integer isInstoreAllot = 0;
        QYAssert.isTrue(dto.getShopId() != null, "门店必填");
        if (!isBigShop) {
            //普通门店调入调出门店必填且调入调出门店不能相同
            //大店默认相同
            QYAssert.isTrue(!shopIdToken.equals(dto.getShopId()), "调入调出门店不能相同");
        } else {
            //大店调出门店和调入门店默认登录门店且不允许修改，大店的调出档口和调入档口必须填写且不能相同
            QYAssert.isTrue(Objects.nonNull(dto.getOutStallId()), "大店调拨调出档口不能为空");
            QYAssert.isTrue(Objects.nonNull(dto.getInStallId()), "大店调拨调入档口不能为空");
            QYAssert.isTrue(!Objects.equals(dto.getOutStallId(), dto.getInStallId()), "调出档口和调入档口不能相同");
            DdCheckShopAllotStallAuthorityDTO ddCheckShopAllotStallAuthorityDTO = new DdCheckShopAllotStallAuthorityDTO();
            ddCheckShopAllotStallAuthorityDTO.setCurrentShopId(shopIdToken);
            ddCheckShopAllotStallAuthorityDTO.setTargetShopId(dto.getShopId());
            ddCheckShopAllotStallAuthorityDTO.setAllotType(dto.getAllotType());
            ddCheckShopAllotStallAuthorityDTO.setUserId(token.getUserId());
            ddCheckShopAllotStallAuthorityDTO.setInStallId(dto.getInStallId());
            ddCheckShopAllotStallAuthorityDTO.setOutStallId(dto.getOutStallId());

            stallAuthorityCheckService.checkAllotStallAuthority(ddCheckShopAllotStallAuthorityDTO);
            //如果是大店，则将入参中的shopId设置成和token信息中的一致，方便后期按照一致的方式进行处理
//            dto.setShopId(shopIdToken);
            if(Objects.equals(shopIdToken,dto.getShopId())){
                isInstoreAllot = 1;
            }

        }
        List<Long> commodityIdList = new ArrayList<Long>();
        dto.getItems().stream().forEach(it -> {
            Integer applyNumber = it.getApplyNumber();
            BigDecimal applyQuantity = it.getApplyQuantity();
            QYAssert.isTrue(applyQuantity != null && applyQuantity.compareTo(new BigDecimal(0)) > 0, "申请数量需大于0");
            commodityIdList.add(it.getCommodityId());
        });

        if (!isBigShop) {

            //校验商品是否有效,“XXX商品入库门店不可售\未上架”
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIdList);
            List<XdShopCommodityListIDTO> commodityListIDTOList = dto.getItems().stream().map(it -> {
                XdShopCommodityListIDTO xscDto = new XdShopCommodityListIDTO();
                xscDto.setCurrentShopId(shopIdToken);
                xscDto.setAllotShopId(dto.getShopId());
                //因为校验商品接口是以目标门店为维度,所以出入类型调换下
                xscDto.setAllotType(new Integer(1).equals(dto.getAllotType()) ? 2 : 1);
                List<String> barCodeList = longListMap.get(it.getCommodityId());
                if (CollectionUtils.isNotEmpty(barCodeList)) {
                    xscDto.setBarCode(barCodeList.get(0));
                }
                return xscDto;
            }).collect(Collectors.toList());
            xdCommodityClient.findShopCommodityByBarcodeList(commodityListIDTOList);

        } else {
            //调出档口和调入档口下的库存表必须存在该商品，
            List<Commodity> commodityList = commodityMapper.queryCommodityByIdList(commodityIdList);
            Map<Long,Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId,Function.identity()));
            List<StallCommodity> stallByCommodityIds = stallCommodityService.getStallCommodityByCommodityIds(Arrays.asList(shopIdToken,dto.getShopId()), Arrays.asList(dto.getInStallId(), dto.getOutStallId()), commodityIdList);
            Map<String, StallCommodity> shopStallCommodityListMap = stallByCommodityIds.stream().collect(Collectors.toMap(e ->  e.getStallId() + "_" + e.getCommodityId(), Function.identity()));
            commodityIdList.forEach(
                    commodityId -> {
                        if(!commodityMap.containsKey(commodityId)){
                            QYAssert.isFalse("商品["+commodityId+"]，不存在,不允许调拨");
                        }

                        Commodity commodity = commodityMap.get(commodityId);

                        if (!shopStallCommodityListMap.containsKey(dto.getOutStallId() + "_" + commodityId)) {
                            QYAssert.isFalse("调出档口下无设置此商品"+commodity.getCommodityCode()+"，不允许调拨");
                        }

                        if (!shopStallCommodityListMap.containsKey(dto.getInStallId() + "_" + commodityId)) {
                            QYAssert.isFalse("调入档口下无设置此商品"+commodity.getCommodityCode()+"，不允许调拨");
                        }

                    }
            );

            //大店之间调拨：价格取移动平均价 取调出档口的移动平均价
            List<StallCommodity> outStallByCommodityIds = stallByCommodityIds.stream().filter(stallCommodity -> Objects.equals(stallCommodity.getStallId(),dto.getOutStallId())).collect(Collectors.toList());
            Map<Long, BigDecimal> weightMap = outStallByCommodityIds.stream().filter(stallCommodity -> Objects.nonNull(stallCommodity.getWeightPrice()))
                    .collect(toMap(StallCommodity::getCommodityId, StallCommodity::getWeightPrice));
            commodityIdList.forEach(
                    commodityId->{
                        if (!weightMap.containsKey(commodityId)) {
                            Commodity commodity = commodityMap.get(commodityId);
                            QYAssert.isFalse(commodity.getCommodityCode() + "无效的移动成本价");
                        }
                    }
            );

        }


        this.checkConsignmentSupplierCommodity(dto);
        //将dto转换为bo，设置是否大店，用于后续流程的判断，无需在后续代码里重复判断是否大店
        StockAllotOrderApplyBO stockAllotOrderApplyBO = BeanCloneUtils.copyTo(dto, StockAllotOrderApplyBO.class);
        stockAllotOrderApplyBO.setBigShop(isBigShop);
        stockAllotOrderApplyBO.setIsInstoreAllot(isInstoreAllot);
        return stockAllotOrderApplyBO;
    }


    /**
     * 查询是否有香烟代销商品
     *
     * @param dto
     */
    private void checkConsignmentSupplierCommodity(StockAllotOrderApplyDTO dto) {
        List<ConsignmentSupplierInfoODTO> consignmentCommodityList = consignmentSupplierClient.queryByShopId(dto.getShopId());
        if (SpringUtil.isNotEmpty(consignmentCommodityList)) {
            List<Long> consignmentCommodityIdList = consignmentCommodityList.stream()
                    .map(ConsignmentSupplierInfoODTO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Long> consignmentCommodityNameList = dto.getItems().stream()
                    .map(StockAllotOrderItemApplyDTO::getCommodityId)
                    .filter(consignmentCommodityIdList::contains)
                    .collect(Collectors.toList());
            QYAssert.isTrue(SpringUtil.isEmpty(consignmentCommodityNameList), "调拨存在代销商品");
        }
    }


    /**
     * 查询申请单详情
     */
    public StockAllotOrderDetailResult detailById(Long stockAllotId) {
        //1.申请单信息
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotId);
        QYAssert.isTrue(stockAllotOrder != null, "调拨申请单不存在");
        StockAllotOrderDetailResult result = new StockAllotOrderDetailResult();
        BeanUtils.copyProperties(stockAllotOrder, result);
        Shop inShop = shopMapper.selectById(stockAllotOrder.getInShopId());
        result.setInShopTxt(inShop != null ? inShop.getShopName() : "");
        Shop outShop = shopMapper.selectById(stockAllotOrder.getOutShopId());
        result.setOutShopTxt(outShop != null ? outShop.getShopName() : "");
        //2.商品信息
        List<StockAllotOrderDetailItemResult> list = stockAllotOrderItemMapper.selectAllotItemList(stockAllotId);
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> commodityIds = list.stream().map(StockAllotOrderDetailItemResult::getCommodityId).collect(Collectors.toList());
            //条形码
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
            for (StockAllotOrderDetailItemResult e : list) {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
            }
            result.setItems(list);
        }
        return result;
    }

    /**
     * 调拨审核
     * 审核时刻记录 调出成本价 调入进价
     * 审核不改数量
     * 调出方 冻结库存  xd xs都不管
     *
     * @param stockAllotId
     */
    @Transactional
    public void audit(Long stockAllotId) {
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotId);
        QYAssert.isTrue(stockAllotOrder != null, "无效调拨单");
        QYAssert.isTrue(StockAllotOrderStatusEnums.WAIT_AUDIT.getCode() == stockAllotOrder.getStatus(), "调拨单状态异常");
        List<Long> allocateWhiteList = shopAllocateWhiteClient.list();
        if (SpringUtil.isEmpty(allocateWhiteList) ||
                !(allocateWhiteList.contains(stockAllotOrder.getInShopId()) || allocateWhiteList.contains(stockAllotOrder.getOutShopId()))) {
            //调拨初库和入库门店是否是同一个部门
            Boolean isSameDepartment = orgRefClient.isTwoShopBelongToTheSameDepartment(stockAllotOrder.getInShopId(), stockAllotOrder.getOutShopId());
            QYAssert.isTrue(isSameDepartment, "不允许跨部门调拨，调出店和调入店必须属于同一个部门。");
        }

        Long outShopId = stockAllotOrder.getOutShopId();
        //判断调出门店是否为大店
        ShopDto outShop = shopClient.findShopById(outShopId);
        Boolean isBigShop = StallUtils.isBigShop(outShop.getManagementMode());

        // 同类型或者同组同部门
        Boolean isSameShopTypeOrGroup = isSameShopTypeOrGroup(stockAllotOrder.getInShopId(), stockAllotOrder.getOutShopId());

        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrderItem>()
                .eq(StockAllotOrderItem::getStockAllotId, stockAllotOrder.getId());
        List<StockAllotOrderItem> allotOrderItems = stockAllotOrderItemMapper.selectList(query);
        List<Long> idList = allotOrderItems.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        if(!Objects.equals(isBigShop,Boolean.TRUE)){
            //1调用价格接口获取成本价、进价 保存价格 审核那一刻的 调出取成本 调入取进价
            CommodityPriceListIDTO priceDto = new CommodityPriceListIDTO();
            priceDto.setShopId(stockAllotOrder.getOutShopId());
            priceDto.setAllotType(StockAllotTypeEnums.ALLOT_OUT.getCode());
            priceDto.setCommodityIdList(idList);
            Map<Long, BigDecimal> weightMap = xdShopCommodityPriceClient.getShopCommodityPrice(priceDto);
            priceDto.setShopId(stockAllotOrder.getInShopId());
            priceDto.setAllotType(StockAllotTypeEnums.ALLOT_IN.getCode());
            Map<Long, BigDecimal> priceMap = xdShopCommodityPriceClient.getShopCommodityPrice(priceDto);

            for (StockAllotOrderItem item : allotOrderItems) {
                if (weightMap.get(item.getCommodityId()) == null) {
                    Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                    throw new BizLogicException(commodity.getCommodityCode() + "无效的商品成本");
                }
                item.setWeightPrice(weightMap.get(item.getCommodityId()));

                // 同门店类型或者同分组同部门调入价格=调出门店成本价
                if(isSameShopTypeOrGroup) {
                    item.setCommodityPrice(weightMap.get(item.getCommodityId()));
                }else {
                    if (priceMap.get(item.getCommodityId()) == null) {
                        Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                        throw new BizLogicException(commodity.getCommodityCode() + "无效的商品进价");
                    }
                    item.setCommodityPrice(priceMap.get(item.getCommodityId()));
                }

                stockAllotOrderItemMapper.updateById(item);
            }

        }else{
            //大店之间调拨：价格取移动平均价 取调出档口的移动平均价
            List<StallCommodity> stallCommodityList = stallCommodityService.getStallByCommodityIds(stockAllotOrder.getOutShopId(), stockAllotOrder.getOutStallId(), idList);
            Map<Long, BigDecimal> weightMap = stallCommodityList.stream().filter(stallCommodity -> Objects.nonNull(stallCommodity.getWeightPrice()))
                    .collect(toMap(StallCommodity::getCommodityId, StallCommodity::getWeightPrice));
            for (StockAllotOrderItem item : allotOrderItems) {
                if (!weightMap.containsKey(item.getCommodityId())) {
                    Commodity commodity = commodityMapper.selectById(item.getCommodityId());
                    throw new BizLogicException(commodity.getCommodityCode() + "无效的移动成本价");
                }
                BigDecimal weightPrice = weightMap.get(item.getCommodityId());
                item.setWeightPrice(weightPrice);
                item.setCommodityPrice(weightPrice);
                stockAllotOrderItemMapper.updateById(item);
            }

        }


        //2调拨单 状态置为 审核
        stockAllotOrder.setStatus(StockAllotOrderStatusEnums.AUDIT_SUCCESS.getCode());
        stockAllotOrder.setAuditId(StockUtils.INSTANCE.userId());
        stockAllotOrder.setAuditTime(new Date());
        stockAllotOrderMapper.updateById(stockAllotOrder);

        //3判断调出方门店类型 xd门店需要冻结库存 -> 不冻结
    }

    /**
     * 获取实际商品列表 数量 份数
     *
     * @param allotOrderItems
     * @return
     */
    @NotNull
    private List<StockItemDTO> getOutStockItemList(List<StockAllotOrderItem> allotOrderItems) {
        List<StockItemDTO> stockItems = new ArrayList<>();
        for (StockAllotOrderItem item : allotOrderItems) {
            StockItemDTO dto = new StockItemDTO();
            dto.setCommodityId(item.getCommodityId());
            dto.setStockNumber(item.getOutNumber());
            dto.setQuantity(item.getOutQuantity());
            stockItems.add(dto);
        }
        return stockItems;
    }

    @Transactional
    public void auditRejected(Long stockAllotId) {
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotId);
        QYAssert.isTrue(stockAllotOrder != null, "无效调拨单");
        QYAssert.isTrue(StockAllotOrderStatusEnums.WAIT_AUDIT.getCode() == stockAllotOrder.getStatus(), "调拨单状态异常");

        stockAllotOrder.setStatus(StockAllotOrderStatusEnums.TURN_DOWN.getCode());
        stockAllotOrder.setAuditId(StockUtils.INSTANCE.userId());
        stockAllotOrder.setAuditTime(new Date());
        stockAllotOrderMapper.updateById(stockAllotOrder);
    }

    /**
     * 调拨取消
     *
     * @param stockAllotId
     */
    @Transactional
    public void allotCancel(Long stockAllotId) {
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotId);
        QYAssert.isTrue(stockAllotOrder != null, "无效调拨单");
        Boolean checkStatus = StockAllotOrderStatusEnums.WAIT_AUDIT.getCode() == stockAllotOrder.getStatus()
                || StockAllotOrderStatusEnums.AUDIT_SUCCESS.getCode() == stockAllotOrder.getStatus();
        QYAssert.isTrue(checkStatus, "调拨单状态异常");

        stockAllotOrder.setStatus(StockAllotOrderStatusEnums.CANCEL.getCode());
        stockAllotOrder.setUpdateId(StockUtils.INSTANCE.userId());
        stockAllotOrder.setUpdateTime(new Date());
        stockAllotOrderMapper.updateById(stockAllotOrder);
    }

    /**
     * 调拨出库
     * 实际出库多少 就出库多少
     * xd 要解冻库存 生成出库单
     *
     * @param stockAllotOutDTO
     */
    @Transactional
    public void allotOut(StockAllotOutDTO stockAllotOutDTO) {
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotOutDTO.getStockAllotId());
        QYAssert.isTrue(stockAllotOrder != null, "无效调拨单");
        QYAssert.isTrue(StockAllotOrderStatusEnums.AUDIT_SUCCESS.getCode() == stockAllotOrder.getStatus(), "调拨单状态异常");
        //1.调拨单出库
        stockAllotOrder.setStatus(StockAllotOrderStatusEnums.OUT_SUCCESS.getCode());
        stockAllotOrder.setOutPerson(StockUtils.INSTANCE.userId());
        stockAllotOrder.setOutTime(new Date());
        stockAllotOrderMapper.updateById(stockAllotOrder);

        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrderItem>()
                .eq(StockAllotOrderItem::getStockAllotId, stockAllotOrder.getId());
        List<StockAllotOrderItem> allotOrderItems = stockAllotOrderItemMapper.selectList(query);
        Map<Long, StockAllotOutItemDTO> itemMap = stockAllotOutDTO.getItems().parallelStream().collect(toMap(StockAllotOutItemDTO::getCommodityId, it -> it));
        for (StockAllotOrderItem item : allotOrderItems) {
            item.setOutQuantity(itemMap.get(item.getCommodityId()).getOutQuantity());
            Commodity commodity = commodityMapper.selectById(item.getCommodityId());
            //0<=称重商品出库数量<申请数量*2
            if (commodity.isWeight()) {
                if (item.getOutQuantity().compareTo((item.getApplyQuantity().multiply(new BigDecimal("2")))) > 0) {
                    throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "商品出库数量超出!");
                }
            } else {
                if (item.getOutQuantity().compareTo(item.getApplyQuantity()) > 0) {
                    throw new BizLogicException(commodity.getCommodityCode() + commodity.getCommodityName() + "商品出库数量超出!");
                }
            }
            stockAllotOrderItemMapper.updateById(item);
        }

        Pair pair = new ImmutablePair(stockAllotOrder.getId(), stockAllotOrder.getOrderCode());
        List<StockItemDTO> outItemList = stockAllotOutDTO.toStockItemList();

        boolean isBigShop = StallUtils.isBigShop(FastThreadLocalUtil.getQY().getManagementMode());
        if(isBigShop){
            //默认从排面区出库，减少排面区的库存
            outItemList.forEach(
                    outItem->{
                        outItem.setDdStockInOutExtraVO(
                                DdStockInOutExtraVO
                                        .buildShelfAreaDdStockInOutExtraVO(outItem.getCommodityId(), stockAllotOrder.getOutStallId())
                        );
                    }
            );
        }

        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, StockInOutTypeEnums.OUT_ALLOT_NORMAL, outItemList
                , stockAllotOrder.getOutShopId(), StockUtils.INSTANCE.userId());
        stockServiceAdapter.stockInOut(stockInOutVO);

    }

    /**
     * 鲜食门店调拨出库前校验库存数量
     */
    private void checkStockOnOut(StockAllotOrder stockAllotOrder, List<StockAllotOutItemDTO> items) {
        ShopCommodityInfoIDTO scid = new ShopCommodityInfoIDTO();
        scid.setShopId(stockAllotOrder.getOutShopId());
        List<Long> commodityIdList = items.stream().mapToLong(StockAllotOutItemDTO::getCommodityId).boxed().collect(Collectors.toList());
        scid.setCommodityIdList(commodityIdList);
        Map<Long, ShopCommodityInfoODTO> shopCommodityMap = shopCommodityClient.getShopCommodityList(scid);
        items.stream().forEach(it -> {
            Long commodityId = it.getCommodityId();
            ShopCommodityInfoODTO v = shopCommodityMap.get(commodityId);
            BigDecimal stockQuantityOfDB = v.getStockQuantity();
            BigDecimal outQuantityOfReq = it.getOutQuantity();
            QYAssert.isTrue(outQuantityOfReq != null && outQuantityOfReq.compareTo(stockQuantityOfDB) <= 0, "商品调拨出库库存不足");
        });
    }


    @NotNull
    private ShopStockIDto getXsStockDto(List<StockAllotOrderItem> outItems, Long shopId, String code, int allotType) {
        ShopStockIDto shopStock = new ShopStockIDto();
        Shop shop = shopMapper.selectById(shopId);
        shopStock.setStoreId(shop.getStoreId());
        shopStock.setUserId(StockUtils.INSTANCE.userId());
        shopStock.setType(XsStockTypeEnums.SHOP_STOCK_ALLOT.getCode());
        shopStock.setReferCode(code);
        List<ShopStockCommodityIDto> outItemList = new ArrayList<>();

        for (StockAllotOrderItem item : outItems) {
            ShopStockCommodityIDto dto = new ShopStockCommodityIDto();
            dto.setCommodityId(item.getCommodityId());
            if (StockAllotTypeEnums.ALLOT_OUT.getCode() == allotType) {
                dto.setModifyQuantity(BigDecimal.ZERO.subtract(item.getOutQuantity()));
            } else {
                dto.setModifyQuantity(item.getOutQuantity());
            }
            outItemList.add(dto);
        }
        shopStock.setList(outItemList);
        return shopStock;
    }

    /**
     * 调拨入库
     * 实际出库多少 就入库多少
     * xd 生成入库单
     *
     * @param stockAllotId
     */
    @Transactional
    public void allotIn(Long stockAllotId) {
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectById(stockAllotId);
        QYAssert.isTrue(stockAllotOrder != null, "无效调拨单");
        QYAssert.isTrue(StockAllotOrderStatusEnums.OUT_SUCCESS.getCode() == stockAllotOrder.getStatus(), "调拨单状态异常");
        //1.调拨单入库
        stockAllotOrder.setStatus(StockAllotOrderStatusEnums.IN_SUCCESS.getCode());
        //-1表示系统
        Long userId = (1 == StockUtils.INSTANCE.userId()) ? -1 : StockUtils.INSTANCE.userId();

        stockAllotOrder.setInPerson(userId);

        stockAllotOrder.setInTime(new Date());
        stockAllotOrderMapper.updateById(stockAllotOrder);

        List<ShopStockReceiptItemIDTO> commodityList = new ArrayList<>();
        ShopStockReceiptItemIDTO stockReceiptItem = null;

        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrderItem>()
                .eq(StockAllotOrderItem::getStockAllotId, stockAllotId);
        List<StockAllotOrderItem> allotOrderItems = stockAllotOrderItemMapper.selectList(query);

        List<Long> commodityIds = allotOrderItems.stream().map(StockAllotOrderItem::getCommodityId).collect(Collectors.toList());
        List<ShopStockDTO> finishedStockList = shopCommodityService.queryShopStock(stockAllotOrder.getInShopId(), commodityIds);
        Map<Long, BigDecimal> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

        for (StockAllotOrderItem item : allotOrderItems) {
            item.setInNumber(item.getOutNumber());
            item.setInQuantity(item.getOutQuantity());
            stockAllotOrderItemMapper.updateById(item);

            stockReceiptItem = new ShopStockReceiptItemIDTO();
            stockReceiptItem.setCommodityId(item.getCommodityId());
            stockReceiptItem.setQuantity(item.getInQuantity());
            stockReceiptItem.setPrice(item.getCommodityPrice());
            stockReceiptItem.setTotalPrice(stockReceiptItem.getQuantity().multiply(stockReceiptItem.getPrice()).setScale(3, BigDecimal.ROUND_HALF_UP));
            stockReceiptItem.setExistStockQuantity(stockCollect.get(item.getCommodityId()));
            commodityList.add(stockReceiptItem);
        }

        //2查询实际出库
        List<StockItemDTO> inItemList = getOutStockItemList(allotOrderItems);
        boolean isBigShop = StallUtils.isBigShop(FastThreadLocalUtil.getQY().getManagementMode());
        if(isBigShop){
            //默认从排面区入库，增加排面区的库存
            inItemList.forEach(
                    inItem->{
                        inItem.setDdStockInOutExtraVO(
                                DdStockInOutExtraVO
                                        .buildShelfAreaDdStockInOutExtraVO(inItem.getCommodityId(), stockAllotOrder.getInStallId())
                        );
                    }
            );
        }

        //3生成入库单（xd xs）
        Pair pair = new ImmutablePair(stockAllotOrder.getId(), stockAllotOrder.getOrderCode());

        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, StockInOutTypeEnums.IN_ALLOT_NORMAL, inItemList, stockAllotOrder.getInShopId(), userId);
        stockServiceAdapter.stockInOut(stockInOutVO);

        ShopStockReceiptIDTO shopStockReceiptIDTO = new ShopStockReceiptIDTO();
        shopStockReceiptIDTO.setShopId(stockAllotOrder.getInShopId());
        shopStockReceiptIDTO.setStallId(stockAllotOrder.getInStallId());
        shopStockReceiptIDTO.setCommodityList(commodityList);
        try {
            shopStockClient.stockReceipt(shopStockReceiptIDTO);
        } catch (Exception e) {
            log.error("调拨出库移动平均价维护失败：" + e);
        }

        // 店内调拨不发送结算消息
        if(!YesOrNoEnums.YES.getCode().equals(stockAllotOrder.getIsInstoreAllot())) {
            //4结算发消息
            sendMessageAllotIn(stockAllotOrder.getOrderCode());
            sendMessageAllotOut(stockAllotOrder.getOrderCode());
        }
    }

    /**
     * 跑JOB 查询状态=30即出库完成的单子，超过48小时的。然后自动给他入库
     *
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Boolean jobAllotIn() {
        Date date = DateUtil.addDay(new Date(), -2);
        Date beginDate = DateUtil.addDay(new Date(), -7);

        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrder>()
                .eq(StockAllotOrder::getStatus, StockAllotOrderStatusEnums.OUT_SUCCESS.getCode())
                .lt(StockAllotOrder::getOutTime, date)
                .ge(StockAllotOrder::getOutTime, beginDate);
        List<StockAllotOrder> allotOrders = stockAllotOrderMapper.selectList(query);
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        if (null != allotOrders && allotOrders.size() > 0) {
            for (StockAllotOrder order : allotOrders) {
                TransactionStatus status = transactionManager.getTransaction(def);
                try {
                    allotIn(order.getId());
                    transactionManager.commit(status);
                } catch (Exception e) {
                    log.error("自动调拨入库，单号{}失败,{}", order.getOrderCode(), e.getMessage());
                    transactionManager.rollback(status);
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 入库申请单列表
     *
     * @param dto
     * @return
     */
    public MPage<StockAllotOrderListDTO> allotInList(StockAllotOrderQueryDTO dto) {
        dto.check();
        return stockAllotOrderMapper.allotInList(dto,stallAuthorityCheckService.getUserStallIdList(dto.getUserId(),dto.getInShopId()));
    }

    /**
     * 出库申请单列表
     *
     * @param dto
     * @return
     */
    public MPage<StockAllotOrderListDTO> allotOutListPda(StockAllotOrderQueryDTO dto) {
        dto.check();
        return stockAllotOrderMapper.allotOutList(dto,stallAuthorityCheckService.getUserStallIdList(dto.getUserId(),dto.getOutShopId()));
    }

    /**
     * 总部：出入调拨单列表
     *
     * @param dto
     * @return
     */
    public MPage<StockAllotOrderListDTO> allotList(StockAllotOrderQueryDTO dto) {
        dto.check();
        return stockAllotOrderMapper.allotList(dto);
    }

    /**
     * 单门店，出入调拨单列表
     *
     * @param dto
     * @return
     */
    public MPage<StockAllotOrderListDTO> singleAllotList(StockAllotSingleQueryDTO dto) {
        return stockAllotOrderMapper.singleAllotList(dto);
    }

    /**
     * 调拨出库完成发消息
     *
     * @param orderCode
     */
    public void sendMessageAllotOut(String orderCode) {
        QYAssert.isTrue(orderCode != null, "sourceCode is not null !");
        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrder>()
                .eq(StockAllotOrder::getOrderCode, orderCode);
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectOne(query);
        if (null == stockAllotOrder) {
            return;
        }
        LambdaQueryWrapper queryItem = new LambdaQueryWrapper<StockAllotOrderItem>()
                .eq(StockAllotOrderItem::getStockAllotId, stockAllotOrder.getId());
        List<StockAllotOrderItem> stockAllotOrderItems = stockAllotOrderItemMapper.selectList(queryItem);
        List<AllotItemMessage> items = new ArrayList<>();
        AllotItemMessage allotItemMessage = null;
        Long storeId = getStoreId(stockAllotOrder.getOutShopId());
        for (StockAllotOrderItem e : stockAllotOrderItems) {
            allotItemMessage = new AllotItemMessage();
            allotItemMessage.setItemId(e.getId());
            allotItemMessage.setCommodityId(e.getCommodityId());
            allotItemMessage.setDeliveryNumber(e.getOutQuantity());
            BigDecimal deliveryTotalPrice = e.getOutQuantity().multiply(e.getWeightPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            allotItemMessage.setDeliveryTotalPrice(deliveryTotalPrice);
            allotItemMessage.setDeliveryUnitPrice(e.getWeightPrice());
            allotItemMessage.setNumber(e.getApplyQuantity());
            allotItemMessage.setSourceId(stockAllotOrder.getId().toString());
            BigDecimal totalPrice = e.getApplyQuantity().multiply(e.getWeightPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            allotItemMessage.setTotalPrice(totalPrice);
            allotItemMessage.setUnitPrice(e.getWeightPrice());
            items.add(allotItemMessage);
        }

        AllotMessage allotMessage = new AllotMessage();
        allotMessage.setCreateTime(stockAllotOrder.getCreateTime());
        allotMessage.setDeliveryTime(stockAllotOrder.getInTime());
        allotMessage.setDeliveryTotalAmount(items.stream().map(AllotItemMessage::getDeliveryTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        allotMessage.setItems(items);
        allotMessage.setOrderTime(stockAllotOrder.getOutTime());
        allotMessage.setSendMqTime(DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT));
        allotMessage.setSourceCode(stockAllotOrder.getOrderCode());
        allotMessage.setSourceId(stockAllotOrder.getId());
        allotMessage.setSourceType(KafkaMessageTypeEnum.RT_ADJUST.name());
        allotMessage.setStoreId(storeId);
        // allotMessage.setStockOutOrderCode();
        allotMessage.setTotalAmount(items.stream().map(AllotItemMessage::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        List<AllotMessage> sendData = new ArrayList<>();
        sendData.add(allotMessage);

        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.SETTLE_ORDER_ALL_TOPIC.getTopic(),
                sendData, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SETTLE_ORDER_SYNC.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }

    /**
     * 调拨入库收货完成发消息
     *
     * @param orderCode
     */
    public void sendMessageAllotIn(String orderCode) {
        QYAssert.isTrue(!StringUtils.isEmpty(orderCode), "sourceCode is not null !");

        LambdaQueryWrapper query = new LambdaQueryWrapper<StockAllotOrder>()
                .eq(StockAllotOrder::getOrderCode, orderCode);
        StockAllotOrder stockAllotOrder = stockAllotOrderMapper.selectOne(query);
        if (null == stockAllotOrder) {
            return;
        }
        LambdaQueryWrapper queryItem = new LambdaQueryWrapper<StockAllotOrderItem>()
                .eq(StockAllotOrderItem::getStockAllotId, stockAllotOrder.getId());
        List<StockAllotOrderItem> stockAllotOrderItems = stockAllotOrderItemMapper.selectList(queryItem);

        Long storeId = getStoreId(stockAllotOrder.getInShopId());
        List<AllotItemMessage> items = new ArrayList<>();
        AllotItemMessage allotItemMessage = null;
        for (StockAllotOrderItem e : stockAllotOrderItems) {
            allotItemMessage = new AllotItemMessage();
            allotItemMessage.setItemId(e.getId());
            allotItemMessage.setCommodityId(e.getCommodityId());
            allotItemMessage.setDeliveryNumber(e.getOutQuantity());
            BigDecimal deliveryTotalPrice = e.getInQuantity().multiply(e.getCommodityPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            allotItemMessage.setDeliveryTotalPrice(deliveryTotalPrice);
            allotItemMessage.setDeliveryUnitPrice(e.getCommodityPrice());
            allotItemMessage.setNumber(e.getApplyQuantity());
            allotItemMessage.setSourceId(stockAllotOrder.getId().toString());
            BigDecimal totalPrice = e.getApplyQuantity().multiply(e.getCommodityPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            allotItemMessage.setTotalPrice(totalPrice);
            allotItemMessage.setUnitPrice(e.getCommodityPrice());
            items.add(allotItemMessage);
        }

        AllotMessage allotMessage = new AllotMessage();
        allotMessage.setCreateTime(stockAllotOrder.getCreateTime());
        allotMessage.setDeliveryTime(stockAllotOrder.getInTime());
        allotMessage.setDeliveryTotalAmount(items.stream().map(AllotItemMessage::getDeliveryTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        allotMessage.setItems(items);
        allotMessage.setOrderTime(stockAllotOrder.getInTime());
        allotMessage.setSendMqTime(DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT));
        allotMessage.setSourceCode(stockAllotOrder.getOrderCode());
        allotMessage.setSourceId(stockAllotOrder.getId());
        allotMessage.setSourceType(KafkaMessageTypeEnum.ADJUST.name());
        allotMessage.setStoreId(storeId);
        // allotMessage.setStockOutOrderCode();
        allotMessage.setTotalAmount(items.stream().map(AllotItemMessage::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));

        List<AllotMessage> sendData = new ArrayList<>();
        sendData.add(allotMessage);

        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.SETTLE_ORDER_ALL_TOPIC.getTopic(),
                sendData, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SETTLE_ORDER_SYNC.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());

    }

    private Long getStoreId(Long shopId) {
        Shop shop = shopMapper.selectById(shopId);
        if (null != shop) {
            return shop.getStoreId();
        }
        return null;
    }

    public List<OrderSettleODTO> findInSettleOrderListByStoreIds(OrderSettleIDTO orderSettleIDTO) {
        return stockAllotOrderMapper.findInSettleOrderListByStoreIds(orderSettleIDTO);
    }

    public List<OrderSettleODTO> findOutSettleOrderListByStoreIds(OrderSettleIDTO orderSettleIDTO) {
        return stockAllotOrderMapper.findOutSettleOrderListByStoreIds(orderSettleIDTO);
    }

    public List<OrderSettleItemODTO> findListByStockAllotIds(List<Long> stockAllotIds) {
        return stockAllotOrderItemMapper.findListByStockAllotIds(stockAllotIds);
    }


    /**
     * to jifeng
     * 调拨单据支持
     *
     * @param dto
     * @return
     */
    public PageInfo<StockAllotOrderSupportDTO> stockAllotOrderSupportPage(StockAllotOrderSupportIDTO dto) {
        PageInfo<StockAllotOrderSupportDTO> pageInfo = PageHelper.startPage((int) dto.getPageNo(), (int) dto.getPageSize()).doSelectPageInfo(() -> {
            stockAllotOrderMapper.stockAllotOrderSupportPage(dto);
        });
        return pageInfo;
    }

}
