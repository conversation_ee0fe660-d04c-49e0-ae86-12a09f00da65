package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.shop.dto.stock.SelectWeightPriceListIDTO;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceODTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageODTO;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockSnapshotCommodityTotalMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockSnapshotCommodityWeeklyMapper;
import com.pinshang.qingyun.xd.wms.model.StockSnapshotCommodityTotal;
import com.pinshang.qingyun.xd.wms.model.StockSnapshotCommodityWeekly;
import com.pinshang.qingyun.xd.wms.vo.StockChangeInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @since 2022-05-11
 * <AUTHOR> yaot0
 */
@Service
@Slf4j
public class StockLogSnapshotService {
    @Autowired
    private StockLogMapper stockLogMapper;

    @Autowired
    private StockSnapshotCommodityWeeklyMapper stockSnapshotCommodityWeeklyMapper;

    @Autowired
    private StockSnapshotCommodityTotalMapper stockSnapshotCommodityTotalMapper;

    @Autowired
    private ShopStockClient shopStockClient;


    @Autowired
    private StockSnapshotTransactionService stockSnapshotTransactionService;
    /**
     * 3*4周执行一次, 生成商品总表
     */
    @Async
    public void generateTotalSchedule(){
        // 判断上次执行时间是否3*4周之前
        Date lastDate = stockSnapshotCommodityTotalMapper.selectLastCreateTime();
        if(null != lastDate && this.compareTotalCreateTime(lastDate)){
            log.info("还未到执行时间, 上次执行总表快照时间:"+DateUtil.get4yMdHm(lastDate));
            return;
        }
        List<Long> shopList = stockSnapshotCommodityTotalMapper.selectStockShopIdList();
        if(SpringUtil.isEmpty(shopList)){
            return;
        }

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 20, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopList.size())
        );

        for(Long shopId: shopList){

            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        stockSnapshotTransactionService.insertTotal(shopId);
                    }
                });
            } catch (Exception e) {
                log.error("快照总表生成异常，shopId={}",shopId,e);
            }
        }
    }

    /**
     * 每周生成快照
     */
    @Async
    public void generateSnapshotWeekly(){
        // 1. 查询所有门店
        List<Long> shopList = stockSnapshotCommodityTotalMapper.selectStockShopIdList();
        if(SpringUtil.isEmpty(shopList)){
            return;
        }
        Date today = getTodayZero();
        String endTime = DateUtil.get4yMdHms(today);
        String beginTime =  DateUtil.get4yMdHms(DateUtil.addDay(today, -84));

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 20, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopList.size())
        );

        // 按门店单独执行
        for(Long shopId: shopList){
            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        stockSnapshotTransactionService.insertWeekly(endTime, beginTime, shopId);
                    }
                });
            } catch (Exception e) {
                log.error("快照总表生成异常，shopId={}",shopId,e);
            }
        }

    }

    private  List<StockSnapshotCommodityWeekly> getChangeInfoForPage(StockSnapshotPageIDTO idto, Date snapshotDate,  List<Long> activeCommodityIdList){
        // 获取变动库存份数(至查询当日0点为止)
        String changeBeginTime = DateUtil.get4yMdHms(snapshotDate);
        // 快照日期在查询日期后面, 则反向查询并且将值取负数
        if(snapshotDate.after(DateUtil.parseDate(idto.getDate(), "yyyy-MM-dd"))){
            List<StockSnapshotCommodityWeekly> changeInfoList = stockSnapshotCommodityWeeklyMapper.getCommodityNumberForPage(idto.getDate() + " 00:00:00", changeBeginTime, activeCommodityIdList, idto);
            changeInfoList = changeInfoList.parallelStream().peek(it -> {
                it.setQuantity(BigDecimal.ZERO.subtract(it.getQuantity()));
                it.setNumber(-it.getNumber());
            }).collect(Collectors.toList());
            return changeInfoList;
        }else{
            return stockSnapshotCommodityWeeklyMapper.getCommodityNumberForPage(changeBeginTime,  idto.getDate()+" 00:00:00",activeCommodityIdList, idto);
        }
    }


    /**
     * 页面查询
     * 查出门店当日下所有数据, 内存中进行分组
     */
    public TablePageInfo<StockSnapshotPageODTO> page(StockSnapshotPageIDTO idto){
        // 查询日期往后一天
        Date date = DateUtil.parseDate(idto.getDate(), "yyyy-MM-dd");
        Date nextDate = DateUtil.addDay(date, 1);
        idto.setDate(DateUtil.get4yMd(nextDate));

        QYAssert.isTrue(StringUtils.isNotBlank(idto.getDate()) && null !=  idto.getShopId(), "库存查询时间和门店id不能为空");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date searchDate = null;
        try {
            searchDate = sdf.parse(idto.getDate());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        // 查询活跃库存(活跃商品必定有库存变动记录, 则必定有成本价变动记录, 成本价日志表首次运行会初始化活跃商品)
        Map<Long, BigDecimal> weightPrice = getActiveWeightPrice(searchDate, idto.getShopId());

        // 查询周快照商品(包括当天0点时间的值)
        List<StockSnapshotPageODTO>  lastRecord = getActiveInfo(idto, searchDate);
        List<StockSnapshotPageODTO> notActiveCommodityList;
        List<StockSnapshotCommodityWeekly> changeInfo;
        if(SpringUtil.isNotEmpty(lastRecord)) {
            Date lastRecordDate = lastRecord.get(0).getShotDate();
            boolean needFilterLastRecord = false;
            List<StockSnapshotPageODTO> lastRecordFiltered = new ArrayList<>(lastRecord.size());

            for(StockSnapshotPageODTO lastRecordItem:lastRecord){
                // 判断周表中是否有多次记录, 发现有日期大于当前记录的周表日期, 则清空之前的列表
                // 后面只保存最大日期的记录
                if(lastRecordItem.getShotDate().after(lastRecordDate)){
                    lastRecordDate  = lastRecordItem.getShotDate();
                    needFilterLastRecord = true;
                    lastRecordFiltered = new ArrayList<>(lastRecord.size());
                    lastRecordFiltered.add(lastRecordItem);
                }else if(lastRecordItem.getShotDate().equals(lastRecordDate)){
                    lastRecordFiltered.add(lastRecordItem);
                }
            }
            if(needFilterLastRecord){
                lastRecord = lastRecordFiltered;
            }

            List<Long> activeCommodityIdList = lastRecord.stream().map(StockSnapshotListForStockAndPriceODTO::getCommodityId).collect(Collectors.toList());
            changeInfo = getChangeInfoForPage(idto, lastRecord.get(0).getShotDate(), null);
            List<StockSnapshotCommodityWeekly> weekChangeInfo = changeInfo.stream().filter(it -> activeCommodityIdList.contains(it.getCommodityId())).collect(Collectors.toList());
            setLastRecord(lastRecord, searchDate, weekChangeInfo, weightPrice);

            // 周快照中不存在的商品拿总表, 并计算总价, 拼接到活跃商品后面
            notActiveCommodityList = getNoActiveInfo(activeCommodityIdList, idto, searchDate);
            if(SpringUtil.isNotEmpty(notActiveCommodityList)){
                changeInfo = getChangeInfoForPage(idto, notActiveCommodityList.get(0).getShotDate(), null);
            }
            // 周快照中不存在的商品拿总表, 并计算总价, 拼接到活跃商品后面
            changeInfo = changeInfo.stream().filter(it -> !activeCommodityIdList.contains(it.getCommodityId())).collect(Collectors.toList());
        }else{
            notActiveCommodityList = getNoActiveInfo(null, idto, searchDate);
            if (SpringUtil.isEmpty(notActiveCommodityList)) {
                return new TablePageInfo<>();
            }
            changeInfo = getChangeInfoForPage(idto, notActiveCommodityList.get(0).getShotDate(), null);
        }

        if (SpringUtil.isNotEmpty(notActiveCommodityList)) {
            setNotActiveCommodityList(notActiveCommodityList);
            lastRecord.addAll(notActiveCommodityList);
        }

        // 处理在week表中不存在的商品
        if(SpringUtil.isNotEmpty(changeInfo)){
            Map<Long, StockSnapshotPageODTO> lastRecordIdMap = lastRecord.stream().collect(Collectors.toMap(StockSnapshotListForStockAndPriceODTO::getCommodityId, it -> it));
            for(StockSnapshotCommodityWeekly it : changeInfo){
                if (lastRecordIdMap.containsKey(it.getCommodityId())){
                    StockSnapshotPageODTO changeRecord = lastRecordIdMap.get(it.getCommodityId());
                    changeRecord.setNumber(changeRecord.getNumber() + it.getNumber());
                    changeRecord.setQuantity(changeRecord.getQuantity().add(it.getQuantity()));
                    changeRecord.setWeightPrice(weightPrice.get(it.getCommodityId()));
                    changeRecord.setTotalPrice(getTotalPrice(changeRecord.getWeightPrice(), changeRecord.getQuantity()));
                }else{
                    StockSnapshotPageODTO changeRecord = stockSnapshotCommodityWeeklyMapper.selectCommodityInfoById(it.getCommodityId());
                    changeRecord.setCommodityId(it.getCommodityId());
                    changeRecord.setNumber(it.getNumber());
                    changeRecord.setQuantity(it.getQuantity());
                    changeRecord.setWeightPrice(weightPrice.get(it.getCommodityId()));
                    changeRecord.setTotalPrice(getTotalPrice(changeRecord.getWeightPrice(), changeRecord.getQuantity()));
                    lastRecordIdMap.put(it.getCommodityId(), changeRecord);
                }
            }
            lastRecord = lastRecordIdMap.entrySet().stream().map(it -> it.getValue()).collect(Collectors.toList());
        }

        // 统一处理没有成本价的商品(大部分情况商品不会有成本价变动, 取81天内成本价会无法取到, 此类商品改为从总表拿)
        this.handleWeightPrice(lastRecord, idto.getShopId());

        // 对拼接的数据 判断成本价成本金额的大小筛选 组装字符串类型的值 按商品编码排升序
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        lastRecord = lastRecord.stream().peek(it -> {
            if(null == it.getWeightPrice()){
                it.setWeightPrice(BigDecimal.ZERO);
            }
            if(null == it.getTotalPrice()){
                it.setTotalPrice(BigDecimal.ZERO);
            }
            it.setWeightPriceStr( decimalFormat.format(it.getWeightPrice().setScale(2, BigDecimal.ROUND_HALF_UP)));
            it.setTotalPriceStr( decimalFormat.format(it.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP)));
            if(YesOrNoEnums.YES.getCode().equals(idto.getStockStatus()) && it.getQuantity().compareTo(BigDecimal.ZERO) < 0){
                it.setTotalPrice(BigDecimal.ZERO);
                it.setTotalPriceStr("0.00");
            }
        }).filter(it ->
                !((null != idto.getWeightPrice() && it.getWeightPrice().compareTo(idto.getWeightPrice()) < 0)
                        || (null != idto.getTotalPrice() && it.getTotalPrice().compareTo(idto.getTotalPrice()) < 0))
        ).sorted(Comparator.comparing(StockSnapshotPageODTO::getCommodityCode)).collect(Collectors.toList());
        if(SpringUtil.isEmpty(lastRecord)){
            return new TablePageInfo<>();
        }
        BigDecimal sumPrice = lastRecord.stream().map(StockSnapshotPageODTO::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分页数据装配
        TablePageInfo<StockSnapshotPageODTO> pageInfo = this.buildPageInfo(lastRecord, idto.getPageNo(), idto.getPageSize(), idto.getStockStatus());
        pageInfo.setHeader(sumPrice);
        return pageInfo;
    }

    /**
     * snapshotDate日期必须小于当日, 跑0点的库存 yyyy-MM-dd 00:00:00
     * @param snapshotDate
     */
    public void insertHistoryWeeklyRecord(String snapshotDate){
        List<Long> shopList = stockSnapshotCommodityTotalMapper.selectStockShopIdList();
        if(SpringUtil.isEmpty(shopList)){
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date searchDate = null;
        try {
            searchDate = sdf.parse(snapshotDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        // 前一天0点, 查询大于前一天84点, 小于等于当天0点的数据
        Date lastDate = DateUtil.addDay(searchDate, -84);

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 20, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopList.size())
        );
        for(Long shopId: shopList){
            try {
                // 创建任务并提交到线程池中
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        stockSnapshotTransactionService.insertHistoryWeekly(snapshotDate + " 00:00:00",  DateUtil.get4yMdHms(lastDate), shopId);
                    }
                });
            } catch (Exception e) {
                log.error("快照周表生成异常，shopId={}",shopId,e);
            }
        }

    }

    private  TablePageInfo<StockSnapshotPageODTO> buildPageInfo(List<StockSnapshotPageODTO>  list , Integer pageNum, Integer pageSize, Integer stockStatus){
        pageNum = (null == pageNum || pageNum.intValue() < 1)? 1: pageNum;
        pageSize = (null == pageSize || pageSize.intValue() < 1)? 10: pageSize;
        TablePageInfo<StockSnapshotPageODTO> pageInfo = new TablePageInfo<>();
        if (SpringUtil.isEmpty(list)) {
            pageInfo.setList(Collections.emptyList());
            return pageInfo;
        }
        int total = list.size();
        int fromIndex = (pageNum - 1) * pageSize;
        if (fromIndex > total - 1) {
            pageInfo.setList(Collections.emptyList());
            return pageInfo;
        }

        int toIndex = fromIndex + pageSize;
        if (toIndex > total) {
            toIndex =total;
        }

        int quotient = total / pageSize;
        int remainder = total % pageSize;
        int pages = quotient + (remainder > 0? 1: 0);

        List<StockSnapshotPageODTO> newList = list.subList(fromIndex, toIndex);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSize(toIndex - fromIndex);
        pageInfo.setStartRow(fromIndex + 1);
        pageInfo.setEndRow(toIndex);
        pageInfo.setPages(pages);
        pageInfo.setPrePage(pageNum - 1);
        pageInfo.setNextPage(pageNum == pages? 0: pageNum + 1);
        pageInfo.setIsFirstPage(pageNum == 1);
        pageInfo.setIsLastPage(pageNum == pages)    ;
        pageInfo.setHasPreviousPage(pageNum > 1);
        pageInfo.setHasNextPage(pageNum < pages);

        pageInfo.setNavigatePages(-1);
        pageInfo.setNavigatepageNums(null);
        pageInfo.setNavigateFirstPage(-1);
        pageInfo.setNavigateLastPage(-1);
        pageInfo.setList(newList);
        return pageInfo;

    }


    private List<StockSnapshotPageODTO> getActiveInfo(StockSnapshotPageIDTO idto, Date searchDate){
        // 查询日期往前7天的0点
        Date ahead7Day = DateUtil.addDay(searchDate, -7);
        String ahead7Time = DateUtil.get4yMdHms(ahead7Day);
        return stockSnapshotCommodityWeeklyMapper.selectLastRecordList(idto,  idto.getDate()+" 00:00:00", ahead7Time);
    }

    private List<StockSnapshotPageODTO> getNoActiveInfo(List<Long> activeCommodityIdList, StockSnapshotPageIDTO idto, Date searchDate){
        // 查询日期往前7天的0点
        Date ahead7Day = DateUtil.addDay(searchDate, -84);
        String ahead84Time = DateUtil.get4yMdHms(ahead7Day);
        List<StockSnapshotPageODTO> stockSnapshotCommodityTotalList = stockSnapshotCommodityTotalMapper.selectNotActiveStock(idto.getShopId(), activeCommodityIdList, idto, idto.getDate() + " 23:59:59", ahead84Time);
        if(SpringUtil.isEmpty(stockSnapshotCommodityTotalList)){
            List<StockSnapshotCommodityTotal> firstTime = stockSnapshotCommodityTotalMapper.selectList(new LambdaQueryWrapper<StockSnapshotCommodityTotal>()
                    .select(StockSnapshotCommodityTotal::getCreateTime).orderByAsc(StockSnapshotCommodityTotal::getId).last("limit 1"));
            if(null != firstTime && null != firstTime.get(0)) {
                String firstDateStr = DateUtil.get4yMd(firstTime.get(0).getCreateTime());
                stockSnapshotCommodityTotalList = stockSnapshotCommodityTotalMapper.selectNotActiveStock(idto.getShopId(), activeCommodityIdList, idto, firstDateStr + " 23:59:59", firstDateStr + " 00:00:00");
            }
        }
        return stockSnapshotCommodityTotalList;
    }

    /**
     * 根据门店id和日期查询库存和成本价 总金额
     * @param idto
     * @return
     */
    public List<StockSnapshotListForStockAndPriceODTO> listForStockAndPrice(StockSnapshotListForStockAndPriceIDTO idto){
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getDate()) && null !=  idto.getShopId(), "库存查询时间和门店id不能为空");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date searchDate;
        try {
            searchDate = sdf.parse(idto.getDate());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        // 查询活跃库存(活跃商品必定有库存变动记录, 则必定有成本价变动记录, 成本价日志表首次运行会初始化活跃商品)
        Map<Long, BigDecimal> weightPrice = getActiveWeightPrice(searchDate, idto.getShopId());

        // 查询周快照商品
        List<StockSnapshotListForStockAndPriceODTO>  lastRecord = getActiveInfoForList(idto, searchDate);

        List<StockSnapshotListForStockAndPriceODTO> notActiveCommodityList;
        List<StockSnapshotCommodityWeekly> changeInfo;
        if( SpringUtil.isNotEmpty(lastRecord)){
            Date lastRecordDate = lastRecord.get(0).getShotDate();
            boolean needFilterLastRecord = false;
            List<StockSnapshotListForStockAndPriceODTO> lastRecordFiltered = new ArrayList<>(lastRecord.size());

            for(StockSnapshotListForStockAndPriceODTO lastRecordItem:lastRecord){
                // 判断周表中是否有多次记录, 发现有日期大于当前记录的周表日期, 则清空之前的列表
                // 后面只保存最大日期的记录
                if(lastRecordItem.getShotDate().after(lastRecordDate)){
                    lastRecordDate  = lastRecordItem.getShotDate();
                    needFilterLastRecord = true;
                    lastRecordFiltered = new ArrayList<>(lastRecord.size());
                    lastRecordFiltered.add(lastRecordItem);
                }else if(lastRecordItem.getShotDate().equals(lastRecordDate)){
                    lastRecordFiltered.add(lastRecordItem);
                }
            }
            if(needFilterLastRecord){
                lastRecord = lastRecordFiltered;
            }

            List<Long> activeCommodityIdList = lastRecord.stream().map(StockSnapshotListForStockAndPriceODTO::getCommodityId).collect(Collectors.toList());
            changeInfo = getChangeInfo(idto, lastRecord.get(0).getShotDate(), null);
            List<StockSnapshotCommodityWeekly> weekChangeInfo = changeInfo.stream().filter(it -> activeCommodityIdList.contains(it.getCommodityId())).collect(Collectors.toList());
            setLastRecord(lastRecord, searchDate, weekChangeInfo, weightPrice);

            // 周快照中不存在的商品拿总表, 并计算总价, 拼接到活跃商品后面
            notActiveCommodityList = getNoActiveInfoForList(activeCommodityIdList, idto.getShopId(), searchDate, idto.getDate());
            // 非活跃商品的库存变更数据
            if(SpringUtil.isNotEmpty(notActiveCommodityList)){
                changeInfo = getChangeInfo(idto, notActiveCommodityList.get(0).getShotDate(), null);
            }
            changeInfo = changeInfo.stream().filter(it -> !activeCommodityIdList.contains(it.getCommodityId())).collect(Collectors.toList());
        }else{
            notActiveCommodityList = getNoActiveInfoForList(null, idto.getShopId(), searchDate, idto.getDate());
            if(SpringUtil.isEmpty(notActiveCommodityList)){
               return new ArrayList<>();
            }
            changeInfo = getChangeInfo(idto, notActiveCommodityList.get(0).getShotDate(), null);
        }
        if(SpringUtil.isNotEmpty(notActiveCommodityList)){
            setNotActiveCommodityList(notActiveCommodityList);
            lastRecord.addAll(notActiveCommodityList);
        }
        // 处理在week表中不存在的商品
        if(SpringUtil.isNotEmpty(changeInfo)){
            Map<Long, StockSnapshotListForStockAndPriceODTO> lastRecordIdMap = lastRecord.stream().collect(Collectors.toMap(StockSnapshotListForStockAndPriceODTO::getCommodityId, it -> it));
            for(StockSnapshotCommodityWeekly it : changeInfo){
               if (lastRecordIdMap.containsKey(it.getCommodityId())){
                   StockSnapshotListForStockAndPriceODTO changeRecord = lastRecordIdMap.get(it.getCommodityId());
                   changeRecord.setNumber(changeRecord.getNumber() + it.getNumber());
                   changeRecord.setQuantity(changeRecord.getQuantity().add(it.getQuantity()));
                   changeRecord.setWeightPrice(weightPrice.get(it.getCommodityId()));
                   changeRecord.setTotalPrice(getTotalPrice(changeRecord.getWeightPrice(), changeRecord.getQuantity()));
               }else{
                   StockSnapshotListForStockAndPriceODTO changeRecord = new StockSnapshotListForStockAndPriceODTO();
                   changeRecord.setCommodityId(it.getCommodityId());
                   changeRecord.setNumber(it.getNumber());
                   changeRecord.setQuantity(it.getQuantity());
                   changeRecord.setWeightPrice(weightPrice.get(it.getCommodityId()));
                   changeRecord.setTotalPrice(getTotalPrice(changeRecord.getWeightPrice(), changeRecord.getQuantity()));
                   lastRecordIdMap.put(it.getCommodityId(), changeRecord);
               }
            }
            lastRecord = lastRecordIdMap.entrySet().stream().map(it -> it.getValue()).collect(Collectors.toList());
        }

        // 5. 统一处理无价格商品(不存在于weightPrice中的, 或者总表商品后面被覆盖掉了成本价的)
        this.handleWeightPrice(lastRecord, idto.getShopId());

        return lastRecord;
    }

    private void setNotActiveCommodityList(List<? extends StockSnapshotListForStockAndPriceODTO> notActiveCommodityList){
        for(StockSnapshotListForStockAndPriceODTO it :notActiveCommodityList ){
            BigDecimal totalPrice = getTotalPrice(it.getWeightPrice(), it.getQuantity());
            it.setTotalPrice(totalPrice);
        }
    }

    private void setLastRecord(List<? extends StockSnapshotListForStockAndPriceODTO> lastRecord,
                               Date searchDate,
                               List<StockSnapshotCommodityWeekly> changeInfo,
                               Map<Long, BigDecimal> weightPrice){
        Date snapshotDate = lastRecord.get(0).getShotDate();
        boolean isOnDay = searchDate.equals(snapshotDate);
        if(isOnDay){
            for(StockSnapshotListForStockAndPriceODTO it : lastRecord){
                if (null != weightPrice.get(it.getCommodityId())) {
                    it.setWeightPrice(weightPrice.get(it.getCommodityId()));
                    it.setTotalPrice(getTotalPrice(it.getQuantity(), it.getWeightPrice()));
                }
            }
        }else{
            // 计算变动后的份数
            if (SpringUtil.isNotEmpty(changeInfo)) {
                Map<Long, StockChangeInfoVO> changeInfoMap = changeInfo.stream().collect(Collectors.toMap(
                        StockSnapshotCommodityWeekly::getCommodityId
                        , it -> new StockChangeInfoVO(it.getNumber(), it.getQuantity())));
                for (StockSnapshotListForStockAndPriceODTO it : lastRecord) {
                    if (null != changeInfoMap.get(it.getCommodityId())) {
                        StockChangeInfoVO changeInfoVO =  changeInfoMap.get(it.getCommodityId());
                        it.setNumber(it.getNumber() + changeInfoVO.getChangeNumber());
                        it.setQuantity(it.getQuantity().add(changeInfoVO.getChangeQuantity()));
                    }
                    if (null != weightPrice.get(it.getCommodityId())) {
                        it.setWeightPrice(weightPrice.get(it.getCommodityId()));
                        it.setTotalPrice(getTotalPrice(it.getQuantity(), it.getWeightPrice()));
                    }
                }
            }
        }
    }

    private  List<StockSnapshotCommodityWeekly> getChangeInfo(StockSnapshotListForStockAndPriceIDTO idto, Date snapshotDate,  List<Long> activeCommodityIdList){
        // 获取变动库存份数(至查询当日0点为止)
        String changeBeginTime = DateUtil.get4yMdHms(snapshotDate);
        if(snapshotDate.after(DateUtil.parseDate(idto.getDate(), "yyyy-MM-dd"))){
            List<StockSnapshotCommodityWeekly> changeInfoList = stockSnapshotCommodityWeeklyMapper.getCommodityNumber(idto.getDate() + " 00:00:00", changeBeginTime, activeCommodityIdList, idto.getShopId());
            changeInfoList = changeInfoList.parallelStream().peek(it -> {
                it.setQuantity(BigDecimal.ZERO.subtract(it.getQuantity()));
                it.setNumber(-it.getNumber());
            }).collect(Collectors.toList());
            return changeInfoList;
        }else{
            return stockSnapshotCommodityWeeklyMapper.getCommodityNumber(changeBeginTime,  idto.getDate()+" 00:00:00",activeCommodityIdList, idto.getShopId());
        }
    }


    private  List<StockSnapshotListForStockAndPriceODTO> getActiveInfoForList(StockSnapshotListForStockAndPriceIDTO idto, Date searchDate){
        // 查询日期往前7天的0点
        Date ahead7Day = DateUtil.addDay(searchDate, -7);
        String ahead7Time = DateUtil.get4yMdHms(ahead7Day);
        List<StockSnapshotListForStockAndPriceODTO> result = stockSnapshotCommodityWeeklyMapper.selectLastRecordByShop(idto.getShopId(), idto.getDate() + " 00:00:00", ahead7Time);
        return SpringUtil.isEmpty(result) ? new ArrayList<>(): result;
    }

    private List<StockSnapshotListForStockAndPriceODTO> getNoActiveInfoForList(List<Long> activeCommodityIdList,Long shopId, Date searchDate, String date){
        // 查询日期往前7天的0点
        Date ahead7Day = DateUtil.addDay(searchDate, -84);
        String ahead84Time = DateUtil.get4yMdHms(ahead7Day);
        List<StockSnapshotListForStockAndPriceODTO> stockSnapshotCommodityTotalList = stockSnapshotCommodityTotalMapper.selectNotActiveStockInfo(shopId, activeCommodityIdList, date + " 23:59:59", ahead84Time);
        if(SpringUtil.isEmpty(stockSnapshotCommodityTotalList)){
            List<StockSnapshotCommodityTotal> firstTime = stockSnapshotCommodityTotalMapper.selectList(new LambdaQueryWrapper<StockSnapshotCommodityTotal>()
                    .select(StockSnapshotCommodityTotal::getCreateTime).orderByAsc(StockSnapshotCommodityTotal::getId).last("limit 1"));
            if(null != firstTime && null != firstTime.get(0)) {
                String firstDateStr = DateUtil.get4yMd(firstTime.get(0).getCreateTime());
                stockSnapshotCommodityTotalList = stockSnapshotCommodityTotalMapper.selectNotActiveStockInfo(shopId, activeCommodityIdList, firstDateStr + " 23:59:59", firstDateStr + " 00:00:00");
            }
        }
        return stockSnapshotCommodityTotalList;
    }


    /**
     * 查询活跃库存(活跃商品必定有库存变动记录, 则必定有成本价变动记录, 成本价日志表首次运行会初始化活跃商品)
     * @param searchDate
     * @param shopId
     */
    private Map<Long, BigDecimal>  getActiveWeightPrice(Date searchDate, Long shopId){
        SelectWeightPriceListIDTO selectWeightPriceListIDTO = new SelectWeightPriceListIDTO();
        selectWeightPriceListIDTO.setDate(searchDate);
        selectWeightPriceListIDTO.setShopId(shopId);
        return shopStockClient.selectWeightPriceList(selectWeightPriceListIDTO);
    }

    /**
     * 统一处理没有成本价的商品(大部分情况商品不会有成本价变动, 取81天内成本价会无法取到, 此类商品改为从总表拿)
     * @param lastRecord
     * @param shopId
     */
    private void handleWeightPrice(List<? extends StockSnapshotListForStockAndPriceODTO> lastRecord, Long shopId){
        List<Long> noPriceCommodityList = lastRecord.stream().filter(it -> null == it.getWeightPrice()).map(StockSnapshotListForStockAndPriceODTO::getCommodityId).collect(Collectors.toList());
        if(SpringUtil.isNotEmpty( noPriceCommodityList) && noPriceCommodityList.size() > 0) {
            List<StockSnapshotListForStockAndPriceODTO> noPriceCommodityWeightPriceList = stockSnapshotCommodityTotalMapper.selectActiveStockWeightPrice(shopId, noPriceCommodityList);
            Map<Long, BigDecimal> noPriceCommodityWeightPriceMap = noPriceCommodityWeightPriceList.stream().collect(Collectors.toMap(StockSnapshotListForStockAndPriceODTO::getCommodityId, StockSnapshotListForStockAndPriceODTO::getWeightPrice));
            lastRecord.forEach(it -> {
                if (null == it.getWeightPrice() && null != noPriceCommodityWeightPriceMap.get(it.getCommodityId())) {
                    it.setWeightPrice(noPriceCommodityWeightPriceMap.get(it.getCommodityId()));
                    it.setTotalPrice(getTotalPrice(it.getQuantity(), it.getWeightPrice()));
                }else if(null == it.getWeightPrice() && null == noPriceCommodityWeightPriceMap.get(it.getCommodityId())){
                    it.setWeightPrice(BigDecimal.ZERO);
                    it.setTotalPrice(BigDecimal.ZERO);
                }
            });
        }
    }


    /**
     * 获取当天0点的时分秒
     * @return
     */
    private Date getTodayZero(){
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    private BigDecimal getTotalPrice(BigDecimal weightPrice, BigDecimal quantity){
        if(null == weightPrice || null == quantity){
            return BigDecimal.ZERO;
        }else{
            return weightPrice.multiply(quantity).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    private boolean compareTotalCreateTime(Date lastDate){
        String lastDateStr = DateUtil.get4yMd(lastDate);
        lastDate = DateUtil.parseDate(lastDateStr, "yyyy-MM-dd");
        Date nowDate = new Date();
        String nowDateStr = DateUtil.get4yMd(nowDate);
        nowDate = DateUtil.parseDate(nowDateStr, "yyyy-MM-dd");
        // 上次执行日期+84天就是本次应该执行的日期, 判断今天是否大于这天, 是则还没到执行时间
        return DateUtil.addDay(lastDate, 84).after(nowDate);
    }
}
