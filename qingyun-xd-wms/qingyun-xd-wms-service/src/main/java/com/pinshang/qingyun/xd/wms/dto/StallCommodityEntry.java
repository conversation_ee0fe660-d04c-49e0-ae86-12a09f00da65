package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by hhf on 2019/12/19.
 * 鲜到商品列表(弹出框分页鲜食)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StallCommodityEntry {

    @ApiModelProperty(value = "商品id ")
    private String commodityId;

    @ApiModelProperty(value = "门店id ")
    private Long shopId;

    @ApiModelProperty(value = "档口id ")
    private Long stallId;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;


    @ApiModelProperty(value = "条码")
    private String barCode;

    /** 子码列表 */
    private String barCodes;

    private List<String> barCodeList;

    @ApiModelProperty(value = "商品后台名称")
    private String commodityName;

    @ApiModelProperty(value = "前台品名:列表显示名称")
    private String commodityAppName;


    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;


    @ApiModelProperty(value = "单位")
    private String commodityUnitName;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("成本价")
    private BigDecimal weightPrice;
}
