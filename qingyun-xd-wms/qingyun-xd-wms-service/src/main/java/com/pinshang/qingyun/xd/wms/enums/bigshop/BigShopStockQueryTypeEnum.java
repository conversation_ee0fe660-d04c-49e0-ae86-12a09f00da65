package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 查询类型 1-按档口、2-按档口+库区、3-按档口+货位
 *
 * <AUTHOR>
 */
public enum BigShopStockQueryTypeEnum {
    STALL_ONLY(1, "档口"),
    STALL_STORAGE_AREA(2, "档口+库区"),

    STALL_GOOD_ALLOCATION(3,"档口+货位"),
    ;
    private Integer code;
    private String name;

    BigShopStockQueryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BigShopStockQueryTypeEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BigShopStockQueryTypeEnum typeEnum : BigShopStockQueryTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BigShopStockQueryTypeEnum typeEnum : BigShopStockQueryTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum.getName();
            }
        }
        return "";
    }
}
