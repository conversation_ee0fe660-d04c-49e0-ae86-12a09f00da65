package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 拣货单消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrderMqDTO {
    /**
     * 拣货单id
     */
    private Long pickOrderId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单来源
     */
    private Integer sourceType;

    /**
     * 仓库id (门店id)
     */
    private Long warehouseId;

    /**
     * 拣货单状态
     */
    private Integer pickStatus;

    /**
     * 配送取货位编号
     */
    private String shelfNo;

    private List<PickOrderItemMqDTO> items;

    private String originalOrderCode;

    private Integer orderType;

    /**
     * 操作人id
     */
    private Long operatorId;

    @ApiModelProperty("取消订单原因选项id, 查字典接口-> optionCode: xd-order-cancel-reason, 并且只展示memo = 1的option")
    private Long reasonOptionId;
    @ApiModelProperty("补充原因")
    private String reason;

    private Integer cancelType;

    @ApiModelProperty("0不推送顺丰  1推送顺丰")
    private Integer pushSf;
}