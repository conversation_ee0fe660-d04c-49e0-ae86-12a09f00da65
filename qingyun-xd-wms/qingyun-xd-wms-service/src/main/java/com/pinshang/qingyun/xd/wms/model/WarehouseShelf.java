package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("t_xd_warehouse_shelf")
public class WarehouseShelf extends BaseEntity {

    /**
     * 货位编号
     */
    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    /**
     * 货位属性:1-拣货位,2-取货位
     */
    @ApiModelProperty(value = "货位属性")
    private Integer type;

    /**
     * 货位状态:0-停用(没有绑定货可停用),1-启用
     */
    @ApiModelProperty(value = "货位状态")
    private Integer status;

    @ApiModelProperty(value = "拣货位排序")
    private Long sort;
}
