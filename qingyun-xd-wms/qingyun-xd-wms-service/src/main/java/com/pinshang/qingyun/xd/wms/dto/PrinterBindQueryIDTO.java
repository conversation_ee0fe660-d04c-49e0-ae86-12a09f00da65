package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrinterBindQueryIDTO {

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    /**
     * 使用方类型
     * @see com.pinshang.qingyun.xd.wms.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "使用方类型(1-加工点 2-打包口)")
    private Integer userType;

    /**
     * 使用方(如果使用方类型为加工点，取当前门店下的加工点，使用方类型为打包口，使用方选当前门店的打包口)
     */
    @ApiModelProperty(value = "使用方(如果使用方类型为加工点，取当前门店下的加工点，使用方类型为打包口，使用方选当前门店的打包口)")
    private Long realUserId;

}
