package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单打包口占用订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@ToString
@TableName("t_dd_packing_order")
public class DdPackingOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属门店ID
     */
    private Long shopId;

    /**
     * 打包口
     */
    private Long packingStationId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
