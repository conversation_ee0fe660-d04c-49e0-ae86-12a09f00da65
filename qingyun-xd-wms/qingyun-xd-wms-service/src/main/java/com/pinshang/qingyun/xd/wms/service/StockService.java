

package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.enums.xd.SaleTypeEnum;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.volcano.VolcanoStockIDTO;
import com.pinshang.qingyun.xd.wms.enums.StockTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockNewMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockFreezeLogMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.ShopCommodityStock;
import com.pinshang.qingyun.xd.wms.model.StockFreezeLog;
import com.pinshang.qingyun.xd.wms.model.StockLog;
import com.pinshang.qingyun.xd.wms.service.stock.NormalStockService;
import com.pinshang.qingyun.xd.wms.service.stock.QualityStockService;
import com.pinshang.qingyun.xd.wms.service.stock.TransportStockService;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 正常库存出入库
 * 质检库存出入库
 * Created by chenqi on 2019/11/21.
 */
@Transactional
@Service
@Lazy
@Slf4j
public class StockService extends AbstractStockService {

    @Autowired
    private StockLogMapper stockLogMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;


    @Autowired
    private ShopCommodityStockMapper shopCommodityStockMapper;

    @Autowired
    private ShopCommodityStockNewMapper shopCommodityStockNewMapper;


    @Autowired
    private StockFreezeLogMapper stockFreezeLogMapper;


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private EveryDayFreshSwitchService everyDayFreshSwitchService;

    @Autowired
    private NormalStockService normalStockService;

    @Autowired
    private QualityStockService qualityStockService;

    @Autowired
    private TransportStockService transportStockService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 出入库统一
     *
     * @param stockInOutVO
     * @return
     */
    public Pair<Long, String> stockInOut(StockInOutVO stockInOutVO) {

        List<StockItemDTO> commodityList = stockInOutVO.getCommodityList().stream()
                .filter(it -> (Objects.nonNull(it.getStockNumber()) && it.getStockNumber() != 0) || it.getQuantity().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        if (SpringUtil.isEmpty(commodityList)) {
            return null;
        }

        List<StockItemDTO> mergeList = groupCommodityList(stockInOutVO.getCommodityList());

        Pair<Long, String> pair = null;
        Long warehouseId = stockInOutVO.getWarehouseId();
        Long userId = stockInOutVO.getUserId();
        StockInOutTypeEnums typeEnum = stockInOutVO.getTypeEnum();
        Pair<Long, String> idAndCode = stockInOutVO.getIdAndCode();

        if (StockTypeEnum.NORMAL.getCode() == typeEnum.getStockType()) {
            pair = normalStockService.stockInOut(idAndCode, typeEnum, mergeList, warehouseId, userId);
        } else if (StockTypeEnum.QUALITY.getCode() == typeEnum.getStockType()) {
            pair = qualityStockService.stockInOut(idAndCode, typeEnum, mergeList, warehouseId, userId);
        } else if (StockTypeEnum.TRANSPORT.getCode() == typeEnum.getStockType()) {
            pair = transportStockService.stockInOut(idAndCode, typeEnum, mergeList, warehouseId, userId);
            //在途库存 先不记录日志，
            return pair;
        }

        //查询处理完之后的库存
        Map<Long, ShopStockDTO> stockCollect = getFinalStockCollect(mergeList, warehouseId);
        //出入库统一处理
        for (StockItemDTO dto : mergeList) {
            //变动后
            ShopStockDTO stockDTO = stockCollect.get(dto.getCommodityId());
            Assert.isTrue(stockDTO != null, "商品库存信息有误");
            StockLog stockLog = new StockLog(IdWorker.getId(), warehouseId, dto.getCommodityId(),
                    dto.getStockNumber(), dto.getQuantity(), typeEnum.getCode(),
                    stockDTO.getStockNumber(), stockDTO.getStockQuantity(),
                    stockDTO.getQualityNumber(), stockDTO.getQualityQuantity(),
                    idAndCode.getRight(), userId, new Date(), null, null, null, null, null
                    , null, null, null);

            stockLogMapper.insert(stockLog);
        }

        //库存时序消息 暂时不用
        if (typeEnum.getTimeCode() != null) {
            sendMessage(warehouseId, typeEnum.getTimeCode(), mergeList);
        }
        return pair;
    }

    public List<StockItemDTO> groupCommodityList(List<StockItemDTO> commodityList) {

        //commodityList 同sku合并 需求3827
        List<StockItemDTO> mergeList = new ArrayList<>();
        Map<Long, List<StockItemDTO>> stockMap = commodityList.stream().collect(Collectors.groupingBy(StockItemDTO::getCommodityId));
        stockMap.forEach((key, value) -> {
            StockItemDTO item = new StockItemDTO();
            item.setCommodityId(key);
            int sumNumber = 0;
            BigDecimal sumQuantity = BigDecimal.ZERO;
            for (StockItemDTO dto : value) {
                if (Objects.nonNull(dto.getStockNumber())) {
                    sumNumber = sumNumber + dto.getStockNumber();
                }
                sumQuantity = sumQuantity.add(dto.getQuantity());
            }
            item.setStockNumber(sumNumber);
            item.setQuantity(sumQuantity);

            mergeList.add(item);
        });
        return mergeList;
    }

    @Override
    public List<ShopStockDTO> queryShopStock(Long warehouseId, List<Long> commodityIdList, List<DdStockInOutExtraVO> ddStockInOutExtraVO) {
        return shopCommodityService.queryShopStock(warehouseId, commodityIdList);
    }

    /**
     * 查询变动后库存信息
     *
     * @param commodityList
     * @param warehouseId
     * @return
     */
    private Map<Long, ShopStockDTO> getFinalStockCollect(List<StockItemDTO> commodityList, Long warehouseId) {
        List<Long> idList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<ShopStockDTO> stockList = shopCommodityService.queryShopStock(warehouseId, idList);
        return stockList.stream().collect(toMap(ShopStockDTO::getCommodityId, it -> it));
    }

    private void sendMessage(Long warehouseId, Integer type, List<StockItemDTO> commodityList) {
        class SendMessageTransactionSynchronizationAdapter extends TransactionSynchronizationAdapter {
            @Override
            public void afterCommit() {
                TimeSeriesDTO timeDto = new TimeSeriesDTO();
                List<TimeSeriesStockDTO> list = new ArrayList<>();
                for (StockItemDTO itemDTO : commodityList) {
                    TimeSeriesStockDTO dto = new TimeSeriesStockDTO(warehouseId, itemDTO.getCommodityId(), itemDTO.getStockNumber(), type);
                    list.add(dto);
                }
                //库存变动
                timeDto.setBusinessType(1);
                timeDto.setObj(list);
//                KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_TIME_SERIES_TYPE
//                        , timeDto, KafkaMessageOperationTypeEnum.INSERT);
//                kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XD_TIME_SERIES_TOPIC, JsonUtil.java2json(message));

                MqMessage msg = new MqMessage();
                msg.setTopic(KafkaTopicConstant.XD_TIME_SERIES_TOPIC);
                msg.setData(timeDto);
                mqSenderComponent.send(msg);
            }
        }
        SendMessageTransactionSynchronizationAdapter sendMessage = new SendMessageTransactionSynchronizationAdapter();
        TransactionSynchronizationManager.registerSynchronization(sendMessage);
    }


    /**
     * 期初门店库存
     *
     * @param list
     * @return
     */
    public int initShopCommodityStock(List<ShopCommodityStockInitIDTO> list) {
        if (SpringUtil.isEmpty(list)) {
            return 0;
        }
        List<String> shopIdAndCommodityId = list.stream().map(p -> p.getShopId() + "_" + p.getCommodityId()).collect(Collectors.toList());
        //查询已经存在的门店id和商品id的数据
        List<String> stockList = shopCommodityStockNewMapper.findShopCommodityStockListByParams(shopIdAndCommodityId);

        List<ShopCommodityStock> shopCommodityStockList = new ArrayList<>();
        for (ShopCommodityStockInitIDTO idto : list) {
            String shopCommodity = idto.getShopId() + "_" + idto.getCommodityId();
            if (!stockList.contains(shopCommodity)) {
                ShopCommodityStock stock = ShopCommodityStock.forInsert(idto.getShopId(), idto.getCommodityId(), idto.getCreateId(), idto.getCreateTime());
                shopCommodityStockList.add(stock);
            }
        }
        if (SpringUtil.isNotEmpty(shopCommodityStockList)) {
            List<List<ShopCommodityStock>> lists = ListUtil.splitSubList(shopCommodityStockList, 1000);
            for (List<ShopCommodityStock> shopCommodityStocks : lists) {
                shopCommodityStockMapper.initShopCommodityStock(shopCommodityStocks);
            }
        }
        return list.size();
    }

    /**
     * 获取冻结库存，直接解冻
     */
    public List<StockFreezeLog> getStockFreezeList(String referCode) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<StockFreezeLog>()
                .eq(StockFreezeLog::getReferCode, referCode);
        List<StockFreezeLog> freezeList = stockFreezeLogMapper.selectList(query);
        return freezeList;
    }


    /**
     * 处理volcano 餐饮库存
     *
     * @param stockIDTO
     */
    public void modifyVolcanoStock(VolcanoStockIDTO stockIDTO) {
        ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());
        List<StockItemDTO> commodityList = stockIDTO.getCommodityList();
        if (SaleTypeEnum.SELL.getCode().equals(stockIDTO.getSaleType())) {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_POS_NORMAL
                    , commodityList, stockIDTO.getShopId(), stockIDTO.getUserId());
            stockInOut(stockInOutVO);
        } else if (SaleTypeEnum.RETURN.getCode().equals(stockIDTO.getSaleType())) {
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_POS_QUALITY
                    , commodityList, stockIDTO.getShopId(), stockIDTO.getUserId());
            stockInOut(stockInOutVO);
        }
    }

    @Async
    public void transportQuantityAdd() {
        String orderTime = everyDayFreshSwitchService.findFreshOrderTime(StockInOutTypeEnums.IN_TRANSPORT);
        if (orderTime == null) {
            return;
        }
        RBucket<String> rBucket = redissonClient.getBucket("transportQuantityAdd");
        if (orderTime.equals(rBucket.get())) {
            log.info("{}:已经添加在途商品了", orderTime);
            return;
        }
        List<Long> commodityIds = everyDayFreshSwitchService.findFreshCommodity();
        if (commodityIds == null || commodityIds.isEmpty()) {
            return;
        }
        //日日鲜商品订单数量入库
        everyDayFreshSwitchService.transportStockInOut(orderTime, commodityIds, StockInOutTypeEnums.IN_TRANSPORT);
        //保存日期到redis
        rBucket.set(orderTime);
    }

    @Override
    public void buildStockCompletePickOutList(Long warehouseId, List<PickOrderItem> pickOrderItems, List<StockItemDTO> outList) {

        for (PickOrderItem item : pickOrderItems) {
            Long commodityId = item.getCommodityId();

            StockItemDTO stockItemDTO = new StockItemDTO(commodityId, item.getPickNumber(), item.getPickQuantity(), null);

            outList.add(stockItemDTO);
        }
    }

}