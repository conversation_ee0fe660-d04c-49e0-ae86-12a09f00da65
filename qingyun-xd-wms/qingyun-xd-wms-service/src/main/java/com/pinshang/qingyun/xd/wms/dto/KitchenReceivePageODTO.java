package com.pinshang.qingyun.xd.wms.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName KitchenReceivePageODTO
 * <AUTHOR>
 * @Date 2021/10/20 11:07
 * @Description KitchenReceivePageODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KitchenReceivePageODTO {
    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("商品code")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("份数")
    private Integer number;

    @ApiModelProperty("成本价")
    private BigDecimal weightPrice;

    @ApiModelProperty("领用日")
    private Date createTime;

    @ApiModelProperty("领用码")
    private String receiveCode;

    @ApiModelProperty("领用人")
    private String employeeName;


}
