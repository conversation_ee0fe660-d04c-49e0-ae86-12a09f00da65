package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockQueryIDTO {
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "档口idList")
    private List<Long> stallIdList;

    @ApiModelProperty(value = "档口id")
    private Long stallId;

    @ApiModelProperty(value = "商品list")
    private List<Long> commodityList;

    @ApiModelProperty(value = "上一次查询时间，兼容门店盘点查询实时库存使用，其他业务不需要传")
    private Date lastUpdateTime;

    public void checkData() {
        QYAssert.isTrue(warehouseId != null, "仓库id不能为空");
    }

    /**
     * APP特殊需求，要求没有的返回0
     * @param returnList
     * @return
     */
    public List<StockItemDTO> dealReturnList(List<StockItemDTO> returnList) {
        if(commodityList.size() > returnList.size()){
            List<Long> returnCommoList = returnList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            commodityList.removeAll(returnCommoList);

            for (Long commodityId : commodityList) {
                returnList.add(new StockItemDTO(commodityId, 0, null,null));
            }
        }

        return returnList;
    }

}