package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="Order对象", description="订单管理")
@TableName("t_xd_order")
public class Order {
    @TableId
    private Long id;
    /**订单编号*/
    private String orderCode;

    /**
     * C端用户ID
     */
    private Long userId;

    private Long shopId;

    /**送货日期(逻辑和B端订单一致理解，目前就云超使用) yyyy-MM-dd **/
    private Date toShopDate;

    /**收货人手机*/
    private String receiveMobile;
    /**收货人*/
    private String receiveMan;

    /**订单类型 0=普通订单 1=团购订单*/
    private Integer orderType;

    /**
     * 订单状态 0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败
     */
    private Integer orderStatus;
    /**
     * 如遇缺货处理方式(用户选项) 0-缺品退款,其他商品继续配送  1-直接取消订单
     */
    private Integer lackProcessMode;

    /**
     * 渠道来源: 1.APP 2.POS 3.小程序 4.饿了么 5.鲜食APP 6.团购 7.京东到家 8.云超小程序 9.云超APP
     */
    private Integer sourceType;

    private Date orderCompleteDate;

    private Long orderNum;
    private Date updateTime;
    private Long updateId;

    private String originalOrderCode;

    private Integer deliveryMode;
}
