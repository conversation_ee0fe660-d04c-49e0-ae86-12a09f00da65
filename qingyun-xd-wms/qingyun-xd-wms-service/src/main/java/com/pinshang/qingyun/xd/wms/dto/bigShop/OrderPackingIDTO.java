package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("OrderPackingIDTO")
public class OrderPackingIDTO extends Pagination implements Serializable {

    private static final long serialVersionUID = -4813033253444483309L;

    @ApiModelProperty(value = "参数 订单编号||打包口||订单短号")
    private String param;

    @ApiModelProperty(value = "查询类型 0:待打包 1：已打包")
    private Integer queryType;

    @JsonIgnore
    private Long shopId;

}
