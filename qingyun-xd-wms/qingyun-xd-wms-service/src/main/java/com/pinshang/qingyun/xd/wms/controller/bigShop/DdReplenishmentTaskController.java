package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdReplenishmentTaskODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdReplenishmentTaskPageIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdReplenishmentTaskSaveIDTO;
import com.pinshang.qingyun.xd.wms.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdReplenishmentTaskBizService;
import com.pinshang.qingyun.xd.wms.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 大店补货任务  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Api(tags = "大店补货任务", description = "大店补货任务")
@RestController
@RequestMapping("/bigShop/replenishment/task")
public class DdReplenishmentTaskController {

    @Autowired
    private DdReplenishmentTaskBizService ddReplenishmentTaskBizService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private IRenderService renderService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 大店补货任务 列表
     */
    @PostMapping("/list")
    @MethodRender
    @ApiOperation(value = "大店补货任务 列表")
    public PageInfo<DdReplenishmentTaskODTO> list(@RequestBody DdReplenishmentTaskPageIDTO req) {
        ddTokenShopIdService.processReadDdTokenShopId(req.getShopId(), req.getStallId());
        return ddReplenishmentTaskBizService.list(req);
    }

    /**
     * PDA 手动添加 补货任务
     */
    @PostMapping("/manual/save")
    @ApiOperation(value = "PDA 手动添加 补货任务", notes = "去补货")
    public Long manualReplenishment(@RequestBody DdReplenishmentTaskSaveIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        RLock lock = redissonClient.getLock("xsWms:manualSaveReplenishment");
        if (lock.tryLock()) {
            try {
                return ddReplenishmentTaskBizService.manualReplenishment(req);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }
        return null;
    }

    /**
     * Job自动添加 补货任务
     */
    @PostMapping("/automatic/save")
    @ApiOperation(value = "job自动添加 补货任务")
    public Boolean automaticReplenishment() {
        return ddReplenishmentTaskBizService.automaticReplenishment();
    }

    /**
     * 导出 补货任务
     */
    @GetMapping("/export")
    @ApiOperation(value = "导出 补货任务")
    public ModelAndView export(DdReplenishmentTaskPageIDTO req) {
        ddTokenShopIdService.processReadDdTokenShopId(req.getShopId(), req.getStallId());
        req.initExportPage();
        PageInfo<DdReplenishmentTaskODTO> result = ddReplenishmentTaskBizService.list(req);
        List<DdReplenishmentTaskODTO> list = result.getList();
        renderService.render(list, "/bigShop/replenishment/task/export");
        Map<String, List<String>> data = new HashMap<>();
        List<String> rowData;
        int i = 0;
        if (SpringUtil.isNotEmpty(list)) {
            for (DdReplenishmentTaskODTO dto : list) {
                rowData = new ArrayList<>();
                rowData.add(dto.getStallName());
                rowData.add(dto.getCommodityCode());
                rowData.add(dto.getCommodityName());
                rowData.add(dto.getBarCode());
                rowData.add(dto.getCommoditySpec());
                rowData.add(dto.getCommodityUnitName());
                rowData.add(dto.getTaskSourceName());
                rowData.add(dto.getTaskTypeName());
                rowData.add(Objects.isNull(dto.getCreateTime()) ? "" : DateUtil.getDateFormate(dto.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                rowData.add(Objects.toString(dto.getSuggestedQuantity(), ""));
                rowData.add(dto.getReplenishedStatusName());
                rowData.add(Objects.toString(dto.getRealQuantity(), ""));
                rowData.add(dto.getTransferCode());
                rowData.add(Objects.toString(dto.getReplenishEmployeeCode(), ""));
                rowData.add(dto.getReplenishUserName());
                rowData.add(Objects.isNull(dto.getReplenishTime()) ? "" : DateUtil.getDateFormate(dto.getReplenishTime(), "yyyy-MM-dd HH:mm:ss"));
                rowData.add(dto.getStatusName());
                rowData.add(dto.getFlagName());
                data.put("key_" + i++, rowData);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "大店补货任务" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.DD_REPLENISHMENT_TASK);
        map.put("data", data);
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

}
