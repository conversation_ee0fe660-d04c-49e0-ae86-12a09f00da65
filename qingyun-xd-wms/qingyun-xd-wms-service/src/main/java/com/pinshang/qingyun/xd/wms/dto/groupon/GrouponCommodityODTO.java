package com.pinshang.qingyun.xd.wms.dto.groupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponCommodityODTO {

    private Long grouponId;
    private String grouponCode;
    private Date grouponStartTime;
    private Date grouponEndTime;

    private String orderTime;

    private Long shopId;

    private Long commodityId;

    private BigDecimal quantity;

    private BigDecimal price;

    private BigDecimal groupPrice;

}
