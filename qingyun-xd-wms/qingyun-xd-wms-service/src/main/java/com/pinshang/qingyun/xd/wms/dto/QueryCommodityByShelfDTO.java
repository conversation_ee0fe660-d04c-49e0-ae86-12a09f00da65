package com.pinshang.qingyun.xd.wms.dto;


import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCommodityByShelfDTO extends Pagination<QueryCommodityByShelfResult> {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "货位id")
    private Long shelfId;

    @ApiModelProperty(value = "是否有货位 0-查询无货位，1-查询有货位")
    private Integer isShelf;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "是否称重  1称重  0非称重")
    private Integer isWeight;

    @ApiModelProperty(value = "是否速冻  1称重  0非称重")
    private Integer commodityIsQuickFreeze;

    @ApiModelProperty(value = "存储条件")
    private String storageCondition;
}
