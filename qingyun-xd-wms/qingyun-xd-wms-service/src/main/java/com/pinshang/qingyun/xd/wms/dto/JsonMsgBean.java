package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;

/**
 * 通用返回体
 */
@Data
public class JsonMsgBean implements Serializable {

    private static final long serialVersionUID = 1L;
    private Boolean success;
    private String code;
    private String message;

    @ApiModelProperty(value = "错误信息List集合对象String")
    private Collection<String> errMsgList;
    //返回数据对象
    private Object data;


    public JsonMsgBean() {
        super();
    }

    public JsonMsgBean(Boolean success) {
        this.success = success;
    }

    public JsonMsgBean(Boolean success, String message) {
        this.success = success;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
    }

    public JsonMsgBean(Boolean success, String message, Object data) {
        this.success = success;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        this.data = data;
    }
    public JsonMsgBean(Boolean success, String code, String message, Object data) {
        super();
        this.success = success;
        this.code = code;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        this.data = data;
    }
    public JsonMsgBean initSuccess(){
        this.success=true;
        return this;
    }
    public JsonMsgBean initSuccess(String message){
        this.success = true;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        return this;
    }
    public JsonMsgBean initSuccess(String code,String message){
        this.success = true;
        this.code = code;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        return this;
    }
    public JsonMsgBean initSuccess(String code,Object data){
        this.success = true;
        this.code = code;
        this.data = data;
        return this;
    }
    public JsonMsgBean initSuccess(String code,String message,Object data){
        this.success = true;
        this.code = code;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        this.data = data;
        return this;
    }
    public JsonMsgBean initFailure(){
        this.success=false;
        return this;
    }
    public JsonMsgBean initFailure(String message){
        this.success = false;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        return this;
    }
    public JsonMsgBean initFailure(String code, String message){
        this.success=false;
        this.code = code;
        this.message = message;
        this.errMsgList = Arrays.asList(message);
        return this;
    }
    public JsonMsgBean initFailure(String code, Object data){
        this.success=false;
        this.code = code;
        this.data = data;
        return this;
    }

}
