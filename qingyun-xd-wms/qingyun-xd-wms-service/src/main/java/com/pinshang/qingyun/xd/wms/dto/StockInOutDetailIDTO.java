package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 库存出入库详情IDTO
 */
@NoArgsConstructor
@AllArgsConstructor
public class StockInOutDetailIDTO {

    @ApiModelProperty(value = "仓库类型 1正常库 2临时库")
    private Integer warehouseType;

    @ApiModelProperty(value = "出入库类型 1入库 2出库")
    private Integer inOutType;

    @ApiModelProperty(value = "出入库单号code")
    private String orderCode;

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @Override
    public String toString() {
        return "StockInOutDetailIDTO{" +
                "warehouseType=" + warehouseType +
                ", inOutType=" + inOutType +
                ", orderCode='" + orderCode + '\'' +
                '}';
    }
}