package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName QueryOrderInfoForCloudTakeGoodODTO
 * <AUTHOR>
 * @Date 2023/7/31 18:06
 * @Description QueryOrderInfoForCloudTakeGoodODTO
 * @Version 1.0
 */
@Data
public class QueryReturnInfoForCloudTakeGoodODTO {
    @ApiModelProperty("退货单id")
    private Long id;

    @ApiModelProperty("退货单code")
    private String returnCode;

    @ApiModelProperty("退货总金额")
    private BigDecimal totalAmount;

    private List<QueryReturnInfoForCloudTakeGoodItemODTO> itemList;

    private Integer status;
}
