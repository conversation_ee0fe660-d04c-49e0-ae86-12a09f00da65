package com.pinshang.qingyun.xd.wms.dto;

import java.math.BigDecimal;

import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockProcessItemDTO {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "份数")
    private Integer number;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "加工单id")
    private Long jgId;

    @ApiModelProperty(value = "大店出入库参数")
    private DdStockInOutExtraVO ddStockInOutExtra;
}
