package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.price.PriceTypeEnums;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.price.dto.CommodityPriceListIDTO;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.shop.dto.MdShopStatusODTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptIDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptItemIDTO;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.xd.wms.dto.ShopStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.StockProcessIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockProcessItemDTO;
import com.pinshang.qingyun.xd.wms.enums.StockTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.StockLog;
import com.pinshang.qingyun.xd.wms.model.bigShop.GoodsAllocationCommodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodityStock;
import com.pinshang.qingyun.xd.wms.service.AbstractStockService;
import com.pinshang.qingyun.xd.wms.service.stock.bigShop.StallQualityStockService;
import com.pinshang.qingyun.xd.wms.service.stock.bigShop.StallNormalStockService;
import com.pinshang.qingyun.xd.wms.util.XdWmsConstantUtil;
import com.pinshang.qingyun.xd.wms.vo.ShopStockReceiptItemKfkVO;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.StorageAreaInOutVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class StallStockService extends AbstractStockService {

    @Autowired
    private StockLogMapper stockLogMapper;

    @Autowired
    private StallNormalStockService stallNormalStockService;

    @Autowired
    private StallQualityStockService stallQualityStockService;

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private CommodityPriceClient commodityPriceClient;

    @Autowired
    private ShopStockClient shopStockClient;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    @Autowired
    private ShopStatusClient shopStatusClient;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 出入库统一
     *
     * @param stockInOutVO
     * @return
     */
    @Override
    public Pair<Long, String> stockInOut(StockInOutVO stockInOutVO) {

        List<StockItemDTO> commodityList = stockInOutVO.getCommodityList().stream()
                .filter(it -> it.getQuantity().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        if (SpringUtil.isEmpty(commodityList)) {
            return null;
        }

        Pair<Long, String> pair = null;
        Long warehouseId = stockInOutVO.getWarehouseId();
        Long userId = stockInOutVO.getUserId();
        StockInOutTypeEnums typeEnum = stockInOutVO.getTypeEnum();
        Pair<Long, String> idAndCode = stockInOutVO.getIdAndCode();

        // 按 商品id 排序，避免后续发生死锁
        commodityList.sort(Comparator
                .comparing((StockItemDTO c) -> c.getDdStockInOutExtraVO().getStallId())
                .thenComparing(StockItemDTO::getCommodityId));

        if (StockTypeEnum.NORMAL.getCode() == typeEnum.getStockType()) {
            pair = stallNormalStockService.stockInOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        } else if (StockTypeEnum.QUALITY.getCode() == typeEnum.getStockType()) {
            pair = stallQualityStockService.stockInOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        } /*else if (StockTypeEnum.TRANSPORT.getCode() == typeEnum.getStockType()) {
            pair = goodsAllocationCommodityService.warehouseStockInOut(idAndCode, typeEnum, mergeList, warehouseId, userId, ddStockInOutExtraVO);
        }*/

        //出入库统一处理
        for (StockItemDTO dto : commodityList) {
            //变动后
            DdStockInOutExtraVO ddStockInOutExtraVO = dto.getDdStockInOutExtraVO();
            Integer storageArea = ddStockInOutExtraVO.getStorageArea();

            Map<Long, StallCommodityStock> stockCollect = getFinalStockCollect(dto, warehouseId);

            StallCommodityStock stockDTO = stockCollect.get(dto.getCommodityId());
            Assert.isTrue(stockDTO != null, "商品库存信息有误");
            StockLog stockLog = new StockLog(IdWorker.getId(), warehouseId, dto.getCommodityId(),
                    dto.getStockNumber(), dto.getQuantity(), typeEnum.getCode(),
                    null, getAreaStockQuantity(stockDTO, storageArea),
                    null, stockDTO.getQualityQuantity(),
                    idAndCode.getRight(), userId, new Date(), null, null, null, null, null,
                    ddStockInOutExtraVO.getStorageArea(),
                    ddStockInOutExtraVO.getGoodsAllocationCode(),
                    ddStockInOutExtraVO.getStallId());

            stockLogMapper.insert(stockLog);
        }

        return pair;
    }

    @Override
    public void buildStockCompletePickOutList(Long warehouseId, List<PickOrderItem> pickOrderItems, List<StockItemDTO> outList) {

        MdShopStatusODTO shopStatusByShopId = shopStatusClient.getShopStatusByShopId(warehouseId);
        Boolean zoneOrderPick = Objects.nonNull(shopStatusByShopId) && PickingMethodEnum.ZONE_ORDER_PICKING.getCode().equals(shopStatusByShopId.getPickingMethod());

        Map<Long, GoodsAllocationCommodity> pickAreaCommodityMap = Collections.emptyMap();
        if (zoneOrderPick) {
            List<Long> commodityIdList = pickOrderItems.stream().map(PickOrderItem::getCommodityId).collect(Collectors.toList());
            List<GoodsAllocationCommodity> goodsAllocationCommodities = goodsAllocationCommodityService.listPickingAreaByCommodityIdsWithAllocationCode(warehouseId, commodityIdList);

            if (SpringUtil.isNotEmpty(goodsAllocationCommodities)) {
                pickAreaCommodityMap = goodsAllocationCommodities.stream()
                        .collect(toMap(GoodsAllocationCommodity::getCommodityId, Function.identity()));
            }
        }

        for (PickOrderItem item : pickOrderItems) {
            Long commodityId = item.getCommodityId();

            GoodsAllocationCommodity commodityPickingArea = pickAreaCommodityMap.get(commodityId);

            DdStockInOutExtraVO ddStockInOutExtraVO;
            Long commodityStallId = item.getStallId();
            if (zoneOrderPick && Objects.nonNull(commodityPickingArea) && Objects.equals(item.getStallId(), commodityPickingArea.getStallId())) {
                ddStockInOutExtraVO = DdStockInOutExtraVO.buildDdStockInOutExtraVO(
                        commodityId,
                        commodityStallId,
                        commodityPickingArea.getStorageArea(),
                        commodityPickingArea.getGoodsAllocationId(),
                        commodityPickingArea.getGoodsAllocationCode()
                );
            } else {
                // 整单拣货 或 commodityPickingArea 为空  都走排面
                ddStockInOutExtraVO = DdStockInOutExtraVO.buildShelfAreaDdStockInOutExtraVO(commodityId, commodityStallId);
            }

            int pickNumber = Objects.isNull(item.getPickNumber()) ? 0 : item.getPickNumber();
            BigDecimal pickQuantity = Objects.isNull(item.getPickQuantity()) ? XdWmsConstantUtil.PICK_QUANTITY_CONSTANT : item.getPickQuantity();
            StockItemDTO stockItemDTO = new StockItemDTO(commodityId, pickNumber, pickQuantity, ddStockInOutExtraVO);
            outList.add(stockItemDTO);
        }
    }

    @Override
    public List<ShopStockDTO> queryShopStock(Long warehouseId, List<Long> commodityIdList, List<DdStockInOutExtraVO> ddStockInOutExtraVOList) {

        if (SpringUtil.isEmpty(ddStockInOutExtraVOList)) {
            return Collections.emptyList();
        }
        //大店库存查询逻辑
        List<DdStockInOutExtraVO> distinctCommodityIdDdStockInOutMap = new ArrayList<>(ddStockInOutExtraVOList.stream()
                .collect(Collectors.toMap(
                        DdStockInOutExtraVO::getCommodityId,
                        vo -> vo,
                        (existing, replacement) -> existing
                ))
                .values());

        //查对应库区的库存并返回
        List<StallCommodityStock> stallCommodityStocks = stallCommodityStockService.queryStallCommodityStock(warehouseId, distinctCommodityIdDdStockInOutMap);

        if (SpringUtil.isEmpty(stallCommodityStocks)) {
            return Collections.emptyList();
        }


        List<ShopStockDTO> shopStockDTOS = new ArrayList<>(stallCommodityStocks.size());
        for (StallCommodityStock stallCommodityStock : stallCommodityStocks) {

            BigDecimal stockQuantity = stallCommodityStock.getStockQuantity().add(stallCommodityStock.getPickingAreaStock()).add(stallCommodityStock.getWarehouseAreaStock());
            ShopStockDTO stockDTO = new ShopStockDTO(warehouseId, stallCommodityStock.getCommodityId()
                    , null, stockQuantity
                    , stallCommodityStock.getFreezeNumber()
                    , null, stallCommodityStock.getQualityQuantity());

            shopStockDTOS.add(stockDTO);
        }

        return shopStockDTOS;
    }

    private static BigDecimal getAreaStockQuantity(StallCommodityStock stallCommodityStock, Integer storageArea) {

        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);

        BigDecimal stockQuantity;
        switch (storageAreaEnum) {
            case SHELF_AREA:
                stockQuantity = stallCommodityStock.getStockQuantity();
                break;
            case PICKING_AREA:
                stockQuantity = stallCommodityStock.getPickingAreaStock();
                break;
            case WAREHOUSE_AREA:
                stockQuantity = stallCommodityStock.getWarehouseAreaStock();
                break;
            case PROVISIONAL_AREA:
                stockQuantity = stallCommodityStock.getQualityQuantity();
                break;
            default:
                throw new IllegalArgumentException("暂不支持该库区");
        }
        return stockQuantity;
    }

    private Map<Long, StallCommodityStock> getFinalStockCollect(StockItemDTO commodity, Long warehouseId) {
        List<StallCommodityStock> stallCommodityStocks = stallCommodityStockService
                .queryStallCommodityStock(warehouseId, commodity.getDdStockInOutExtraVO().getStallId(), Collections.singletonList(commodity.getCommodityId()));
        return stallCommodityStocks.stream().collect(toMap(StallCommodityStock::getCommodityId, it -> it));
    }

    public void storageAreaInOut(StorageAreaInOutVO storageAreaInOutVO) {
        StockInOutVO stockInVO = storageAreaInOutVO.getStockIn();
        List<StockInOutVO> stockOutList = storageAreaInOutVO.getStockOutList();

        QYAssert.notNull(stockInVO, "入库信息不能为空");
        QYAssert.notEmpty(stockOutList, "出库信息不能为空");

        for (StockInOutVO stockOutVO : stockOutList) {
            stockInOut(stockOutVO);
        }

        stockInOut(stockInVO);

    }

    public void stockProcessing(StockProcessIDTO stockProcessIDTO) {

        Long stallId = stockProcessIDTO.getStallId();

        List<StockProcessItemDTO> originList = stockProcessIDTO.getOriginList();
        List<StockProcessItemDTO> finishedList = stockProcessIDTO.getFinishedList();


        for (StockProcessItemDTO stockProcessItemDTO : originList) {
            DdStockInOutExtraVO ddStockInOutExtra = DdStockInOutExtraVO.buildShelfAreaDdStockInOutExtraVO(stockProcessItemDTO.getCommodityId(), stallId);
            stockProcessItemDTO.setDdStockInOutExtra(ddStockInOutExtra);
        }

        for (StockProcessItemDTO stockProcessItemDTO : finishedList) {
            DdStockInOutExtraVO ddStockInOutExtra = DdStockInOutExtraVO.buildShelfAreaDdStockInOutExtraVO(stockProcessItemDTO.getCommodityId(), stallId);
            stockProcessItemDTO.setDdStockInOutExtra(ddStockInOutExtra);
        }


        stallStockProcessing(stockProcessIDTO);
    }

    /**
     * 大店加工单 产成品成本价计算
     * （加工前档口库存金额+本次加工入库金额）/（加工前档口库存数量+本次加工入库数量）
     * 加工出库总金额 = 加工入库总金额 = 加工出库数量*成本价
     * A + 2B = C + 2D
     * 产成品C的加工入库金额占比 = （C零售价*数量）/ (C零售价*数量 + D零售价*数量 )
     * 加工出库总金额 = A成本价*数量 + B成本价*数量
     * C的加工入库金额 = C的加工入库金额占比 * 加工出库总金额
     */
    public void stallStockProcessing(StockProcessIDTO stockProcessIDTO) {
        //加工单 处理成本价
        List<StockProcessItemDTO> originList = stockProcessIDTO.getOriginList();
        List<StockProcessItemDTO> finishedList = stockProcessIDTO.getFinishedList();

        List<Long> originIdList = new ArrayList<>(originList.size());
        List<DdStockInOutExtraVO> originDdStockInOutExtraVOList = new ArrayList<>(originList.size());
        extractLists(originList, originIdList, originDdStockInOutExtraVOList);

        List<Long> finishedIdList = new ArrayList<>(finishedList.size());
        List<DdStockInOutExtraVO> finishedDdStockInOutExtraVOList = new ArrayList<>(finishedList.size());
        extractLists(finishedList, finishedIdList, finishedDdStockInOutExtraVOList);

        List<ShopStockDTO> finishedStockList = queryShopStock(stockProcessIDTO.getShopId(), finishedIdList, finishedDdStockInOutExtraVOList);
        Map<Long, BigDecimal> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

        //出入库
        ImmutablePair idAndCode = new ImmutablePair(stockProcessIDTO.getReferId(), stockProcessIDTO.getReferCode());

        //自动加工出库  不校验库存
        if (YesOrNoEnums.YES.getCode().equals(stockProcessIDTO.getAutoProcess())) {
            StockInOutVO outStockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.AUTO_OUT_PROCESS_NORMAL
                    , stockProcessIDTO.toOriginList(), stockProcessIDTO.getShopId(), stockProcessIDTO.getUserId());
            stockInOut(outStockInOutVO);
        } else {
            StockInOutVO outStockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_PROCESS_NORMAL
                    , stockProcessIDTO.toOriginList(), stockProcessIDTO.getShopId(), stockProcessIDTO.getUserId());
            stockInOut(outStockInOutVO);
        }

        StockInOutVO inStockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_PROCESS_NORMAL
                , stockProcessIDTO.toFinishedList(), stockProcessIDTO.getShopId(), stockProcessIDTO.getUserId());
        stockInOut(inStockInOutVO);

        //获取所有原材料成本价
        CommodityPriceListIDTO priceIDTO = new CommodityPriceListIDTO();
        priceIDTO.setAllotType(PriceTypeEnums.WEIGHT_PRICE.getCode());
        priceIDTO.setCommodityIdList(originIdList);
        priceIDTO.setStallId(stockProcessIDTO.getStallId());
        priceIDTO.setShopId(stockProcessIDTO.getShopId());
        Map<Long, BigDecimal> originPriceMap = commodityPriceClient.getShopStallCommodityPriceByShopId(priceIDTO);
        Assert.isTrue(originIdList.size() == originPriceMap.size(), "获取所有原材料成本价失败");

        //获取所有产成品零售价
        priceIDTO.setAllotType(PriceTypeEnums.RETAIL_PRICE.getCode());
        priceIDTO.setCommodityIdList(finishedIdList);
        priceIDTO.setShopId(stockProcessIDTO.getShopId());
        Map<Long, BigDecimal> retailPriceMap = commodityPriceClient.getShopCommodityPriceByShopId(priceIDTO);
        Assert.isTrue(finishedIdList.size() == retailPriceMap.size(), "获取所有产成品零售价失败");

        //加工出库总金额 = 加工入库总金额 = 加工出库数量*成本价
        BigDecimal originWeightSum = originList.stream().map(item -> item.getQuantity().multiply(originPriceMap.get(item.getCommodityId()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        originWeightSum = originWeightSum.setScale(4, BigDecimal.ROUND_HALF_UP);

        //产成品的零售价
        BigDecimal finishedPriceSum = finishedList.stream().map(item -> item.getQuantity().multiply(retailPriceMap.get(item.getCommodityId()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        finishedPriceSum = finishedPriceSum.setScale(4, BigDecimal.ROUND_HALF_UP);
        Assert.isTrue(finishedPriceSum.compareTo(BigDecimal.ZERO) > 0, "产成品零售价异常");

        ShopStockReceiptIDTO stockIDTO = new ShopStockReceiptIDTO();
        stockIDTO.setStallId(stockProcessIDTO.getStallId());
        stockIDTO.setShopId(stockProcessIDTO.getShopId());
        List<ShopStockReceiptItemIDTO> receiptList = new ArrayList<>();
        ShopStockReceiptItemIDTO dto = null;

        Collections.sort(finishedList, Comparator.comparing(StockProcessItemDTO::getCommodityId));
        List<ShopStockReceiptItemKfkVO> kfkVOList = new ArrayList<>();
        // 分摊比例
        BigDecimal commodityTotalRate = BigDecimal.ZERO;

        int size = finishedList.size() - 1;
        BigDecimal countedWeightPrice = BigDecimal.ZERO;
        for (int i = 0; i < finishedList.size(); i++) {
            dto = new ShopStockReceiptItemIDTO();
            dto.setCommodityId(finishedList.get(i).getCommodityId());
            dto.setQuantity(finishedList.get(i).getQuantity());
            dto.setStockNumber(finishedList.get(i).getNumber());
            dto.setExistStockQuantity(stockCollect.get(finishedList.get(i).getCommodityId()));
            BigDecimal weightTotal = null;
            if (i == size) {
                weightTotal = originWeightSum.subtract(countedWeightPrice);
            } else {
                weightTotal = finishedList.get(i).getQuantity().multiply(retailPriceMap.get(finishedList.get(i).getCommodityId()))
                        .divide(finishedPriceSum, 4, BigDecimal.ROUND_HALF_UP).multiply(originWeightSum).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            dto.setPrice(weightTotal.divide(finishedList.get(i).getQuantity(), 2, BigDecimal.ROUND_HALF_UP));
            dto.setTotalPrice(weightTotal);

            countedWeightPrice = countedWeightPrice.add(weightTotal);
            receiptList.add(dto);

            ShopStockReceiptItemKfkVO kfkVO = BeanCloneUtils.copyTo(dto, ShopStockReceiptItemKfkVO.class);
            kfkVO.setJgId(finishedList.get(i).getJgId());
            // 计算分摊比例,最后一个减
            BigDecimal commodityRate = null;
            if (i == size) {
                commodityRate = new BigDecimal("100").subtract(commodityTotalRate);
            } else {
                commodityRate = finishedList.get(i).getQuantity().multiply(retailPriceMap.get(finishedList.get(i).getCommodityId()))
                        .divide(finishedPriceSum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
            }
            kfkVO.setCommodityRate(commodityRate);
            commodityTotalRate = commodityTotalRate.add(commodityRate);
            kfkVOList.add(kfkVO);

            log.info("分摊比例大店: jgId={} commodityId={} retailPrice={} finishedPriceSum={} commodityRate={}",
                    kfkVO.getJgId(), dto.getCommodityId(),
                    finishedList.get(i).getQuantity().multiply(retailPriceMap.get(finishedList.get(i).getCommodityId())),
                    finishedPriceSum, commodityRate);
        }

        stockIDTO.setCommodityList(receiptList);
        shopStockClient.stockReceipt(stockIDTO);

        // 门店加工单记录加工入库商品的分摊比例
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.JG_SAVE_COMMODITY_PRICE_TOPIC.getTopic(),
                kfkVOList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.JG_SAVE_COMMODITY_PRICE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }

}
