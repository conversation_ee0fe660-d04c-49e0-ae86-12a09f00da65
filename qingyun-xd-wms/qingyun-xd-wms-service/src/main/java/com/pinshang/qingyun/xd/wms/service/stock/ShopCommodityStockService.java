

package com.pinshang.qingyun.xd.wms.service.stock;

import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.model.ShopCommodityStock;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 仓库库存调整明细表 Service
 *
 * <AUTHOR>
 * @since 2024-12-13 14点10分
 */
@Service
public class ShopCommodityStockService {

    @Autowired
    private ShopCommodityStockMapper shopCommodityStockMapper;


    @Transactional
    public void batchUpdateStock(List<ShopCommodityStock> stockUpdates) {
        if (CollectionUtils.isEmpty(stockUpdates)) {
            return;
        }
        shopCommodityStockMapper.batchUpdateStock(stockUpdates);
    }
}