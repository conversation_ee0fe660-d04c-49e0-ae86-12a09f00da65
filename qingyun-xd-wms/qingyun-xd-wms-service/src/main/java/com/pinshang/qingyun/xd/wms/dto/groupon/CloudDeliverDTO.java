package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudDeliverDTO extends Pagination<CloudDeliverListDTO> {

    @ApiModelProperty("门店")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("预约开始日期")
    private String startArrivalTime;

    @ApiModelProperty("预约结束日期")
    private String endArrivalTime;

    @ApiModelProperty("C端订单编号")
    private String orderCode;

    @ApiModelProperty("提货人手机号")
    private String receiveMobile;

    @ApiModelProperty("登录用户手机号")
    private String userMobile;

    @ApiModelProperty("提货码")
    private String pickupCode;

    @ApiModelProperty("0=普通订单 1=团购订单 2=云超订单")
    private Integer orderType;

}
