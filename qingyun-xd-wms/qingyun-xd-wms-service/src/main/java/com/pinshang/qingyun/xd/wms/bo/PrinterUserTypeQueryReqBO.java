package com.pinshang.qingyun.xd.wms.bo;


import com.pinshang.qingyun.xd.wms.plus.Pagination;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("PrinterUserTypeQueryReqVO")
public class PrinterUserTypeQueryReqBO extends PrinterUserTypeQueryReqVO {

    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店ID")
    private Long  shopId;


}
