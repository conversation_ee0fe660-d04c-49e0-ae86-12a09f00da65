package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.JsonMsgBean;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderDTO;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderResult;
import com.pinshang.qingyun.xd.wms.dto.PrintWorkOrderrResult;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.PickWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "仓库拣货加工单", tags = "PickWorkOrderController")
@RequestMapping("/pick/work/order")
public class PickWorkOrderController {

    @Autowired
    private PickWorkOrderService pickWorkOrderService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @PostMapping(value = "/pickWorkOrderList")
    @ApiOperation(value = "查询加工单列表")
    public MPage<PickWorkOrderResult> pickWorkOrderList(@RequestBody PickWorkOrderDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddTokenShopIdService.processReadDdTokenShopId(tokenInfo.getShopId(), tokenInfo.getStallId());
        return pickWorkOrderService.pickWorkOrderList(dto);
    }

    @GetMapping(value = "/printWorkOrder/{pickWorkOrderId}")
    @ApiOperation(value = "打印加工单数据")
    public PrintWorkOrderrResult printWorkOrder(@PathVariable("pickWorkOrderId") Long pickWorkOrderId) {
        return pickWorkOrderService.printWorkOrder(pickWorkOrderId);
    }

    @GetMapping(value = "/tiotPrintWorkOrder/{pickWorkOrderId}")
    @ApiOperation(value = "网络打印机打印加工单数据")
    public JsonMsgBean tiotPrintWorkOrder(@PathVariable("pickWorkOrderId") Long pickWorkOrderId) {
        return pickWorkOrderService.tiotPrintWorkOrder(pickWorkOrderId);
    }

    @ApiOperation(value = "加工完成")
    @GetMapping(value = "/workComplete/{pickWorkOrderId}")
    public Boolean workComplete(@PathVariable("pickWorkOrderId") Long pickWorkOrderId) {
        pickWorkOrderService.workComplete(pickWorkOrderId);
        return Boolean.TRUE;
    }

}
