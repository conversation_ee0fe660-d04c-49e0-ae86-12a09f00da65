package com.pinshang.qingyun.xd.wms.service.stock;

import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseDefaultDTO;
import com.pinshang.qingyun.xd.wms.dto.volcano.VolcanoStockIDTO;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.StockFreezeLog;
import com.pinshang.qingyun.xd.wms.service.AbstractStockService;
import com.pinshang.qingyun.xd.wms.service.StockService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallStockService;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.StorageAreaInOutVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 针对大店 普通门店出入库的分发
 */
@Slf4j
@Service
public class StockServiceAdapter {

    @Autowired
    private StockService stockService;

    @Autowired
    private StallStockService stallStockService;

    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;

    private AbstractStockService getStockService(StockIDTO stockIDTO) {
        if (Objects.nonNull(stockIDTO.getCommodityList().get(0).getDdStockInOutExtraVO())
                && Objects.nonNull(stockIDTO.getCommodityList().get(0).getDdStockInOutExtraVO().getStallId())) {
            return SpringBeanFinder.getBean(StallStockService.class);
        }
        return SpringBeanFinder.getBean(StockService.class);
    }

    private AbstractStockService getStockService(Long stallId) {
        if (Objects.nonNull(stallId)) {
            return SpringBeanFinder.getBean(StallStockService.class);
        }
        return SpringBeanFinder.getBean(StockService.class);
    }

    private AbstractStockService getStockService(StockReceiptIDTO stockReceiptIDTO) {
        if (Objects.nonNull(stockReceiptIDTO.getCommodityList().get(0).getDdStockInOutExtraVO())
                && Objects.nonNull(stockReceiptIDTO.getCommodityList().get(0).getDdStockInOutExtraVO().getStallId())) {
            return SpringBeanFinder.getBean(StallStockService.class);
        }
        return SpringBeanFinder.getBean(StockService.class);
    }

    private AbstractStockService getStockService(List<StockItemDTO> commodityList) {
        if (!commodityList.isEmpty()) {
            DdStockInOutExtraVO extraVO = commodityList.get(0).getDdStockInOutExtraVO();
            if (extraVO != null && extraVO.getStallId() != null) {
                return SpringBeanFinder.getBean(StallStockService.class);
            }
        }
        return SpringBeanFinder.getBean(StockService.class);
    }


    public Pair<Long, String> stockInOut(StockInOutVO stockInOutVO) {
        return getStockService(stockInOutVO.getCommodityList()).stockInOut(stockInOutVO);
    }

    /**
     * 大店移库
     *
     * @param storageAreaInOutVO
     */
    public void storageAreaInOut(StorageAreaInOutVO storageAreaInOutVO) {
        stallStockService.storageAreaInOut(storageAreaInOutVO);
    }

    /**
     * 在途库存添加
     */
    public void transportQuantityAdd() {
        stockService.transportQuantityAdd();
    }

    /**
     * 处理volcano 餐饮库存
     *
     * @param stockIDTO
     */
    public void modifyVolcanoStock(VolcanoStockIDTO stockIDTO) {
        stockService.modifyVolcanoStock(stockIDTO);
    }

    /**
     * 库存处理
     *
     * @param stockIDTO
     */
    public void modifyStock(StockIDTO stockIDTO) {
        getStockService(stockIDTO).modifyStock(stockIDTO);
    }

    /**
     * 库存处理
     *
     * @param stockProcessIDTO
     */
    public void stockProcessing(StockProcessIDTO stockProcessIDTO) {

        Long stallId = stockProcessIDTO.getStallId();
        getStockService(stallId).stockProcessing(stockProcessIDTO);

    }

    public int initShopCommodityStock(List<ShopCommodityStockInitIDTO> list) {
        return stockService.initShopCommodityStock(list);
    }


    /**
     * 门店退货
     *
     * @param stockIDTOList
     * @return
     */
    public Boolean stockShopReturnList(List<StockIDTO> stockIDTOList) {
        StockIDTO stockIDTO = stockIDTOList.get(0);
        return getStockService(stockIDTO).stockShopReturnList(stockIDTOList);
    }

    /**
     * 门店退货
     *
     * @param stockIDTO
     */
    public void stockShopReturn(StockIDTO stockIDTO) {
        getStockService(stockIDTO).stockShopReturn(stockIDTO);
    }

    public void checkStockReturn(StockIDTO stockIDTO) {
        getStockService(stockIDTO).checkStockReturn(stockIDTO);
    }

    public void stockReceipt(StockReceiptIDTO stockReceiptIDTO) {
        getStockService(stockReceiptIDTO).stockReceipt(stockReceiptIDTO);
    }

    public Boolean stockInventory(CommodityXsStockIDTO stockIDTO, ImmutablePair idAndCode) {
        return getStockService(stockIDTO.getCommodityList()).stockInventory(stockIDTO, idAndCode);
    }

    public void stockCompletePick(Long orderId, String orderCode, Long warehouseId, ImmutablePair idAndCode, List<PickOrderItem> pickOrderItems, Integer orderType) {

        Long stallId = pickOrderItems.get(0).getStallId();
        getStockService(stallId).stockCompletePick(orderId, orderCode, warehouseId, idAndCode, pickOrderItems, orderType);
    }

    public void addStockFreezeLog(Long referId, String referCode, Long shopId, List<StockItemDTO> commodityList) {
        stockService.addStockFreezeLog(referId, referCode, shopId, commodityList);
    }

    public List<StockFreezeLog> getStockFreezeList(String referCode) {
        return stockService.getStockFreezeList(referCode);
    }

    public void processXiaoeTongStock(XiaoeTongStockIDTO req) {
        StockInOutTypeEnums stockInOutTypeEnum = req.getStockInOutTypeEnum();

        if (StockInOutTypeEnums.XIAOE_TOING_IN_CLIENT_QUALITY.equals(stockInOutTypeEnum)) {
            processXiaoeTongInClientQuality(req.getCommodityList());
        }

        ImmutablePair<Long, String> idAndCode = ImmutablePair.of(req.getReferId(), req.getReferCode());
        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, stockInOutTypeEnum
                , req.getCommodityList(), req.getWarehouseId(), req.getUserId());

        stockInOut(stockInOutVO);
    }

    private void processXiaoeTongInClientQuality(List<StockItemDTO> commodityList) {
        if (SpringUtil.isEmpty(commodityList)) {
            return;
        }

        Set<Long> stallIdSet = commodityList.stream().map(StockItemDTO::getDdStockInOutExtraVO)
                .filter(Objects::nonNull)
                .map(DdStockInOutExtraVO::getStallId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (SpringUtil.isEmpty(stallIdSet)) {
            return;
        }
        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap = ddTempWarehouseAllocationService
                .getStallIdProvisionalAreaGoodsAllocationMap(stallIdSet, TempOperationTypeEnum.APP_RETURN);

        for (StockItemDTO itemDTO : commodityList) {
            DdStockInOutExtraVO ddStockInOutExtraVO = itemDTO.getDdStockInOutExtraVO();
            if (Objects.isNull(ddStockInOutExtraVO)) {
                log.warn("ddStockInOutExtraVO is null,itemDTO:[{}]", itemDTO);
                continue;
            }

            Long stallId = ddStockInOutExtraVO.getStallId();

            DdTempWarehouseDefaultDTO ddTempWarehouseDefaultDTO = stallIdProvisionalAreaGoodsAllocationMap.get(stallId);
            DdStockInOutExtraVO stockInOutExtraVO = DdStockInOutExtraVO.buildProvisionaAreaDdStockInOutExtraVO(itemDTO.getCommodityId(), stallId
                    , ddTempWarehouseDefaultDTO.getGoodsAllocationId(), ddTempWarehouseDefaultDTO.getGoodsAllocationCode());
            itemDTO.setDdStockInOutExtraVO(stockInOutExtraVO);
        }
    }

}
