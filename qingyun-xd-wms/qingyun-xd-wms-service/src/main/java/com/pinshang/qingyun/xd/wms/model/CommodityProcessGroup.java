package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="CommodityProcessGroup对象", description="商品前台加工方式")
@TableName("t_xd_commodity_process_group")
public class CommodityProcessGroup {

    private Long id;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "加工方式分组id")
    private Long processGroupId;

    @ApiModelProperty(value = "加工方式分组选项id")
    private Long processGroupItemId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    private Long createId;

    private Date createTime;
}
