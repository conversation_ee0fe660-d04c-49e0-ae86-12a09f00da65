package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderDetailItemResult {
    @ApiModelProperty(value = "商品主键")
    private Long commodityId;
    @ApiModelProperty(value = "商品编号")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;
    private String barCode;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("商品单位")
    private String commodityUnitName;
    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;
    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;
    @ApiModelProperty("(整包或散装)")
    private String commodityPackageName;
    @ApiModelProperty("申请份数")
    private Integer applyNumber;
    @ApiModelProperty("申请数量")
    private BigDecimal applyQuantity;
    @ApiModelProperty("实际出货的份数")
    private Integer outNumber;
    @ApiModelProperty("实际出货的数量")
    private BigDecimal outQuantity;
    @ApiModelProperty("实际入货的份数")
    private Integer inNumber;
    @ApiModelProperty("实际入货的数量")
    private BigDecimal inQuantity;
    @ApiModelProperty("成本价")
    private BigDecimal weightPrice;
    @ApiModelProperty("进价")
    private BigDecimal commodityPrice;
}
