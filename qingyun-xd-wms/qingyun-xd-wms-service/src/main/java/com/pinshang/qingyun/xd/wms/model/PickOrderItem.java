package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
* <p>
* 仓库拣货单明细表
* </p>
*
* <AUTHOR>
* @since 2019-12-02
*/
@ApiModel(value="PickOrderItem对象", description="仓库拣货单明细表")
@TableName("t_xd_pick_order_item")
public class PickOrderItem extends BaseEntity {
    @ApiModelProperty(value = "订单id" )
    private Long orderId;

    @ApiModelProperty(value = "订单itemId" )
    private Long orderItemId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty(value = "拣货单id" )
    private Long pickOrderId;

    @ApiModelProperty(value = "商品ID" )
    private Long commodityId;

    @ApiModelProperty(value = "商品数量" )
    private BigDecimal quantity;

    @ApiModelProperty(value = "商品份数" )
    private Integer stockNumber;

    @ApiModelProperty(value = "商品拣货数量" )
    private BigDecimal pickQuantity;

    @ApiModelProperty(value = "拣货份数" )
    private Integer pickNumber;

    @ApiModelProperty(value = "成本价" )
    private BigDecimal weightPrice;

    @ApiModelProperty(value = "1=称重，0=非称重" )
    private Integer isWeight;

    @ApiModelProperty(value = "是否需要处理 0＝不加工，1＝加工(针对称重商品)" )
    private Integer isProcess;

    @ApiModelProperty(value = "是否完成拣货 0=未完成，1=已完成" )
    private Integer isComplete;

    private String originSubBizId;

    /**
     * 拣货单id
     */
    private Long pickPartitionOrderId;

    public PickOrderItem() {
    }

    public Integer getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Integer isComplete) {
        this.isComplete = isComplete;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getPickOrderId() {
        return pickOrderId;
    }

    public void setPickOrderId(Long pickOrderId) {
        this.pickOrderId = pickOrderId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(Integer stockNumber) {
        this.stockNumber = stockNumber;
    }

    public BigDecimal getPickQuantity() {
        return pickQuantity;
    }

    public void setPickQuantity(BigDecimal pickQuantity) {
        this.pickQuantity = pickQuantity;
    }

    public Integer getPickNumber() {
        return pickNumber;
    }

    public void setPickNumber(Integer pickNumber) {
        this.pickNumber = pickNumber;
    }

    public BigDecimal getWeightPrice() {
        return weightPrice;
    }

    public void setWeightPrice(BigDecimal weightPrice) {
        this.weightPrice = weightPrice;
    }

    public Integer getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(Integer isWeight) {
        this.isWeight = isWeight;
    }

    public Integer getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(Integer isProcess) {
        this.isProcess = isProcess;
    }

    public String getOriginSubBizId() {
        return originSubBizId;
    }

    public void setOriginSubBizId(String originSubBizId) {
        this.originSubBizId = originSubBizId;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Long getPickPartitionOrderId() {
        return pickPartitionOrderId;
    }

    public void setPickPartitionOrderId(Long pickPartitionOrderId) {
        this.pickPartitionOrderId = pickPartitionOrderId;
    }
}
