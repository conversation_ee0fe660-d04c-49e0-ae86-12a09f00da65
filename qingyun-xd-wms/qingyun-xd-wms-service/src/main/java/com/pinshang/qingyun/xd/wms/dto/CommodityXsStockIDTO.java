package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 门店库存处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityXsStockIDTO {
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @ApiModelProperty(value = "商品list")
    private List<StockItemDTO> commodityList;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "库存处理code")
    private Integer typeCode;
}