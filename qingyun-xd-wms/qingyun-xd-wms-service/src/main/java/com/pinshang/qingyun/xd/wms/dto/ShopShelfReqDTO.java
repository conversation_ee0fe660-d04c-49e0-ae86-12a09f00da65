package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopShelfReqDTO {

    @ApiModelProperty(value = "门店id,必填", required = true)
    private Long shopId;

    @ApiModelProperty(value = "商品集合，必填", required = true)
    private List<Long> commdityIdList;

    public void checkData() {
        QYAssert.isTrue(shopId != null, "门店Id不能为空");
        QYAssert.isTrue(!CollectionUtils.isEmpty(commdityIdList), "商品集合不能为空");
    }
}
