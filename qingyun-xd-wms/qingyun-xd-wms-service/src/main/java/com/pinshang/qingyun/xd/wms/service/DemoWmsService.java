package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.xd.wms.enums.ShelfTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.DemoWmsMapper;
import com.pinshang.qingyun.xd.wms.model.DemoWms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DemoWmsService {
   @Autowired
   private DemoWmsMapper demoWmsMapper;

   public DemoWms getById(){

       ShelfTypeEnum.PICK.getCode();
       ShelfTypeEnum.TAKE.getCode();

       return demoWmsMapper.selectById(1L);
   }
}