package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.cms.service.CommodityEverydayFreshClient;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityQueryIDTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.EverydayFreshSwitchMapper;
import com.pinshang.qingyun.xd.wms.mapper.ShopMapper;
import com.pinshang.qingyun.xd.wms.model.EverydayFreshSwitch;
import com.pinshang.qingyun.xd.wms.model.Shop;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/13 14:42
 * @Description
 */
@Service
@Slf4j
public class EveryDayFreshSwitchService {
    @Autowired
    private EverydayFreshSwitchMapper everydayFreshSwitchMapper;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    @Lazy
    private StockServiceAdapter stockServiceAdapter;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private CommodityEverydayFreshClient commodityEverydayFreshClient;

    /**
     * 查询该门店是否开启日日鲜清零开关
     * @param shopId
     * @return
     */
    public Boolean shopSwitchByShopId(Long shopId){
       Shop shop= shopMapper.selectById(shopId);
       if (shop==null||shop.getShopType()==null){
           return Boolean.FALSE;
       }
       List<EverydayFreshSwitch> switchList=everydayFreshSwitchMapper.selectList(null);
       if (switchList==null||switchList.isEmpty()){
           return Boolean.FALSE;
       }
       //鲜到开
       if (shop.getShopType().equals(ShopTypeEnums.XD.getCode())&&switchList.get(0).getXd().equals(YesOrNoEnums.YES.getCode())){
           return Boolean.TRUE;
       }
        if (shop.getShopType().equals(ShopTypeEnums.XS.getCode())&&switchList.get(0).getXs().equals(YesOrNoEnums.YES.getCode())){
            return Boolean.TRUE;
        }
        if (shop.getShopType().equals(ShopTypeEnums.THZT.getCode())&&switchList.get(0).getThzt().equals(YesOrNoEnums.YES.getCode())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Async
    public void transportQuantitySubtract(StockReceiptIDTO stockReceiptIDTO) {

        // 收货stockEnums传空，预订单收货传enums
        //如果不是收货，直接返回值
        if (stockReceiptIDTO.getStockEnums() != null && !stockReceiptIDTO.getStockEnums().equals(StockInOutTypeEnums.IN_RECEIPT_NORMAL)) {
            log.info("出入库类型为:{} 不处理在途",stockReceiptIDTO.getStockEnums().getName());
            return;
        }

        String orderTime = findFreshOrderTime(StockInOutTypeEnums.OUT_TRANSPORT);
        if (orderTime == null) {
            return;
        }
        RBucket<String> rBucket = redissonClient.getBucket("transportQuantitySubtract");
        if (orderTime.equals(rBucket.get())) {
            log.info("{}:已经减少过在途商品了", orderTime);
            return;
        }
        log.info("时间：{}在途出库",orderTime);
        List<StockReceiptItemDTO> dtos = stockReceiptIDTO.getCommodityList();
        //当前收货商品ID
        List<Long> commodityIds = dtos.stream().map(StockReceiptItemDTO::getCommodityId).collect(Collectors.toList());
        //日日鲜商品id
        List<Long> freshCommodityIds = findFreshCommodity();
        if (freshCommodityIds == null || freshCommodityIds.isEmpty()) {
            return;
        }
        if (Collections.disjoint(commodityIds, freshCommodityIds)) {
            log.info("本次收货不包含日日鲜商品");
            return;
        }
        rBucket.set(orderTime);
        transportStockInOut(orderTime, freshCommodityIds, StockInOutTypeEnums.OUT_TRANSPORT);

    }

    public String findFreshOrderTime(StockInOutTypeEnums enums){
        List<EverydayFreshSwitch> everydayFreshSwitches = everydayFreshSwitchMapper.selectList(null);
        if (everydayFreshSwitches == null || everydayFreshSwitches.isEmpty()) {
            log.info("日日鲜下单开关未设置");
            return null;
        }
        //判断当前时间是否在规定时间段内
        if (StringUtil.isNullOrEmpty(everydayFreshSwitches.get(0).getTodayTime()) || StringUtil.isNullOrEmpty(everydayFreshSwitches.get(0).getTomorrowTime())) {
            log.info("时间设置为null");
            return null;
        }
        String orderTime;
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.HOUR_MIN_DATE_FORMAT);
        try {
            Date toDayDate = dateFormat.parse(everydayFreshSwitches.get(0).getTodayTime());
            Date tomorrowDate = dateFormat.parse(everydayFreshSwitches.get(0).getTomorrowTime());
            String nowStr = DateUtil.getDateFormate(new Date(), DateUtil.HOUR_MIN_DATE_FORMAT);
            Date now = dateFormat.parse(nowStr);
            if (now.after(toDayDate)) {
                orderTime = DateUtil.get4yMd(DateUtil.addDay(1));
            } else if (now.before(tomorrowDate)||StockInOutTypeEnums.OUT_TRANSPORT.equals(enums) ) {
                orderTime = DateUtil.get4yMd(new Date());
            } else {
                log.info("当前时间还未到清零时间");
                return null;
            }

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return orderTime;
    }

    public List<Long> findFreshCommodity(){
        return commodityEverydayFreshClient.getAllEverydayFresh();
    }

    //日日鲜商品订单数量出入库
    public void transportStockInOut(String orderTime,List<Long> commodityIds,StockInOutTypeEnums enums){
        ShopOrderedQuantityQueryIDTO idto = new ShopOrderedQuantityQueryIDTO();
        idto.setOrderTime(orderTime);
        idto.setCommodityIdList(commodityIds);

        List<ShopOrderedQuantityODTO> quantityODTOS = orderClient.queryShopOrderedQuantity(idto);
        if (quantityODTOS==null||quantityODTOS.isEmpty()){
            return;
        }
        Map<Long,List<ShopOrderedQuantityODTO>> map = quantityODTOS.stream().collect(Collectors.groupingBy(t->t.getShopId()));
        for(Long key:map.keySet()){
            if(!shopSwitchByShopId(key)){
                continue;
            }
            List<StockItemDTO> commodityList = new ArrayList<>();
            for (ShopOrderedQuantityODTO shopOrderedQuantityODTO:map.get(key)){
                StockItemDTO stockItemDTO = new StockItemDTO();
                stockItemDTO.setQuantity(shopOrderedQuantityODTO.getQuantity());
                //份数没有实际用处，不为空
                stockItemDTO.setStockNumber(1);
                if (StockInOutTypeEnums.OUT_TRANSPORT.equals(enums)){
                    //出库取反
                    stockItemDTO.setQuantity( stockItemDTO.getQuantity().negate());
                }
                stockItemDTO.setCommodityId(shopOrderedQuantityODTO.getCommodityId());
                commodityList.add(stockItemDTO);
            }
            ImmutablePair idAndCode = new ImmutablePair(IdWorker.getId(), IdWorker.getIdStr());
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, enums, commodityList, key, StockUtils.INSTANCE.userId());
            stockServiceAdapter.stockInOut(stockInOutVO);
        }
    }
}
