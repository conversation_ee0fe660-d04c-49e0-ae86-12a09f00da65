package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: sk
 * @Date: 2024/9/25
 */
@RestController
@RequestMapping("/demo/")
@Api(value = "DemoController", tags = "DemoController")
public class DemoController {

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @PostMapping("/test/{shopId}/{stallId}")
    @ApiOperation(value = "测试大店token")
    public Boolean test(@PathVariable("shopId") Long shopId, @PathVariable("stallId") Long stallId) {
        ddTokenShopIdService.processDdTokenShopId(shopId, stallId);
        return Boolean.TRUE;
    }

}
