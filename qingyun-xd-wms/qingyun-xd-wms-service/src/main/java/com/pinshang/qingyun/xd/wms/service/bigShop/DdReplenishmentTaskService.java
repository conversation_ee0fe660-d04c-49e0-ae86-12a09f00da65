package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskStatusEnum;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdReplenishmentTaskMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdReplenishmentTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 大店补货任务  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Slf4j
@Service
public class DdReplenishmentTaskService extends ServiceImpl<DdReplenishmentTaskMapper, DdReplenishmentTask> {
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchTasks(List<DdReplenishmentTask> ddReplenishmentTasks) {
        if (CollectionUtils.isEmpty(ddReplenishmentTasks)) {
            return;
        }
        // 执行批量保存任务操作
        this.saveBatch(ddReplenishmentTasks);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchTasks(List<DdReplenishmentTask> ddReplenishmentTasks) {
        if (CollectionUtils.isEmpty(ddReplenishmentTasks)) {
            return;
        }
        // 执行批量更新任务操作
        this.updateBatchById(ddReplenishmentTasks);
    }

    /**
     * 查询档口商品的未完成的补货任务
     */
    public DdReplenishmentTask queryReplenishmentTask(Long shopId, Long stallId, Long commodityId, Integer taskType) {
        LambdaQueryWrapper<DdReplenishmentTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DdReplenishmentTask::getShopId, shopId)
                .eq(DdReplenishmentTask::getStallId, stallId)
                .eq(DdReplenishmentTask::getCommodityId, commodityId)
                .eq(DdReplenishmentTask::getStatus, ReplenishmentTaskStatusEnum.ACTIVE.getCode())
                .eq(DdReplenishmentTask::getFlag, YesOrNoEnums.NO.getCode())
                .eq(DdReplenishmentTask::getTaskType, taskType);
        return baseMapper.selectOne(wrapper);
    }
}
