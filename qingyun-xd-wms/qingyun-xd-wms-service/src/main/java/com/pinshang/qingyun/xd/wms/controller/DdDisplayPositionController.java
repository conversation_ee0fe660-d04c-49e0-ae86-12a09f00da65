package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.service.DdDisplayPositionService;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 排面陈列位管理  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Api(tags = "排面陈列位管理", description = "排面陈列位管理")
@RestController
@RequestMapping("/ddDisplayPosition")
public class DdDisplayPositionController {

    @Autowired
    private DdDisplayPositionService ddDisplayPositionService;

    /**
    * 排面陈列位管理 列表
    * @param req
    * @return
    */
    @PostMapping("/page")
    @ApiOperation(value = "排面陈列位管理 列表")
    public PageInfo<DdDisplayPositionODTO> page(@RequestBody DdDisplayPositionPageIDTO req) {
        return ddDisplayPositionService.page(req);
    }

    @PostMapping("/listByName")
    @ApiOperation(value = "搜索门店陈列位")
    @MethodRender
    public List<DdDisplayPositionODTO> listDdDisplayPosition(@RequestBody DdDisplayPositionListIDTO req){
        return ddDisplayPositionService.listDdDisplayPosition(req);
    }

    /**
    * 保存 排面陈列位管理
    * @param req
    */
    @PostMapping("/save")
    @ApiOperation(value = "保存 排面陈列位管理")
    public Boolean save(@RequestBody DdDisplayPositionSaveIDTO req) {
        return ddDisplayPositionService.save(req);
    }

    /**
    * 更新 排面陈列位管理
    * @param req
    */
    @PostMapping("/update")
    @ApiOperation(value = "更新 排面陈列位管理")
    public Boolean update(@RequestBody DdDisplayPositionUpdateIDTO req) {
        return ddDisplayPositionService.update(req);
    }

    /**
    * 删除 排面陈列位管理
    * @param req
    */
    @PostMapping("/delete")
    @ApiOperation(value = "解绑 排面陈列位")
    public Boolean delete(@RequestBody DdDisplayPositionDeleteIDTO req) {
        return ddDisplayPositionService.delete(req);
    }

    @GetMapping("/list")
    @ApiOperation(value = "陈列位")
    public List<DdDisplayPositionODTO> list(@RequestParam("displayPosition") String displayPosition, @RequestParam(value = "stallId", required = false) Long stallId){
        return ddDisplayPositionService.list(displayPosition, stallId);
    }


    @ApiOperation(value = "导入陈列位", notes = "导入陈列位", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public ExcelResult importExccel(@ApiParam(value = "待上传的文件") @RequestParam(value = "file", required = true) MultipartFile file,
                                    @ApiParam(value = "档口") @RequestParam(value = "stallId", required = true) Long stallId,
                                    @ApiParam(value = "门店") @RequestParam(value = "shopId", required = true) Long shopId) throws Exception {
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        Assert.notNull(wb, "工作簿不能为空");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "表单不能为空");
        List<DdDisplayPositionSaveIDTO> positionList = new ArrayList<>();
        QYAssert.isTrue(sheet.getLastRowNum() < 1001, "每次最多导入1000行");
        List<String> errorInfo = new ArrayList<>();
        sheet.forEach(row ->{
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货架编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "陈列位号*"), "模板不正确");
            }
            // 第二行开始
            if (rowNum > 0) {
                DdDisplayPositionSaveIDTO  position = new DdDisplayPositionSaveIDTO();
                Cell codeCell = row.getCell(0);
                if(null == codeCell){
                    position.setShelveCode("");
                }else{
                    position.setShelveCode(codeCell.getStringCellValue().trim());
                }
                Cell nameCell = row.getCell(1);
                if(null == nameCell){
                    position.setDisplayPositionName("");
                }else {
                    position.setDisplayPositionName(nameCell.getStringCellValue().trim());
                }
                positionList.add(position);

                if ("".equals(position.getShelveCode()) || "".equals(position.getDisplayPositionName())) {
                    errorInfo.add(rowNum + "行信息填写不完整");
                }
                if (position.getDisplayPositionName().length() > 50) {
                    errorInfo.add(rowNum + "陈列位最长50个字");
                }
            }
        });
        if(SpringUtil.isNotEmpty(errorInfo)){
            return new ExcelResult(errorInfo, null);
        }
        QYAssert.isTrue(SpringUtil.isNotEmpty(positionList), "导入数据不能为空");
        return ddDisplayPositionService.importExcel(positionList, stallId, shopId);
    }
}
