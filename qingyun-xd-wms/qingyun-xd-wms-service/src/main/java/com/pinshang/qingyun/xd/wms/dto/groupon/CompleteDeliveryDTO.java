package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompleteDeliveryDTO {

    private Long orderId;

    private List<BigDecimal> pickQuantitys;

   // private List<CompleteDeliveryItemDTO> pickList;

    public void check() {
        QYAssert.isTrue(null != orderId, "订单号不能为空");
        QYAssert.isTrue(!CollectionUtils.isEmpty(pickQuantitys), "拣货量不能为空");
    }

}
