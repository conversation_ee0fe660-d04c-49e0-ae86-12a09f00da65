package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 补货任务来源
 *
 * <AUTHOR>
 */
public enum ReplenishmentTaskSourceEnum {
    SYSTEM_WARNING(1, "系统预警"),
    MANUAL_ADDITION(2, "手动添加"),
    ;
    private Integer code;
    private String name;

    ReplenishmentTaskSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ReplenishmentTaskSourceEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReplenishmentTaskSourceEnum typeEnum : ReplenishmentTaskSourceEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
