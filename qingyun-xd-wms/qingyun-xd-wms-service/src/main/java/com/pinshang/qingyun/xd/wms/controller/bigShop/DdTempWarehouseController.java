package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseEditIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehousePageIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehousePageODTO;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DdTempWarehouseController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/20 19:08
 * @Version 1.0
 */
@Api(tags = "临时库货位业务设置", description = "临时库货位业务设置")
@RestController
@RequestMapping("/bigShop/ddTempWarehouse")
@RequiredArgsConstructor
public class DdTempWarehouseController {
    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;

    @PostMapping("/page")
    public PageInfo<DdTempWarehousePageODTO> page(@RequestBody DdTempWarehousePageIDTO page) {
        return ddTempWarehouseAllocationService.page(page);
    }
    @PostMapping("/editDdTempWarehouse")
    public Boolean editDdTempWarehouse(@RequestBody DdTempWarehouseEditIDTO dto) {
        return ddTempWarehouseAllocationService.editDdTempWarehouse(dto);
    }

}
