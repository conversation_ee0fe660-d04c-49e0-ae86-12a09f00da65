package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_dd_goods_allocation_commodity")
public class GoodsAllocationCommodity extends BaseEntity {

    private Long shopId;

    private Long areaId;

    private Long stallId;

    /**
     * 库区
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum
     */
    private Integer storageArea;

    /**
     * 货位id
     */
    private Long goodsAllocationId;

    @TableField(exist = false)
    @Transient
    private String goodsAllocationCode;

    /**
     *排序值：值越小越靠前
     */
    @TableField(exist = false)
    @Transient
    private Integer sortNum;

    private Long commodityId;

    /**
     * 库存
     */
    private BigDecimal stock;

    /**
     * 最小库存
     */
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    private BigDecimal maxStock;

    public GoodsAllocationCommodity(Long commodityId, BigDecimal stock) {
        this.commodityId = commodityId;
        this.stock = stock;
    }
}
