package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 库区枚举
 * 表示库区的类型：1-排面区，2-拣货区，3-存储区
 *
 * <AUTHOR>
 */
public enum StorageAreaEnum {
    SHELF_AREA(1, "排面区"),
    PICKING_AREA(2, "拣货区"),
    WAREHOUSE_AREA(3, "存储区"),

    PROVISIONAL_AREA(4, "临时库"),
    ;
    private Integer code;
    private String name;

    StorageAreaEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static StorageAreaEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StorageAreaEnum typeEnum : StorageAreaEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (StorageAreaEnum typeEnum : StorageAreaEnum.values()) {
            if (typeEnum.name.equals(name)) {
                return typeEnum.code;
            }
        }
        return null;
    }
}
