package com.pinshang.qingyun.xd.wms.enums;

/**
 * 打印机部门类型
 * <AUTHOR>
 */
public enum PrinterDeptTypeEnum {
    SHOP(1,"门店"),
    WAREHOUSE(2,"仓库"),
    PARK(3,"园区"),

    ;
    private Integer code;
    private String name;

    PrinterDeptTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PrinterDeptTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (PrinterDeptTypeEnum typeEnum : PrinterDeptTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
