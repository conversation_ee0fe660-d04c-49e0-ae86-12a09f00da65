package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xd.wms.dto.StockItemPageODTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryPageIDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.FreshCommodityService;
import com.pinshang.qingyun.xd.wms.util.ExcelExportUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fresh")
@Api(value = "日日鲜", tags = "FreshCommodityController")
public class FreshCommodityController {

    @Autowired
    private FreshCommodityService freshCommodityService;

    @PostMapping("/queryFreshStock")
    @ApiOperation(value = "日日鲜库存查询")
    public MPage<StockItemPageODTO> queryFreshStock(@RequestBody StockQueryPageIDTO dto) {
        return freshCommodityService.queryFreshStock(dto);
    }

    @GetMapping("/exportFreshStock")
    @ApiOperation(value = "导出日日鲜库存")
    @FileCacheQuery(bizCode = "SHOP_FRESH_STOCK")
    public void exportFreshStock(StockQueryPageIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        MPage<StockItemPageODTO> page = freshCommodityService.queryFreshStock(dto);

        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "日日鲜库存报表" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("门店编码");
        tableHeader.add("门店名称");
        tableHeader.add("商品编码");
        tableHeader.add("条形码");
        tableHeader.add("商品名称");
        tableHeader.add("前台名称");
        tableHeader.add("规格");
        tableHeader.add("计量单位");
        tableHeader.add("是否称重");
        tableHeader.add("及时达上下架状态");
        tableHeader.add("库存数量");
        tableHeader.add("临时库存数量");
        tableHeader.add("冻结库存份数");
        StringBuilder stringBuilder = new StringBuilder();
        List<List<String>> dataList = new ArrayList<>();
        if(page!=null && SpringUtil.isNotEmpty(page.getList())) {
            for(StockItemPageODTO e : page.getList()) {
                stringBuilder.setLength(0);
                List<String> row = new ArrayList<>();
                row.add(e.getShopCode());
                row.add(e.getShopName());
                row.add(e.getCommodityCode());
                row.add(e.getBarCode());
                row.add(e.getCommodityName());
                row.add(e.getCommodityAppName());
                row.add(e.getCommoditySpec());
                row.add(e.getCommodityUnitName());

                if (e.getIfWeight() != null) {
                    row.add(YesOrNoEnums.getByCode(e.getIfWeight()).getName());
                } else {
                    row.add("");
                }

                if (e.getAppStatus() != null) {
                    if (e.getAppStatus().equals(0)) {
                        row.add("上架");
                    } else if(e.getAppStatus().equals(1)) {
                        row.add("下架");
                    }
                } else {
                    row.add("");
                }
                row.add(e.getStockQuantity()+"");
                row.add(e.getQualityQuantity()+"");
                row.add(e.getFreezeNumber()+"");
                dataList.add(row);
            }
        }

        //覆盖文件名, 无需扩展名
        fileName = "日日鲜库存报表" + sdf.format(new Date());
        ExcelUtil.setFileNameAndHead(response,  fileName);
        List<List<String>> excelHead = tableHeader.stream().map(Collections::singletonList).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet("数据").doWrite(dataList);

        /* 已重构, 后续稳定后删除
        SXSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbookS(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcelS(response,fileName,xb);
        */
    }
}
