package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xd.wms.dto.QueryOrderInfoForCloudTakeGoodODTO;
import com.pinshang.qingyun.xd.wms.dto.cloud.*;
import com.pinshang.qingyun.xd.wms.service.cloud.NewCloudService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cloud")
@Api(value = "云超", tags = "CloudController")
public class CloudController {

    @Autowired
    private NewCloudService cloudService;

    @PostMapping("/cloudPickList")
    @ApiOperation(value = "云超配送单 拣货列表")
    public PageInfo<CloudPickListDTO> cloudPickList(@RequestBody CloudPickListIDTO cloudPickListIDTO) {
        PageInfo<CloudPickListDTO> mpage = cloudService.cloudPickListNew(cloudPickListIDTO);
        return mpage;
    }

    @GetMapping("/cloudDetail")
    @ApiOperation(value = "云超配送单 详情")
    public CloudDetailDTO cloudDetail(@RequestParam(value = "orderId",required = false) Long orderId) {
        return cloudService.cloudDetail(orderId);
    }

    @GetMapping("/pickPackage")
    @ApiOperation(value = "云超拣包裹")
    public Boolean pickPackage(@RequestParam(value = "orderId",required = false) Long orderId,@RequestParam(value = "orderCode",required = false) String orderCode) {
        return cloudService.pickPackage(orderId, orderCode);
    }

    @PostMapping("/pickSuccess")
    @ApiOperation(value = "云超配送拣货完成 包括普通单和团购单子")
    public CloudPickSuccessDTO pickSuccess(@RequestBody PickSuccessDTO dto) {
        return cloudService.pickSuccess(dto);
    }

    /**
     * 大仓已经发货的普通单子和拣货完成的云超团购单子
     * @param orderId
     * @return
     */
    @GetMapping("/cancelOrderInputStock")
    @ApiOperation(value = "云超普通单子和团购单子 取消单入库")
    public Boolean cancelOrderInputStock(@RequestParam(value = "orderId",required = false) Long orderId) {
        return cloudService.cancelOrderInputStock(orderId,  null);
    }



    @GetMapping("/clearOccupyList")
    @ApiOperation(value = "清理占用清单")
    public Boolean clearOccupyList(@RequestParam(value = "shopId",required = false) Long shopId) {
        return cloudService.clearOccupyList(shopId);
    }


    @PostMapping("/cloudCancelAutoIn")
    @ApiOperation(value = "取消单自动入库")
    public Boolean cloudCancelAutoIn() {
        return cloudService.cloudCancelAutoIn();
    }

    @GetMapping("/selfPickOrder")
    @ApiOperation(value = "云超自提查询")
    public SelfPickOrderODTO selfPickOrder(@RequestParam("orderCode") String orderCode) {
        return cloudService.selfPickOrder(orderCode);
    }

    @PostMapping("/selfPickCancel")
    @ApiOperation(value = "云超自提核销 包括普通单和团购单子")
    public CloudPickSuccessDTO selfPickCancel(@RequestBody PickSuccessDTO dto) {
        return cloudService.selfPickCancel(dto);
    }

    /**
     * 为了兼容性考虑,这个接口留一下,下次上线就可以注释掉了
     * @param orderCode
     * @param shopId
     * @return
     */
    @GetMapping("/queryOrderInfoForCloudTakeGood/{orderCode}/{shopId}")
    @ApiModelProperty(value = "根据订单id查询云超订单")
    public QueryOrderInfoForCloudTakeGoodODTO queryOrderInfoForCloudTakeGood(@PathVariable("orderCode") String orderCode, @PathVariable("shopId") Long shopId){
        return cloudService.queryOrderInfoForCloudTakeGood(orderCode, shopId);
    }


    /**
     * 为了兼容性考虑,这个接口留一下,下次上线就可以注释掉了
     * @param orderId
     * @return
     */
    @GetMapping("/completeOrderInfoForCloudTakeGood/{orderId}")
    @ApiModelProperty(value = "根据订单id提云超订单")
    public Boolean completeOrderInfoForCloudTakeGood(@PathVariable("orderId") Long orderId){
        return cloudService.completeOrderInfoForCloudTakeGood(orderId);
    }
}
