package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.SmsMessageClientVo;
import com.pinshang.qingyun.common.service.WeChatSendMessageClient;
import com.pinshang.qingyun.xd.wms.mapper.DeliveryOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.OrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName AbnormalOrderWarnService
 * <AUTHOR>
 * @Date 2022/12/23 14:57
 * @Description AbnormalOrderWarnService
 * @Version 1.0
 */
@Service
public class XdAbnormalOrderWarnService {
    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private WeChatSendMessageClient weChatSendMessageClient;

    public boolean warnAbnormalOrder(String date){
        // 如果日期是空的则执行前一天
        if(StringUtils.isEmpty(date)){
            Date nowDate = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            calendar.add(Calendar.DATE, -1);
            date = DateUtil.get4yMd(calendar.getTime());
        }
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";
        List<String> abnormalDeliveryOrder = deliveryOrderMapper.selectAbnormalDeliveryOrder(beginTime, endTime);
//        List<String> nullShelfDeliveryOrder = deliveryOrderMapper.selectNullShelfDeliveryOrder(beginTime, endTime);
        List<String> deliveryStatusOrder = deliveryOrderMapper.selectAbnormalDeliveryStatusOrder(beginTime, endTime);
        List<String> abnormalStatusOrder = orderMapper.selectAbnormalStatusOrder(beginTime, endTime);
        List<String> abnormalCancelOrder = orderMapper.selectAbnormalCancelOrder(beginTime, endTime);
        List<String> abnormalPickOrder = pickOrderMapper.selectAbnormalPickOrder(beginTime, endTime);
        List<String> abnormalSelfOrder = pickOrderMapper.selectAbnormalSelfOrder(beginTime, endTime);
        Map<String, List<String>> orderCodeMap = new HashMap<>(8);
        orderCodeMap.put("异常配送单", abnormalDeliveryOrder);
//        orderCodeMap.put("货位为空的订单", nullShelfDeliveryOrder);
        orderCodeMap.put("配送单状态异常订单", deliveryStatusOrder);
        orderCodeMap.put("订单状态异常的订单", abnormalStatusOrder);
        orderCodeMap.put("订单取消", abnormalCancelOrder);
        orderCodeMap.put("异常拣货单", abnormalPickOrder);
        orderCodeMap.put("自提异常单", abnormalSelfOrder);

        StringBuilder warningInfo = new StringBuilder();
        for(Map.Entry<String, List<String>> entry : orderCodeMap.entrySet()){
            if(SpringUtil.isNotEmpty(entry.getValue())){
                warningInfo.append(entry.getKey()).append(" 订单号: ");
                entry.getValue().forEach(it -> warningInfo.append(it).append(","));
                warningInfo.append("; \r\n");
            }
        }
        String warningInfoStr = warningInfo.toString();
        if(StringUtils.isNotBlank(warningInfoStr)){
            this.sendSmsMessage(warningInfoStr);
        }
        return true;
    }

    /**
     * 发送微信消息
     * @param warningInfoStr
     */
    public void sendSmsMessage(String warningInfoStr) {
        SmsMessageClientVo smsMessageIDTO = new SmsMessageClientVo();
        //content长度限制
        smsMessageIDTO.setContent(warningInfoStr);
        smsMessageIDTO.setMessageTypeCode(SmsMessageTypeEnums.XD_WMS_INFO_WARN.getCode());
        weChatSendMessageClient.xdMessageWeiXinWarning(smsMessageIDTO);
    }
}
