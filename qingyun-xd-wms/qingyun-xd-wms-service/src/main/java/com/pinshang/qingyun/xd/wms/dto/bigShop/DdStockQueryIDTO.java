package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;


import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DdStockQueryIDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 18:14
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DdStockQueryIDTO implements Serializable {
    private static final long serialVersionUID = -7833677836100849094L;
    @ApiModelProperty(value = "门店id")
    private Long shopId;
    @ApiModelProperty(value = "商品list")
    private List<Long> commodityList;


    public void checkData() {
        QYAssert.isTrue(shopId != null, "门店id不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityList), "商品id不能为空");
    }
}
