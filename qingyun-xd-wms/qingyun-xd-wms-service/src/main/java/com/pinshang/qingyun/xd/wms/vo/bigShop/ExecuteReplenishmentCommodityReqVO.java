package com.pinshang.qingyun.xd.wms.vo.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@ApiModel("ExecuteReplenishmentCommodityReqVO")
public class ExecuteReplenishmentCommodityReqVO {

    @ApiModelProperty("补货类型： 1：排面补货 2：拣货位补货")
    private Integer type = 1;

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("拣货位(2：拣货位补货需要传递)")
    private String pickingPosition;

    /**
     * 补货(出)
     */
    private List<ReplenishmentGoodsAllocationDTO> replenishmentList;

//    /**
//     * 拣货位补货
//     */
//    private PickingPositionReplenishment pickingPositionReplenishment;
//
//    /**
//     * 排面补货
//     */
//    private ShelfPositionReplenishment shelfPositionReplenishment;
//
//    @Data
//    public static class PickingPositionReplenishment{
//
//        @ApiModelProperty("拣货位ID")
//        private Long pickingPositionId;
//
//        @ApiModelProperty("拣货位")
//        private String pickingPosition;
//
//        @ApiModelProperty("拣货位补货数量")
//        private BigDecimal pickingQuantity;
//    }
//
//    @Data
//    public class WarehousePositionReplenishment{
//
//        @ApiModelProperty("存储位Id")
//        private Long warehousePositionId;
//
//        @ApiModelProperty("存储位")
//        private String warehousePosition;
//
//        @ApiModelProperty("存储位补货数量")
//        private BigDecimal warehouseQuantity;
//    }
//
//    @Data
//    public static class ShelfPositionReplenishment{
//
//        @ApiModelProperty("排面补货数量")
//        private BigDecimal shelfQuantity;
//    }



}