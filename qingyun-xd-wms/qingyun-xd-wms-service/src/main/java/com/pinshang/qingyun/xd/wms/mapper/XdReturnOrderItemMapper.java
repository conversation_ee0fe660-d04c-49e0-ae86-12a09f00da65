package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.QueryReturnInfoForCloudTakeGoodItemODTO;
import com.pinshang.qingyun.xd.wms.model.XdReturnOrderItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName XdReturnOrderItemMapper
 * <AUTHOR>
 * @Date 2021/6/17 18:13
 * @Description XdReturnOrderItemMapper
 * @Version 1.0
 */
public interface XdReturnOrderItemMapper extends BaseMapper<XdReturnOrderItemEntity> {
   List<XdReturnOrderItemEntity> selectItemListForCloudReturn(@Param("returnOrderId") Long returnOrderId);

   List<QueryReturnInfoForCloudTakeGoodItemODTO> selectItemListForCloudReturnTakeSelf(@Param("returnOrderId") Long returnOrderId);
}
