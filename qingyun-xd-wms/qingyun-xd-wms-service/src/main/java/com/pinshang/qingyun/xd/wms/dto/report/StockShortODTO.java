package com.pinshang.qingyun.xd.wms.dto.report;

import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/4/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockShortODTO {

    private String shopCode;
    /** 门店名称 */
    private String shopName;


    /** 订单号 */
    private String orderCode;

    /** 出库时间 */
    private Date stockOutTime;


    /** 商品编码 */
    private String commodityCode;
    /** 商品名称 */
    private String commodityName;
    /** 商品规格 */
    private String commoditySpec;


    /** 缺发商品单价 */
    private BigDecimal price;
    /** 缺发件数 */
    private BigDecimal quantity;
    /** 缺发金额 */
    private BigDecimal totalPrice;

    /** 拣货员 */
    private String pickEmployName;

    public String getStockOutTimeStr() {
        if(null != stockOutTime ){
            return DateUtil.getDateFormate(stockOutTime,"yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }
}
