package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseShelfCommodityDTO extends BaseEntity {

    @ApiModelProperty(value = "货位id")
    private Long shelfId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    public void checkData() {
        QYAssert.isTrue(null != shelfId, "仓库货位id不能为空");
        QYAssert.isTrue(null != commodityId, "商品id不能为空");
    }

}
