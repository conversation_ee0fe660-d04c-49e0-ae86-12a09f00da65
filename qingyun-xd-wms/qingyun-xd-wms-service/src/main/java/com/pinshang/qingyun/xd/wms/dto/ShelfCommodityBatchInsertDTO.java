package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShelfCommodityBatchInsertDTO {

    private Long warehouseId;

    private List<ShelfCommodityDTO> dtoList;

    public void checkData() {
        QYAssert.isTrue(null != warehouseId, "仓库id不能为空");
        QYAssert.notEmpty(dtoList, "批量绑定数据不能为空");
    }
}
