package com.pinshang.qingyun.xd.wms.dto.pkg;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageTrackIDTO extends Pagination<PackageTrackIDTO> {

    @ApiModelProperty(value = "送货日期开始时间 yyyy-MM-dd")
    private String orderTimeBegin;
    @ApiModelProperty(value = "送货日期结束时间 yyyy-MM-dd")
    private String orderTimeEnd;

    @ApiModelProperty(value = "配送批次")
    private Integer deliveryBatch;

    @ApiModelProperty(value = "包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货")
    private Integer packageStatus;

    @ApiModelProperty(value = "门店")
    private Long shopId;

    @ApiModelProperty(value = "云超订单号")
    private String orderCode;

    @ApiModelProperty(value = "包裹号")
    private String packageOrderCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "是否少货 0=是 1=否")
    private Integer quantityStatus;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    private List<Long> packageOrderIdList;
}
