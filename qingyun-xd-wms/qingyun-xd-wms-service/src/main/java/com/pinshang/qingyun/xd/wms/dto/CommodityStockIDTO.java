package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 门店库存处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityStockIDTO {
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "商品list")
    private List<CommodityStockItemDTO> commodityList;

    @ApiModelProperty(value = "销售类型:SaleTypeEnum 1 销售 2退货")
    private Integer saleType;

    @ApiModelProperty(value = "用户id")
    private Long userId;
}