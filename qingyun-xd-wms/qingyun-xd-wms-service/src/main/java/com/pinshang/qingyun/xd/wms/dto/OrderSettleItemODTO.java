package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSettleItemODTO {

    private String orderSettleUid; //
    private String kid;
    private long commodityId; // 商品ID
    private String commodityName; // 商品名称
    private String commodityCode; // 商品Code
    private String commoditySpec; // 商品规格
    private Long commodityUnitId;//单位ID
    private Long taxRateId;//税率 ID
    private BigDecimal quantity; // 下单数量
    private BigDecimal unitPrice; // 下单单价
    private BigDecimal totalPrice; // 下单总金额

    private BigDecimal inPrice; // 实际发货单价
    private BigDecimal inQuantity; // 实际发货数量
    private BigDecimal inTotalPrice; // 实际发货总金额


     private BigDecimal outPrice; // 实际发货单价
    private BigDecimal outQuantity; // 实际发货数量
    private BigDecimal outTotalPrice; // 实际发货总金额


    private long categoryFirstId; // 一级分类ID
    private long categorySecondId; // 二级分类ID
    private long commodityThirdId;
    private Long itemId; // 订单明细ID
    private BigDecimal rate; // 税率
    private Date createTime; //
    private String commodityUnit; // 商品单位
    private String categoryFirstName; // 一级分类名称
    private String categorySecondName; // 二级分类名称
    private String commodityThirdName;




    private int status; // 0:无效，1：有效
    private Date updateTime; //

//    public BigDecimal getDeliveryTotalPrice() {
//        return getDeliveryUnitPrice().multiply(getDeliveryQuantity()).setScale(2, RoundingMode.HALF_UP );
//    }


    public BigDecimal getQuantity() {
        return inQuantity;
    }

    public BigDecimal getUnitPrice() {
        return inPrice;
    }

    public BigDecimal getTotalPrice() {
        return inTotalPrice;
    }
}
