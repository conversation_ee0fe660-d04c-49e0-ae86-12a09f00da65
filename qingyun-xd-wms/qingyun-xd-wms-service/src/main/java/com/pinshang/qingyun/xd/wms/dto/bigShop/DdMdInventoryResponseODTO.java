package com.pinshang.qingyun.xd.wms.dto.bigShop;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/11/11
 */
@Data
public class DdMdInventoryResponseODTO {

    private Long commodityId;

    // 货位号id
    private Long goodsAllocationId;
    private String goodsAllocationCode;

    // 库存
    private BigDecimal stockQuantity;

    // 成本价(直接获取t_stall_commodity weight_price值)
    private BigDecimal weightPrice;

    // 包装规格
    private BigDecimal commodityPackageSpec;
}
