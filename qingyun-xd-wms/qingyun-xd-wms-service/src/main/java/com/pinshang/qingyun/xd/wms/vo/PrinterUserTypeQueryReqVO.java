package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.EnableStatusEnums;
import com.pinshang.qingyun.xd.wms.enums.UserTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;

@Data
@ToString
@ApiModel("PrinterUserTypeQueryReqVO")
public class PrinterUserTypeQueryReqVO extends Pagination {

    /**
     * 使用方类型
     * @see com.pinshang.qingyun.xd.wms.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "使用方类型")
    private Integer  userType;

    /**
     * 启用状态，1-启用，0-停用
     * @see com.pinshang.qingyun.base.enums.EnableStatusEnums
     */
    @ApiModelProperty(value = "启用状态")
    private Integer status;

    @ApiModelProperty(value = "搜索关键字")
    private String searchKey;

    public void check(){
        if(Objects.nonNull(userType) && Objects.isNull(UserTypeEnum.getTypeEnumByCode(userType))){
            QYAssert.isTrue(Objects.nonNull(UserTypeEnum.getTypeEnumByCode(userType)),"使用方类型只能为加工点或打包口");
        }
        if(Objects.nonNull(status) && !Objects.equals(status, EnableStatusEnums.ENABLE.getCode())){
            QYAssert.isTrue(Objects.nonNull(UserTypeEnum.getTypeEnumByCode(userType)),"只能查询启用状态的加工点或打包口");
        }
    }

}
