package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息场景：云超商品大仓库存变动同步到es
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DdCommodityLimitStockKafkaVo {
    @ApiModelProperty(value = "门店id")
    private Long shopId;
    @ApiModelProperty(value = "时间戳")
    private Long timestamp;
    @ApiModelProperty(value = "是否有库存")
    private Boolean hasStock;
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    /** 档口id */
    private Long stallId;
    public DdCommodityLimitStockKafkaVo convert(Long shopId, Long commodityId, Boolean hasStock, Long stallId){
        this.shopId = shopId;
        this.timestamp = System.currentTimeMillis();
        this.commodityId = commodityId;
        this.hasStock = hasStock;
        this.stallId = stallId;
        return this;
    }

}
