package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("PartitionPickOrderPageODTO")
public class PartitionPickOrderPageODTO implements Serializable {

    private static final long serialVersionUID = 2390904577506232380L;

    @ApiModelProperty(value = "分区拣货单Id")
    private Long pickPartitionOrderId;

    @ApiModelProperty(value = "分区拣货单号")
    private String pickPartitionOrderCode;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "拣货员Id")
    private Long pickId;

    @ApiModelProperty(value = "拣货分区Id")
    private Long pickAreaId;

    @ApiModelProperty(value = "分区拣货单状态")
    private Integer pickPartitionOrderStatus;

    @ApiModelProperty(value = "拣货人姓名")
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE,fieldName = RenderFieldHelper.Employee.employeeName,keyName = "pickId")
    private String pickEmployeeName;

    @ApiModelProperty(value = "拣货分区名称")
    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA,fieldName = RenderFieldHelper.PickArea.pickAreaName,keyName = "pickAreaId")
    private String pickAreaName;

}
