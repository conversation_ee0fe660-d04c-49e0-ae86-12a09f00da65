package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xd.wms.bo.PrinterUserTypeQueryReqBO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseWorkDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityCount;
import com.pinshang.qingyun.xd.wms.enums.WorkStatusEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseWorkCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseWorkMapper;
import com.pinshang.qingyun.xd.wms.model.WarehouseWork;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryRspVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WarehouseWorkService {

    @Autowired
    private WarehouseWorkMapper warehouseWorkMapper;

    @Autowired
    private WarehouseWorkCommodityMapper warehouseWorkCommodityMapper;

    @Autowired
    private CodeClient codeClient;

    public Integer insertWarehouseWork(WarehouseWorkDTO warehouseDTO) {
        QYAssert.isTrue(!StringUtils.isEmpty(warehouseDTO.getWorkName()), "加工点名称不能为空");

        WarehouseWork warehouseWork = new WarehouseWork();
        warehouseWork.setWorkName(warehouseDTO.getWorkName());
        warehouseWork.setStatus(WorkStatusEnum.ENABLE.getCode());
        warehouseWork.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        String code = codeClient.createCode("WAREHOUSE_WORK_CODE");
        warehouseWork.setWorkNo(code);

        return warehouseWorkMapper.insert(warehouseWork);
    }

    @Transactional
    public Integer updateWarehouseWork(WarehouseWorkDTO warehouseDTO) {
        QYAssert.isTrue(!StringUtils.isEmpty(warehouseDTO.getWorkName()), "加工点名称不能为空");
        QYAssert.isTrue(null != warehouseDTO.getId(), "加工点id不能为空");

        long warehouseId = StockUtils.INSTANCE.warehouseId();
        WarehouseWork warehouseWork = warehouseWorkMapper.selectById(warehouseDTO.getId(), warehouseId);
        QYAssert.isTrue(null != warehouseWork, "加工点不存在");

        WarehouseWork update = new WarehouseWork();
        update.setId(warehouseDTO.getId());
        update.setWorkName(warehouseDTO.getWorkName());
        return warehouseWorkMapper.updateById(update);
    }

    /**
     * 停用/启用加工位
     * @param warehouseDTO
     * @return
     */
    public Integer updateWarehouseWorkStatus(WarehouseWorkDTO warehouseDTO) {
        QYAssert.isTrue(null != warehouseDTO.getId(), "加工点id不能为空");
        QYAssert.isTrue(null != warehouseDTO.getStatus(), "状态不能为空");

        long warehouseId = StockUtils.INSTANCE.warehouseId();
        WarehouseWork warehouseWork = warehouseWorkMapper.selectById(warehouseDTO.getId(), warehouseId);
        QYAssert.isTrue(null != warehouseWork, "加工点不存在");

        //停用需要判断是否有商品
        if (warehouseDTO.getStatus().equals(WorkStatusEnum.DISABLE.getCode())) {
            Integer count = warehouseWorkCommodityMapper.countWarehouseWorkByWorkid(warehouseDTO.getId(), warehouseId);
            QYAssert.isTrue(count<=0,"有加工商品，不可停用");
        }
        WarehouseWork updateData = new WarehouseWork();
        updateData.setId(warehouseDTO.getId());
        updateData.setStatus(warehouseDTO.getStatus());
        updateData.setWarehouseId(warehouseId);
        return warehouseWorkMapper.updateWarehouseWorkStatus(updateData);
    }

    /**
     * 根据状态查询加工点
     * @param status
     * @return
     */
    public List<WarehouseWorkDTO> warehouseWorkByStatus(Integer status) {
        return warehouseWorkMapper.warehouseWorkByStatus(status, StockUtils.INSTANCE.warehouseId());
    }

    /**
     * 加工点列表查询
     * @param data
     * @return
     */
    public MPage<QueryWorkByConditionResult> queryWorByCondition(QueryWorkByConditionDTO data) {
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        data.setWarehouseId(warehouseId);
        MPage<QueryWorkByConditionResult> res = warehouseWorkMapper.queryWorByCondition(data);

        List<WorkCommodityCount> list = warehouseWorkMapper.workCommodityCount(warehouseId);
        if (!list.isEmpty()) {
            Map<Long, Integer> map = list.stream().collect(Collectors.toMap(WorkCommodityCount::getWorkId, WorkCommodityCount::getCount));
            res.getList().forEach(e->{
                e.setCount(map.get(e.getId()));
            });
        }
        return res;
    }

    public WarehouseWork queryWarehouseWorkById(Long warehouseId,Long id){
        LambdaQueryWrapper<WarehouseWork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseWork::getWarehouseId,warehouseId);
        queryWrapper.eq(WarehouseWork::getId,id);
        return  warehouseWorkMapper.selectOne(queryWrapper);
    }

    public List<WarehouseWork> list(List<Long> ids){
        if(SpringUtil.isEmpty(ids)){
            return Collections.emptyList();
        }

        return warehouseWorkMapper.selectList(new LambdaQueryWrapper<WarehouseWork>().in(WarehouseWork::getId,ids));
    }

    public MPage<PrinterUserTypeQueryRspVO> pageByShopId(PrinterUserTypeQueryReqBO bo){
        return warehouseWorkMapper.pageByShopId(bo);
    }
}
