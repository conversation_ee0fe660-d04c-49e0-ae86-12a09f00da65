package com.pinshang.qingyun.xd.wms.enums.bigshop;

import com.pinshang.qingyun.base.enums.BaseEnum;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/23
 * @Version 1.0
 */
public enum BigShopOperateTypeEnum {
    INSERT(1, "新增"),
    UPDATE(2, "修改"),
    DELETE(3,"删除"),
    BATCH_INSERT(4, "批量导入"),
    BATCH_DELETE(5, "批量解绑"),
    SET_SAFE_STOCK(6, "设置安全库存"),
    IMPORT_SAFE_STOCK(7, "导入安全库存"),
    BIND(8, "新增绑定"),
    BATCH_BIND(9, "批量绑定"),
    UNBIND(10, "解绑"),
//    BATCH_UNBIND(11, "批量解绑"),
    ;
    private Integer code;

    private  String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    BigShopOperateTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
