package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.pinshang.qingyun.base.spring.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheConfiguration;
import com.pinshang.qingyun.infrastructure.loadBalancer.starter.EnableQyLoadBalancer;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qingyun.infrastructure.switcher.starter.EnableOnlineSwitchComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
@SpringBootApplication(scanBasePackages = {"com.pinshang.qingyun"})
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan(basePackages = {"com.pinshang.qingyun.xd.wms.mapper"})
@EnableCircuitBreaker
@EnableScheduling
@Import(value = {RedisServiceDefinition.class, FileCacheConfiguration.class})
@EnableAsync
@EnableApolloConfig
@EnableMqComponent
@EnableCatMetrics
@EnableQyLoadBalancer
@EnableOnlineSwitchComponent
public class ApplicationXdWmsService {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationXdWmsService.class, MainArgsPreHandler.argsHandle(args));
    }

    @GetMapping(value={"","/"})
    public String index(){
        return "redirect:/chk.html";
    }

}
