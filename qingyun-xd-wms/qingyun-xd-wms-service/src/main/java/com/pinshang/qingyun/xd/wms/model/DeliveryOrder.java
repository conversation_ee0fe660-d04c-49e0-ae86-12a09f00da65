package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="DeliveryOrder对象", description="仓库配送取货单表")
@TableName("t_xd_delivery_order")
public class DeliveryOrder extends BaseEntity {

    @ApiModelProperty(value = "配送单code")
    private String deliveryCode;

    @ApiModelProperty(value = "订单/退单id")
    private Long orderId;

    @ApiModelProperty(value = "订单/退单编号")
    private String orderCode;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "配送单状态")
    private Integer deliveryStatus;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "收货人")
    private String receiveMan;

    @ApiModelProperty(value = "收货人手机号")
    private String mobile;

    @ApiModelProperty(value = "类型(1配送单，2取货单)")
    private Integer deliveryType;

/*    @ApiModelProperty(value = "送(收)货时间区间")
    private String receiveInterval;*/

    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    @ApiModelProperty(value = "配送人")
    private Integer deliveryUserId;

    @ApiModelProperty(value = "配送开始时间(配送员开始配送)")
    private Long deliveryBeginTime;

    @ApiModelProperty(value = "配送完成时间")
    private Long deliveryEndTime;

    @ApiModelProperty(value = "配送超时时间（记录到秒）")
    private Long deliveryTimeOut;
}
