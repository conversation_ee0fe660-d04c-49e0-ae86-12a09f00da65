package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.bo.PrinterUserTypeQueryReqBO;
import com.pinshang.qingyun.xd.wms.enums.UserTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPackingStationService;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryRspVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class PrinterUserTypeService {

    private final WarehouseWorkService warehouseWorkService;

    public MPage<PrinterUserTypeQueryRspVO> page(PrinterUserTypeQueryReqBO bo) {

       return warehouseWorkService.pageByShopId(bo);

    }
}
