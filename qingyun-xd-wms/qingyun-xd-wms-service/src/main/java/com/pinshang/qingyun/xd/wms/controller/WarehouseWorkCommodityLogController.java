package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.WorkCommodityLogDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityLogListDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseWorkCommodityLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/warehouse/work/commodity/log")
@Api(value = "加工点商品管理日志", tags = "WarehouseWorkCommodityLogController")
public class WarehouseWorkCommodityLogController {

    @Autowired
    private WarehouseWorkCommodityLogService warehouseWorkCommodityLogService;

    @PostMapping("/workCommodityLogList")
    @ApiOperation(value = "查询日志列表", notes = "查询日志 列表")
    public MPage<WorkCommodityLogListDTO> workCommodityLogList(@RequestBody WorkCommodityLogDTO dto) {
        return warehouseWorkCommodityLogService.workCommodityLogList(dto);
    }
}
