package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配送单消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderDTO {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 仓库id (门店id)
     */
    private Long warehouseId;

    /**
     * 配送单状态
     */
    private Integer deliveryStatus;

    /**
     * 配送取货位
     */
    private String shelfNo;

    private Integer deliveryType;

    /**
     * 是否自动退货，0-否，1是
     */
    private Integer ifAutomaticReturns;

    /**
     * 订单类型 XdOrderTypeEnum
     */
    private Integer orderType;


    /**
     * 3仅退款 目前是大店仅退款用，表示不需要处理库存
     */
    private Integer returnType;
}