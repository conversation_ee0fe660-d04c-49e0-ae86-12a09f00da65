package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * 已上货位 返回值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryGoodsAllocatedODTO {

    @ApiModelProperty("排面区库存")
    private BigDecimal shelfAreaStock;

    @ApiModelProperty("来源库区")
    private Integer storageArea;

    @ApiModelProperty("来源库区")
    private String storageAreaName;

    @ApiModelProperty("已上货位")
    private List<GoodsAllocatedODTO> goodsAllocatedList;

    public String getStorageAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.storageArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }
}