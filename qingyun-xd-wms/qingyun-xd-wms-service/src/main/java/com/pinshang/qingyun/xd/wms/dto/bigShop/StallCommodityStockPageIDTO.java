package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/12
 * @Version 1.0
 */
@Data
public class StallCommodityStockPageIDTO extends Pagination {
    @ApiModelProperty("查询类型 1-按档口、2-按档口+库区、3-按档口+货位")
    private Integer type;

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("档口id")
    private Long stallId;

    private List<Long> stallIdList;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区  ")
    private Integer storageArea;

    private List<Integer> storageAreaList;

    @ApiModelProperty("货位id")
    private Long goodsAllocationId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("后台品类")
    private Long cateId;

    @ApiModelProperty("前台品类ID")
    private Long categoryId;

    @ApiModelProperty(value = "前台品名")
    private String commodityAppName;

   @ApiModelProperty("app状态：0-上架，1-下架")
   private Integer appStatus;

   @ApiModelProperty("最大库存")
   private BigDecimal maxStock;

    @ApiModelProperty("最小库存")
    private BigDecimal minStock;

    @ApiModelProperty("最大冻结库存")
    private Integer maxFreezeNumber;

    @ApiModelProperty("最小冻结库存")
    private Integer minFreezeNumber;

    @ApiModelProperty("查询排面拣货存储临时库是否有库存（1有 其他无）")
    private Integer hasStock;

    @ApiModelProperty("搜索条件")
    private String keyword;

    @ApiModelProperty("货位号")
    private String shelfNo;
}
