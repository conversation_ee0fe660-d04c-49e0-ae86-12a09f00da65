package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.bomorder.dto.CommodityAutoProcessDTO;
import com.pinshang.qingyun.bomorder.service.BomOrderClient;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TransferTypeEnum;
import com.pinshang.qingyun.xd.wms.model.bigShop.GoodsAllocation;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodityStock;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 后仓上架  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@Service
public class BackWarehouseService {

    @Autowired
    private StallCommodityStockService stockCommodityStockService;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    @Autowired
    private GoodsAllocationService goodsAllocationService;

    @Autowired
    private DdTransferRecordService ddTransferRecordService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private BomOrderClient bomOrderClient;

    /**
     * 确认上架
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmUp(BackWarehouseUpIDTO req) {
        // 校验请求参数
        validateUpRequest(req);

        // 获取货位信息
        GoodsAllocation goodsAllocation = goodsAllocationService.getGoodsAllocationIdByCode(req.getShopId(), req.getStallId(), req.getGoodsAllocationCode());
        QYAssert.isTrue(Objects.nonNull(goodsAllocation), "货位号不存在或当前档口不可用");

        // 校验货位是否可用(只校验拣货区货位/存储区不存在则初始化)
        if (Objects.equals(goodsAllocation.getStorageArea(), StorageAreaEnum.PICKING_AREA.getCode())) {
            Boolean flag = goodsAllocationCommodityService.checkGoodsAllocationId(req.getShopId(), req.getStallId(), req.getCommodityId(), goodsAllocation.getId());
            QYAssert.isTrue(flag, "货位号不存在或当前档口不可用");
        }

        // 获取当前商品的排面库存
        StallCommodityStock stallCommodityStock = stockCommodityStockService.queryOneStock(req.getShopId(), req.getStallId(), req.getCommodityId());

        String lockKey = StringUtils.joinWith(":", req.getShopId(), req.getStallId(), req.getCommodityId());
        redisLockService.lock(RedisLockEnums.BACK_WAREHOUSE_UP, lockKey, () -> processUp(req, stallCommodityStock, goodsAllocation));

        return Boolean.TRUE;
    }

    /**
     * 校验上架请求参数
     */
    private void validateUpRequest(BackWarehouseUpIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getStallId(), "档口id不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品id不能为空");
        QYAssert.notNull(req.getGoodsAllocationCode(), "货位号不能为空");
        QYAssert.isTrue(Objects.nonNull(req.getUpQuantity()) && req.getUpQuantity().compareTo(BigDecimal.ZERO) > 0 && req.getUpQuantity().compareTo(BigDecimal.valueOf(99999.999)) <= 0, "上架数量范围0-99999.999");
    }

    private void processUp(BackWarehouseUpIDTO req, StallCommodityStock stallCommodityStock, GoodsAllocation goodsAllocation) {
        // 如果存在排面库存，则进行上架处理
        if (Objects.isNull(stallCommodityStock)) {
            return;
        }
        BigDecimal stockQuantity = stallCommodityStock.getStockQuantity();
        BigDecimal upQuantity = req.getUpQuantity();

        // 校验上架数量是否小于排面库存
        boolean canProceed = true;
        if (stockQuantity.compareTo(upQuantity) < 0) {
            canProceed = handleInsufficientStock(req, upQuantity.subtract(stockQuantity));
        }

        if (BooleanUtils.isTrue(canProceed)) {
            // 初始化【存储区】商品信息
            if (Objects.equals(goodsAllocation.getStorageArea(), StorageAreaEnum.WAREHOUSE_AREA.getCode())) {
                GoodsAllocationCommodityInitIDTO idto = buildInitDto(req, goodsAllocation);
                goodsAllocationCommodityService.initWarehouseAreaCommodity(idto);
            }

            // 生成移库单
            createTransferRecord(req, goodsAllocation);
        }
    }

    /**
     * 处理排面库存不足的情况
     *
     * @param remainingQuantity 加工数量
     */
    private boolean handleInsufficientStock(BackWarehouseUpIDTO req, BigDecimal remainingQuantity) {
        // 构建加工 DTO
        CommodityAutoProcessDTO dto = new CommodityAutoProcessDTO();
        dto.setShopId(req.getShopId());
        dto.setStallId(req.getStallId());
        dto.setCommodityId(req.getCommodityId());
        dto.setQuantity(remainingQuantity);

        // 查询是否满足加工
        Boolean canProcess = bomOrderClient.checkAutoProcess(dto);
        QYAssert.isTrue(BooleanUtils.isTrue(canProcess), "排面库存不足");

        // 首次上架
        if (Objects.isNull(req.getNeedBomProcess())) {
            // 满足加工条件 返回前端 提示信息，库存不足,是否自动生成加工单?
            throw new BizLogicException(ApiErrorCodeEnum.PDA_BOM_WARN);
        }

        // 再次上架且需要加工
        if (Objects.equals(req.getNeedBomProcess(), YesOrNoEnums.YES.getCode())) {
            // 生成加工单
            Boolean processFlag = bomOrderClient.autoBomProcess(dto);
            QYAssert.isTrue(BooleanUtils.isTrue(processFlag), "加工失败,请重试");
            return true;
        }

        return false;
    }

    private static GoodsAllocationCommodityInitIDTO buildInitDto(BackWarehouseUpIDTO req, GoodsAllocation goodsAllocation) {
        GoodsAllocationCommodityInitIDTO idto = BeanCloneUtils.copyTo(req, GoodsAllocationCommodityInitIDTO.class);
        GoodsAllocationCommodityListInitIDTO listInitDto = new GoodsAllocationCommodityListInitIDTO();
        listInitDto.setGoodsAllocationId(goodsAllocation.getId());
        listInitDto.setCommodityId(req.getCommodityId());
        idto.setGoodsAllocationCommodityList(Collections.singletonList(listInitDto));
        return idto;
    }

    /**
     * 调用 生成移库单
     */
    private void createTransferRecord(BackWarehouseUpIDTO req, GoodsAllocation goodsAllocation) {
        DdTransferRecordSaveIDTO idto = new DdTransferRecordSaveIDTO();
        idto.setShopId(req.getShopId());
        idto.setStallId(req.getStallId());
        idto.setType(TransferTypeEnum.BACKSTAGE_SHELF.getCode());
        idto.setCommodityId(req.getCommodityId());

        List<DdTransferRecordItemsSaveIDTO> itemsList = new ArrayList<>();
        DdTransferRecordItemsSaveIDTO item = new DdTransferRecordItemsSaveIDTO();
        item.setOutStoreArea(StorageAreaEnum.SHELF_AREA.getCode());
        item.setInStoreArea(goodsAllocation.getStorageArea());
        item.setInGoodsAllocationCode(req.getGoodsAllocationCode());
        item.setMoveQuantity(req.getUpQuantity());
        itemsList.add(item);

        idto.setItemsList(itemsList);
        idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
        ddTransferRecordService.save(idto);
    }


}
