package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocationCommodityDTO {

    private Long shopId;

    private Long areaId;

    private Long stallId;

    /**
     * 库区
     *
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum
     */
    private Integer storageArea;


    private Long commodityId;

    private Long goodsAllocationId;

    @ApiModelProperty("拣货位")
    private String goodsAllocationCode;

    @ApiModelProperty("拣货位最小数量")
    private BigDecimal minStock;

    @ApiModelProperty("拣货位最大数量")
    private BigDecimal maxStock;

    @ApiModelProperty("库存")
    private BigDecimal stock;


}
