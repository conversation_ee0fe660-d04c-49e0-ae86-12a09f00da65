package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/16
 * @Version 1.0
 */
@Data
public class GoodAllocationPrintForPDAODTO {
    @ApiModelProperty("库区 1排面区 2拣货区 3存储区  ")
    private Integer storageArea;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区  ")
    private String storageAreaStr;

    @ApiModelProperty("货位号")
    private String goodsAllocationCode;

}
