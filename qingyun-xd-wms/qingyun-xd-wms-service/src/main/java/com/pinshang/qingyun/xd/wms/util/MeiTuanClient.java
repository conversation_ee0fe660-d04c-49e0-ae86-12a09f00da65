package com.pinshang.qingyun.xd.wms.util;

import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.request.SgOpenRequest;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName MeiTuanClient
 * <AUTHOR>
 * @Date 2023/7/3 13:45
 * @Description MeiTuanClient
 * @Version 1.0
 */
@Slf4j
@Component
public class MeiTuanClient {
    @Value("${pinshang.meituan.sg.appId:122580}")
    private String appId;
    @Value("${pinshang.meituan.sg.appSecret:871eb4c3f6401efd31da822b630c8117}")
    private String appSecret;

    public SystemParam getSystemParam(){
        // 拼接接口认证信息
        return new SystemParam(appId,appSecret);
    }


    public SgOpenResponse execute(SgOpenRequest request) throws Exception {

        // 调用美团的http方法
        SgOpenResponse sgOpenResponse = request.doRequest();

        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        log.warn("requestSig：{}", requestSig);

        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        log.warn("requestResult：{}", requestResult);

        return sgOpenResponse;
    }

}
