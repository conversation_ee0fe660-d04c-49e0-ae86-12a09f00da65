package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.TransferTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@ApiModel("DdTransferRecordSaveIDTO")
public class DdTransferRecordSaveIDTO {

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 类型1-排面补货、2-拣货位补货、3-后仓上架、4-移库
     * @see TransferTypeEnum
     */
    private Integer type;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 移出库区信息
     */
    private List<DdTransferRecordItemsSaveIDTO> itemsList;

}
