package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.PartitionPickItemColorEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("PickPartitionOrderPageODTO")
public class PickPartitionOrderPageODTO implements Serializable {

    private static final long serialVersionUID = 744603476700623777L;

    @ApiModelProperty("拣货子单id")
    private Long pickPartitionOrderId;

    @ApiModelProperty("拣货子单编号")
    private String pickPartitionOrderCode;

    @ApiModelProperty("订单短号")
    private String orderNum;

    @ApiModelProperty("已拣货商品数量")
    private Integer pickedNum = 0;

    @ApiModelProperty("总拣货商品数量")
    private Integer totalPickNum;

    @ApiModelProperty("打包口")
    private String packingPort;

    @JsonIgnore
    private Long pickAreaId;

    @ApiModelProperty("分区")
    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA, fieldName = RenderFieldHelper.PickArea.pickAreaName, keyName = "pickAreaId")
    private String pickAreaName;

    @ApiModelProperty("拣货单状态 0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private Integer pickOrderStatus;

    @ApiModelProperty("拣货子单状态 0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消 4=待交接")
    private Integer pickPartitionOrderStatus;

    @ApiModelProperty("拣货人id")
    private Long pickId;

    @ApiModelProperty("颜色     YELLOW(1, \"黄色-其他\"),\n" +
            "    GREY(2, \"灰色-拣货子单关联的拣货单已取消\"),\n" +
            "    WHITE(3, \"白色-系统时间<拣货预警开始时间\"),\n" +
            "    RED(4, \"红色-系统时间>拣货预约开始时间\"),")
    private Integer itemColor = PartitionPickItemColorEnum.YELLOW.getCode();

    @ApiModelProperty("缺货处理方式 1-缺货时与我电话沟通 2-退款其他商品继续配送（缺货商品直接)")
    private Integer lackProcessMode;

    @ApiModelProperty("收货人手机")
    private String receiveMobile;

    /**
     * 需要计算一下返回结果，如果拣货单状态为拣货中，则返回拣货子单状态，否则返回拣货单状态
     *
     * @return
     */
    public Integer getPickOrderStatus() {
        if (XdPickOrderStatusEnum.MIDDLE.convert().equals(pickOrderStatus)) {

            return pickPartitionOrderStatus;
        }

        return pickOrderStatus;
    }
}
