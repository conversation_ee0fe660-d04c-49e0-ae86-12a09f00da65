package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2021/7/1 11:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopPackageDetailDTO {
    @ApiModelProperty(value = "包裹id")
    private Long id;
    @ApiModelProperty(value = "提货门店")
    private String shopName;
    @ApiModelProperty(value = "包裹号")
    private String orderCode;
    @ApiModelProperty(value = "C端订单编号")
    private String orderId;
    @ApiModelProperty(value = "提货人")
    private String receiveMan;
    @ApiModelProperty(value = "提货人手机号")
    private String receiveMobile;
    @ApiModelProperty(value = "提货日期")
    private Date arrivalTime;
    @ApiModelProperty(value = "详情")
    private List<ShopPackageDetailItemODTO> orderItemList;
    @ApiModelProperty(value = "提货门店id")
    private Long shopId;
    @ApiModelProperty(value = "验收人")
    private String checker;
    @ApiModelProperty(value = "验收时间")
    private Date checkTime;
    @ApiModelProperty(value = "包裹状态 (门店使用)　1= 门店未验证  4=待顾客提货  7＝顾客已提货")
    private Integer packageStatus;
    @ApiModelProperty(value = "是否缺货")
    private Boolean lessGoods;


}
