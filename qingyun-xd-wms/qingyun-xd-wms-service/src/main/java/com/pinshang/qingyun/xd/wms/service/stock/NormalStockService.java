

package com.pinshang.qingyun.xd.wms.service.stock;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.mapper.StockInOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockOutOrderMapper;
import com.pinshang.qingyun.xd.wms.model.StockInOrder;
import com.pinshang.qingyun.xd.wms.model.StockOutOrder;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.StockInventoryOrderService;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 正常库存出入库
 * Created by chenqi on 2019/11/21.
 */
@Transactional
@Service
@Lazy
public class NormalStockService {

    @Autowired
    private StockInOrderMapper stockInOrderMapper;

    @Autowired
    private StockOutOrderMapper stockOutOrderMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private StockInventoryOrderService stockInventoryOrderService;

    @Lazy
    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    /**
     * 正常库存出入库
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @param warehouseId
     * @param userId
     */
    public Pair<Long,String> stockInOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        Pair<Long,String> pair = null;

        if (StockInOutEnum.IN.getCode() == typeEnum.getInOutType()){
            pair = stockIn(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }else if (StockInOutEnum.OUT.getCode() == typeEnum.getInOutType()){
            pair = stockOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }

        return pair;
    }

    /**
     * 入库
     *
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockIn(Pair<Long,String> idAndCode, StockInOutTypeEnums typeEnum,
                                     List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        if(!StockInOutTypeEnums.IN_INVENTORY_PLUS.equals(typeEnum)) {
            LockUtils.checkLock(LockUtils.STOCK_INOUT, "stockIn:" + typeEnum.toString() + idAndCode.getLeft() + idAndCode.getRight());

            //冻结检测
            checkStockLock(commodityList, warehouseId);
        }

        //处理库存+
        if(YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())){
            shopCommodityService.increaseStock(commodityList, warehouseId);
        }else{
            shopCommodityService.increaseStockUnCheck(commodityList, warehouseId);
        }

        if(typeEnum.equals(StockInOutTypeEnums.IN_INVENTORY_PLUS)){
            return null;
        }

        //入库
        String code = codeClient.createCode("XD_STOCK_IN_CODE");
        StockInOrder inOrder = new StockInOrder();
        inOrder.setId(IdWorker.getId());
        inOrder.setOrderCode(code);
        inOrder.setWarehouseId(warehouseId);
        inOrder.setReferId(idAndCode.getLeft());
        inOrder.setReferCode(idAndCode.getRight());
        inOrder.setType(typeEnum.getCode());
        inOrder.setCreateId(userId);
        stockInOrderMapper.insert(inOrder);

//        for (StockItemDTO itemDTO : commodityList) {
//            StockInOrderItem item = new StockInOrderItem();
//            item.setStockInOrderId(inOrder.getId());
//            item.setCommodityId(itemDTO.getCommodityId());
//            item.setStockNumber(itemDTO.getStockNumber());
//            item.setQuantity(itemDTO.getQuantity());
//
//            stockInOrderItemMapper.insert(item);
//        }

        return new ImmutablePair(inOrder.getId(),code);
    }

    /**
     * 出库
     *
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockOut( Pair<Long,String> idAndCode, StockInOutTypeEnums typeEnum,
                                       List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        if(!StockInOutTypeEnums.OUT_INVENTORY_LOSS.equals(typeEnum)) {
            LockUtils.checkLock(LockUtils.STOCK_INOUT, "stockOut:" + typeEnum.toString() + idAndCode.getLeft() + idAndCode.getRight());

            //冻结检测
            checkStockLock(commodityList, warehouseId);
        }

        //库存调整出库，需要直接临时库入库 历史处理方式
        //现改为 “正常库转临” 返回给调用者临时库入库code
        Pair<Long,String> qualityPair = null;
        if(StockInOutTypeEnums.OUT_CHANGE_NORMAL.equals(typeEnum)){
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_CHANGE_QUALITY, commodityList, warehouseId, userId);
            qualityPair = stockServiceAdapter.stockInOut(stockInOutVO);
        }else if(StockInOutTypeEnums.OUT_POS_CODE_NORMAL.equals(typeEnum)){
            //POS折扣特价码出库 直接 临时库入库
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_POS_CODE_QUALITY, commodityList, warehouseId, userId);
            stockServiceAdapter.stockInOut(stockInOutVO);
        }

        //处理库存-
        if(YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())){
            shopCommodityService.reduceStock(commodityList, warehouseId);
        }else{
            shopCommodityService.reduceStockUnCheck(commodityList, warehouseId);
        }

        if(StockInOutTypeEnums.OUT_INVENTORY_LOSS.equals(typeEnum)){
            return null;
        }

        //出库
//        String code = codeClient.createCode("XD_STOCK_OUT_CODE");
        String code = idAndCode.getRight();
        StockOutOrder outOrder = new StockOutOrder();
        outOrder.setId(IdWorker.getId());
        outOrder.setOrderCode(code);
        outOrder.setWarehouseId(warehouseId);
        outOrder.setReferId(idAndCode.getLeft());
        outOrder.setReferCode(idAndCode.getRight());
        outOrder.setType(typeEnum.getCode());
        outOrder.setCreateId(userId);
        stockOutOrderMapper.insert(outOrder);

//        for (StockItemDTO itemDTO : commodityList) {
//            StockOutOrderItem item = new StockOutOrderItem();
//            item.setStockOutOrderId(outOrder.getId());
//            item.setCommodityId(itemDTO.getCommodityId());
//            item.setStockNumber(Math.abs(itemDTO.getStockNumber()));
//            item.setQuantity(itemDTO.getQuantity().abs());
//
//            stockOutOrderItemMapper.insert(item);
//        }

        if(qualityPair != null){
            return qualityPair;
        }
        return new ImmutablePair(outOrder.getId(),code);
    }

    /**
     * 冻结检测
     * @param commodityList
     */
    private void checkStockLock(List<StockItemDTO> commodityList, Long warehouseId){
        List<Long> todoList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        //获取已锁库的所有明细
        List<Long> lockedList = stockInventoryOrderService.lockedInventoryCommodityId(warehouseId);

        lockedList.retainAll(todoList);
        if(SpringUtil.isNotEmpty(lockedList)){
            throw new BizLogicException("商品正在锁库状态，无法进行库存操作");
        }
    }
}