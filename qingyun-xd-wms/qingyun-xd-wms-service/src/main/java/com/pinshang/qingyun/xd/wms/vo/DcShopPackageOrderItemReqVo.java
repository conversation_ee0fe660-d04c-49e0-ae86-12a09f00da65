package com.pinshang.qingyun.xd.wms.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DcShopPackageOrderItemReqVo {

    private Long id;

    /** t_dc_shop_package_order.id */
    private Long packOrderId;

    /** t_xd_sub_order_item.id */
    private Long subOrderItemId;

    /** 商品ID */
    private Long commodityId;

    /** 订货数量 */
    private BigDecimal quantity;

    /** 订货份数 */
    private Integer number;

    /** 打包数量 */
    private BigDecimal packageQuantity;

    /** 打包份数 */
    private Integer packageNumber;

    /** 价格 */
    private BigDecimal price;

    private Long cloudPickItemId;
    private BigDecimal pickQuantity;
    private Integer pickNumber;
}
