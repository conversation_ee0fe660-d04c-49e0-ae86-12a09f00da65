package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import lombok.ToString;

@ToString
public class DdStockInOutExtraVO {

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 库区
     */
    private Integer storageArea;

    /**
     * 货位号
     */
    private Long goodsAllocationId;

    /**
     * 货位号
     */
    private String goodsAllocationCode;


    public static DdStockInOutExtraVO buildShelfAreaDdStockInOutExtraVO(Long commodityId, Long stallId) {
        return buildDdStockInOutExtraVO(commodityId, stallId, StorageAreaEnum.SHELF_AREA.getCode(), null, null);
    }

    /**
     * 参数缺少 临时库的 货位
     * 使用的话，需要补充货位
     *
     * @param commodityId
     * @param stallId
     * @return
     */
    @Deprecated
    public static DdStockInOutExtraVO buildProvisionaAreaDdStockInOutExtraVO(Long commodityId, Long stallId) {
        return buildDdStockInOutExtraVO(commodityId, stallId, StorageAreaEnum.PROVISIONAL_AREA.getCode(), null, null);
    }

    public static DdStockInOutExtraVO buildProvisionaAreaDdStockInOutExtraVO(Long commodityId, Long stallId, Long goodsAllocationId, String goodsAllocationCode) {
        return buildDdStockInOutExtraVO(commodityId, stallId, StorageAreaEnum.PROVISIONAL_AREA.getCode(), goodsAllocationId, goodsAllocationCode);
    }

    public static DdStockInOutExtraVO buildDdStockInOutExtraVO(Long commodityId, Long stallId, Integer storageArea, Long goodsAllocationId, String goodsAllocationCode) {
        DdStockInOutExtraVO ddStockInOutExtraVO = new DdStockInOutExtraVO();
        ddStockInOutExtraVO.setCommodityId(commodityId);
        ddStockInOutExtraVO.setStallId(stallId);
        ddStockInOutExtraVO.setStorageArea(storageArea);
        ddStockInOutExtraVO.setGoodsAllocationId(goodsAllocationId);
        ddStockInOutExtraVO.setGoodsAllocationCode(goodsAllocationCode);
        return ddStockInOutExtraVO;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Integer getStorageArea() {
        return storageArea;
    }

    public void setStorageArea(Integer storageArea) {
        this.storageArea = storageArea;
    }

    public Long getGoodsAllocationId() {
        return goodsAllocationId;
    }

    public void setGoodsAllocationId(Long goodsAllocationId) {
        this.goodsAllocationId = goodsAllocationId;
    }

    public String getGoodsAllocationCode() {
        return goodsAllocationCode;
    }

    public void setGoodsAllocationCode(String goodsAllocationCode) {
        this.goodsAllocationCode = goodsAllocationCode;
    }

}
