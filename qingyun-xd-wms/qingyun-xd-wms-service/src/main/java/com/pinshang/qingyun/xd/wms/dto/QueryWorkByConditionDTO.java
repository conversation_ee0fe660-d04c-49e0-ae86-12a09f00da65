package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkByConditionDTO extends Pagination<QueryWorkByConditionResult> {

    @ApiModelProperty(value = "加工点名称")
    private String workName;

    @ApiModelProperty(value = "状态 1-启用  0-停用")
    private Integer status;

    private Long warehouseId;

}
