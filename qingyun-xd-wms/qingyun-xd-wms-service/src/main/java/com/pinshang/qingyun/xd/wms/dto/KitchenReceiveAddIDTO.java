package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName KitchenReceiveIDTO
 * <AUTHOR>
 * @Date 2021/10/20 17:18
 * @Description KitchenReceiveIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KitchenReceiveAddIDTO {
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("份数")
    private Integer number;
}
