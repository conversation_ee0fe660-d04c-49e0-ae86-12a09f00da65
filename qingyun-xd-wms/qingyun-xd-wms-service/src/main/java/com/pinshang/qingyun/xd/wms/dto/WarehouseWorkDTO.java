package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseWorkDTO extends BaseEntity {

    @ApiModelProperty(value = "加工点编号")
    private String workNo;

    @ApiModelProperty(value = "加工点名字")
    private String workName;

    @ApiModelProperty(value = "仓库Id")
    private Long warehouseId;

    @ApiModelProperty(value = "状态，0-停用(没有绑定货可停用),1-启用")
    private Integer status;
}
