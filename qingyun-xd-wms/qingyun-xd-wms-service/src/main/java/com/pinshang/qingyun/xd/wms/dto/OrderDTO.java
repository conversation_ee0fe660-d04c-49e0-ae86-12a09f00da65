package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDTO {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 仓库id (门店id)
     */
    private Long shopId;

    /**
     * 订单状态
     */
    private XdOrderStatusEnum status;

    /**
     * 配送开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String deliveryBeginTime;
    private String deliveryEndTime;

    /**
     * 订单明细 （订单支付完成时传）
     */
    private List<OrderItemDTO> items;

    /**
     * 订单来源（1APP，2POS, 3小程序,4饿了么）
     */
    private Integer sourceType;

    /**
     * 原始订单(第三方订单)
     */
    private String originalOrderCode;

    @ApiModelProperty(value = "0=普通订单 1=团购订单")
    private Integer orderType;

    private String orderNum;
}