package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudPickSuccessDTO {

    @ApiModelProperty("001:取消订单  003:订单已核销提货 004完成提货核销")
    private String code;

//    @ApiModelProperty("提示的标题")
//    private String reminderTitle;

    @ApiModelProperty("提示的内容")
    private String reminderText;
}
