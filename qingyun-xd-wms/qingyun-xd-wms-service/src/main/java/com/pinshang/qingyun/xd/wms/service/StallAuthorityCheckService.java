package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.StockAllotTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdCheckShopAllotStallAuthorityDTO;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StallAuthorityCheckService {

    private final StallMapper stallMapper;
    private final UserStallClient userStallClient;


    public void checkAllotStallAuthority(DdCheckShopAllotStallAuthorityDTO dto){
        //如果调入申请，调出的档口可以选择门店下的所有档口，调入档口只能选择权限范围下的档口
        Long currentShopId = dto.getCurrentShopId();
        Long targetShopId = dto.getTargetShopId();
        Integer allotType = dto.getAllotType();
        Long userId = dto.getUserId();
        Long outStallId = dto.getOutStallId();
        Long inStallId = dto.getInStallId();
        QYAssert.notNull(currentShopId,"当前登录门店不能为空");
        QYAssert.notNull(targetShopId,"目标门店不能为空");
        QYAssert.notNull(allotType,"调拨类型不能为空");
        QYAssert.notNull(userId,"登录用户不能为空");
        QYAssert.notNull(inStallId,"调入档口不能为空");
        QYAssert.notNull(outStallId,"调出档口不能为空");

        List<Stall> shopStallList = stallMapper.selectList(new LambdaQueryWrapper<Stall>().eq(Stall::getShopId,targetShopId));
        Map<Long, Long> shopStallIdMap = shopStallList.stream().collect(Collectors.toMap(Stall::getId, Stall::getId));
        SelectUserStallIdListIDTO idto =  new SelectUserStallIdListIDTO();
        idto.setUserId(userId);
        idto.setShopId(currentShopId);
        List<Long> userStallIdList = userStallClient.selectUserStallIdList(idto);
        if(Objects.equals(allotType, StockAllotTypeEnums.ALLOT_IN.getCode())){
            if(!shopStallIdMap.containsKey(outStallId)){
                QYAssert.isFalse("调出的档口只能选择门店下的所有档口");
            }

            if(SpringUtil.isEmpty(userStallIdList) || !userStallIdList.contains(inStallId)){
                QYAssert.isFalse("调入档口只能选择用户权限范围下的档口");
            }
        }else{
            if(!shopStallIdMap.containsKey(inStallId)){
                QYAssert.isFalse("调入的档口只能选择门店下的所有档口");
            }

            if(SpringUtil.isEmpty(userStallIdList) || !userStallIdList.contains(outStallId)){
                QYAssert.isFalse("调出档口只能选择用户权限范围下的档口");
            }
        }
    }

    public List<Long> getUserStallIdList(Long userId,Long shopId){
        SelectUserStallIdListIDTO idto =  new SelectUserStallIdListIDTO();
        idto.setUserId(userId);
        idto.setShopId(shopId);
        return userStallClient.selectUserStallIdList(idto);
    }

}
