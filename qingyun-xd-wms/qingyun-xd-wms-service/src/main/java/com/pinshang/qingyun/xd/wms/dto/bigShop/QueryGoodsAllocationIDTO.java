package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryGoodsAllocationIDTO {

    private Long shopId;

    private Long stallId;

    @ApiModelProperty("货位号支持模糊搜索")
    private String goodsAllocationCode;

    @ApiModelProperty("是否绑定商品 1绑定 0未绑定")
    private Integer bind;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty("最多查询条数，不传默认50")
    private Integer maxNum = 50;
}
