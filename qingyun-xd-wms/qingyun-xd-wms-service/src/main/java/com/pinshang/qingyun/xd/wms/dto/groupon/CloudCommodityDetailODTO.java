package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudCommodityDetailODTO {

    private String orderCode;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "提货时间")
    private Date arrivalTime;

    @ApiModelProperty(value = "商品列表")
    List<CloudCommodityDetailDTO> details;
}
