package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderODTO {

    @ApiModelProperty(value = "订单来源")
    private Integer sourceType;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty(value = "订单时间")
    private Date orderTime;

    @ApiModelProperty(value = "取货号")
    private String pickCode;

    @ApiModelProperty(value = "配送员姓名")
    private String deliveryMan;

    @ApiModelProperty(value = "配送员电话")
    private String deliveryMobile;

    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;

    @ApiModelProperty(value = "入库时间")
    private Date stockInTime;

    @ApiModelProperty(value = "入库状态: 0 未入库   1 已入库")
    private Integer stockInStatus;

    @ApiModelProperty(value = "收货人姓名")
    private String receiveMan;

    public String getSourceTypeName(){
        return OrderSourceTypeEnum.getMsg(sourceType);
    }
}
