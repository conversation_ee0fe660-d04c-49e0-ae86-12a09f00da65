package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/15 14:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopPackageOrderODTO {
    @ApiModelProperty(value = "包裹id")
    private Long id;
    @ApiModelProperty(value = "C端订单编号")
    private Long orderId;
    @ApiModelProperty(value = "包裹号")
    private String orderCode;
    @ApiModelProperty(value = "提货人手机号")
    private String receiveMobile;
    @ApiModelProperty(value = "提货人")
    private String receiveMan;
    @ApiModelProperty(value = "下单人手机号")
    private String userMobile;
    @ApiModelProperty(value = "提货门店")
    private String shopName;
    @ApiModelProperty(value = "提货门店id")
    private Long shopId;
    @ApiModelProperty(value = "提货日期")
    private Date arrivalTime;
    @ApiModelProperty(value = "包裹状态 (门店使用)　1= 门店未验证  4=待顾客提货  7＝顾客已提货")
    private Integer packageStatus;
    @ApiModelProperty(value = "修改时间，当顾客已提货时，为完成提货时间")
    private Date updateTime;
    @ApiModelProperty(value = "操作人")
    private String updateMan;
    @ApiModelProperty(value = "下单人手机号加密的123****1234")
    private String receiveMobileMI;
    @ApiModelProperty(value = "提货人手机号加密的123****1234")
    private String userMobileMI;
    @ApiModelProperty(value = "包裹状态")
    private String packageStatusStr;

    public void setReceiveMobile(String receiveMobile) {
        this.receiveMobile = receiveMobile;
        if (StringUtils.isNotEmpty(receiveMobile)) {
            this.receiveMobileMI = receiveMobile.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        }
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
        if (StringUtils.isNotEmpty(userMobile)) {
            this.userMobileMI = userMobile.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        }
    }

    public void setPackageStatus(Integer packageStatus) {
        this.packageStatus = packageStatus;
        switch (packageStatus) {
            case 1:
                this.packageStatusStr = "门店未验收";
                break;
            case 4:
                this.packageStatusStr = "待顾客提货";
                break;
            case 7:
                this.packageStatusStr = "顾客已提货";
                break;
        }
    }


}
