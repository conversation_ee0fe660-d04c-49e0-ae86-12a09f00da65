package com.pinshang.qingyun.xd.wms.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageODTO;
import com.pinshang.qingyun.xd.wms.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.xd.wms.service.StockLogSnapshotService;
import com.pinshang.qingyun.xd.wms.service.StockSnapshotDayService;
import com.pinshang.qingyun.xd.wms.util.ViewExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @since 2022-05-11
 * <AUTHOR> yaot0
 */
@RestController
@RequestMapping("/stockLogSnapshot")
@Api(value = "库存快照", tags = "StockLogSnapshotController", description = "库存快照")
public class StockLogSnapshotController {

    @Autowired
    private StockLogSnapshotService stockLogSnapshotService;

    @Autowired
    private StockSnapshotDayService stockSnapshotDayService;

    @ApiOperation(value = "生成总表", notes = "生成总表")
    @GetMapping("/generateTotalSchedule")
    public Boolean generateTotalSchedule(){
        stockLogSnapshotService.generateTotalSchedule();
        return true;
    }

    @ApiOperation(value = "生成每周快照", notes = "生成每周快照")
    @GetMapping("/generateSnapshotWeekly")
    public Boolean generateSnapshotWeekly(){
        stockLogSnapshotService.generateSnapshotWeekly();
        return true;
    }

    @ApiOperation(value = "单门店快照查询", notes = "单门店快照查询")
    @PostMapping("/page")
    public PageInfo<StockSnapshotPageODTO> page(@RequestBody StockSnapshotPageIDTO idto){
        return stockLogSnapshotService.page(idto);
    }

    @ApiOperation(value = "生成历史周表", notes = "生成历史周表")
    @GetMapping("/insertHistoryWeeklyRecord")
    public Boolean insertHistoryWeeklyRecord(@RequestParam(value = "snapshotDate",required = false) String snapshotDate){
        stockLogSnapshotService.insertHistoryWeeklyRecord(snapshotDate);
        return true;
    }

    @ApiOperation(value = "单门店快照查询-导出", notes = "单门店快照查询-导出")
    @GetMapping("/detailExport")
    public ModelAndView detailExport(StockSnapshotPageIDTO vo){
        vo.initExportPage();
        TablePageInfo<StockSnapshotPageODTO> result = stockLogSnapshotService.page(vo);

        List<StockSnapshotPageODTO> list = result.getList();
        Map<String, List<String>> data = new HashMap<>();
        List<String> dataLst;
        int i = 0;
        if (null != list && !list.isEmpty()) {
            for (StockSnapshotPageODTO dto : list) {
                dataLst = new ArrayList<>();
                dataLst.add(dto.getCommodityCode());
                dataLst.add(dto.getBarCode());
                dataLst.add(dto.getCommodityName());
                dataLst.add(dto.getCommoditySpec());
                dataLst.add(dto.getCommodityUnitName());
                dataLst.add(null == dto.getQuantity()? "" : dto.getQuantity().toString());
                dataLst.add(null == dto.getWeightPrice()? "" : dto.getWeightPrice().toString());
                dataLst.add(null == dto.getTotalPrice() ? "" : dto.getTotalPrice().toString());
                dataLst.add(dto.getCommodityFirstKindName());
                dataLst.add(dto.getCommoditySecondKindName());
                dataLst.add(dto.getCommodityThirdKindName());
                dataLst.add(YesOrNoEnums.YES.getCode().equals(dto.getIsWeight()) ? "是" : "否");
                data.put("key_" + i++, dataLst);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "库存快照商品详情" + "_" + sdf.format(new Date());
        // 参数设置
        map.put("filename", filename);
        map.put("sheetTitle", ExcelSheetTitleEnum.STOCK_SNAPSHOT_DETAIL);
        map.put("data", data);
        map.put("tableHeader", "合计:" +result.getHeader()+ "元  ");
        ViewExcel viewExcel = new ViewExcel();
        return new ModelAndView(viewExcel, map);
    }

    @ApiOperation(value = "门店库存快照", notes = "跑job生成")
    @GetMapping("/insertStockSnapshotDay")
    public Boolean insertStockSnapshotDay(@RequestParam(value = "stockDay",required = false) String stockDay) {
        stockSnapshotDayService.insertStockSnapshotDay(stockDay);
        return Boolean.TRUE;
    }

    @ApiOperation(value = "门店库存快照列表", notes = "门店库存快照列表")
    @PostMapping("/stockList")
    public TablePageInfo<StockSnapshotDayDTO> stockList(@RequestBody StockSnapshotDayIDTO dto) {
        return stockSnapshotDayService.stockList(dto);
    }

    @ApiOperation(value = "门店库存快照列表导出", notes = "门店库存快照列表导出")
    @PostMapping("/exportStockList")
    public List<StockSnapshotDayDTO> exportStockList(@RequestBody StockSnapshotDayIDTO dto) {
        List<StockSnapshotDayDTO> list = new ArrayList<StockSnapshotDayDTO>();
        dto.initExportPage();
        TablePageInfo<StockSnapshotDayDTO> page = stockSnapshotDayService.stockList(dto);
        if (page.getSize() > 0) {
            ObjectMapper objectMapper = new ObjectMapper();
            StockSnapshotDayDTO stockSnapshotDayDTO = objectMapper.convertValue(page.getHeader(), StockSnapshotDayDTO.class);
            list.add(stockSnapshotDayDTO);
            list.addAll(page.getList());
        }
        return list;
    }
}
