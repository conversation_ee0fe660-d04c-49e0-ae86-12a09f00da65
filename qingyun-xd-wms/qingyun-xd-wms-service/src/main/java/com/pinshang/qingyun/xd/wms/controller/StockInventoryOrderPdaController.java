package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.dto.InventoryOrderItemPage;
import com.pinshang.qingyun.xd.wms.dto.InventoryOrderPdaPageODTO;
import com.pinshang.qingyun.xd.wms.dto.OrderProgressIDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.StockInventoryOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  注意:
 *  点击盘点按钮锁库确认接口走网页接口不新写(StockInventoryOrderController#lockInventory)
 *  保存盘点单,走网页接口不新写(StockInventoryOrderController#saveInventoryNumber)
 *  生成盘点单和确认盘点单在网页后台操作，不走PDA
 */
@RestController
@RequestMapping("/stockInventoryOrder/pda")
@Api(value = "PDA盘点管理", tags = "StockInventoryOrderPdaController")
public class StockInventoryOrderPdaController {
    @Autowired
    private StockInventoryOrderService stockInventoryOrderService;

    @PostMapping("/page")
    @ApiOperation(value = "PDA盘点计划列表", notes = "PDA盘点计划列表")
    public InventoryOrderPdaPageODTO pdaPage() {
        return stockInventoryOrderService.stockInventoryOrderPdaPage();
    }

//    @GetMapping("/detail/{id}")
//    @ApiOperation(value = "PDA根据id查询盘点单详情", notes = "PDA根据id查询盘点单详情")
//    public InventoryOrderPdaDetailODTO detailById(@PathVariable("id") Long id) {
//        return stockInventoryOrderService.stockInventoryOrderPdaDetail(id);
//    }

//    @PostMapping("/orderItemPage")
//    @ApiOperation(value = "PDA盘点页面商品列表", notes = "PDA盘点页面商品列表")
//    public MPage<InventoryOrderItemPage> orderItemPage(@RequestBody InventoryOrderItemPageQuery query) {
//        QYAssert.isTrue(query != null, "参数不能为空");
//        QYAssert.isTrue(query.getInventoryOrderId() != null, "盘点单id不能为空");
//        //走已有网页端搜索
//        MPage<InventoryOrderItemPage> mPage = stockInventoryOrderService.inventoryOrderItemPage(query);
//        QYAssert.isTrue(mPage != null&& CollectionUtils.isNotEmpty(mPage.getList()), "商品没有盘点信息");
//        return mPage;
//    }

    @PostMapping("/orderProgressDetail")
    @ApiOperation(value = "PDA盘点进度详情", notes = "PDA盘点进度详情")
    public MPage<InventoryOrderItemPage> orderProgressDetail(@RequestBody OrderProgressIDTO idto) {
        QYAssert.isTrue(idto != null, "参数不能为空");
        QYAssert.isTrue(idto.getInventoryOrderId() != null, "盘点单id不能为空");
        MPage<InventoryOrderItemPage> itemPage = stockInventoryOrderService.orderProgressDetail(idto);
        return itemPage;
    }

}
