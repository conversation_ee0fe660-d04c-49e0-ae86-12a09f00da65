package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 订单打包口订单占用
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@ToString
@ApiModel("DdPackingStationOrderODTO")
public class DdPackingStationOrderODTO {

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("订单编码")
    private String orderCode;

    @ApiModelProperty("分区拣货子单编码")
    private String pickPartitionOrderCode;

    private Long pickAreaId;

    @ApiModelProperty("拣货分区")
    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA, fieldName = RenderFieldHelper.PickArea.pickAreaName, keyName = "pickAreaId")
    private String pickAreaName;

    private Integer pickPartitionStatus;

    @ApiModelProperty("分区拣货单状态(0=待拣货，1＝拣货中，2＝待交接，3＝已取消)")
    private String pickPartitionStatusName;

    private Long pickId;

    @ApiModelProperty("拣货员")
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "pickId")
    private String pickName;

    private Integer orderStatus;

    @ApiModelProperty("订单状态:0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定")
    private String orderStatusName;

    public String getOrderStatusName() {
        return Objects.isNull(orderStatus) ? "" : XdOrderStatusEnum.getName(orderStatus);
    }

    public String getPickPartitionStatusName() {
        return Optional.ofNullable(pickPartitionStatus)
                .map(XdPickOrderStatusEnum::getByCode)
                .map(XdPickOrderStatusEnum::getRemark)
                .orElse("");
    }

}