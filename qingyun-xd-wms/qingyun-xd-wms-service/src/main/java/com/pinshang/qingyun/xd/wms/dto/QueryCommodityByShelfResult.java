package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCommodityByShelfResult {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品编号")
    private String commodityCode;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "库存")
    private Long stockNumber;

    @ApiModelProperty(value = "是否锁定库存 0 未锁定 1 已锁定")
    private Integer locked;

    @ApiModelProperty(value = "拣货单状态(0=未拣货，1＝拣货中")
    private Integer pickStatus;

    @ApiModelProperty(value = "货位id")
    private Long shelfId;

    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "是否称重  1称重  0非称重")
    private Integer isWeight;

    @ApiModelProperty(value = "是否速冻  1称重  0非称重")
    private Integer commodityIsQuickFreeze;

    @ApiModelProperty(value = "存储条件")
    private String storageCondition;
}
