package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PickPathDTO {

    @ApiModelProperty("区域")
    @ExcelProperty("区域")
    private String areaCode;

    @ApiModelProperty("货位号")
    @ExcelProperty("货位号")
    private String goodsAllocationCode;

    @ApiModelProperty("拣货路径顺序")
    @ExcelProperty("拣货路径顺序")
    private Integer sortNum;
}
