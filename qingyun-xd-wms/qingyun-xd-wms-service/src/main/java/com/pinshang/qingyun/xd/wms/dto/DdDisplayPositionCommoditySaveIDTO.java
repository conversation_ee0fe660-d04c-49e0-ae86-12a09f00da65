package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;


@Data
@ToString
@ApiModel("DdDisplayPositionCommoditySaveIDTO")
public class DdDisplayPositionCommoditySaveIDTO {
    private Long id;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("所属区域")
    private Long areaId;

    @ApiModelProperty("所属档口")
    private Long stallId;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("陈列位最小数量")
    private BigDecimal minStock;

    @ApiModelProperty("陈列位最大数量")
    private BigDecimal maxStock;

    @ApiModelProperty("商品code")
    private String commodityCode;

    @ApiModelProperty("陈列位id")
    private String displayPositionName;


}