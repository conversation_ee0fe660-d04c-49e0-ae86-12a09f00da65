package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.service.ShopStockTrackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName ShopStockTrackController
 * <AUTHOR>
 * @Date 2022/11/10 20:03
 * @Description ShopStockTrackController
 * @Version 1.0
 */
@RestController
@RequestMapping("ShopStockTrackController")
@Api(value = "门店", tags = "ShopStockTrackController", description = "门店")
public class ShopStockTrackController {
    @Autowired
    private ShopStockTrackService service;


    @GetMapping("/insertStockList")
    @ApiOperation(value = "及时达门店在线商品跟踪", notes = "及时达门店在线商品跟踪")
    public Boolean insertStockList() {
        return service.insertStockList();
    }
}
