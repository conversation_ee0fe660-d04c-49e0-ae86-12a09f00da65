package com.pinshang.qingyun.xd.wms.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StockAllotOrderSupportDTO {

    private Long id;

    /**
     * 单据号
     */
    private String orderCode;

    /**
     * 详情id
     */
    private Long itemId;

    /**
     * 入库门店
     */
    private Long inShopId;
    
    /**
     * 调入档口ID
     */
    private Long inStallId;

    /**
     * 出库门店
     */
    private Long outShopId;
    
    /**
     * 调出档口ID
     */
    private Long outStallId;

    /**
     * 出库时间
     */
    private Date outTime;

    /**
     * 入库时间
     */
    private Date inTime;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 申请数量
     */
    private BigDecimal applyQuantity;

    /**
     * 出库数量
     */
    private BigDecimal outQuantity;

    /**
     * 入库数量
     */
    private BigDecimal inQuantity;
}
