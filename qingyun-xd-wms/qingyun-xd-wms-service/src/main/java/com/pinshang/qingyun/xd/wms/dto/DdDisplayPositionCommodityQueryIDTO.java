package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
@ApiModel("DdDisplayPositionCommodityQueryIDTO")
public class DdDisplayPositionCommodityQueryIDTO {

    @ApiModelProperty("陈列位id")
    private Long ddDisplayPositionId;

    @ApiModelProperty("商品查询参数 扫描或输入商品条码或名称")
    private String commodityQueryParam;


    public void checkData() {
        QYAssert.isTrue(Objects.nonNull(ddDisplayPositionId), "陈列位不能为空");
        QYAssert.isTrue(SpringUtil.hasText(commodityQueryParam), "商品查询参数不能为空");
    }

}
