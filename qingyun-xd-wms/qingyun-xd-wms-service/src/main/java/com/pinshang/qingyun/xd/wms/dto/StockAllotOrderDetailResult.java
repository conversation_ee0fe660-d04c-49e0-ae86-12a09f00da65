package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderDetailResult extends BaseEntity {

    @ApiModelProperty(value = "申请单号")
    private String orderCode;
    @ApiModelProperty(value = "状态 10待审核 20审核通过=待出库 30出库完成=待入库 40入库完成=完成 50驳回")
    private Integer status;
    @ApiModelProperty(value = "调入门店")
    private Long inShopId;
    @ApiModelProperty(value = "调入档口")
    private Long inStallId;
    @ApiModelProperty(value = "调入档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "inStallId")
    private String inStallName;
    @ApiModelProperty(value = "调入门店描述")
    private String inShopTxt;
    @ApiModelProperty(value = "调出门店")
    private Long outShopId;
    @ApiModelProperty(value = "调出档口")
    private Long outStallId;
    @ApiModelProperty(value = "调出档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "outStallId")
    private String outStallName;
    @ApiModelProperty(value = "调出门店描述")
    private String outShopTxt;
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;
    @ApiModelProperty(value = "商品信息")
    private List<StockAllotOrderDetailItemResult> items;

}
