package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
public class DdCommodityLimitPageIDTO extends Pagination {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("大类ID")
    private Long categoryFirstId;
    @ApiModelProperty("中类ID")
    private Long categorySecondId;
    @ApiModelProperty("小类ID")
    private Long categoryThirdId;

    @ApiModelProperty("生效方式: 1 不循环生效 2 每天循环生效")
    private Integer effectType;

    @ApiModelProperty("上架状态: 0 上架 1 下架")
    private Integer appStatus;

    private List<Long> stallIdList;

}
