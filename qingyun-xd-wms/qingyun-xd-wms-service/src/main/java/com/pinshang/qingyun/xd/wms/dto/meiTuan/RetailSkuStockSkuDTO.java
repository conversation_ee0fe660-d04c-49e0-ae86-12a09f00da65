package com.pinshang.qingyun.xd.wms.dto.meiTuan;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName RetailSkuStockSkuDTO
 * <AUTHOR>
 * @Date 2023/7/21 10:48
 * @Description RetailSkuStockSkuDTO
 * @Version 1.0
 */
public class RetailSkuStockSkuDTO {

    @ApiModelProperty("取我们系统的commodity_code")
    private String sku_id;

    @ApiModelProperty("sku的库存，传非负整数，若传\"*\"默认为系统当前允许的最大值99999")
    private String stock;

    public RetailSkuStockSkuDTO() {
    }

    public RetailSkuStockSkuDTO(String sku_id, String stock) {
        this.sku_id = sku_id;
        this.stock = stock;
    }

    public String getSku_id() {
        return sku_id;
    }

    public void setSku_id(String sku_id) {
        this.sku_id = sku_id;
    }

    public String getStock() {
        return stock;
    }

    public void setStock(String stock) {
        this.stock = stock;
    }

    @Override
    public String toString() {
        return "RetailSkuStockSkuDTO{" +
                "sku_id='" + sku_id + '\'' +
                ", stock='" + stock + '\'' +
                '}';
    }
}
