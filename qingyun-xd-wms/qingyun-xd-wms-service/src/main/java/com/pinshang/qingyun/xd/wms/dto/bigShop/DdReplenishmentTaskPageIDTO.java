package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel("DdReplenishmentTaskPageIDTO")
public class DdReplenishmentTaskPageIDTO extends Pagination {
    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("任务来源，1-系统预警，2-手动添加")
    private Integer taskSource;

    @ApiModelProperty("任务类型，1-排面补货，2-拣货区补货")
    private Integer taskType;

    @ApiModelProperty("对应单据编号")
    private String transferCode;

    @ApiModelProperty("任务状态，1-生效中，2-已过期")
    private Integer status;

    @ApiModelProperty("是否完成补货，0-未完成，1-已完成")
    private Integer replenishedStatus;

    @ApiModelProperty("任务生成开始时间")
    private String startTime;

    @ApiModelProperty("任务生成结束时间")
    private String endTime;

    private List<Long> stallIdList;

}