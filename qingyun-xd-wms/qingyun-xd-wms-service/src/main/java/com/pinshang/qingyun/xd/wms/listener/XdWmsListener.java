package com.pinshang.qingyun.xd.wms.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.shop.ShopCommodityBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.xd.*;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.shop.dto.MdShopStatusODTO;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.xd.order.dto.ReturnOrderCommdityODTO;
import com.pinshang.qingyun.xd.order.service.XdReturnOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseDefaultDTO;
import com.pinshang.qingyun.xd.wms.enums.OrderTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.OrderMapper;
import com.pinshang.qingyun.xd.wms.model.Order;
import com.pinshang.qingyun.xd.wms.service.CancelledOrderService;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import com.pinshang.qingyun.xd.wms.service.groupon.CloudReturnOrderXjService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.XdWmsConstantUtil;
import com.pinshang.qingyun.xd.wms.vo.InventoryInitialItemVO;
import com.pinshang.qingyun.xd.wms.vo.InventoryInitialVO;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.BigShopCommodityUpDownKafkaVo;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by chenqi on 2019/12/02.
 */
@Component
@Slf4j
public class XdWmsListener {
    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private WarehouseShelfService warehouseShelfService;

    @Autowired
    private XdReturnOrderClient xdReturnOrderClient;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private CancelledOrderService cancelledOrderService;

    @Autowired
    private CloudReturnOrderXjService cloudReturnOrderXjService;

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    @Autowired
    private ShopStatusClient shopStatusClient;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;


    /**
     * 订单状态变更后，通知拣货单、配送单
     */
    @KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.XD_ORDER_CHANGE_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}" + KafkaTopicConstant.XD_ORDER_CHANGE_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.XD_ORDER_CHANGE_TOPIC
            },
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory")
    public void orderChangeBroadcast(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

        if (messageWrapper != null && messageWrapper.getData() != null) {
            OrderDTO orderDTO = JSON.parseObject(messageWrapper.getData().toString(), OrderDTO.class);
            log.info("收到订单状态变更通知, orderCode:{}, orderStatus:{} ", orderDTO.getOrderCode(), orderDTO.getStatus().getCode());
            if (OrderTypeEnum.CLOUD.getCode().equals(orderDTO.getOrderType()) || OrderTypeEnum.CLOUD_GROUP.getCode().equals(orderDTO.getOrderType())
                    || OrderSourceTypeEnum.JIULANG.getCode().equals(orderDTO.getSourceType()) || OrderSourceTypeEnum.LIQING.getCode().equals(orderDTO.getSourceType()))
                return;


            if (XdOrderStatusEnum.WAITING_PICK.getCode() == orderDTO.getStatus().getCode()) {
                MdShopStatusODTO shopStatusByShopId = shopStatusClient.getShopStatusByShopId(orderDTO.getShopId());
                if (Objects.nonNull(shopStatusByShopId) && PickingMethodEnum.ZONE_ORDER_PICKING.getCode().equals(shopStatusByShopId.getPickingMethod())) {
                    ddPickPartitionOrderService.createPickOrder(orderDTO);
                } else {
                    pickOrderService.createPickOrder(orderDTO);
                }
            } else if (XdOrderStatusEnum.CANCEL.getCode() == orderDTO.getStatus().getCode()) {
                pickOrderService.cancelPickOrder(orderDTO.getOrderId(), null);
            }
        }
    }

    /**
     * 退单状态变更后，通知取货单
     */
    @KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.XD_RETURN_ORDER_CHANGE_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}" + KafkaTopicConstant.XD_RETURN_ORDER_CHANGE_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.XD_RETURN_ORDER_CHANGE_TOPIC
            },
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory")
    public void returnOrderChangeBroadcast(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

        if (messageWrapper != null && messageWrapper.getData() != null) {
            ReturnOrderDTO returnOrderDTO = JSON.parseObject(messageWrapper.getData().toString(), ReturnOrderDTO.class);

        }
    }

    /**
     * 配送单状态变更后，通知
     */
    @KafkaListener(
            id = "${application.name.switch}" + KafkaTopicConstant.XD_DELIVERY_CHANGE_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}" + KafkaTopicConstant.XD_DELIVERY_CHANGE_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.XD_DELIVERY_CHANGE_TOPIC
            },
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory")
    public void deliveryOrderChangeBroadcast(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

        if (messageWrapper != null && messageWrapper.getData() != null) {
            DeliveryOrderDTO deliveryOrderDTO = JSON.parseObject(messageWrapper.getData().toString(), DeliveryOrderDTO.class);
            log.info("收到配送单状态变更通知, orderCode:{}, deliveryStatus:{}, deliveryType:{} "
                    , deliveryOrderDTO.getOrderCode(), deliveryOrderDTO.getDeliveryStatus(), deliveryOrderDTO.getDeliveryType());
            // 配送单 配送中    取货单 取货完成
            // px 后台强制配送完成/失败
            if (XdDeliveryOrderTypeEnum.DELIVERY.getCode() == deliveryOrderDTO.getDeliveryType()
                    && (XdDeliveryOrderStatusEnum.DISTRIBUTION.getCode() == deliveryOrderDTO.getDeliveryStatus()
                    || XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode() == deliveryOrderDTO.getDeliveryStatus()
                    || XdDeliveryOrderStatusEnum.DELIVERY_FAILURE.getCode() == deliveryOrderDTO.getDeliveryStatus())) {

                Long warehouseId = deliveryOrderDTO.getWarehouseId();

                MdShopStatusODTO shopStatusByShopId = shopStatusClient.getShopStatusByShopId(warehouseId);
                Long orderId = deliveryOrderDTO.getOrderId();
                if (Objects.nonNull(shopStatusByShopId) && PickingMethodEnum.ZONE_ORDER_PICKING.getCode().equals(shopStatusByShopId.getPickingMethod())) {
                    ddPickPartitionOrderService.releasePackingStation(orderId);

                    // 骑手取货 更新分区拣货单 打包时间 打包人=系统
                    if (XdDeliveryOrderStatusEnum.DISTRIBUTION.getCode() == deliveryOrderDTO.getDeliveryStatus()) {
                        pickOrderService.updatePackingIdAndEndTimeByOrderId(orderId, XdWmsConstantUtil.SYSTEM_PICK_ID);
                    }
                }

                TokenInfo tokenInfo = new TokenInfo();
                tokenInfo.setShopId(warehouseId);
                FastThreadLocalUtil.setQY(tokenInfo);
                warehouseShelfService.completeShelfDelivery(orderId, deliveryOrderDTO.getShelfNo());
            } else if (XdDeliveryOrderTypeEnum.PICKUP.getCode() == deliveryOrderDTO.getDeliveryType()
                    && XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode() == deliveryOrderDTO.getDeliveryStatus()) {

                if (Objects.nonNull(deliveryOrderDTO.getReturnType()) && Objects.equals(deliveryOrderDTO.getReturnType(), 3)) {
                    return;
                }

                // 取货完成 要查退单信息入质检库
                List<ReturnOrderCommdityODTO> returnCommoditys = xdReturnOrderClient.rerurnOrderData(deliveryOrderDTO.getOrderId(), deliveryOrderDTO.getOrderCode());
                QYAssert.isTrue(SpringUtil.isNotEmpty(returnCommoditys), deliveryOrderDTO.getOrderId() + " 退单id： {} 未查询到返回内容");
                log.info("退单id： {} 查询到返回内容 -> {} ", deliveryOrderDTO.getOrderId(), JsonUtil.java2json(returnCommoditys));

                ImmutablePair pair = new ImmutablePair(deliveryOrderDTO.getOrderId(), deliveryOrderDTO.getOrderCode());
                List<StockItemDTO> abnormalList;
                // 自动退款——> 配送失败的情况 查看实际拣货数量 否则查看实际退单情况
                if (YesOrNoEnums.YES.getCode().equals(deliveryOrderDTO.getIfAutomaticReturns())) {
                    abnormalList = toAbnormalCommodityFromPick(returnCommoditys.get(0).getOrderId());
                } else {
                    abnormalList = toAbnormalCommodity(returnCommoditys);
                }

                // 云超订单退货期初后返回
                if (null != deliveryOrderDTO.getOrderType() && XdOrderTypeEnum.CLOUDXJ.getCode() == deliveryOrderDTO.getOrderType()) {
                    cloudReturnPriceIni(deliveryOrderDTO, abnormalList, returnCommoditys.get(0).getOrderId());
                    return;
                }

                // 团购退货入正常库
                if (null != deliveryOrderDTO.getOrderType() && XdOrderTypeEnum.GROUPON.getCode() == deliveryOrderDTO.getOrderType()) {

                    StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, StockInOutTypeEnums.IN_GROUPON_NORMAL
                            , abnormalList, deliveryOrderDTO.getWarehouseId(), StockUtils.INSTANCE.userId());
                    stockServiceAdapter.stockInOut(stockInOutVO);
                } else {
                    StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(pair, StockInOutTypeEnums.IN_CLIENT_QUALITY
                            , abnormalList, deliveryOrderDTO.getWarehouseId(), StockUtils.INSTANCE.userId());
                    stockServiceAdapter.stockInOut(stockInOutVO);
                }
            }
        }
    }

    private List<StockItemDTO> toAbnormalCommodityFromPick(Long orderId) {
        // 原始单号查看拣货情况
        PickOrderMqDTO realPick = pickOrderService.pickOrderByOrderId(orderId);
        List<PickOrderItemMqDTO> realPickItem = realPick.getItems().stream()
                .filter(item -> item.getPickNumber() > 0).collect(Collectors.toList());

        Set<Long> stallIdSet = realPickItem.stream().map(PickOrderItemMqDTO::getStallId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap =
                ddTempWarehouseAllocationService.getStallIdProvisionalAreaGoodsAllocationMap(stallIdSet, TempOperationTypeEnum.APP_RETURN);

        List<StockItemDTO> list = new ArrayList<>();
        for (PickOrderItemMqDTO itemDTO : realPickItem) {
            Long stallId = itemDTO.getStallId();

            DdStockInOutExtraVO ddStockInOutExtraVO = buildAbnormalCommodity(stallId, itemDTO.getCommodityId(), stallIdProvisionalAreaGoodsAllocationMap);
            list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getPickNumber(), itemDTO.getPickQuantity(), ddStockInOutExtraVO));
        }
        return list;
    }

    private List<StockItemDTO> toAbnormalCommodity(List<ReturnOrderCommdityODTO> returnCommoditys) {

        Set<Long> stallIdSet = returnCommoditys.stream().map(ReturnOrderCommdityODTO::getStallId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap =
                ddTempWarehouseAllocationService.getStallIdProvisionalAreaGoodsAllocationMap(stallIdSet, TempOperationTypeEnum.APP_RETURN);

        List<StockItemDTO> list = new ArrayList<>();
        for (ReturnOrderCommdityODTO itemDTO : returnCommoditys) {
            Long stallId = itemDTO.getStallId();

            DdStockInOutExtraVO ddStockInOutExtraVO = buildAbnormalCommodity(stallId, itemDTO.getCommodityId(), stallIdProvisionalAreaGoodsAllocationMap);
            list.add(new StockItemDTO(itemDTO.getCommodityId(), itemDTO.getNumber(), itemDTO.getQuantity(), ddStockInOutExtraVO));
        }

        return list;
    }

    private static DdStockInOutExtraVO buildAbnormalCommodity(Long stallId, Long commodityId, Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap) {
        DdStockInOutExtraVO ddStockInOutExtraVO = null;
        if (Objects.nonNull(stallId)) {
            // 大店设置额外参数
            DdTempWarehouseDefaultDTO ddTempWarehouseDefaultDTO = stallIdProvisionalAreaGoodsAllocationMap.get(stallId);
            Long goodsAllocationId = Optional.ofNullable(ddTempWarehouseDefaultDTO).map(DdTempWarehouseDefaultDTO::getGoodsAllocationId).orElse(null);
            String goodsAllocationCode = Optional.ofNullable(ddTempWarehouseDefaultDTO).map(DdTempWarehouseDefaultDTO::getGoodsAllocationCode).orElse(null);
            ddStockInOutExtraVO = DdStockInOutExtraVO.buildProvisionaAreaDdStockInOutExtraVO(commodityId, stallId, goodsAllocationId, goodsAllocationCode);
        }
        return ddStockInOutExtraVO;
    }


    /**
     * 饿了么订单取消时候接收消息
     */
    @KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.XD_ELM_CANCEL_ORDER_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = "${application.name.switch}" + KafkaTopicConstant.XD_ELM_CANCEL_ORDER_TOPIC,
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory")
    public void elemCancelledOrder(String message) {
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

        if (messageWrapper != null && messageWrapper.getData() != null) {
            CancelledOrderDTO cancelledOrderDTO = JSON.parseObject(messageWrapper.getData().toString(), CancelledOrderDTO.class);
            cancelledOrderService.elemCancelledOrder(cancelledOrderDTO);
        }
    }

    /**
     * 云超订单退货期初
     *
     * @param deliveryOrderDTO
     * @param returnCommodities
     */
    private void cloudReturnPriceIni(DeliveryOrderDTO deliveryOrderDTO, List<StockItemDTO> returnCommodities, Long orderId) {
        InventoryInitialVO inventoryInitialVO = new InventoryInitialVO();
        inventoryInitialVO.setReferOrderId(deliveryOrderDTO.getOrderId());
        inventoryInitialVO.setReferOrderCode(deliveryOrderDTO.getOrderCode());
        // 获取云超门店的storeId
        inventoryInitialVO.setShopId(deliveryOrderDTO.getWarehouseId());
        // 获取该云超门店的商品的零售价
        List<Long> commodityIds = returnCommodities.stream().map(StockItemDTO::getCommodityId).distinct().collect(Collectors.toList());
        Map<Long, List<StockItemDTO>> returnCommodityMap = returnCommodities.stream().collect(Collectors.groupingBy(StockItemDTO::getCommodityId));
        List<InventoryInitialItemVO> itemList = new ArrayList<>();
        InventoryInitialItemVO inventoryInitialItemVO;
        for (Long commodityId : commodityIds) {
            inventoryInitialItemVO = new InventoryInitialItemVO();
            inventoryInitialItemVO.setCommodityId(commodityId);
            List<StockItemDTO> returnCommodityNum = returnCommodityMap.get(commodityId);
            inventoryInitialItemVO.setNumber(new BigDecimal(returnCommodityNum.stream().map(StockItemDTO::getStockNumber).reduce(0, Integer::sum)));
            inventoryInitialItemVO.setQuantity(returnCommodityNum.stream().map(StockItemDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            itemList.add(inventoryInitialItemVO);
        }
        inventoryInitialVO.setItemList(itemList);
        inventoryInitialVO.setStockInOutTypeEnums(StockInOutTypeEnums.IN_CLIENT_QUALITY);
        inventoryInitialVO.setBusinessTypeEnum(ShopCommodityBusinessTypeEnum.CLOUD_RETURN);
        Order order = orderMapper.getOneById(orderId);
        if (order != null && XdOrderStatusEnum.DELIVERY_FAIL.getCode() == order.getOrderStatus()) {
            inventoryInitialVO.setBusinessTypeEnum(ShopCommodityBusinessTypeEnum.CLOUD_DELIVERY_FAILED);
            inventoryInitialVO.setOrderCode(order.getOrderCode());
        }
        cloudReturnOrderXjService.cloudInventoryInitial(inventoryInitialVO);
    }

    /**
     * 前置仓商品上下架
     * 消费时，添加判断，非大店return，大店处理逻辑，门店 商品 到底在哪个档口(t_stall_commodity)是上架的
     * -> 初始化覆盖 门店库存  (t_stall_commodity_stock)
     */
    @KafkaListener(
            topics = "${application.name.switch}" + KafkaTopicConstant.UPDATE_XD_SHOP_COMMODITY_APP_STATUS
            , errorHandler = "kafkaConsumerErrorHandler"
            , containerFactory = "kafkaListenerContainerFactory")
    public void updateShopCommodityAppStatus(String message) {
        try {
            log.info("上下架message = {}", message);

            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<BigShopCommodityUpDownKafkaVo> appStatusList = JSON.parseArray(messageWrapper.getData().toString(), BigShopCommodityUpDownKafkaVo.class);
            if (SpringUtil.isEmpty(appStatusList)) {
                log.error("前置仓商品上下架消息空异常");
                return;
            }
            stallCommodityStockService.dealBigShopStallCommodityStock(appStatusList);
        } catch (Exception e) {
            log.error("前置仓商品上下架，kafkaTopic={}，消息消费异常：", KafkaTopicConstant.UPDATE_XD_SHOP_COMMODITY_APP_STATUS, e);
        }
    }
}
