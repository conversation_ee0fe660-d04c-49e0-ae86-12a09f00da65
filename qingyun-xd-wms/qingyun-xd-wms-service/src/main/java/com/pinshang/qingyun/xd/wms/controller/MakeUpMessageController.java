
package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.MackUpDTO;
import com.pinshang.qingyun.xd.wms.service.MackUpMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/message")
public class MakeUpMessageController {
    @Autowired
    private MackUpMessageService mackUpMessageService;

    /**
     * 单条补偿
     * @param uuid
     * @return
     */
    @PostMapping("/makeUp/{uuid}")
    public Boolean makeUpMessage(@PathVariable("uuid") String uuid){
        mackUpMessageService.makeUpMessage(uuid);
        return Boolean.TRUE;
    }

    /**
     * 批量补偿
     * 一般用于单点消费的 慎用
     * beginTime endTime 精确到时分秒 处理
     * @param dto
     * @return
     */
    @PostMapping("/makeUp/batch")
    public Boolean makeUpMessageBatch(@RequestBody MackUpDTO dto){
        dto.checkData();
        mackUpMessageService.makeUpMessageBatch(dto);
        return Boolean.TRUE;
    }
}
