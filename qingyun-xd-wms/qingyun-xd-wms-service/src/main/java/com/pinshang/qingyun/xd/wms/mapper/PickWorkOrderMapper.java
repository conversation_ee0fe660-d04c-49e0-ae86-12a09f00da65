package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderDTO;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderResult;
import com.pinshang.qingyun.xd.wms.dto.PrintWorkOrderrResult;
import com.pinshang.qingyun.xd.wms.model.PickWorkOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 仓库拣货加工单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@Repository
public interface PickWorkOrderMapper extends BaseMapper<PickWorkOrder> {

    void distributeShelfNo(@Param("pickOrderId") Long pickOrderId, @Param("shelfNo") String shelfNo);

    /**
     *加工点取货位是否占用
     */
    Integer shelfOccupyCount(@Param("shelfNo") String shelfNo, @Param("warehouseId") Long warehouseId);

    MPage<PickWorkOrderResult> pickWorkOrderList(@Param("dto") PickWorkOrderDTO dto);

    PrintWorkOrderrResult printWorkOrder(@Param("pickWorkOrderId") Long pickWorkOrderId);

    PickWorkOrderResult pickWorkOrderById(@Param("id") Long id,@Param("warehouseId") Long warehouseId);


}
