package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudCommodityDetailDTO {

    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("barcode")
    private String barCode;

    @ApiModelProperty("副码")
    private List<String> barCodeList;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("订货数量")
    private BigDecimal quantity;

    @ApiModelProperty("打包数量")
    private BigDecimal packageQuantity;
}
