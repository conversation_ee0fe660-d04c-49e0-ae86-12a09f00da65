package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 补货任务类型
 *
 * <AUTHOR>
 */
public enum ReplenishmentTaskTypeEnum {
    SHELF_REPLENISHMENT(1, "排面补货"),
    PICKING_POSITION_REPLENISHMENT(2, "拣货位补货"),
    ;
    private Integer code;
    private String name;

    ReplenishmentTaskTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ReplenishmentTaskTypeEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReplenishmentTaskTypeEnum typeEnum : ReplenishmentTaskTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
