package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.xd.wms.mapper.CategoryMapper;
import com.pinshang.qingyun.xd.wms.model.Category;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    public List<Category> queryByIds(List<Long> list) {
        return categoryMapper.selectBatchIds(list);
    }
}
