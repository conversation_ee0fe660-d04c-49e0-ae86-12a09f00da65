package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.xd.wms.dto.bigShop.BackWarehouseUpIDTO;
import com.pinshang.qingyun.xd.wms.service.bigShop.BackWarehouseService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockEnums;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 后仓上架  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Api(tags = "后仓上架", description = "后仓上架")
@RestController
@RequestMapping("/bigShop/backWarehouse")
public class BackWarehouseShelfController {

    @Autowired
    private BackWarehouseService backWarehouseService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private RedisLockService redisLockService;

    /**
     * 确认上架
     */
    @PostMapping("/confirmUp")
    @ApiOperation(value = "确认上架")
    public Boolean confirmUp(@RequestBody BackWarehouseUpIDTO req) {
        //档口权限校验
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return redisLockService.lock(RedisLockEnums.BACK_WAREHOUSE_UP, "confirmUp", () -> backWarehouseService.confirmUp(req));
    }

}
