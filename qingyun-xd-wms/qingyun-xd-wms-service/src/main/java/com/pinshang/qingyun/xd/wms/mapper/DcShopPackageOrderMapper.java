package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.cloud.CloudPickListIDTO;
import com.pinshang.qingyun.xd.wms.dto.cloud.CloudPickNumberDTO;
import com.pinshang.qingyun.xd.wms.dto.cloud.PackageListDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.ShopPackageDetailItemODTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponPickQuantityDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.ShopPackageDetailItemODTO;
import com.pinshang.qingyun.xd.wms.model.DcShopPackageOrder;
import com.pinshang.qingyun.xd.wms.model.DcShopPackageOrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface DcShopPackageOrderMapper extends BaseMapper<DcShopPackageOrder> {

    /**
     * 获取每个orderItem拣货的数量
     * @param orderId
     * @return
     */
    List<GrouponPickQuantityDTO> getPickQuantity(@Param("orderId") Long orderId);

    /**
     * 获取已经打包发货的itemId
     * @param orderId
     * @return
     */
    List<Long> getShipped(@Param("orderId") Long orderId);

    /**
     * 获取该订单未发货商品
     * @param orderId
     * @return
     */
    List<ShopPackageDetailItemODTO> getUnShippedCommodity(@Param("orderId") Long orderId, @Param("shipped") List<Long> shipped);

    Boolean updatePackageOrder(@Param("dcShopPackageOrder") DcShopPackageOrder dcShopPackageOrder, @Param("list") List<String> list);

    /**
     * 新版云超包裹查询
     * @param orderIds
     * @return
     */
    List<DcShopPackageOrder> packageListByOrderId(@Param("orderIds") List<Long> orderIds);

    /**
     * 获取订单里面大仓未打包的数量
     * @param orderId
     * @return
     */
    Integer unPackage(@Param("orderId") Long orderId);

    /**
     *
     * @param orderId
     * @return
     */
    Integer updateSubDeliveryStatus(@Param("orderId") Long orderId);

    /**
     * 拣货得数量
     * @param orderId
     * @return
     */
    List<CloudPickNumberDTO> packageNumber(@Param("orderId") Long orderId);

    /**
     * 大仓拣货的数量，用于标品合并以后
     * 称重品不合并，继续用packageNumber
     * @param orderId
     * @return
     */
    List<CloudPickNumberDTO> packageFixNumber(@Param("orderId") Long orderId);

    /**
     * 根据订单号更新包裹状态
     * @param dcShopPackageOrder
     * @return
     */
    Integer updatePackageStatus(@Param("dcShopPackageOrder") DcShopPackageOrder dcShopPackageOrder);

    /**
     * 已经取消入库的订单
     */
    List<Long> cancelOrderId(@Param("cloudPickListIDTO") CloudPickListIDTO cloudPickListIDTO);

    /**
     * 取消的订单，但是没有走取消入库的流程
     * @param cloudPickListIDTO
     * @return
     */
    List<Long> noCancelOrderId(@Param("cloudPickListIDTO") CloudPickListIDTO cloudPickListIDTO);
}
