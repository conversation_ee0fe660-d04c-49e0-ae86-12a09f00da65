package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderStatusInfoReqVo {

    @ApiModelProperty("比较的类型：1-包裹单缺少数量，2-包裹单明细缺少数量，3-包裹单状态不一致，4-包裹单（状态）明细数据不一致")
    private Integer packageFlag;

    private List<PackageOrderStatusInfoItemReqVo> list;

}
