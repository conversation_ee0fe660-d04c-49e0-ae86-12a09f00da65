package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022-05-13-17:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotPageIDTO extends StockSnapshotListForStockAndPriceIDTO {
    private Long shopId;

    private Long commodityId;

    @ApiModelProperty(value = "库存查询时间", notes = "yyyy-MM-dd")
    private String date;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("大类")
    private Long firstKindId;

    @ApiModelProperty("中类")
    private Long secondKindId;

    @ApiModelProperty("小类")
    private Long thirdKindId;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("1 负库存金额以0计算   0 否")
    private Integer stockStatus;

    @ApiModelProperty("成本价")
    private BigDecimal weightPrice;

    @ApiModelProperty("库存成本金额")
    private BigDecimal totalPrice;
}
