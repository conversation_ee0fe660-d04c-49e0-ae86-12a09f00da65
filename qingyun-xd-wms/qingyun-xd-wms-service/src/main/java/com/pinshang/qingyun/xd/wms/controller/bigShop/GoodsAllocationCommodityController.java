package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.GoodsAllocationCommodityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * <p>
 * 货位商品  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Api(tags = "货位商品", description = "货位商品")
@RestController
@RequestMapping("/bigShop/goodsAllocationCommodity")
@Slf4j
public class GoodsAllocationCommodityController {

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;


    /**
     * 根据商品id 查询已上货位，排面库存
     *
     * @param req 搜索条件
     * @return 已上货位
     */
    @PostMapping(value = "/queryGoodsAllocated")
    @ApiOperation(value = "查询已上货位")
    public QueryGoodsAllocatedODTO queryGoodsAllocated(@RequestBody GoodsAllocatedIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return goodsAllocationCommodityService.queryGoodsAllocated(req);
    }

    @PostMapping(value = "/pickingAreaBind")
    @ApiOperation(value = "拣货位和商品绑定")
    public Boolean pickingAreaBind(@RequestBody GoodsAllocationCommodityBindDTO dto) {
        return goodsAllocationCommodityService.pickingAreaBind(dto);
    }

    @GetMapping("/cancelBind")
    @ApiOperation(value = "解绑")
    public Boolean cancelBind(@RequestParam Long goodsAllocationCommodityId) {
        return goodsAllocationCommodityService.cancelBind(goodsAllocationCommodityId);
    }

    @ApiOperation(value = "批量绑定 拣货位和商品")
    @PostMapping("/importPickingAreaBind")
    public ExcelResult importPickingAreaBind(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return goodsAllocationCommodityService.importPickingAreaBind(wb);
    }

    @PostMapping("/batchCancelBind")
    @ApiOperation(value = "批量解绑 拣货位和商品")
    public BatchCancelBindDTO batchCancelBind(@RequestBody BatchCancelBindIDTO dto) {
        return goodsAllocationCommodityService.batchCancelBind(dto);
    }

    @PostMapping("/setSecureStock")
    @ApiOperation(value = "设置安全库存")
    public Boolean setSecureStock(@RequestBody SecureStockIDTO dto) {
        return goodsAllocationCommodityService.setSecureStock(dto);
    }

    @ApiOperation(value = "导入安全库存")
    @PostMapping("/importSecureStock")
    public ExcelResult importSecureStock(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return goodsAllocationCommodityService.importSecureStock(wb);
    }

    @PostMapping("/page")
    @ApiOperation("门店拣货位绑定商品列表")
    public MPage<GoodsAllocationCommodityPageDTO> page(@RequestBody GoodsAllocationCommodityPageIDTO dto) {
        return goodsAllocationCommodityService.page(dto);
    }

    @PostMapping("/exportList")
    @ApiOperation("门店拣货位绑定商品列表导出")
    public void exportList(@RequestBody GoodsAllocationCommodityPageIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();

        MPage<GoodsAllocationCommodityPageDTO> page = goodsAllocationCommodityService.page(dto);

        try {
            ExcelUtil.setFileNameAndHead(response, "门店拣货位绑定商品列表" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream(), GoodsAllocationCommodityPageDTO.class).autoCloseStream(Boolean.FALSE).sheet("门店拣货位绑定商品列表")
                    .doWrite(page.getList());
        }catch (Exception e){
            log.error("门店拣货位绑定商品列表", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }


    /**
     * 根据货位ID+商品 查询货位库存
     *
     * @param req 货位ID+商品
     * @return 货位库存
     */
    @PostMapping(value = "/queryStockByGoodsAllocationId")
    @ApiOperation(value = "查询货位库存")
    public GoodsAllocationCommodityDTO queryGoodsAllocateStock(@RequestBody GoodsAllocationCommodityIDTO req) {
        return goodsAllocationCommodityService.queryGoodsAllocateStock(req);
    }

}
