package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@TableName("t_xd_stock_freeze_log")
public class StockFreezeLog {

    @TableId
    private Long id;

    @ApiModelProperty("拣货单id")
    private Long referId;

    @ApiModelProperty("拣货单code")
    private String  referCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("冻结解冻数量")
    private BigDecimal quantity;

    private Date createTime;

    public StockFreezeLog() {
    }

    public StockFreezeLog(Long id, Long referId, String referCode, Long shopId, Long commodityId, BigDecimal quantity, Date createTime) {
        this.id = id;
        this.referId = referId;
        this.referCode = referCode;
        this.shopId = shopId;
        this.commodityId = commodityId;
        this.quantity = quantity;
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReferId() {
        return referId;
    }

    public void setReferId(Long referId) {
        this.referId = referId;
    }

    public String getReferCode() {
        return referCode;
    }

    public void setReferCode(String referCode) {
        this.referCode = referCode;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }
}
