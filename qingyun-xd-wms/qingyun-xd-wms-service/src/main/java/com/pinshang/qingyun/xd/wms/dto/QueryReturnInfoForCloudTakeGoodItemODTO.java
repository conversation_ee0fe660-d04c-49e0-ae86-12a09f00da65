package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName QueryOrderInfoForCloudTakeGoodItemODTO
 * <AUTHOR>
 * @Date 2023/7/31 18:20
 * @Description QueryOrderInfoForCloudTakeGoodItemODTO
 * @Version 1.0
 */
@Data
public class QueryReturnInfoForCloudTakeGoodItemODTO {
    @ApiModelProperty("退货单子单id")
    private Long returnOrderItemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty("退货数量")
    private BigDecimal realQuantity;

    @ApiModelProperty("退货金额")
    private BigDecimal realAmount;

}
