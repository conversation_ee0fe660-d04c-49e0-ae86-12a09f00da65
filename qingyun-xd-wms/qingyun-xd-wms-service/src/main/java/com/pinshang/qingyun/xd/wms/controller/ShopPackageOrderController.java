package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.xd.wms.service.ShopPackageOrderService;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/15 14:38
 */
@RestController
@RequestMapping("shopPackage")
@Api(value = "云超包裹", tags = "ShopPackageOrderController", description = "云超包裹查询验收")
public class ShopPackageOrderController {
    @Autowired
    private ShopPackageOrderService shopPackageOrderService;

    /**
     * /cloudSM/cloudSupermarketPackage
     * 该界面目前已经废弃了
     * @param shopPackageOrderIDTO
     * @return
     */
    @PostMapping("/queryOrderPage")
    @ApiOperation(value = "查询包裹列表，分页", notes = "查询包裹列表，分页")
    public PageInfo<ShopPackageOrderODTO> queryCancelledOrderPage(@RequestBody ShopPackageOrderIDTO shopPackageOrderIDTO) {
        return shopPackageOrderService.grouponOrderPage(shopPackageOrderIDTO);
    }

    @GetMapping("/packageDetail/{orderCode}")
    @ApiOperation(value = "查询包裹详情", notes = "查询包裹详情")
    public ShopPackageDetailDTO packageDetail(@PathVariable("orderCode") String orderCode) {
        return shopPackageOrderService.queryPackageDetail(orderCode);
    }

    @GetMapping("/check/{orderCode}")
    @ApiOperation(value = "校验验收包裹", notes = "校验验收包裹")
    public ShopPackageDetailDTO check(@PathVariable("orderCode") String orderCode) {
        return shopPackageOrderService.checkPackage(orderCode, true);
    }

    @PutMapping("/confirmPackage/{orderCode}")
    @ApiOperation(value = "确认验收包裹", notes = "确认验收包裹")
    public ShopPackageDetailDTO confirmPackage(@PathVariable("orderCode") String orderCode) {
        return shopPackageOrderService.checkPackage(orderCode, false);
    }

    /**
     * 仅限于云超
     * @param warehouseDeliveryDetailsIDTO
     * @return
     */
    @PostMapping("/queryWarehouseDeliveryPage")
    @ApiOperation(value = "查询发货商品列表，分页", notes = "查询发货商品列表，分页")
    public TablePageInfo<WarehouseDeliveryDetailsODTO> queryWarehouseDeliveryPage(@RequestBody WarehouseDeliveryDetailsIDTO warehouseDeliveryDetailsIDTO) {
        return shopPackageOrderService.queryWarehouseDeliveryPage(warehouseDeliveryDetailsIDTO);
    }

    @GetMapping("/queryWarehouseDeliveryExport")
    @ApiOperation(value = "导出发货商品列表", notes = "导出发货商品列表")
    public ModelAndView queryWarehouseDeliveryExport(WarehouseDeliveryDetailsIDTO idto) {
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        TablePageInfo<WarehouseDeliveryDetailsODTO> pageInfo = shopPackageOrderService.queryWarehouseDeliveryPage(idto);

        List<WarehouseDeliveryDetailsODTO> list = pageInfo.getList();
        list.forEach(item ->{
            item.setBarCodeList(null);
            item.setStoreId(null);
            item.setCommodityId(null);
        });
        WarehouseDeliveryDetailsODTO header = (WarehouseDeliveryDetailsODTO) pageInfo.getHeader();
        Map<String, List<String>> data = new HashMap<>();
        list.add(0, header);
        ReportUtil.buildData(list, data ,3,"yyyy-MM-dd");
        return ReportUtil.buildModelAndView(ExcelSheetTitleEnum.WAREHOUSE_DELIVERY_DETAIL, data);


    }

}
