package com.pinshang.qingyun.xd.wms.dto.report;

//@Data
//public class StockAllotDetailListOfHeadODTO {
//    @ApiModelProperty(value = "调入门店")
//    private Long inShopId;
//    private String inShopName;
//    @ApiModelProperty(value = "调出门店")
//    private Long outShopId;
//    private String outShopName;
//    @ApiModelProperty(value = "调拨单号")
//    private String orderCode;
//    @ApiModelProperty(value = "调拨类型 1调拨入库申请 2调拨出库申请")
//    private Integer allotType;
//    @ApiModelProperty(value = "商品id")
//    private Long commodityId;
//    @ApiModelProperty(value = "商品编号")
//    private String commodityCode;
//    @ApiModelProperty(value = "商品名称")
//    private String commodityName;
//    @ApiModelProperty(value = "主条形码")
//    private String barCode;
//    @ApiModelProperty(value = "商品条码")
//    private List<String> barCodeList;
//    @ApiModelProperty("规格")
//    private String commoditySpec;
//    @ApiModelProperty("商品单位")
//    private String commodityUnitName;
//    @ApiModelProperty("包装规格")
//    private BigDecimal commodityPackageSpec;
//    @ApiModelProperty("是否称重0-不称量,1-称重")
//    private Integer isWeight;
//    @ApiModelProperty("(整包或散装)")
//    private String commodityPackageName;
//    @ApiModelProperty(value = "调拨份数")
//    private Integer allotNumber;
//    @ApiModelProperty(value = "调拨数量")
//    private BigDecimal allotQuantity;
//    @ApiModelProperty(value = "单价")
//    private BigDecimal unitPrice;
//    @ApiModelProperty(value = "金额")
//    private BigDecimal sumOfMoney;
//    @ApiModelProperty(value = "完成时间")
//    private Date completeTime;
//
//    @ApiModelProperty(value = "入库时间")
//    @JsonIgnore
//    private Date inTime;
//    @ApiModelProperty(value = "出库时间")
//    @JsonIgnore
//    private Date outTime;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "调入调出申请表主键")
//    private Long stockAllotId;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "调入调出申请详情表主键")
//    private Long stockAllotItemId;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value ="实际出货的份数")
//    private Integer outNumber;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "实际出货的数量")
//    private BigDecimal outQuantity;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "实际入货的份数")
//    private Integer inNumber;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "实际入货的数量")
//    private BigDecimal inQuantity;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "成本价")
//    private BigDecimal weightPrice;
//    @JsonIgnore
//    @ApiModelProperty(hidden = true,value = "进价")
//    private BigDecimal commodityPrice;
//}
