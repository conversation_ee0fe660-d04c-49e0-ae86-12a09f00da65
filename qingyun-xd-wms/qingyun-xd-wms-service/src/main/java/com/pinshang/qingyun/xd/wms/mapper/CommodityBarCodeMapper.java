package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.CommodityBarCodeEntry;
import com.pinshang.qingyun.xd.wms.model.CommodityBarCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CommodityBarCodeMapper extends BaseMapper<CommodityBarCode> {

    List<CommodityBarCodeEntry> selectBarCodeByCidAndStatus(@Param("commodityIds") List<Long> commodityIds, @Param("defaultState") Integer defaultState);
}