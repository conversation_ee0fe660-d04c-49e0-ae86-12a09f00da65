package com.pinshang.qingyun.xd.wms.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022-05-26-15:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotVO {
    private Integer id;

    private Long commodityId;

    private Integer stockNum;

    private BigDecimal stockQuantity;

    private BigDecimal weightPrice;
}
