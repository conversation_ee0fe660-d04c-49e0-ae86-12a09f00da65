package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 拣货完成DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickCompleteDTO {
    /**
     * 拣货单id
     */
    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    /**
     * 拣货单明细
     */
    @ApiModelProperty(value = "拣货单明细")
    private List<PickCompleteItemDTO> items;

    public void checkData() {
        QYAssert.isTrue(pickOrderId != null, "拣货单id不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(items), "拣货单明细不能为空");
        for (PickCompleteItemDTO item : items) {
            QYAssert.isTrue(item.getPickQuantity() != null, "拣货数量不能为空");
        }
        BigDecimal quantityReduce = items.stream().map(PickCompleteItemDTO::getPickQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        QYAssert.isTrue(quantityReduce.compareTo(BigDecimal.ZERO) > 0, "拣货数量不能全部为0");
    }
}