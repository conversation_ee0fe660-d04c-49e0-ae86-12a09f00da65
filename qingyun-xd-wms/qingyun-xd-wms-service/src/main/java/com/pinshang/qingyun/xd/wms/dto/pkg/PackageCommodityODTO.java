package com.pinshang.qingyun.xd.wms.dto.pkg;

import com.pinshang.qingyun.base.enums.shop.PackageStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/1/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageCommodityODTO {

    @ApiModelProperty(value = "条形码")
    private String barCode;
    private Long commodityId;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "商品Code")
    private String commodityCode;
    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;
    @ApiModelProperty(value = "单位")
    private String commodityUnitName;
    @ApiModelProperty(value = "订货数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "订货份数")
    private Integer number;
    @ApiModelProperty(value = "实发数量")
    private BigDecimal realQuantity;
    @ApiModelProperty(value = "实发份数")
    private Integer realNumber;
    @ApiModelProperty(value = "1=称重，0=非称重")
    private Integer isWeight;

    @ApiModelProperty(value = "(多个)包裹号")
    private List<String> packageOrderCodeList;
    @ApiModelProperty(value = "(多个)包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货")
    private List<String> packageStatusNameList;

    @ApiModelProperty(value = "包裹号")
    private String packageOrderCode;
    @ApiModelProperty(value = "包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货")
    private Integer packageStatus;
    public String getPackageStatusName(){
        return PackageStatusEnum.getValue(packageStatus);
    }

}
