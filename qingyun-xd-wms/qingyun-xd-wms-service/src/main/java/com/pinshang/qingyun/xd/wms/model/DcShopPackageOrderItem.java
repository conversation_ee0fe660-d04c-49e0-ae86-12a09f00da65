package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="云超包裹信息item", description="云超包裹信息item")
@TableName("t_dc_shop_package_order_item")
public class DcShopPackageOrderItem {


    private Long id;

    /**
     * t_dc_shop_package_order.id
     */
    private Long packOrderId;

    /**
     * t_xd_sub_order_item.id
     */
    private String subOrderItemId;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 订货数量
     */
    private BigDecimal quantity;

    /**
     * 订货份数
     */
    private Integer number;

    /**
     * 打包数量
     */
    private BigDecimal packageQuantity;

    /**
     * 打包份数
     */
    private Integer packageNumber;

    /**
     * 价格
     */
    private BigDecimal price;
}
