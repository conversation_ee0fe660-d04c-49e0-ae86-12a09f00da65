package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.xd.wms.dto.ShopAbnormalReqDTO;
import com.pinshang.qingyun.xd.wms.dto.ShopAbnormalResDTO;
import com.pinshang.qingyun.xd.wms.mapper.StockQualityOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StockQualityOrderService {
    @Autowired
    private StockQualityOrderMapper orderMapper;

    public List<ShopAbnormalResDTO> queryShopAbnormal(ShopAbnormalReqDTO reqDTO) {
        QYAssert.isTrue(reqDTO != null, "日期不能为空");
        reqDTO.checkData();
        Date startTime = DateTimeUtil.buildDayStartOfDate(reqDTO.getDate());
        Date endTime = DateTimeUtil.buildDayLastOfDate(reqDTO.getDate());
        List<ShopAbnormalResDTO> resultList = orderMapper.queryShopAbnormal(startTime, endTime);
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        List<ShopAbnormalResDTO> shopAbnormalResDTOS = orderMapper.queryShopAbnormalForOverdue(startTime, endTime);
        if (!CollectionUtils.isEmpty(shopAbnormalResDTOS)) {
            Map<ShopAbnormalResDTO, Integer> shopMap = shopAbnormalResDTOS.parallelStream()
                    .collect(Collectors.toMap(item -> item, ShopAbnormalResDTO::getAbnormalNum2, (k1, k2) -> k2));
            if (!CollectionUtils.isEmpty(shopMap)) {
                resultList.parallelStream().forEach(item->{
                    Integer value = shopMap.get(item);
                    if (value != null) {
                        item.setAbnormalNum2(value);
                    }
                });
            }
        }
        return resultList;
    }
}
