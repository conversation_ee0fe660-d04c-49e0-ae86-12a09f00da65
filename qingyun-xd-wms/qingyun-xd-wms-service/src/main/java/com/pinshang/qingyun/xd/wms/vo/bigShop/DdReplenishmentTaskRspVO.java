package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 我的任务、全部任务查询
 * </p>
 *
 */
@Data
@ToString
@ApiModel("DdReplenishmentTaskRspVO")
public class DdReplenishmentTaskRspVO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityCode,keyName = "commodityId")
    private String commodityCode;

    @ApiModelProperty("条形码")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.barCode,keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType= FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityUnit,keyName = "commodityId")
    private String commodityUnitName;

    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.isWeight,keyName = "commodityId")
    private Integer isWeight;

    private String isWeightStr;

    public String getIsWeightStr() {
        return YesOrNoEnums.getName(isWeight);
    }


    @ApiModelProperty("计划补货数量(后端用)")
    private BigDecimal plannedQuantity;

    @ApiModelProperty("建议补货数量(后端用)")
    private BigDecimal suggestedQuantity;

    @ApiModelProperty("补货数量(计划、建议共用)")
    private BigDecimal replenishmentQuantity;

    @ApiModelProperty("实际补货数量")
    private BigDecimal realQuantity;

    @ApiModelProperty("补货人ID")
    private Long replenishUserId;

    @ApiModelProperty(value = "补货人工号")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.employeeNumber, keyName = "replenishUserId")
    private String replenishEmployeeCode;

    @ApiModelProperty("补货人姓名")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.realName, keyName = "replenishUserId")
    private String replenishUserName;

    @ApiModelProperty("存储位id(后端用)")
    private Long warehouseGoodsAllocationId;

    @ApiModelProperty("存储位(后端用)")
    private String warehouseGoodsAllocationCode;

    @ApiModelProperty("陈列位id(后端用)")
    private Long displayPositionId;

    @ApiModelProperty("陈列位(后端用)")
    private String displayPositionName;

    @ApiModelProperty("拣货位id(后端用)")
    private Long pickingGoodsAllocationId;

    @ApiModelProperty("拣货位(后端用)")
    private String pickingGoodsAllocationCode;

    @ApiModelProperty("货位id")
    private Long goodsAllocationId;

    @ApiModelProperty("货位Code")
    private String goodsAllocationCode;

    @ApiModelProperty("是否完成补货，0-未完成，1-已完成")
    private Integer replenishedStatus;



}