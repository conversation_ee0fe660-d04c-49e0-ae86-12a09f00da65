package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickWorkOrderDTO extends Pagination<PickWorkOrderResult> {

    @ApiModelProperty(value = "加工单状态(0=待处理，1=处理中, 2＝已处理,3=已取消)")
    private Integer workStatus;

    private Long warehouseId;

    @ApiModelProperty("档口id")
    private Long stallId;
}
