package com.pinshang.qingyun.xd.wms.dto.report;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotDetailListODTO {

    @ApiModelProperty(value = "出库门店")
    private String outShopName;

    @ApiModelProperty(value = "入库门店")
    private String inShopName;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品编号")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "包装规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "申请份数")
    private Integer applyNumber;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyQuantity;

    @ApiModelProperty(value = "出库份数")
    private Integer outNumber;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outQuantity;

    @ApiModelProperty(value = "入库份数")
    private Integer inNumber;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inQuantity;

    @ApiModelProperty(value = "成本价")
    private BigDecimal weightPrice;

    @ApiModelProperty(value = "出库商品金额")
    private BigDecimal sumWeightPrice;

    @ApiModelProperty(value = "进价")
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "入库商品金额")
    private BigDecimal sumCommodityPrice;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "出库时间")
    private Date outTime;

    @ApiModelProperty(value = "入库时间")
    private Date inTime;

    @ApiModelProperty(value = "调入档口ID")
    private Long inStallId;

    @ApiModelProperty(value = "调出档口ID")
    private Long outStallId;

    @ApiModelProperty(value = "调入档口")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "inStallId")
    private String inStallName;

    @ApiModelProperty(value = "调出档口")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "outStallId")
    private String outStallName;



}
