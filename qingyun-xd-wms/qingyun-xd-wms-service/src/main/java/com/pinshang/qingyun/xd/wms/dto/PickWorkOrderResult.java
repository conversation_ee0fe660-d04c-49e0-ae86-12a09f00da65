package com.pinshang.qingyun.xd.wms.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickWorkOrderResult extends BaseEntity {

    @ApiModelProperty(value = "订单id" )
    private Long orderId;

    @ApiModelProperty(value = "门店id" )
    private Long shopId;

    @ApiModelProperty(value = "商品ID" )
    private Long commodityId;

    @ApiModelProperty(value = "商品规格" )
    private String commoditySpec;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "处理标签ID" )
    private Long processId;

    @ApiModelProperty(value = "处理名称" )
    private String processName;

    @ApiModelProperty(value = "加工点取货位" )
    private String shelfNo;

    @ApiModelProperty(value = "加工单状态(0=待处理，1=处理中, 2＝已处理,3=已取消)" )
    private Integer workStatus;

    @ApiModelProperty(value = "完成时间")
    private Date completeTime;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "主码")
    private String barCode;

    @ApiModelProperty(value = "商品条码")
    private List<String> barCodeList;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty("加工点名称")
    private String workName;
    
    @ApiModelProperty("档口名称")
    private String stallName;

    /**-----------------*
     * 网络打印新增
     */
    @ApiModelProperty(value = "拣货单号 ")
    private String pickCode;

    @ApiModelProperty("是否称重品 1是0否")
    private Integer isWight;

    @ApiModelProperty(value = "加工点ID" )
    private Long workId;

    @ApiModelProperty(value = "拣货单明细id" )
    private Long pickOrderItemId;
}
