package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkCommodityBatchBindDTO {

    @ApiModelProperty(value = "加工点id")
    private Long workId;

    @ApiModelProperty(value = "商品id")
    private List<Long> commodityList;

    public void checkData() {
        QYAssert.isTrue(workId != null, "加工点不能为空");
        QYAssert.notEmpty(commodityList, "商品不能为空");
    }
}
