package com.pinshang.qingyun.xd.wms.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.shop.dto.ShopCommodityStockODTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.mapper.KitchenReceiveMapper;
import com.pinshang.qingyun.xd.wms.model.KitchenReceive;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName KitchenReceiveService
 * <AUTHOR>
 * @Date 2021/10/20 11:25
 * @Description KitchenReceiveService
 * @Version 1.0
 */
@Service
public class KitchenReceiveService {
    @Autowired
    private KitchenReceiveMapper kitchenReceiveMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    public Boolean add(@RequestBody KitchenReceiveAddIDTO idto){
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        Date date = new Date();
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        check(idto, shopId);

        List<Long> idList = new ArrayList<>(4);
        idList.add(idto.getCommodityId());
        List<ShopStockDTO> stockList = shopCommodityService.queryShopStock(shopId, idList);
        ShopStockDTO stockInfo = stockList.get(0);
        boolean isStockEnught = null == stockInfo || null ==  stockInfo.getStockNumber()
                || stockInfo.getStockNumber() < idto.getNumber();
        QYAssert.isTrue(!isStockEnught, "库存不足");

        String code =  codeClient.createCode("KITCHEN_RECEIVE_CODE");

        // 获取成本价
        List<Long> commodityIdList = new ArrayList<>(4);
        commodityIdList.add(idto.getCommodityId());
        Map<Long, ShopCommodityStockODTO> shopCommodityMap = shopCommodityClient.queryShopCommodityValidStock(shopId, commodityIdList);

        KitchenReceive kitchenReceive = new KitchenReceive();
        kitchenReceive.setId(IdWorker.getId());
        kitchenReceive.setReceiveCode(code);
        kitchenReceive.setShopId(shopId);
        kitchenReceive.setCommodityId(idto.getCommodityId());
        kitchenReceive.setNumber(idto.getNumber());
        kitchenReceive.setQuantity(idto.getQuantity());
        if(SpringUtil.isNotEmpty(shopCommodityMap) && null != shopCommodityMap.get(idto.getCommodityId())) {
            kitchenReceive.setWeightPrice(shopCommodityMap.get(idto.getCommodityId()).getWeightPrice());
        }
        kitchenReceive.setCreateId(userId);
        kitchenReceive.setCreateTime(date);
        kitchenReceive.setUpdateId(userId);
        kitchenReceive.setUpdateTime(date);
        kitchenReceiveMapper.insert(kitchenReceive);

        // 处理库存
        ImmutablePair<Long, String> idAndCode = new ImmutablePair<>(kitchenReceive.getId(), kitchenReceive.getReceiveCode());
        List<StockItemDTO> commodityList = new ArrayList<>(4);
        StockItemDTO stockItemDTO = new StockItemDTO();
        stockItemDTO.setCommodityId(idto.getCommodityId());
        stockItemDTO.setStockNumber(idto.getNumber());
        stockItemDTO.setQuantity(idto.getQuantity());
        commodityList.add(stockItemDTO);

        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.KITCHEN_RECEIVE_NORMAL, commodityList, shopId, userId);
        stockServiceAdapter.stockInOut(stockInOutVO);

        return true;
    }

    public PageInfo<KitchenReceivePageODTO> page(@RequestBody KitchenReceivePageIDTO idto){
        return PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> kitchenReceiveMapper.page((idto)));
    }

    /**
     * 校验库存
     */
    private void check(KitchenReceiveAddIDTO idto, Long shopId){
        // 获取剩余库存
        List<Long> commodityIdList = new ArrayList<>(4);
        commodityIdList.add(idto.getCommodityId());
        List<ShopStockDTO> finishedStockList = shopCommodityService.queryShopStock(shopId, commodityIdList);
        boolean hasStock = SpringUtil.isNotEmpty(finishedStockList) && null != finishedStockList.get(0).getStockNumber()
                && finishedStockList.get(0).getStockNumber() >= idto.getNumber();
        QYAssert.isTrue(hasStock, "剩余库存不足");
    }
}

