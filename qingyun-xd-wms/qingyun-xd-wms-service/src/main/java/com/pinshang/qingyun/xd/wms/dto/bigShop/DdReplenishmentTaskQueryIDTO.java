package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel("DdReplenishmentTaskIDTO")
public class DdReplenishmentTaskQueryIDTO {

    @ApiModelProperty("大店Id")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private List<Long> stallIds;

    @ApiModelProperty("任务状态，1-生效中，2-已过期")
    private Integer status;

    @ApiModelProperty("是否完成补货，0-未完成，1-已完成")
    private Integer replenishedStatus;

}