package com.pinshang.qingyun.xd.wms.enums;

public enum RadioTemplateEnum {

    NEW_ORDER("您有新的及时达订单", "7572"),
    NEW_YC_ORDER("您有新的云超订单", "7572"),
    NEW_THIRD_ORDER("您有新的第三方订单", "7572"),
    WAITING_JSD_ORDER("您有及时达订单待处理", "7571"),
    WAITING_YC_ORDER("您有云超订单待处理", "7571"),
    WAITING_THIRD_ORDER("您有第三方订单待处理", "7571");

    private String  name;
    private String code;

    RadioTemplateEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
