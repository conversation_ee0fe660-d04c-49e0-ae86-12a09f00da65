package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xd.wms.dto.ClouodCancelOrderListIDTO;
import com.pinshang.qingyun.xd.wms.dto.ClouodCancelOrderListODTO;
import com.pinshang.qingyun.xd.wms.dto.QueryReturnInfoForCloudTakeGoodODTO;
import com.pinshang.qingyun.xd.wms.dto.cloud.CloudOrderCancelIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.CloudApplyReturnOrderIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.CloudCancelIniIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.CloudOrderForReturnODTO;
import com.pinshang.qingyun.xd.wms.service.groupon.CloudReturnOrderXjService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName CloudXjReturnOrderController
 * <AUTHOR>
 * @Date 2021/6/22 15:03
 * @Description CloudXjReturnOrderController
 * @Version 1.0
 */
@RestController
@RequestMapping("/cloudXjReturnOrder")
@Api(value = "云商退单处理", tags = "CloudXjReturnOrderController")
public class CloudXjReturnOrderController {
    @Autowired
    private CloudReturnOrderXjService cloudReturnOrderXjService;

    @PostMapping("/applyReturnOrder")
    @ApiOperation(value = "云商退单处理", notes = "云商退单处理")
    public Boolean applyReturnOrder(@RequestBody CloudApplyReturnOrderIDTO idto){
        return cloudReturnOrderXjService.returnOrder(idto);
    }

    /**
     *  根据订单code查询订单信息
     * @param orderCode
     * @return
     */
    @GetMapping("/queryOrderInfo/{orderCode}")
    @ApiOperation(value = "根据订单code查询订单信息", notes = " 根据订单code查询订单信息")
    public CloudOrderForReturnODTO queryOrderInfoForReturn(@PathVariable String orderCode){
        return cloudReturnOrderXjService.queryOrderInfoForReturn(orderCode);
    }

    /**
     * 该方法应该已经废弃掉了
     * @param idto
     * @return
     */
    @Deprecated
    @PostMapping("/cloudCancelIni")
    @ApiOperation(value="取消订单期初库存", notes = "取消订单期初库存")
    public Boolean cloudCancelIni(@RequestBody CloudCancelIniIDTO idto){
        return cloudReturnOrderXjService.cloudCancelIni(idto);
    }

    @PostMapping("/checkForInventoryInitial")
    @ApiOperation(value="取消订单期初前校验", notes = "取消订单期初前校验")
    public List<String> checkForInventoryInitial(@RequestBody List<String> orderCodeList){
        return cloudReturnOrderXjService.checkForInventoryInitial(orderCodeList);
    }

    @PostMapping("/checkStorePriceForReturn")
    @ApiOperation(value="退单期初前校验", notes = "取消订单期初前校验")
    public Boolean checkStorePriceForReturn(@RequestBody CloudApplyReturnOrderIDTO idto){
        return cloudReturnOrderXjService.checkStorePriceForReturn(idto);
    }

    @GetMapping("/cancelOrder")
    @ApiOperation(value="出库中、待配送取消云超订单", notes = "出库中、待配送取消云超订单")
    public Boolean cancelOrder(@RequestParam(value = "orderId",required = false) Long orderId){
        return cloudReturnOrderXjService.cancelOrder(orderId);
    }

    /**
     *  呼叫系统对接 云超订单取消
     * @return
     */
    @PostMapping("/cancelCloudOrderEx")
    @ApiOperation(value="呼叫系统对接 云超订单取消.出库中、待配送取消云超订单", notes = "呼叫系统对接 云超订单取消.出库中、待配送取消云超订单")
    public Boolean cancelCloudOrderEx(@RequestBody CloudOrderCancelIDTO cancelIDTO){
        return cloudReturnOrderXjService.cancelCloudOrderEx(cancelIDTO);
    }

    @GetMapping("/distributionWarnByShopId")
    @ApiOperation(value="是否存在预警的订单", notes = "无预警订单返回true, 否则返回false")
    public Boolean distributionWarnByShopId(@RequestParam(value = "shopId", required = false) Long shopId){
        return cloudReturnOrderXjService.distributionWarnByShopId(shopId);
    }

    @GetMapping("/distributionWarnForPDA")
    @ApiOperation(value="是否存在预警的订单-PDA", notes = "存在预警订单则发送message")
    public Boolean distributionWarnForPDA(){
        return cloudReturnOrderXjService.distributionWarnForPDA();
    }

    @PostMapping("/cancelOrderList")
    @ApiOperation(value="出库中、待配送取消云超订单列表页", notes = "出库中、待配送取消云超订单列表页")
    public PageInfo<ClouodCancelOrderListODTO> cancelOrderList(@RequestBody ClouodCancelOrderListIDTO idto){
        return cloudReturnOrderXjService.cancelOrderList(idto);
    }

    @GetMapping("/queryReturnInfoForCloudTakeGood/{returnCode}/{shopId}")
    @ApiModelProperty(value = "根据退货单code询云超退单")
    public QueryReturnInfoForCloudTakeGoodODTO queryReturnInfoForCloudTakeGood(@PathVariable("returnCode") String returnCode, @PathVariable("shopId") Long shopId){
        return cloudReturnOrderXjService.queryReturnInfoForCloudTakeGood(returnCode, shopId);
    }

    @GetMapping("/completeReturnInfoForCloudTakeGood/{returnOrderId}")
    @ApiModelProperty(value = "根据订单id核销云超退单")
    public Boolean completeReturnInfoForCloudTakeGood(@PathVariable("returnOrderId") Long returnOrderId){
        return cloudReturnOrderXjService.completeReturnInfoForCloudTakeGood(returnOrderId);
    }

}
