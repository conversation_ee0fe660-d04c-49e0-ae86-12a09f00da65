package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("DdPackingStationPageIDTO")
public class DdPackingStationPageIDTO extends Pagination {

    private Long shopId;

    @ApiModelProperty(value = "打包口", required = true)
    private String packingPort;

    @ApiModelProperty(value = "状态，1-启用，0-停用")
    private Integer status;

}