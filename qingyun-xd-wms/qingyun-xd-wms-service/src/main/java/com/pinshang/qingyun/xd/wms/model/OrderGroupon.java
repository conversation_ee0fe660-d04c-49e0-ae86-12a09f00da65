package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/01/14 12:58
 */
@Data
@TableName("t_xd_order_groupon")
@AllArgsConstructor
@NoArgsConstructor
public class OrderGroupon {
    @TableId
    private Long id;
    private Long orderId;
    private Long grouponId;
    /**
     * 团购类型:group_type_1 预售  group_type_2 现货 group_type_６(云超订单)
     */
    private String type;
    /**
     * 0=普通订单 1=团购订单 2=云超订单
     */
    private Integer orderType;
    private String receiveMan;
    private String receiveMobile;
    private Date arrivalTime;
    private Date endTime;
    private String userMobile;
    private String deliveryMan;
    private String pickupCode;
}
