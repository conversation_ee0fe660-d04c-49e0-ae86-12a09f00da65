package com.pinshang.qingyun.xd.wms.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.service.WarehouseEmployeeService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickAreaEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by ch<PERSON>qi on 2021/7/20.
 */
@Component
@Slf4j
public class EmployeeListener {

    @Autowired
    private WarehouseEmployeeService employeeService;
    @Autowired
    private DdPickAreaEmployeeService ddPickAreaEmployeeService;

    /**
     * 职员停用、离职 把工作状态改为停止工作
     */
    @KafkaListener(id="${application.name.switch}"+ KafkaTopicConstant.CLOSE_PX_ACCOUNT_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = "${application.name.switch}"+ KafkaTopicConstant.CLOSE_PX_ACCOUNT_TOPIC,
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory")
    public void employeeChangeBroadcast(String message){
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);

        if(messageWrapper != null && messageWrapper.getData() != null){
            List<Long> employeeIds = JSON.parseArray(messageWrapper.getData().toString(), Long.class);
            log.info("收到职员停用、离职通知, employeeIds:{}", employeeIds);
            for (Long employeeId : employeeIds) {
                WarehouseEmployee warehouseEmployee = employeeService.queryEmployee(employeeId, WarehouseEmployeeTypeEnum.PICK.getCode());
                if(warehouseEmployee != null && YesOrNoEnums.YES.getCode().equals(warehouseEmployee.getWorkStatus())){
                    employeeService.stopPickWork(warehouseEmployee.getWarehouseId(), employeeId);
                }
                WarehouseEmployee warehouseDelivery = employeeService.queryEmployee(employeeId, WarehouseEmployeeTypeEnum.DELIVERY.getCode());
                if(warehouseDelivery != null && YesOrNoEnums.YES.getCode().equals(warehouseDelivery.getWorkStatus())){
                    employeeService.stopDeliverWork(warehouseDelivery.getWarehouseId(), employeeId);
                }

                try {
                    // 移除分区拣货员
                    ddPickAreaEmployeeService.deleteByEmployeeId(employeeId);
                }catch (Exception e){
                    log.warn("移除分区拣货员失败, employeeId:{}  error {}", employeeId, e);
                }
            }
        }
    }
}
