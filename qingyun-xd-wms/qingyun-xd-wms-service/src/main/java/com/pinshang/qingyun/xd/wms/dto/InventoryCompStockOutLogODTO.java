package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: mystery
 * @DateTime: 2025/2/18 15:46
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InventoryCompStockOutLogODTO {
    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 总的变化数量（与pos总流水变化量符号相反）
     */
    private BigDecimal sumChangeQuantity;

}
