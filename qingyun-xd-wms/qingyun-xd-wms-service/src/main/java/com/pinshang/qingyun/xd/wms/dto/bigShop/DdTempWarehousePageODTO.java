package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName DdTempWarehousePageODTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 12:50
 * @Version 1.0
 */
@Data
public class DdTempWarehousePageODTO {
    @ApiModelProperty(value = "档口名称")
    private String stallName;

    @ApiModelProperty(value = "入库默认货位")
    private String goodsAllocationCode;

    @ApiModelProperty(value = "操作类型 0-POS退货入库、1-APP退货入库、2-折扣特价码入库")
    private Integer tempOperationType;

    @ApiModelProperty(value = "操作类型中文")
    private Integer tempOperationName;

    private Long stallId;

    private Long shopId;
    @ApiModelProperty(value = "入库默认货位id")
    private Long goodsAllocationId;

   public String getTempOperationName() {
       return TempOperationTypeEnum.getNameByCode(tempOperationType);
   }

}
