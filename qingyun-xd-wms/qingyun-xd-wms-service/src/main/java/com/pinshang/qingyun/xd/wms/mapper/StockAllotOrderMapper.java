package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.model.StockAllotOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockAllotOrderMapper extends BaseMapper<StockAllotOrder> {

    MPage<StockAllotOrderListDTO> allotInList(@Param("dto") StockAllotOrderQueryDTO dto,
                                              @Param("userStallIdList") List<Long> userStallIdList);

    MPage<StockAllotOrderListDTO> allotOutList(@Param("dto") StockAllotOrderQueryDTO dto,
                                               @Param("userStallIdList") List<Long> userStallIdList);

    MPage<StockAllotOrderListDTO> allotList(@Param("dto") StockAllotOrderQueryDTO dto);

    List<OrderSettleODTO> findInSettleOrderListByStoreIds(OrderSettleIDTO orderSettleIDTO);
    List<OrderSettleODTO> findOutSettleOrderListByStoreIds(OrderSettleIDTO orderSettleIDTO);

    MPage<StockAllotOrderListDTO> singleAllotList(@Param("dto") StockAllotSingleQueryDTO dto);

    List<StockAllotOrderSupportDTO> stockAllotOrderSupportPage(@Param("dto") StockAllotOrderSupportIDTO dto);
}
