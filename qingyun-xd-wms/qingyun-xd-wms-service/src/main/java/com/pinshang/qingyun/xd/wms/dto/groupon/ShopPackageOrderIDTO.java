package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/15 14:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopPackageOrderIDTO extends Pagination {
    @ApiModelProperty(value = "门店id")
    private Long shopId;
    private List<Long> shopIdList;
    @ApiModelProperty(value = "包裹号")
    private String orderCode;
    @ApiModelProperty(value = "C端订单编号")
    private String orderId;
    @ApiModelProperty(value = "预约提货日期-开始")
    private String arrivalBeginTime;
    @ApiModelProperty(value = "预约提货日期-结束")
    private String arrivalEndTime;
    @ApiModelProperty(value = "提货人手机号")
    private String receiveMobile;
    @ApiModelProperty(value = "下单人手机号")
    private String userMobile;
    @ApiModelProperty(value = "包裹状态")
    private Integer packageStatus;
    @ApiModelProperty(value = "完成提货时间-开始")
    private String orderCompleteDateBeginTime;
    @ApiModelProperty(value = "完成提货时间-结束")
    private String orderCompleteDateEndTime;
    @ApiModelProperty(value = "提货码")
    private String pickupCode;
    public void checkData(){
        boolean isArrivalTimeNull = (StringUtils.isBlank(arrivalBeginTime) || StringUtils.isBlank(arrivalEndTime));
        QYAssert.isTrue(!(isArrivalTimeNull && StringUtils.isBlank(pickupCode)), "预约提货日期和提货码不能同时为空");
    }

}
