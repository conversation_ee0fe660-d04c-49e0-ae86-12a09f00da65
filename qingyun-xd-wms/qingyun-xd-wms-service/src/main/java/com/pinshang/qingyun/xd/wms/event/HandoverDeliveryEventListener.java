package com.pinshang.qingyun.xd.wms.event;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPickPartitionOrder;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.HandoverDeliveryListenerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class HandoverDeliveryEventListener implements ApplicationListener<HandoverDeliveryEvent> {

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Override
    @Async
    public void onApplicationEvent(HandoverDeliveryEvent event) {
        Long pickOrderId = event.getPickOrderId();
        List<DdPickPartitionOrder> ddPickPartitionOrders = event.getDdPickPartitionOrders();
        try {
            ddPickPartitionOrderService.finishPickOrderIfAllPartitionPickOrderFinish(pickOrderId, ddPickPartitionOrders, null);
        } catch (Exception e) {
            log.error("HandoverDeliveryEventListener.onApplicationEvent error:", e);
            HandoverDeliveryListenerVO msg = new HandoverDeliveryListenerVO();
            msg.setPickOrderId(pickOrderId);
            msg.setDdPickPartitionOrders(ddPickPartitionOrders);
            mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.DD_PARTITION_PICK_HANDOVER_TOPIC,
                    msg,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.DD_PARTITION_PICK_HANDOVER.name(),
                    KafkaMessageOperationTypeEnum.UPDATE.getCode());
            log.warn("送货交接事件监听处理失败，转发MQ，pickOrderId:[{}]", event.getPickOrderId());
        }
    }
}
