package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchWarehouseEmployeeDTO extends Pagination {

    @ApiModelProperty("搜索框职员编号/职员姓名")
    private String keyword;

    @ApiModelProperty("职员类型, 不能为空")
    private WarehouseEmployeeTypeEnum typeEnum;

    public void checkData(){
        QYAssert.isTrue(typeEnum != null, "职员类型不能为空");
    }
}
