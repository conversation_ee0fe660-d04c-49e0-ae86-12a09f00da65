package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <p>
 * 移库单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
@ApiModel("DdTransferRecordPageIDTO")
public class DdTransferRecordPageIDTO extends Pagination {
    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("移库单号")
    private String transferCode;

    @ApiModelProperty("类型1-排面补货、2-拣货位补货、3-后仓上架、4-移库")
    private Integer type;

    @ApiModelProperty("操作开始时间")
    private String startTime;

    @ApiModelProperty("操作结束时间")
    private String endTime;

    private List<Long> stallIdList;

}