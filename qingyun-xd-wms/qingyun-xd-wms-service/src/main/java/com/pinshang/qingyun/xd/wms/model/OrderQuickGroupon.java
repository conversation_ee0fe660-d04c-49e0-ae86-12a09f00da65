package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_xd_order_quick_groupon")
@AllArgsConstructor
@NoArgsConstructor
public class OrderQuickGroupon extends BaseEntity {

    /**
     * t_xd_order 表主键
     */
    private Long orderId;

    /**
     * t_xd_quick_groupon表主键
     */
    private Long grouponId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 参考XdOrderTypeEnum
     */
    private Integer orderType;

    /**
     * 下单人
     */
    private Long userId;

    /**
     * 提货人/收货人(t_xd_order.receive_man)
     */
    private String receiveMan;

    /**
     * 提货人手机号/收货人手机(t_xd_order.receive_mobile)
     */
    private String receiveMobile;

    /**
     * 团购活动截止时间(t_xd_quick_groupon表冗余)
     */
    private Date endTime;

    /**
     * 登录用户手机号(用户名)
     */
    private String userMobile;

    /**
     * 核销人
     */
    private String deliveryMan;

    /**
     * 提货码
     */
    private String pickupCode;

    /**
     * 跟团号
     */
    private String withGroupNo;

    /**
     * 用户昵称(姓+*)
     */
    private String userNick;

    /**
     * 用户头像地址
     */
    private String userHeadUrl;
}
