package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.StockOutCollectIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockOutCollectODTO;
import com.pinshang.qingyun.xd.wms.dto.StockOutLogIDTO;
import com.pinshang.qingyun.xd.wms.model.StockOutCollect;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: liu zhen
 * @DateTime: 2023/4/3 15:52
 * @Description
 */
@Repository
public interface StockOutCollectMapper extends BaseMapper<StockOutCollect> {

    Integer saveBatch(List<StockOutCollect> list);

    List<StockOutCollectODTO> findAllByIdto(StockOutCollectIDTO idto);

    Integer deleteByShopIdAndCollectTime(StockOutLogIDTO idto);
}
