package com.pinshang.qingyun.xd.wms.vo.bigShop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 前置仓商品上下架
 * 上架新增搜索，下架从搜索中删除
 * 消息topic：UPDATE_XD_SHOP_COMMODITY_APP_STATUS
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BigShopCommodityUpDownKafkaVo {

    //上架状态：0=上架，1=下架
    private Integer appStatus;

    private Long shopId;

    private List<Long> commodityIdList;

}
