package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponPackageDTO {

    @ApiModelProperty("t_dc_shop_package_order表id")
    private Long packOrderId;

    @ApiModelProperty("包裹单号")
    private String orderCode;

    @ApiModelProperty("状态(大仓使用) 0=异常 1=正常")
    private Integer status;

}
