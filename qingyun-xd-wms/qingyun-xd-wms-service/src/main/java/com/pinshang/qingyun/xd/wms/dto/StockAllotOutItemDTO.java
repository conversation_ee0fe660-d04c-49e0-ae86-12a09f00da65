package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 调拨出库明细DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOutItemDTO {

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    /**
     * 出库数量
     */
    @ApiModelProperty(value = "出库数量")
    private BigDecimal outQuantity;
}