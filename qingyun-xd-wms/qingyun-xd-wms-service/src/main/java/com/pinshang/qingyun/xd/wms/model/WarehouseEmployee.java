package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.Data;

@Data
@TableName("t_xd_warehouse_employee")
public class WarehouseEmployee extends BaseEntity {

    /**
     * 职员id
     */
    private Long employeeId;
    /**
     * 员工编号/工号
     */
    private String employeeCode;
    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 员类型:1-拣货员,2-配送员
     */
    private Integer type;

    /**
     * 工作状态：0停止工作，1开始工作
     */
    private Integer workStatus;

    /**
     * 职员手机
     */
    private String employeePhone;

}
