package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.model.bigShop.GoodsAllocation;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.GoodsAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 货位  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Api(tags = "货位", description = "货位")
@RestController
@RequestMapping("/bigShop/goodsAllocation")
@Slf4j
public class GoodsAllocationController {

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @Autowired
    private GoodsAllocationService goodsAllocationService;


    /**
     * 查询可选货位
     *
     * @param req 搜索条件
     * @return 可选货位
     */
    @PostMapping(value = "/queryGoodsAllocationList")
    @ApiOperation(value = "选择货位")
    public List<GoodsAllocationEnableListODTO> queryGoodsAllocationList(@RequestBody GoodsAllocationListIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return goodsAllocationService.queryGoodsAllocationPdaList(req);
    }

    @PostMapping(value = "/queryGoodsAllocationWebList")
    @ApiOperation(value = "选择货位(web)")
    public List<GoodsAllocationListODTO> queryGoodsAllocationWebList(@RequestBody GoodsAllocationListIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return goodsAllocationService.queryGoodsAllocationWebList(req);
    }

    @PostMapping(value = "/queryGoodsAllocationByIdList")
    @ApiOperation(value = "根据货位id获取货位信息")
    public List<GoodsAllocation> queryGoodsAllocationByIdList(@RequestBody List<Long> goodsAllocationIdList) {
        return goodsAllocationService.queryGoodsAllocationByIdList(goodsAllocationIdList);
    }

    /**
     * 校验货位号
     */
    @PostMapping(value = "/checkGoodsAllocation")
    @ApiOperation(value = "校验货位号")
    public CheckGoodsAllocationIDTO checkGoodsAllocation(@RequestBody CheckGoodsAllocationIDTO req) {
        ddTokenShopIdService.processDdTokenShopId(req.getShopId(), req.getStallId());
        return goodsAllocationService.checkGoodsAllocation(req);
    }

    @PostMapping("/add")
    @ApiOperation("添加货位")
    public Boolean add(@RequestBody GoodsAllocationDTO dto) {
        return goodsAllocationService.add(dto);
    }

    @GetMapping("/startOrStop")
    @ApiOperation("启用或停用货位")
    public Boolean startOrStop(@RequestParam Long id, @RequestParam Integer status) {
        return goodsAllocationService.startOrStop(id, status);
    }

    @GetMapping("/distributionStall")
    @ApiOperation("分配档口")
    public Boolean distributionStall(@RequestParam Long goodsAllocationId, @RequestParam Long stallId) {
        return goodsAllocationService.distributionStall(goodsAllocationId, stallId);
    }

    @PostMapping("/page")
    @ApiOperation("门店货位管理")
    public MPage<GoodsAllocationPageDTO> page(@RequestBody GoodsAllocationPageIDTO dto) {
        return goodsAllocationService.page(dto);
    }

    @PostMapping("/exportList")
    @ApiOperation("导出门店货位管理")
    public void exportList(@RequestBody GoodsAllocationPageIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();

        MPage<GoodsAllocationPageDTO> page = goodsAllocationService.page(dto);

        try {
            ExcelUtil.setFileNameAndHead(response, "门店货位管理列表" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream(), GoodsAllocationPageDTO.class).autoCloseStream(Boolean.FALSE).sheet("门店货位管理列表")
                    .doWrite(page.getList());
        }catch (Exception e){
            log.error("门店货位管理列表", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @ApiOperation(value = "导入货位")
    @PostMapping("/importGoodsAllocation")
    public ExcelResult importGoodsAllocation(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return goodsAllocationService.importGoodsAllocation(wb);
    }

    @ApiOperation(value = "批量分配档口")
    @PostMapping("/batchDistributionStall")
    public ExcelResult batchDistributionStall(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return goodsAllocationService.batchDistributionStall(wb);
    }

    @PostMapping("/pathPage")
    @ApiOperation("门店拣货路径列表")
    public MPage<PickPathDTO> pathPage(@RequestBody PickPathIDTO dto) {
        return goodsAllocationService.pathPage(dto);
    }

    @PostMapping("/exportPathPage")
    @ApiOperation("导出门店拣货路径列表")
    public void exportPathPage(@RequestBody PickPathIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();

        MPage<PickPathDTO> page = goodsAllocationService.pathPage(dto);

        try {
            ExcelUtil.setFileNameAndHead(response, "门店拣货路径列表" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream(), PickPathDTO.class).autoCloseStream(Boolean.FALSE).sheet("门店拣货路径列表")
                    .doWrite(page.getList());
        }catch (Exception e){
            log.error("门店拣货路径列表", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @ApiOperation(value = "导入拣货路径")
    @PostMapping("/importPickPath")
    public ExcelResult importPickPath(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return goodsAllocationService.importPickPath(wb);
    }

//    @GetMapping("/goodsAllocationsByShopId")
//    @ApiOperation("根据门店查询 启用并且没有分配档口的货位")
//    public List<GoodsAllocation> goodsAllocationsByShopId(@RequestParam("shopId") Long shopId) {
//        return goodsAllocationService.goodsAllocationsByShopId(shopId);
//    }

    @GetMapping("/printForPDA")
    @ApiOperation("pda打印货位")
    public List<GoodAllocationPrintForPDAODTO> printForPDA(@RequestParam(value = "areaId", required = false) Long areaId,
                                                           @RequestParam(value = "storageArea", required = false) Integer storageArea,
                                                           @RequestParam(value = "goodsAllocationCode", required = false) String goodsAllocationCode){
        return goodsAllocationService.printForPDA(areaId, storageArea, goodsAllocationCode);
    }

//    @GetMapping("/queryUnBind")
//    @ApiOperation("查询当前档口下的拣货位 启用状态的，未绑定商品得")
//    public List<GoodsAllocation> queryUnBind(@RequestParam(value = "stallId") Long stallId,@RequestParam(value = "goodsAllocationCode", required = false) String goodsAllocationCode ) {
//        return goodsAllocationService.queryUnBind(stallId, goodsAllocationCode);
//    }

    @PostMapping("/queryGoodsAllocation")
    @ApiOperation("根据条件查询当前门店下的货位")
    public List<GoodsAllocation> queryGoodsAllocation(@RequestBody QueryGoodsAllocationIDTO dto) {
        return goodsAllocationService.queryGoodsAllocation(dto);
    }

    @PostMapping("/queryGoodsAllocationDropDownList")
    @ApiOperation("查询货位下拉框")
    public List<GoodsAllocation> queryGoodsAllocationDropDownList(@RequestBody GoodsAllocationDropDownIDTO dto) {
        return goodsAllocationService.queryGoodsAllocationDropDownList(dto);
    }

    @GetMapping("/getTempLocationNumbers")
    @ApiOperation("根据档口id获取临时库货位号")
    public List<String> getTempLocationNumbers(@RequestParam(value = "stallId", required = false) Long stallId){
         return goodsAllocationService.getTempLocationNumbers(stallId);
    }
}
