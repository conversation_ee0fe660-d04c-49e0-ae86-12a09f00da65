package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderProgressIDTO  extends Pagination<InventoryOrderItemPage> {
    @ApiModelProperty(value = "盘点单id")
    private Long inventoryOrderId;
    @ApiModelProperty(value = "0 未盘点 1已盘点")
    private Integer type;
}
