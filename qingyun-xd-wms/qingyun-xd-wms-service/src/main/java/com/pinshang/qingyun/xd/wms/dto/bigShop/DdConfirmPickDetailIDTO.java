package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@ApiModel("DdConfirmPickDetailIDTO")
public class DdConfirmPickDetailIDTO implements Serializable {

    private static final long serialVersionUID = -2115774395268651091L;

    @ApiModelProperty("分区拣货子单id")
    private Long pickPartitionOrderId;

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单明细id")
    private Long pickOrderItemId;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty(value = "拣货份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "商品id")
    private String commodityId;
}
