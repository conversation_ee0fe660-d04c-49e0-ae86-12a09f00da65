package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;

@RestController
@RequestMapping("/bigShop/stallCommodityStock")
@Api(value = "档口商品库存", tags = "StallCommodityStockController")
@Slf4j
public class StallCommodityStockController {

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private IRenderService renderService;

    @PostMapping("/add")
    @ApiOperation(value = "添加档口商品库存")
    public Integer batchInsertStallCommodityStock(@RequestBody StallCommodityStockIDTO idto) {
        QYAssert.isTrue(idto.getShopId() != null, "门店不能为空，请选择！");
        QYAssert.isTrue(idto.getStallId() != null, "档口不能为空，请选择！");
        QYAssert.isTrue(SpringUtil.isNotEmpty(idto.getCommodityIdSet()), "商品不能为空！");
        return stallCommodityStockService.batchInsertStallCommodityStock(idto);
    }

    @ApiOperation("设置预留库存")
    @GetMapping("/setReserveStock")
    public Boolean setReserveStock(@RequestParam Long id, @RequestParam(value = "reserveStock", required = false) BigDecimal reserveStock) {
        return stallCommodityStockService.setReserveStock(id, reserveStock);
    }

    @ApiOperation(value = "导入预留库存")
    @PostMapping("/importReserveStock")
    public ExcelResult importReserveStock(@RequestParam(value = "file", required = true) MultipartFile file,
                                          @RequestParam(value = "stallId") Long stallId) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return stallCommodityStockService.importReserveStock(wb, stallId);
    }

    @PostMapping("/reserveStockPage")
    @ApiOperation("门店线下预留库存列表")
    public MPage<ReserveStockPageDTO> reserveStockPage(@RequestBody ReserveStockPageIDTO dto) {
        MPage<ReserveStockPageDTO> page = stallCommodityStockService.reserveStockPage(dto);
        if(CollectionUtils.isNotEmpty(page.getList())){
            renderService.render(page.getList(), "/reserveStockPage");
        }
        return page;
    }

    @PostMapping("/exportReserveStockPage")
    @ApiOperation("门店线下预留库存列表 导出")
    public void exportReserveStockPage(@RequestBody ReserveStockPageIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();

        MPage<ReserveStockPageDTO> page = stallCommodityStockService.reserveStockPage(dto);
        if(CollectionUtils.isNotEmpty(page.getList())){
            renderService.render(page.getList(), "/exportReserveStockPage");
        }
        try {
            ExcelUtil.setFileNameAndHead(response, "门店线下预留库存列表" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream(), ReserveStockPageDTO.class).autoCloseStream(Boolean.FALSE).sheet("门店线下预留库存列表")
                    .doWrite(page.getList());
        }catch (Exception e){
            log.error("门店线下预留库存列表", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }
}
