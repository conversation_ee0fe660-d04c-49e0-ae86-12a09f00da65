package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderIDTO;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderItemODTO;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderODTO;
import com.pinshang.qingyun.xd.wms.model.CancelledOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CancelledOrderMapper extends BaseMapper<CancelledOrder> {

    MPage<CancelledOrderODTO> queryCancelledOrderPage(@Param("e") CancelledOrderIDTO cancelledOrderIDTO);

    List<CancelledOrderItemODTO> queryCancelledOrderItemList(@Param("orderCode") String orderCode);
}
