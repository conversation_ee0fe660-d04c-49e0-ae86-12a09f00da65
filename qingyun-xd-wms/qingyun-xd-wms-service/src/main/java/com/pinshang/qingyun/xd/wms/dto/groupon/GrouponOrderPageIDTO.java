package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName orderGrouponPageIDTO
 * <AUTHOR>
 * @Date 2020/12/21 14:11
 * @Description orderGrouponPageIDTO
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderPageIDTO extends Pagination {
    @ApiModelProperty("提货门店id")
    private Long shopId;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("提货人手机号")
    private String receiveMobile;
    @ApiModelProperty("商品名称")
    private String userMobile;
    @ApiModelProperty("提货状态")
    private Integer status;
    @ApiModelProperty("门店到货日期-开始")
    private String arrivalBeginTime;
    @ApiModelProperty("门店到货日期-结束")
    private String arrivalEndTime;
    @ApiModelProperty(value = "团购类型字典编码")
    private String typeCode;
    @ApiModelProperty("完成提货日期-开始")
    private String succReceiveBeginTime;
    @ApiModelProperty("完成提货日期-结束")
    private String succReceiveEndTime;
    @ApiModelProperty("提货码")
    private String pickupCode;
    @ApiModelProperty("订单编号")
    private String orderCode;

    public void checkData(){
        boolean isArrivalTimeNull = (StringUtils.isBlank(arrivalBeginTime) || StringUtils.isBlank(arrivalEndTime));
        QYAssert.isTrue(!(isArrivalTimeNull && StringUtils.isBlank(pickupCode) && StringUtils.isBlank(orderCode)), "预约提货日期、提货码、订单编号不能同时为空");
    }
}
