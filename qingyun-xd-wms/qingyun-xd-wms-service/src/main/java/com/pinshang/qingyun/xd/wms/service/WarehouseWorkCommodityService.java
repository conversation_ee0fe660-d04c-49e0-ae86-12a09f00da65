package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xd.wms.dto.CommodityProcessName;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityBatchBindDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListResult;
import com.pinshang.qingyun.xd.wms.enums.WorkCommodityLogTypeEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseWorkCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseWorkMapper;
import com.pinshang.qingyun.xd.wms.model.WarehouseWorkCommodity;
import com.pinshang.qingyun.xd.wms.model.WarehouseWorkCommodityLog;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class WarehouseWorkCommodityService {

    @Autowired
    private WarehouseWorkMapper warehouseWorkMapper;

    @Autowired
    private WarehouseWorkCommodityMapper warehouseWorkCommodityMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private CommodityProcessGroupService commodityProcessGroupService;

    @Autowired
    private WarehouseWorkCommodityLogService warehouseWorkCommodityLogService;

    /**
     * 批量绑定加工点
     * @param dto
     */
    public void batchBind(WorkCommodityBatchBindDTO dto) {
        dto.checkData();
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseWorkCommodity>()
                .in(WarehouseWorkCommodity::getCommodityId, dto.getCommodityList())
                .eq(WarehouseWorkCommodity::getWarehouseId, warehouseId);
        List<WarehouseWorkCommodity> list = warehouseWorkCommodityMapper.selectList(query);
        Map<Long, WarehouseWorkCommodity> map = list.stream().collect(Collectors.toMap(WarehouseWorkCommodity::getCommodityId, it->it));
        List<WarehouseWorkCommodityLog> logList = new ArrayList<>();
        WarehouseWorkCommodityLog log = null;
        WarehouseWorkCommodity data = null;
        long userId = StockUtils.INSTANCE.userId();
        String createName = FastThreadLocalUtil.getQY().getRealName();
        for (Long commodityId : dto.getCommodityList()) {
            data = new WarehouseWorkCommodity();
            log = new WarehouseWorkCommodityLog();
            WarehouseWorkCommodity warehouseWorkCommodity = map.get(commodityId);
            //已经存在了更新
            if (null != warehouseWorkCommodity) {
                data.setId(warehouseWorkCommodity.getId());
                data.setWorkId(dto.getWorkId());
                warehouseWorkCommodityMapper.updateById(data);
            } else {
                data.setWorkId(dto.getWorkId());
                data.setCommodityId(commodityId);
                data.setWarehouseId(warehouseId);
                warehouseWorkCommodityMapper.insert(data);
            }
            //添加操作日志
            log.setCommodityId(commodityId);
            log.setWarehouseId(warehouseId);
            log.setCreateId(userId);
            log.setCreateName(createName);
            log.setCreateTime(new Date());
            log.setType(WorkCommodityLogTypeEnum.BIND.getCode());
            logList.add(log);
        }
        warehouseWorkCommodityLogService.batchInsert(logList);
    }

    /**
     * 批量删除绑定
     * @param list
     */
    public void deleteBind(List<Long> list) {
        Assert.notEmpty(list, "必须选择要解绑的商品");
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseWorkCommodity>()
                .in(WarehouseWorkCommodity::getId, list);
        List<WarehouseWorkCommodity> workCommodityList = warehouseWorkCommodityMapper.selectList(query);
        WarehouseWorkCommodityLog log = null;
        List<WarehouseWorkCommodityLog> logList = new ArrayList<>();
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        long userId = StockUtils.INSTANCE.userId();
        for (WarehouseWorkCommodity e : workCommodityList) {
            log = new WarehouseWorkCommodityLog();
            log.setCommodityId(e.getCommodityId());
            log.setWarehouseId(warehouseId);
            log.setType(WorkCommodityLogTypeEnum.UNBIND.getCode());
            log.setCreateId(userId);
            log.setCreateName(FastThreadLocalUtil.getQY().getRealName());
            log.setCreateTime(new Date());
            logList.add(log);
        }
        warehouseWorkCommodityLogService.batchInsert(logList);
        warehouseWorkCommodityMapper.deleteBatchIds(list);
    }

    /**
     * 查询列表
     * @param dto
     * @return
     */
    public MPage<WorkCommodityListResult> workCommodityList(WorkCommodityListDTO dto) {
        dto.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        MPage<WorkCommodityListResult> mPage = warehouseWorkCommodityMapper.workCommodityList(dto);
        Map<Long, List<String>> longListMap = new HashMap<>();
        Map<Long, String> processName = new HashMap<>();
        if (!mPage.getList().isEmpty()) {
            List<Long> commodityIds = mPage.getList().stream().map(WorkCommodityListResult::getId).collect(Collectors.toList());
            longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
            //商品加工方式
            List<CommodityProcessName> processNameList = commodityProcessGroupService.commodityProcessNameList(commodityIds);
            if (!CollectionUtils.isEmpty(processNameList)) {
                processName = processNameList.stream().
                        collect(Collectors.toMap(CommodityProcessName::getCommodityId, CommodityProcessName::getProcessName));
            }
        }
        for (WorkCommodityListResult e : mPage.getList()) {
            e.setBarCodeList(longListMap.get(e.getId()));
            e.setProcessName(processName.get(e.getId()));
        }
        return mPage;
    }


    public List<WorkCommodityListResult> queryWorkCommodityList(List<Long> commodityIds, Long warehouseId){
        return warehouseWorkCommodityMapper.queryWorkCommodityByIds(commodityIds, warehouseId);
    }


}
