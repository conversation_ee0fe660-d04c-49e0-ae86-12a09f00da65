package com.pinshang.qingyun.xd.wms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindQueryReqBO;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindSaveBO;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindUnbundleBO;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.PrinterBindService;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindQueryReqVO;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindQueryRspVO;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindSaveVO;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindUnbundleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 打印机绑定
 */
@RestController
@RequestMapping("/printerBind")
@Api(value = "打印机绑定", tags = "PrinterBindController")
@RequiredArgsConstructor
public class PrinterBindController {

    private final PrinterBindService printerBindService;

    /**
     * 打印机绑定状态查询
     * @param
     * @return
     */
    @PostMapping("/isExistPrinterBind/{printerCode}")
    @ApiOperation(value = "api：打印机绑定状态查询", notes = "打印机绑定状态查询")
    public Boolean isExistPrinterBind(@PathVariable("printerCode") String printerCode){
        return printerBindService.isExistPrinterBind(printerCode);
    }

    /**
     * 查询打印机绑定关系
     * @param
     * @return
     */
    @PostMapping("/selectPrinterByParams")
    @ApiOperation(value = "api：查询打印机绑定关系", notes = "查询打印机绑定关系")
    public PrinterBindODTO selectPrinterByParams(@RequestBody PrinterBindQueryIDTO idto){
        return printerBindService.selectPrinterByParams(idto);
    }


    @PostMapping("/bind")
    @ApiOperation(value = "后端：新增绑定", notes = "新增绑定")
    public Boolean bind(@RequestBody PrinterBindSaveVO saveVO){
        saveVO.checkData();
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token) && Objects.nonNull(token.getShopId()),"请登陆后绑定打印机关系");
        PrinterBindSaveBO printerBindSaveBO = BeanCloneUtils.copyTo(saveVO, PrinterBindSaveBO.class);
        printerBindSaveBO.setShopId(token.getShopId());
        printerBindSaveBO.setCreateId(token.getUserId());
        printerBindService.bind(printerBindSaveBO);
        return Boolean.TRUE;
    }

    @PostMapping("/unbundle")
    @ApiOperation(value = "后端：解绑打印机", notes = "解绑打印机")
    public Boolean unbundle(@RequestBody PrinterBindUnbundleVO unbundleVO){
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token) && Objects.nonNull(token.getShopId()),"请登陆后绑定打印机关系");
        PrinterBindUnbundleBO printerBindUnbundleBO = BeanCloneUtils.copyTo(unbundleVO, PrinterBindUnbundleBO.class);
        printerBindUnbundleBO.setShopId(token.getShopId());
        printerBindService.unbundle(printerBindUnbundleBO);
        return Boolean.TRUE;
    }

    @PostMapping("/bindPage")
    @ApiOperation(value = "后端：打印机绑定列表", notes = "打印机绑定列表")
    public MPage<PrinterBindQueryRspVO> bindPage(@RequestBody PrinterBindQueryReqVO queryReqVO) {
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token) && Objects.nonNull(token.getShopId()),"请登陆");
        PrinterBindQueryReqBO printerBindQueryReqBO = BeanCloneUtils.copyTo(queryReqVO, PrinterBindQueryReqBO.class);
        printerBindQueryReqBO.setShopId(token.getShopId());
        return printerBindService.bindPage(printerBindQueryReqBO);
    }


}
