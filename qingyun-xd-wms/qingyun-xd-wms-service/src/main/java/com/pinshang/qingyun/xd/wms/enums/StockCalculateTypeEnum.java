package com.pinshang.qingyun.xd.wms.enums;

/**
 * 订单类型
 * <AUTHOR>
 */
public enum StockCalculateTypeEnum {
    UN_NORMAL(1,"负库存以0计算"),
    NORMAL(0,"负库存正常计算")
    ;
    private Integer code;
    private String name;

    StockCalculateTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static StockCalculateTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (StockCalculateTypeEnum typeEnum : StockCalculateTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
