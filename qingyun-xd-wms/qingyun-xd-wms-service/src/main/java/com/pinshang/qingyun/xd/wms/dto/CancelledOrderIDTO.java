package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderIDTO extends Pagination<CancelledOrderODTO> {

     private Long shopId;

     @ApiModelProperty(value = "订单号")
     private String orderCode;

    @ApiModelProperty(value = "订单来源 0饿了么")
     private Integer sourceType;


     @ApiModelProperty(value = "订单开始时间")
     private String orderTimeBegin;
     @ApiModelProperty(value = "订单结束时间")
     private String orderTimeEnd;


     @ApiModelProperty(value = "取消开始时间")
     private String cancelTimeBegin;
     @ApiModelProperty(value = "取消结束时间")
     private String cancelTimeEnd;


     @ApiModelProperty(value = "收货人id")
     private Long receiveId;

    @ApiModelProperty(value = "入库状态: 0 未入库   1 已入库")
    private Integer stockInStatus;


    @ApiModelProperty(value = "入库开始时间")
    private String stockInTimeBegin;
    @ApiModelProperty(value = "入库结束时间")
    private String stockInTimeEnd;


    @ApiModelProperty(value = "配送员电话")
    private String deliveryMobile;

}
