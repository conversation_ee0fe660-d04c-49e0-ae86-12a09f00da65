package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StallService extends ServiceImpl<StallMapper, Stall> {

    /**
     * 查询档口id 与 分区拣货位 id的映射关系
     *
     * @param shopId
     * @param stallIds
     * @return
     */
    public Map<Long, Long> queryStallIdPickAreaIdMap(Long shopId, Collection<Long> stallIds) {
        if (SpringUtil.isEmpty(stallIds)) {
            return Collections.emptyMap();
        }

        Collection<Stall> stalls = this.queryStallListByShopAndIds(shopId, stallIds);
        return stalls.stream().collect(Collectors.toMap(Stall::getId, Stall::getPickAreaId));
    }

    public List<Stall> queryStallListByShopAndIds(Long shopId, Collection<Long> stallIds) {

        LambdaQueryWrapper<Stall> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Stall::getShopId, shopId)
                .in(Stall::getId, stallIds);
        return this.baseMapper.selectList(queryWrapper);
    }

}
