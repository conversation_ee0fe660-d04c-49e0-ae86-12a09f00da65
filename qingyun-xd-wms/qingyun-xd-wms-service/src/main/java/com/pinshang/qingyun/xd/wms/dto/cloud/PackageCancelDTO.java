package com.pinshang.qingyun.xd.wms.dto.cloud;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageCancelDTO {

    /**
     * 订单id
     */
    private Long orderId;

    private Long userId;

    private String userName;

    private Date cancelTime;

    /**
     * PackageStatusEnum  目前只用到了0和60发消息
     * {@link com.pinshang.qingyun.base.enums.shop.PackageStatusEnum}
     */
    private Integer packageStatus;

    /**
     * 包裹id
     */
    private Long packageOrderId;
}
