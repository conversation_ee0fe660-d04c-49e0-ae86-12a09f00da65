package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ShopCommodityStockByCateIdIDTO
 * <AUTHOR>
 * @Date 2022/10/18 18:22
 * @Description ShopCommodityStockByCateIdIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityStockByCateIdIDTO {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("大类id")
    private List<Long> firstCategoryIdList;
}
