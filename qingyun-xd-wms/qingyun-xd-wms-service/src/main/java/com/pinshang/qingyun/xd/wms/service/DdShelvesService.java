package com.pinshang.qingyun.xd.wms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.shop.dto.shop.SelectShopIdListByConditionsIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.BigShopOperateTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.DdDisplayPositionMapper;
import com.pinshang.qingyun.xd.wms.mapper.DdShelvesMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.DdShelves;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdShelvesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 排面货架管理  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Slf4j
@Service
public class DdShelvesService extends ServiceImpl<DdShelvesMapper,DdShelves> {

    @Autowired
    private DdShelvesMapper ddShelvesMapper;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private DdDisplayPositionMapper ddDisplayPositionMapper;

    @Autowired
    private StallMapper stallMapper;

    /**
    * 排面货架管理 列表
    * @param req
    * @return
    */
    public PageInfo<DdShelvesODTO> page(DdShelvesPageIDTO req) {
        List<Long> shopIdList = shopClient.selectShopIdListByConditions(new SelectShopIdListByConditionsIDTO(FastThreadLocalUtil.getQY().getUserId(), null, null));
        if(SpringUtil.isEmpty(shopIdList) || (null != req.getShopId() && !shopIdList.contains(req.getShopId()))){
            return new PageInfo<>();
        }
        return PageHelper.startPage(req.getPageNo(), req.getPageSize()).doSelectPageInfo(() -> {
            ddShelvesMapper.list(req);
        });
    }

    /**
    * 保存 排面货架管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(DdShelvesSaveIDTO req) {
        //档口+货架编码不重复，档口+货架名称不重复
        DdShelves ddShelves = BeanCloneUtils.copyTo(req, DdShelves.class);
        Integer isRepeat = ddShelvesMapper.isRepeat(ddShelves);
        if(null != isRepeat && isRepeat > 0){
            throw new BizLogicException("货架编码已存在/货架名称已存在");
        }
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String userName= tokenInfo.getRealName();
        String employeeCode = tokenInfo.getEmployeeNumber();
        Date now = new Date();

        ddShelves.setCreateId(userId);
        ddShelves.setCreateTime(now);
        ddShelves.setUpdateId(userId);
        ddShelves.setUpdateTime(now);
        ddShelvesMapper.insert(ddShelves);

        LogDdShelvesVO log = ddShelvesMapper.selectLog(ddShelves.getId());
        log.setOperateType(BigShopOperateTypeEnum.INSERT.getCode());
        log.setOperateUserCode(employeeCode);
        log.setOperateTime(DateUtil.get4yMdHms(now));
        log.setOperateUserName(userName);
        log.setOperateUserId(userId);
        this.sendDdShelvesLogMessage(Collections.singletonList(log));
        return Boolean.TRUE;
    }

    /**
    * 更新 排面货架管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(DdShelvesUpdateIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String employeeCode = tokenInfo.getEmployeeNumber();
        String userName = tokenInfo.getRealName();
        Date now = new Date();

        DdShelves oldRecord = ddShelvesMapper.selectById(req.getId());
        oldRecord.setShelveCode(req.getShelveCode());
        oldRecord.setShelveName(req.getShelveName());
        //档口+货架编码不重复，档口+货架名称不重复
        Integer isRepeat = ddShelvesMapper.isRepeat(oldRecord);
        if(null != isRepeat && isRepeat >= 1){
            throw new BizLogicException("货架编码已存在/货架名称已存在");
        }
        DdShelves update = new DdShelves();
        update.setId(req.getId());
        update.setUpdateId(userId);
        update.setUpdateTime(now);
        update.setShelveCode(req.getShelveCode());
        update.setShelveName(req.getShelveName());
        ddShelvesMapper.updateById(update);

        LogDdShelvesVO log = ddShelvesMapper.selectLog(req.getId());
        log.setOperateType(BigShopOperateTypeEnum.UPDATE.getCode());
        log.setOperateUserCode(employeeCode);
        log.setOperateTime(DateUtil.get4yMdHms(now));
        log.setOperateUserName(userName);
        log.setOperateUserId(userId);
        this.sendDdShelvesLogMessage(Collections.singletonList(log));
        return Boolean.TRUE;
    }

    /**
    * 删除 排面货架管理
    * @param req
    * @return
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(DdShelvesDeleteIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        String employeeCode = tokenInfo.getEmployeeNumber();
        String userName = tokenInfo.getRealName();
        Date now = new Date();
        //陈列位数＞0的货架不可操作删除
        LogDdShelvesVO log = ddShelvesMapper.selectLog(req.getId());
        Integer displayCount = ddDisplayPositionMapper.countDisplay(req.getId());
        if(null != displayCount && displayCount > 0){
            throw new BizLogicException("陈列位数＞0的货架不可操作删除");
        }
        ddShelvesMapper.deleteById(req.getId());
        log.setOperateType(BigShopOperateTypeEnum.DELETE.getCode());
        log.setOperateUserCode(employeeCode);
        log.setOperateTime(DateUtil.get4yMdHms(now));
        log.setOperateUserName(userName);
        log.setOperateUserId(userId);
        this.sendDdShelvesLogMessage(Collections.singletonList(log));
        return Boolean.TRUE;
    }

    /**
     * 排面货架管理 列表
     * @param req
     * @return
     */
    public List<DdShelvesODTO> list( String shelves, Long stallId) {
        DdShelvesPageIDTO idto = new DdShelvesPageIDTO();
        idto.setShelves(shelves);
        TokenInfo token = FastThreadLocalUtil.getQY();
        idto.setShopId(token.getShopId());
        idto.setStallId(stallId);
        return ddShelvesMapper.list(idto);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importExcel( List<DdShelvesSaveIDTO> shelveList, Long stallId, Long shopId){
        Date now = new Date();
        String nowStr = DateUtil.get4yMdHms(now);
        TokenInfo token = FastThreadLocalUtil.getQY();
        String userCode = token.getEmployeeNumber();
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        String userName =  FastThreadLocalUtil.getQY().getRealName();
        List<String> errorList = new ArrayList<>();
        List<DdShelves> recordList = ddShelvesMapper.selectList(new LambdaQueryWrapper<DdShelves>().eq(DdShelves::getStallId, stallId));
        Stall stallInfo = stallMapper.selectById(stallId);
        List<String> codeList = recordList.stream().map(DdShelves::getShelveCode).collect(Collectors.toList());
        List<String> nameList = recordList.stream().map(DdShelves::getShelveName).collect(Collectors.toList());
        List<DdShelves> insertList = new ArrayList<>(shelveList.size());
        List<LogDdShelvesVO> logList = new ArrayList<>();
        Map<String, Integer> codeMap = new HashMap<>();
        Map<String, Integer> nameMap = new HashMap<>();
        boolean flag = true;
        for (int i = 0; i < shelveList.size(); i++) {
            int index = i + 1;
            DdShelvesSaveIDTO ddShelvesSaveIDTO = shelveList.get(i);

            if (codeMap.containsKey(ddShelvesSaveIDTO.getShelveCode())) {
                if (1 == codeMap.get(ddShelvesSaveIDTO.getShelveCode())) {
                    errorList.add(ddShelvesSaveIDTO.getShelveCode() + "货架编码重复");
                }
                codeMap.put(ddShelvesSaveIDTO.getShelveCode(), 2);
                flag = false;
                continue;
            }

            if (nameMap.containsKey(ddShelvesSaveIDTO.getShelveName())) {
                if ( 1 == nameMap.get(ddShelvesSaveIDTO.getShelveName())) {
                    errorList.add(ddShelvesSaveIDTO.getShelveName() + "货架名称重复");
                }
                nameMap.put(ddShelvesSaveIDTO.getShelveName(), 2);
                flag = false;
                continue;
            }

            codeMap.put(ddShelvesSaveIDTO.getShelveCode(), 1);
            nameMap.put(ddShelvesSaveIDTO.getShelveName(), 1);

            if(codeList.contains(ddShelvesSaveIDTO.getShelveCode())){
                errorList.add(ddShelvesSaveIDTO.getShelveCode() + "货架编码已存在");
                flag = false;
                continue;
            }
            if(nameList.contains(ddShelvesSaveIDTO.getShelveName())){
                errorList.add(ddShelvesSaveIDTO.getShelveName() + "货架名称已存在");
                flag = false;
                continue;
            }

            DdShelves insertVo = new DdShelves();
            insertVo.setShopId(shopId);
            insertVo.setStallId(stallId);
            insertVo.setShelveName(ddShelvesSaveIDTO.getShelveName());
            insertVo.setShelveCode(ddShelvesSaveIDTO.getShelveCode());
            insertVo.setCreateTime(now);
            insertVo.setCreateId(userId);
            insertVo.setUpdateTime(now);
            insertVo.setUpdateId(userId);
            insertList.add(insertVo);

            LogDdShelvesVO logVo = new LogDdShelvesVO();
            logVo.setShopId(shopId);
            logVo.setStallId(stallId);
            logVo.setStallCode(stallInfo.getStallCode());
            logVo.setStallName(stallInfo.getStallName());
            logVo.setShelveName(ddShelvesSaveIDTO.getShelveName());
            logVo.setShelveCode(ddShelvesSaveIDTO.getShelveCode());
            logVo.setOperateTime(nowStr);
            logVo.setOperateType(BigShopOperateTypeEnum.BATCH_INSERT.getCode());
            logVo.setOperateUserId(userId);
            logVo.setOperateUserCode(userCode);
            logVo.setOperateUserName(userName);
            logList.add(logVo);
        }
        if(flag) {
            this.saveBatch(insertList);
            this.sendDdShelvesLogMessage(logList);
        }
        return new ExcelResult(errorList, null);
    }

    /**
    * 发送 排面货架管理 日志
    * @param operateType
    */
    private void sendDdShelvesLogMessage(List<LogDdShelvesVO> voList){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName", "t_log_dd_shelves");
        jsonObject.put("data", voList);
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.LOG_CREATE_TOPIC, jsonObject.toJSONString(),
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.LOG_CREATE.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }
}
