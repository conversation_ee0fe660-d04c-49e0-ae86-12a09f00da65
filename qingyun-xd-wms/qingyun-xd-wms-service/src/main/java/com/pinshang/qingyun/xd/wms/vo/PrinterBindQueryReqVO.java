package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@ToString
@ApiModel("PrinterBindQueryReqVO")
public class PrinterBindQueryReqVO extends Pagination {

    /**
     * 打印机编码模糊查询
     */
    @ApiModelProperty(value = "打印机编码")
    private String printerCodeSearchKey;

    /**
     * 使用方类型（1-加工点 2-打包口）
     * @see com.pinshang.qingyun.xd.wms.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "使用方类型（1-加工点 2-打包口）")
    private Integer userType;

    /**
     * 使用方ID
     */
    @ApiModelProperty(value = "使用方ID")
    private Long realUserId;




}
