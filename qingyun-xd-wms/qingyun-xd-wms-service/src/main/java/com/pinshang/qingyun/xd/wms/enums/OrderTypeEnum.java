package com.pinshang.qingyun.xd.wms.enums;

/**
 * 订单类型
 * <AUTHOR>
 */
public enum OrderTypeEnum {
    NORMAL(0,"普通订单"),
    GROUP(1,"团购订单"),
    CLOUD(2,"云超订单"),
    THIRD(3, "第三方订单"),
    QUICK_GROUPON(4, "清美团团订单"),
    XD_WAREHOUSE(5, "前置仓订单"),
    CLOUD_GROUP(6, "云超团购订单"),
    ;
    private Integer code;
    private String name;

    OrderTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static OrderTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (OrderTypeEnum typeEnum : OrderTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
