package com.pinshang.qingyun.xd.wms.enums;

/**
 * 第三方平台类型
 * <AUTHOR>
 */
public enum PlatFormTypeEnum {
    ELM(0,"饿了么"),
    MT(1,"美团"),
    JD(2,"京东")
    ;
    private Integer code;
    private String name;

    PlatFormTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PlatFormTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (PlatFormTypeEnum typeEnum : PlatFormTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
