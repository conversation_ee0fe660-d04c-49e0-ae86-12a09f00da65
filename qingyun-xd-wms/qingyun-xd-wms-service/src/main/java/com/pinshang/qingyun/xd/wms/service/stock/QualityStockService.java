

package com.pinshang.qingyun.xd.wms.service.stock;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.mapper.StockQualityInOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockQualityOutOrderMapper;
import com.pinshang.qingyun.xd.wms.model.StockQualityInOrder;
import com.pinshang.qingyun.xd.wms.model.StockQualityOutOrder;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 质检库存出入库
 * Created by chenqi on 2019/11/21.
 */
@Transactional
@Service
@Lazy
public class QualityStockService {
    @Autowired
    private StockQualityInOrderMapper stockQualityInOrderMapper;


    @Autowired
    private StockQualityOutOrderMapper stockQualityOutOrderMapper;


    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private CodeClient codeClient;

    @Lazy
    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    /**
     * 质检库存出入库
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @param warehouseId
     * @param userId
     */
    public Pair<Long,String> stockInOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        Pair<Long,String> pair = null;

        if (StockInOutEnum.IN.getCode() == typeEnum.getInOutType()){
            pair = stockQualityIn(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }else if (StockInOutEnum.OUT.getCode() == typeEnum.getInOutType()){
            pair = stockQualityOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }

        return pair;
    }

    /**
     * 质检库入库
     *
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockQualityIn(Pair<Long,String> idAndCode, StockInOutTypeEnums typeEnum,
                                             List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        //处理库存+
        shopCommodityService.increaseQualityStock(commodityList, warehouseId);

        //入库
        String code = codeClient.createCode("XD_STOCK_OUT_CODE");
        StockQualityInOrder qualityInOrder = new StockQualityInOrder();
        qualityInOrder.setId(IdWorker.getId());
        qualityInOrder.setOrderCode(code);
        qualityInOrder.setWarehouseId(warehouseId);
        qualityInOrder.setReferId(idAndCode.getLeft());
        qualityInOrder.setReferCode(idAndCode.getRight());
        qualityInOrder.setType(typeEnum.getCode());
        qualityInOrder.setCreateId(userId);
        stockQualityInOrderMapper.insert(qualityInOrder);

//        for (StockItemDTO itemDTO : commodityList) {
//            StockQualityInOrderItem qualityItem = new StockQualityInOrderItem();
//            qualityItem.setStockQualityInOrderId(qualityInOrder.getId());
//            qualityItem.setCommodityId(itemDTO.getCommodityId());
//            qualityItem.setStockNumber(itemDTO.getStockNumber());
//            qualityItem.setQuantity(itemDTO.getQuantity());
//
//            stockQualityInOrderItemMapper.insert(qualityItem);
//        }

        return new ImmutablePair(qualityInOrder.getId(),code);
    }

    /**
     * 质检库出库
     *
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockQualityOut( Pair<Long,String> idAndCode, StockInOutTypeEnums typeEnum,
                                               List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        //质检正常品出库，需要直接入库
        if(StockInOutTypeEnums.OUT_QUALITY_NORMAL.equals(typeEnum)){
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_QUALITY_NORMAL, commodityList, warehouseId, userId);
            stockServiceAdapter.stockInOut(stockInOutVO);
        }

        //处理库存-
        if(YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())){
            shopCommodityService.reduceQualityStock(commodityList, warehouseId);
        }else{
            shopCommodityService.reduceQualityStockUnCheck(commodityList, warehouseId);
        }

        //出库
        String code = codeClient.createCode("XD_STOCK_OUT_CODE");
        StockQualityOutOrder qualityOutOrder = new StockQualityOutOrder();
        qualityOutOrder.setId(IdWorker.getId());
        qualityOutOrder.setOrderCode(code);
        qualityOutOrder.setWarehouseId(warehouseId);
        qualityOutOrder.setReferId(idAndCode.getLeft());
        qualityOutOrder.setReferCode(idAndCode.getRight());
        qualityOutOrder.setType(typeEnum.getCode());
        qualityOutOrder.setCreateId(userId);
        stockQualityOutOrderMapper.insert(qualityOutOrder);

//        for (StockItemDTO itemDTO : commodityList) {
//            StockQualityOutOrderItem qualityItem = new StockQualityOutOrderItem();
//            qualityItem.setStockQualityOutOrderId(qualityOutOrder.getId());
//            qualityItem.setCommodityId(itemDTO.getCommodityId());
//            qualityItem.setStockNumber(Math.abs(itemDTO.getStockNumber()));
//            qualityItem.setQuantity(itemDTO.getQuantity().abs());
//
//            stockQualityOutOrderItemMapper.insert(qualityItem);
//        }

        return new ImmutablePair(qualityOutOrder.getId(),code);
    }
}