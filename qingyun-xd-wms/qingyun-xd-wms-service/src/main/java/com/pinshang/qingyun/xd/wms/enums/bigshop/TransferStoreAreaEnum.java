package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 移库库区
 * 1拣货区  2存储区 3排面区 4临时库
 *
 * <AUTHOR>
 */
public enum TransferStoreAreaEnum {
    SHELF(1, "排面区"),

    PICKING(2, "拣货区"),
    WAREHOUSE(3, "存储区"),

    TEMP(4, "临时库"),
    ;
    private Integer code;
    private String name;

    TransferStoreAreaEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static TransferStoreAreaEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferStoreAreaEnum typeEnum : TransferStoreAreaEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
