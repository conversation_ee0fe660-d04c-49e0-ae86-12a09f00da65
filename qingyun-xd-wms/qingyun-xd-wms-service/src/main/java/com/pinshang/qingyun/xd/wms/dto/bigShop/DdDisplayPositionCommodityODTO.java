package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 门店商品绑定陈列位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@ApiModel("DdDisplayPositionCommodityODTO")
public class DdDisplayPositionCommodityODTO {

    private Long id;

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("所属区域")
    private Long areaId;

    @ApiModelProperty("所属档口")
    private Long stallId;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("最小数量")
    private BigDecimal minStock;

    @ApiModelProperty("最大数量")
    private BigDecimal maxStock;

    @ApiModelProperty("创建人ID")
    private Long createId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改人ID")
    private Long updateId;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("是否可售：1-是,0-否 和t_xs_shop_commodity统一")
    private Integer commoditySaleStatus;

    @ApiModelProperty("当前库存")
    private BigDecimal stockQuantity;

    @ApiModelProperty("建议数量=最大数量-当前库存")
    private BigDecimal suggestQuantity;

    @ApiModelProperty("APP状态：0-上架、1-下架")
    private Integer appStatus;
}