package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrderListResult extends BaseEntity {

    @ApiModelProperty(value = "拣货单code")
    private String pickCode;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "拣货单状态(0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货人")
    private Long pickId;

    @ApiModelProperty(value = "拣货人姓名")
    private String employeeName;

    @ApiModelProperty(value = "拣货开始时间(分配拣货人时间)")
    private Date pickBeginTime;

    @ApiModelProperty(value = "拣货完成时间")
    private Date pickEndTime;

    @ApiModelProperty(value = "配送取货位")
    private String shelfNo;

    @ApiModelProperty(value = "客户要求配送开始时间")
    private Date orderDeliveryBeginTime;

    @ApiModelProperty(value = "客户要求配送完成时间")
    private Date orderDeliveryEndTime;

    @ApiModelProperty(value = "是否缺货 1缺货  0不缺货")
    private Integer stockOutStatus;

    @ApiModelProperty(value = "收货人姓名")
    private String receiveMan;

    @ApiModelProperty(value = "收货人电话")
    private String receiveMobile;

    @ApiModelProperty(value = "店内序号")
    private String storeSerialNum;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "0 整单 1 分区")
    private Integer pickingMethod;

    @ApiModelProperty(value = "打包完成时间")
    private Date packingEndTime;

    @ApiModelProperty(value = "打包人")
    private Long packingId;

    @ApiModelProperty(value = "打包人姓名")
    private String packingName;

    @ApiModelProperty(value = "打包口名称")
    private String packingStationName;
}
