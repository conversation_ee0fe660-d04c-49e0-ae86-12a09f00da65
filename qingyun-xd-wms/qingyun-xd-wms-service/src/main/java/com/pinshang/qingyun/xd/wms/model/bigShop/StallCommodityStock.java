package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.dto.bigShop.StallCommodityStockIDTO;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 档口商品库存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@TableName("t_stall_commodity_stock")
public class StallCommodityStock implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 档口所属门店ID
     */
    private Long shopId;

    /**
     * 档口ID
     */
    private Long stallId;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 拣货区库存
     */
    private BigDecimal pickingAreaStock;

    /**
     * 存储区库存
     */
    private BigDecimal warehouseAreaStock;

    /**
     * 临时库存
     */
    private BigDecimal qualityQuantity;

    /**
     * 冻结库存
     */
    private Integer freezeNumber;

    /**
     * 预留库存
     */
    private BigDecimal reserveStock;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    public StallCommodityStock() {

    }

    public StallCommodityStock(Long shopId, Long stallId, Long commodityId) {
        this.shopId = shopId;
        this.stallId = stallId;
        this.commodityId = commodityId;
    }


    public StallCommodityStock initBasic(Long userId, String userName, Date date) {
        setCreateId(userId);
        setCreateName(userName);
        setCreateTime(date);
        setUpdateId(userId);
        setUpdateName(userName);
        setUpdateTime(date);
        return this;
    }

    public static List<StallCommodityStock> forInsert(StallCommodityStockIDTO idto) {

        List<StallCommodityStock> list = new ArrayList<>();
        Date date = new Date();
        idto.getCommodityIdSet().forEach(c -> {
            StallCommodityStock stallCommodityStock = new StallCommodityStock(idto.getShopId(), idto.getStallId(), c);
            stallCommodityStock.setStockQuantity(BigDecimal.ZERO);
            stallCommodityStock.setPickingAreaStock(BigDecimal.ZERO);
            stallCommodityStock.setWarehouseAreaStock(BigDecimal.ZERO);
            stallCommodityStock.setQualityQuantity(BigDecimal.ZERO);
            stallCommodityStock.setFreezeNumber(0);
            stallCommodityStock.setReserveStock(BigDecimal.ZERO);
            stallCommodityStock.initBasic(idto.getUserId(), idto.getUserName(), date);
            list.add(stallCommodityStock);
        });
        return list;
    }

}
