package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.CommodityBaseInfoSearchIDTO;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallCommodityMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.pinshang.qingyun.xd.wms.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 档口商品  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Slf4j
@Service
public class StallCommodityService {

    @Autowired
    private StallCommodityMapper stallCommodityMapper;

    public List<CommodityBaseInfoODTO> fuzzySearchByCommodityCode(CommodityBaseInfoSearchIDTO params) {
        Map<String, Object> sqlParams = buildSqlMap(params);
        List<CommodityBaseInfoODTO> list = stallCommodityMapper.fuzzySearchByCommodityCode(sqlParams);
        QYAssert.notEmpty(list, "未查询到档口商品");

        // 检查是否为称重码，解析重量
        BigDecimal weight = Optional.ofNullable(params.getCommodityCode())
                .filter(code -> code.length() == 18 && code.startsWith("2"))
                .map(code -> new BigDecimal(code.substring(12, 14) + "." + code.substring(14, 17)).setScale(3, RoundingMode.DOWN))
                .orElse(null);

        list.forEach(item -> {
            if (Objects.nonNull(weight)) {
                item.setWeight(weight);
            }
        });
        return list;
    }

    private Map<String, Object> buildSqlMap(CommodityBaseInfoSearchIDTO params) {
        Map<String, Object> result = new HashMap<>();
        QYAssert.notNull(params, "搜索条件不能为空");
        QYAssert.notNull(params.getShopId(), "门店不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(params.getCommodityCode()), "商品编码不能为空");
        String commodityCode = params.getCommodityCode();
        QYAssert.isTrue(StringUtils.isNotBlank(commodityCode), "商品搜索条件不能为空");
        if (!StrUtil.containsChinese(commodityCode)) {
            if (commodityCode.length() == 18 && commodityCode.startsWith("2")) {
                commodityCode = commodityCode.substring(1, 7);
            }
            result.put("barCode", commodityCode);
        } else {
            result.put("commodityCode", commodityCode);
        }
        result.put("shopId", params.getShopId());
        result.put("stallId", params.getStallId());
        return result;
    }


    /**
     * 查询商品所在的档口
     *
     * @param shopId
     * @param commodityIds
     * @return
     */
    public List<StallCommodity> getStallByCommodityIds(Long shopId, List<Long> commodityIds) {
        if (null !=  shopId && SpringUtil.isNotEmpty(commodityIds)) {
            return this.getStallByCommodityIds(shopId, null, commodityIds);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * @param shopId
     * @param stallId
     * @param commodityIds
     * @return
     */
    public List<StallCommodity> getStallByCommodityIds(Long shopId, Long stallId, List<Long> commodityIds) {
        if (SpringUtil.isNotEmpty(commodityIds)) {
            LambdaQueryWrapper<StallCommodity> query = new LambdaQueryWrapper<StallCommodity>()
                    .eq(StallCommodity::getShopId, shopId)
                    .eq(Objects.nonNull(stallId), StallCommodity::getStallId, stallId)
                    .in(StallCommodity::getCommodityId, commodityIds);
            return stallCommodityMapper.selectList(query);
        } else {
            return new ArrayList<>();
        }

    }

    /**
     * @param shopId
     * @param stallIdList
     * @param commodityIds
     * @return
     */
    public List<StallCommodity> getStallCommodityByCommodityIds(List<Long> shopIdList, List<Long> stallIdList, List<Long> commodityIds) {
        if (SpringUtil.isNotEmpty(commodityIds)) {
            LambdaQueryWrapper<StallCommodity> query = new LambdaQueryWrapper<StallCommodity>()
                    .in(StallCommodity::getShopId, shopIdList)
                    .in(StallCommodity::getStallId, stallIdList)
                    .in(StallCommodity::getCommodityId, commodityIds);
            return stallCommodityMapper.selectList(query);
        } else {
            return new ArrayList<>();
        }

    }


    /**
     * 过滤出app上架状态 的 档口商品
     *
     * @param shopId
     * @param stallId
     * @param commodityIds
     * @return
     */
    public List<StallCommodity> filterStallCommodityAppOnStatus(Long shopId, Long stallId, List<Long> commodityIds) {
        if (Objects.isNull(shopId) || SpringUtil.isEmpty(commodityIds)) {
            return Collections.emptyList();
        }

        List<StallCommodity> stallByCommodityIds = getStallByCommodityIds(shopId, stallId, commodityIds);
        if (SpringUtil.isEmpty(stallByCommodityIds)) {
            return Collections.emptyList();
        }

        return stallByCommodityIds.stream()
                .filter(it -> it.getAppStatus() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 门店+商品查询商品在哪个档口上架
     *
     * @param shopId
     * @param commodityIdList
     * @return
     */
    public List<StallCommodity> listShopCommodityStall(Long shopId, List<Long> commodityIdList) {
        if (Objects.isNull(shopId) || SpringUtil.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<StallCommodity> query = new LambdaQueryWrapper<StallCommodity>()
                .eq(StallCommodity::getShopId, shopId)
                .in(StallCommodity::getCommodityId, commodityIdList)
                .eq(StallCommodity::getAppStatus, 0);
        return stallCommodityMapper.selectList(query);
    }

    public List<CommodityBaseInfoODTO> listByCommodity(CommodityBaseInfoSearchIDTO req) {
        Map<String, Object> result = new HashMap<>();
        QYAssert.notNull(req, "搜索条件不能为空");
        QYAssert.notNull(req.getShopId(), "门店不能为空");
        QYAssert.notNull(req.getStallId(), "档口不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(req.getCommodityCode()), "商品编码不能为空");
        result.put("shopId", req.getShopId());
        result.put("stallId", req.getStallId());
        result.put("commodityCode", req.getCommodityCode());

        return stallCommodityMapper.fuzzySearchByCommodityCode(result);
    }

    public List<StallCommodity> searchByCommodityIdAndStall(Long commodityId, Long stallId) {
        return stallCommodityMapper.selectList(new LambdaQueryWrapper<StallCommodity>().eq(StallCommodity::getStallId, stallId)
                .eq(StallCommodity::getCommodityId, commodityId));
    }

    public List<Long> listStallCommodityByNameOrBarCode(Long stallId, String commodityQueryParam) {
        return stallCommodityMapper.listStallCommodityByNameOrBarCode(stallId, commodityQueryParam);
    }
}
