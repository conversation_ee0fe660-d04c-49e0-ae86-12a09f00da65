package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("DdSingleConfirmPickODTO")
public class DdSingleConfirmPickODTO implements Serializable {

    private static final long serialVersionUID = 3018288817991516217L;

    @ApiModelProperty(value = "拣货状态 0=待拣货 1=拣货中 2=拣货完成 3=已取消")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货子单状态 0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消，4=待交接")
    private Integer partitionPickStatus;

    @ApiModelProperty("拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty("拣货份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "拣货单明细id")
    private Long pickOrderItemId;

}
