package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotDayIDTO extends Pagination {

//    @ApiModelProperty("开始时间")
////    private String startTime;
////
////    @ApiModelProperty("结束时间")
////    private String endTime;
    @ApiModelProperty("库存快照时间")
    private String stockDate;

    @ApiModelProperty("部门Code")
    private String deptCode;

    @ApiModelProperty("门店id")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用）")
    private List<Integer> shopStatusList;

    @ApiModelProperty("1 负库存金额以0计算   0 否")
    private Integer stockStatus;

    @ApiModelProperty("经营模式: 1-直营, 2-外包")
    private Integer managementMode;
}
