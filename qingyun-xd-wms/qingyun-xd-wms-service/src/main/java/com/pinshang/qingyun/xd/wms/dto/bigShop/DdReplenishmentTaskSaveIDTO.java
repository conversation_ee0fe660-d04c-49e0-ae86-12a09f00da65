package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;


@Data
@ToString
@ApiModel("DdReplenishmentTaskSaveIDTO")
public class DdReplenishmentTaskSaveIDTO {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("任务类型，1-排面补货，2-拣货区补货")
    private Integer taskType;

    @ApiModelProperty("计划补货数量")
    private BigDecimal plannedQuantity;

}