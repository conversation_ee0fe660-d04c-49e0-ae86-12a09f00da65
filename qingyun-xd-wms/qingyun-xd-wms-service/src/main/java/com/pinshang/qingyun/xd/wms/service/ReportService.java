package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.PickNumReportDTO;
import com.pinshang.qingyun.xd.wms.dto.PickNumReportListDTO;
import com.pinshang.qingyun.xd.wms.dto.report.*;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockAllotOrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockReportMapper;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import com.pinshang.qingyun.xd.wms.service.bigShop.UserStallServiceImpl;
import com.pinshang.qingyun.xd.wms.util.StallUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class ReportService {

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private StockReportMapper stockReportMapper;
    @Autowired
    private StockAllotOrderItemMapper stockAllotOrderItemMapper;
    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private UserStallServiceImpl userStallServiceImpl;

    /**
     * 捡货数量统计
     * @param dto
     * @return
     */
    public MPage<PickNumReportListDTO> pickNumReportList(PickNumReportDTO dto) {
        dto.checkData();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        //如果查询全部就获取店铺列表
        if (StringUtils.isEmpty(dto.getWarehouseId()) || dto.getWarehouseId() == 0) {
            dto.setShopIdList(shopIdList);
        } else {
            dto.setShopIdList( new ArrayList<Long>(){{add(dto.getWarehouseId());}} );
        }
        //查询合计
        Integer sum = pickOrderMapper.pickNumReprotSum(dto);
        List<PickNumReportListDTO> list = new ArrayList<>();
        PickNumReportListDTO pickNumReportListDTO = new PickNumReportListDTO();
        pickNumReportListDTO.setNum(sum);
        list.add(pickNumReportListDTO);
        MPage<PickNumReportListDTO> res = pickOrderMapper.pickNumReportList(dto);
        list.addAll(res.getList());
        res.setRecords(list);
        return res;
    }

    /**
     * 报损明细
     * @param idto
     * @return
     */
    public MPage<StockBreakageODTO> queryStockBreakagePage(StockBreakageIDTO idto) {
        if(StringUtils.isEmpty(idto.getBeginDate()) || StringUtils.isEmpty(idto.getEndDate()) ){
            QYAssert.isTrue(false, "报损日期不能为空");
        }
        if (!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "报损日期的跨度不能超过31天");
        }
        MPage<StockBreakageODTO> page = stockReportMapper.queryStockBreakagePage(idto);
        if(page != null && SpringUtil.isNotEmpty(page.getList())){
            page.getList().stream().forEach(e->{
                if(null != e.getTotalPrice()){
                    e.setTotalPrice(e.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                }else {
                    e.setTotalPrice(BigDecimal.ZERO);
                }
            });
        }
        return page;
    }

    /**
     * 缺发少发明细
     * @param idto
     * @return
     */
    public MPage<StockShortODTO> queryStockShortPage(StockShortIDTO idto) {
        if(StringUtils.isEmpty(idto.getBeginDate()) || StringUtils.isEmpty(idto.getEndDate()) ){
            QYAssert.isTrue(false, "出库日期不能为空");
        }
        if (!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginDate(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "出库日期的跨度不能超过31天");
        }
        MPage<StockShortODTO> page = stockReportMapper.queryStockShortPage(idto);
        if(page != null && SpringUtil.isNotEmpty(page.getList())){
            page.getList().stream().forEach(e->{
                if(null != e.getTotalPrice()){
                    e.setTotalPrice(e.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                }else {
                    e.setTotalPrice(BigDecimal.ZERO);
                }
            });
        }
        return page;
    }

    public MPage<StockAllotDetailListODTO> allotOutDetailList(StockAllotDetailListIDTO dto) {
        boolean isNotEmptyInTime = !StringUtil.isNullOrEmpty(dto.getStartInTime()) && !StringUtil.isNullOrEmpty(dto.getEndInTime());
        boolean isNotEmptyOutTime = !StringUtil.isNullOrEmpty(dto.getStartOutTime()) && !StringUtil.isNullOrEmpty(dto.getEndOutTime());

        QYAssert.isTrue(isNotEmptyInTime || isNotEmptyOutTime,"出库日期或入库日期至少选择一个");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (StallUtils.isSingleShop(tokenInfo.getShopId()) && StallUtils.isBigShop(tokenInfo.getManagementMode()) && Objects.isNull(dto.getOutStallId())) {
            //单门店 大店，没传档口，则查询用户权限下的档口
            List<Long> stallIdList = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
            if (CollectionUtils.isEmpty(stallIdList)) {
                //没有档口，返回空的列表
                return new Pagination<>();
            }
            dto.setOutStallIdList(stallIdList);
        }
        MPage<StockAllotDetailListODTO> page =  stockAllotOrderItemMapper.allotOutDetailList(dto);
        if (page!=null && SpringUtil.isNotEmpty(page.getList())) {
            List<Long> commodityIds = page.getList().stream().map(StockAllotDetailListODTO::getCommodityId).collect(Collectors.toList());
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
            page.getList().forEach(e -> {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
                if (null != e.getWeightPrice() && null != e.getOutQuantity()) {
                    e.setSumWeightPrice(e.getWeightPrice().multiply(e.getOutQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
            });
        }
        return page;
    }

    public MPage<StockAllotDetailListODTO> allotInDetailList(StockAllotDetailListIDTO dto) {
        QYAssert.isTrue(!StringUtil.isNullOrEmpty(dto.getStartInTime()) && !StringUtil.isNullOrEmpty(dto.getEndInTime()),"入库日期条件不能为空");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (StallUtils.isSingleShop(tokenInfo.getShopId()) && StallUtils.isBigShop(tokenInfo.getManagementMode()) && Objects.isNull(dto.getInStallId())) {
            //单门店 大店，没传档口，则查询用户权限下的档口
            List<Long> stallIdList = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
            if (CollectionUtils.isEmpty(stallIdList)) {
                //没有档口，返回空的列表
                return new Pagination<>();
            }
            dto.setInStallIdList(stallIdList);
        }
        MPage<StockAllotDetailListODTO> page = stockAllotOrderItemMapper.allotInDetailList(dto);
        if (page!=null && SpringUtil.isNotEmpty(page.getList())) {
            List<Long> commodityIds = page.getList().stream().map(StockAllotDetailListODTO::getCommodityId).collect(Collectors.toList());
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
            page.getList().forEach(e -> {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
                if (null != e.getCommodityPrice() && null != e.getInQuantity()) {
                    e.setSumCommodityPrice(e.getCommodityPrice().multiply(e.getInQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            });
        }
        return page;
    }


}
