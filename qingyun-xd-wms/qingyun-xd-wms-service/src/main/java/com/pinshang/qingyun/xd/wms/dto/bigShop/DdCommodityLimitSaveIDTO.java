package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
public class DdCommodityLimitSaveIDTO{

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("生效方式: 1 不循环生效 2 每天循环生效")
    private Integer effectType;

    @ApiModelProperty("每天循环的时间起点，时 分 秒")
    private String loopTime;

    @ApiModelProperty("限量计算方式 1=从0计算  2=累加计算")
    private Integer limitType;

    @ApiModelProperty("商品明细")
    private List<DdCommodityLimitSaveItemIDTO> itemList;

}
