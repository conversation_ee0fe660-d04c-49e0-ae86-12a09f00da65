package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/16 18:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopPackageDetailItemODTO {

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "品名")
    private String commodityName;

    @ApiModelProperty(value = "规格")
    private String commoditySpec;

    @ApiModelProperty(value = "订货数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "商品计量单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "打包数量")
    private BigDecimal packageQuantity;

    @ApiModelProperty(value = "是否缺货")
    private Boolean lessGoods;
}
