package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@ToString
@ApiModel("DdConfirmPickIDTO")
public class DdConfirmPickIDTO implements Serializable {

    private static final long serialVersionUID = 3592777277290463302L;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "是否称重 0非称重 1称重")
    private Integer isWeight;

    @ApiModelProperty(value = "拣货明细")
    private List<DdConfirmPickDetailIDTO> groupPickOrderDetailList;

    public void checkData() {

        QYAssert.isTrue(Objects.nonNull(commodityId), "商品id不能为空");

        QYAssert.notEmpty(groupPickOrderDetailList, "拣货明细不能为空");

        groupPickOrderDetailList.forEach(data -> {
            QYAssert.notNull(data.getPickPartitionOrderId(), "分区拣货子单id不能为空");
            QYAssert.notNull(data.getPickOrderId(), "拣货单id不能为空");
            QYAssert.notNull(data.getPickOrderItemId(), "拣货单明细id不能为空");
            QYAssert.notNull(data.getPickQuantity(), "拣货数量不能为空");
            QYAssert.notNull(data.getPickNumber(), "拣货份数不能为空");
        });
    }
}
