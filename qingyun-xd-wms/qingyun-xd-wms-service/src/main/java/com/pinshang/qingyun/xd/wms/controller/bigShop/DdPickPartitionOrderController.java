package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.PickChangeDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import java.util.Objects;


@Slf4j
@RestController
@RequestMapping("/dd/pick/partition")
@Api(value = "大店分区拣货", tags = "大店分区拣货")
public class DdPickPartitionOrderController {

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;


    @PostMapping("/pickPartitionOrderList")
    @ApiOperation(value = "后管：分区拣货子单列表", notes = "分区拣货子单列表")
    public MPage<PickPartitionOrderListResult> pickPartitionOrderList(@RequestBody PickPartitionOrderDTO pickPartitionOrderDTO) {

        pickPartitionOrderDTO.setWarehouseId(FastThreadLocalUtil.getQY().getShopId());
        QYAssert.isTrue(Objects.nonNull(pickPartitionOrderDTO.getWarehouseId()), "门店不能为空");
        QYAssert.isTrue(StringUtils.isNotEmpty(pickPartitionOrderDTO.getOrderDeliveryBeginTime()), "请选择预约日期");
        QYAssert.isTrue(StringUtils.isNotEmpty(pickPartitionOrderDTO.getOrderDeliveryEndTime()), "请选择预约日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addDay(DateTimeUtil.parse(pickPartitionOrderDTO.getOrderDeliveryBeginTime(), DateTimeUtil.YYYY_MM_DD), 31),
                DateTimeUtil.parse(pickPartitionOrderDTO.getOrderDeliveryEndTime(), DateTimeUtil.YYYY_MM_DD)), "预约日期范围不能超过31天");


        pickPartitionOrderDTO.setOrderDeliveryBeginTime(pickPartitionOrderDTO.getOrderDeliveryBeginTime() + " 00:00:00");
        pickPartitionOrderDTO.setOrderDeliveryEndTime(pickPartitionOrderDTO.getOrderDeliveryEndTime() + " 23:59:59");

        if (SpringUtil.hasText(pickPartitionOrderDTO.getCreateBeginTime())) {
            pickPartitionOrderDTO.setCreateBeginTime(pickPartitionOrderDTO.getCreateBeginTime() + " 00:00:00");
            pickPartitionOrderDTO.setCreateEndTime(pickPartitionOrderDTO.getCreateEndTime() + " 23:59:59");
        }

        return ddPickPartitionOrderService.pickPartitionOrderList(pickPartitionOrderDTO);
    }

    @PostMapping("/pagePartitionPickOrder")
    @ApiOperation(value = "pda-拣货子单查询-分页", notes = "pda-拣货子单查询-分页")
    public MPage<PartitionPickOrderPageODTO> pagePartitionPickOrder(@RequestBody PartitionPickOrderPageIDTO req) {
        return ddPickPartitionOrderService.pagePartitionPickOrder(req);
    }

    @PostMapping("/listPartitionPickOrder")
    @ApiOperation(value = "手持：分区拣货-拣货中 1  待拣货 2 待交接 3", notes = "手持：分区拣货-拣货中 1  待拣货 2 待交接 3")
    @MethodRender
    public PickPartitionOrderODTO listPartitionPickOrder(@RequestBody PickPartitionOrderPageIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return ddPickPartitionOrderService.listPartitionPickOrder(tokenInfo, req);
    }

    @PostMapping("/beginPickOrder")
    @ApiOperation(value = "开始拣货", notes = "开始拣货")
    public Boolean beginPickOrder(@RequestParam(value = "pickPartitionOrderId") Long pickPartitionOrderId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return ddPickPartitionOrderService.beginPickOrder(tokenInfo, pickPartitionOrderId);
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "确认拣货-合单拣货使用", notes = "确认拣货-合单拣货使用")
    public DdConfirmPickODTO confirmPick(@RequestBody DdConfirmPickIDTO req) {
        return ddPickPartitionOrderService.confirmPick(req, true, true);
    }

    @PostMapping("/singleConfirm")
    @ApiOperation(value = "确认拣货-单个拣货子单 拣货 使用", notes = "确认拣货-单个拣货子单 拣货 使用")
    public DdSingleConfirmPickODTO singleConfirmPick(@RequestBody DdSingleConfirmPickIDTO req) {
        return ddPickPartitionOrderService.singleConfirmPick(req);
    }

    @PostMapping("/complete")
    @ApiOperation(value = "大店-手持、后端：完成拣货", notes = "完成拣货")
    public Boolean completePickOrder(@RequestBody DdPickCompleteIDTO req) {
        ddPickPartitionOrderService.completePickOrder(req);
        return Boolean.TRUE;
    }

    @Deprecated
    @PostMapping("/orderRush")
    @ApiOperation(value = "待拣货-抢单", notes = "待拣货-抢单")
    public Boolean orderRush(@RequestBody DdPickOrderRushIDTO req) {
        ddPickPartitionOrderService.orderRush(req);
        return Boolean.TRUE;
    }

    @PostMapping("/listCancelPickOrder")
    @ApiOperation(value = "取消单回收列表", notes = "取消单回收列表")
    @MethodRender
    public List<CancelPickOrderODTO> listCancelPickOrder() {
        return ddPickPartitionOrderService.listCancelPickOrder();
    }

    @PostMapping("/confirmCancel")
    @ApiOperation(value = "取消单确认回收", notes = "取消单确认回收")
    public Boolean confirmCancel(@RequestBody DdPartitionPickConfirmCancelIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddPickPartitionOrderService.confirmCancel(tokenInfo, req);
        return Boolean.TRUE;
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "大店-手持、后端：取消拣货", notes = "取消拣货")
    public Boolean cancelPickOrder(@RequestBody DdPickCancelIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddPickPartitionOrderService.cancelPartitionPickOrder(tokenInfo, req);
        return Boolean.TRUE;
    }

    @PostMapping("/groupPickDetail")
    @ApiOperation(value = "合单拣货详情", notes = "合单拣货详情")
    @MethodRender
    public List<GroupPickDetailODTO> groupPickDetail(@RequestBody GroupPickDetailIDTO req) {
        return ddPickPartitionOrderService.groupPickDetail(req);
    }

    @PostMapping("/distributePartitionPickOrder")
    @ApiOperation(value = "分配分区拣货单", notes = "分配分区拣货单")
    public Boolean distributePartitionPickOrder(@RequestBody(required = false) String shopId) {
        log.warn("-------分配分区拣货单任务执行,门店id:[{}]-------", shopId);
        ddPickPartitionOrderService.distributePartitionPickOrderList(shopId);
        return Boolean.TRUE;
    }

    @PostMapping("/distributePicker")
    @ApiOperation(value = "分配拣货人", notes = "分配拣货人")
    public Boolean distributePicker(@RequestBody PickChangeDTO pickChangeDTO) {
        pickChangeDTO.checkData();

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddPickPartitionOrderService.distributePicker(tokenInfo, pickChangeDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/changePicker")
    @ApiOperation(value = "改派拣货人", notes = "改派拣货人")
    public Boolean changePicker(@RequestBody PickChangeDTO pickChangeDTO) {
        pickChangeDTO.checkData();

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddPickPartitionOrderService.changePicker(tokenInfo, pickChangeDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/handoverDelivery")
    @ApiOperation(value = "送货交接", notes = "送货交接")
    public Boolean handoverDelivery(@RequestBody HandoverDeliveryIDTO req) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        ddPickPartitionOrderService.handoverDelivery(tokenInfo, req);
        return Boolean.TRUE;
    }

    @GetMapping("/getGroupPickLimitConfig")
    @ApiOperation(value = "合并拣货，最多可选n单", notes = "合并拣货，最多可选n单")
    public Integer getGroupPickLimitConfig() {
        return ddPickPartitionOrderService.getGroupPickLimitConfig();
    }

    @PostMapping("/pageOrderPacking")
    @ApiOperation(value = "分页查询订单打包", notes = "分页查询订单打包")
    public MPage<OrderPackingODTO> pageOrderPacking(@RequestBody OrderPackingIDTO req) {
        return ddPickPartitionOrderService.pageOrderPacking(req);
    }

    @PostMapping("/completeOrderPacking")
    @ApiOperation(value = "打包完成", notes = "打包完成")
    public Boolean completeOrderPacking(@RequestBody CompleteOrderPackingIDTO req) {
        return ddPickPartitionOrderService.completeOrderPacking(req);
    }

    @GetMapping("/orderPartitionPickDetail/{orderId}")
    @ApiOperation(value = "订单打包-订单详情", notes = "订单打包-订单详情")
    @MethodRender
    public OrderDetailODTO orderPartitionPickDetail(@PathVariable("orderId") String orderId) {
        return ddPickPartitionOrderService.orderPartitionPickDetail(orderId);
    }

}
