package com.pinshang.qingyun.xd.wms.listener;

import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 云超拣货抢单释放
 * Created by chenqi
 */
@Component
@Slf4j
public class CloudPickOccupyFreed implements RedisDelayQueueHandle<String> {

    @Autowired
    private RedissonClient redissonClient;

    private static String ORDER_CLOUD_OCCUPY = "WMS:ORDER_CLOUD_OCCUPY:";
    private static String ORDER_CLOUD_OCCUPY_USER = "WMS:ORDER_CLOUD_OCCUPY_USER:";

    @Override
    public void execute(String occupyCode) {
        log.info(occupyCode + " -> 云超拣货抢单释放");

        String[] split = occupyCode.split(":");
        Long shopId = Long.parseLong(split[0]);
        Long orderId = Long.parseLong(split[1]);
        Long userId = Long.parseLong(split[2]);
        RSet<Long> occupySet = redissonClient.getSet(ORDER_CLOUD_OCCUPY + shopId);
        RAtomicLong occupyLong = redissonClient.getAtomicLong(ORDER_CLOUD_OCCUPY_USER + userId);
        occupyLong.delete();
        occupySet.remove(orderId);
    }
}