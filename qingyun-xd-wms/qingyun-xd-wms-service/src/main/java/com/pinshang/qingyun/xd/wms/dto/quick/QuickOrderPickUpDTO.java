package com.pinshang.qingyun.xd.wms.dto.quick;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuickOrderPickUpDTO extends WarnCodeDTO {

    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("跟团号")
    private String withGroupNo;

    @ApiModelProperty("提货人")
    private String receiveMan;

    @ApiModelProperty("提货人手机号")
    private String receiveMobile;

    private Integer orderStatus;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("预约开始时间")
    private Date receiveTimeBegin;

    @ApiModelProperty("预约结束时间")
    private Date receiveTimeEnd;

    @ApiModelProperty("订单完成时间")
    private Date orderCompleteDate;

    @ApiModelProperty("取消时间：最后一次更新时间")
    private Date updateTime;

    @ApiModelProperty("商品列表")
    private List<QuickOrderItemPickUpDTO> items;
}
