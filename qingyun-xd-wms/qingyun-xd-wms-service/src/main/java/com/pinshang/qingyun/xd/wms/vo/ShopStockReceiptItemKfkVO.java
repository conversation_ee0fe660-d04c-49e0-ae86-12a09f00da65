package com.pinshang.qingyun.xd.wms.vo;/**
 * @Author: sk
 * @Date: 2025/7/2
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年07月02日 上午10:20
 */
@Data
public class ShopStockReceiptItemKfkVO {

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "现有份数")
    private Integer existStockNumber;

    @ApiModelProperty(value = "现有数量")
    private BigDecimal existStockQuantity;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "分摊比例")
    private BigDecimal commodityRate;

    @ApiModelProperty(value = "加工单id")
    private Long jgId;
}
