package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CloudGroupCommodityDTO {

    private Long itemId;

    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("数量")
    private BigDecimal totalQuantity;


    private String barCode;
    @ApiModelProperty("条码")
    private List<String> barCodeList;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String unitName;

    @ApiModelProperty("已经拣货数量")
    private BigDecimal pickQuantity;

}
