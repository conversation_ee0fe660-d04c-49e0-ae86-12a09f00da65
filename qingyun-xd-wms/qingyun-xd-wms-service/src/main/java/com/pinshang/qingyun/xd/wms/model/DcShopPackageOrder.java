package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="云超包裹信息", description="云超包裹信息")
@TableName("t_dc_shop_package_order")
public class DcShopPackageOrder {


    private Long id;

    /**
     * 包裹单号
     */
    private String orderCode;

    /**
     * 包裹生成方式(0:手工；1:自动)
     */
    private Integer packageType;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 提送日期
     */
    private Date orderTime;

    /**
     * 0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次(装箱用)
     */
    private Integer deliveryBatch;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 客户ID
     */
    private Long storeId;

    /**
     * 箱码(装箱用)
     */
    private String boxCode;

    /**
     * 配送任务id(t_ps_delivery_task.id) 揽收时才赋值，因为揽收到不能再调配送任务，此时送货员才能固定)
     */
    private Long deliveryTaskId;

    /**
     * 送货员id(揽收时才赋值，因为揽收到不能再调配送任务，此时送货员才能固定)
     */
    private Long deliveryManId;

    /**
     * 1:周转筐，2:冻品箱，3:整箱(装箱用)
     */
    private Integer boxType;


    /**
     * 是否速冻产品(0:否；1：是)
     */
    private Integer commodityIsQuickFreeze;

    /**
     * 播种区域ID(发货仓库数据)
     */
    private Long seedAreaId;

    /**
     * 状态(大仓使用) 0=异常 1=正常
     */
    private Integer status;

    /**
     * 重量状态(门店使用) 0=异常 1=正常(只要有一个明细重量<订单数量即异常)
     */
    private Integer quantityStatus;

    /**
     * 包裹状态(门店使用)　0=已取消 1=待装筐，2=待揽收，3=待卸货，4=待收货，5=待门店拣货，6=已拣货( 一期时状态  1= 门店未验证  4=待顾客提货  7＝顾客已提货）
     */
    private Integer packageStatus;

    /**
     * 是否装箱(0:否；1：是)
     */
    private Integer isBox;

//    /**
//     * 子单主键(t_xd_sub_order.id)
//     */
//    private Long subOrderId;
//    /**
//     * 发货单ID(t_dc_delivery_order.id)
//     */
//    private Long deliveryOrderId;
    /**
     *
     */
    private Integer referType;

    /**
     * 相关单号(t_xd_order.order_code)
     */
    private String referCode;

    /**
     * 相关单据ID(t_xd_order.id)
     */
    private Long referId;


    /**
     * 相关单号(t_xd_order.order_code)
     */
    private String referOrderCode;

    /**
     * 相关单据ID(t_xd_order.id)
     */
    private Long referOrderId;

    /**
     * 相关单据用户手机号(鲜到的用户名)
     */
    private String referOrderUserMobile;

    /**
     * 相关单据收货人手机号
     */
    private String referOrderReceiveMobile;

    /**
     * 商品总数量(sum(package_quantity))
     */
    private BigDecimal totalQuantity;

    /**
     * 商品总份数(sum(package_number))
     */
    private BigDecimal totalNumber;

    /**
     * 装箱人ID(装筐人ID user用户)
     */
    private Long loaderBoxId;

    /**
     * 装箱时间
     */
    private Date loadBoxTime;

    /**
     * 揽收人ID（user用户）
     */
    private Long takerBoxId;

    /**
     * 揽收时间
     */
    private Date takeBoxTime;

    /**
     * 卸货人（user用户）
     */
    private Long unloaderBoxId;

    /**
     * 卸货时间
     */
    private Date unloadBoxTime;

    /**
     * 收货人（此时货已送到门店，门店人员操作收货）
     */
    private Long receiverBoxId;

    /**
     * 收货时间
     */
    private Date receiveBoxTime;

    /**
     * 拣货人（门店人员操作的动作）
     */
    private Long pickerBoxId;

    /**
     * 拣货时间
     */
    private Date pickBoxTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    private Date updateTime;

    private Long updateId;
}
