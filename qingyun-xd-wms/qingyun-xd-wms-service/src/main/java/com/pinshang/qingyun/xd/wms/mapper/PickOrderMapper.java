package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdPickAreaEmployeeODTO;
import com.pinshang.qingyun.xd.wms.model.PickOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PickOrderMapper extends BaseMapper<PickOrder> {

    /**
     * 门店下面正在拣货的商品
     * @param warehouseId
     * @return
     */
    List<Long> pickOrderMidCommodityId(@Param("warehouseId") Long warehouseId);

    /**
     * 仓库拣货详情
     * @param warehouseId
     * @param commodityId
     * @return
     */
    int pickOrderByCommodity(@Param("warehouseId") Long warehouseId, @Param("commodityId") Long commodityId);

    /**
     * 查询拣货单
     * @param pickOrderDTO
     * @return
     */
    MPage<PickOrderListResult> pickOrderList(@Param("dto") PickOrderDTO pickOrderDTO);

    /**
     * 根据id查询拣货单
     * @param pickOrderId
     * @return
     */
    PickOrderListResult pickOrderById(@Param("pickOrderId") Long pickOrderId, @Param("warehouseId") Long warehouseId);

    /**
     * 根据订单ids查询拣货单
     * @param orderIds
     * @return
     */
    List<PickOrderResult> selectPickByOrderId(@Param("orderIds") List<Long> orderIds);

    PickOrderResult getPickOrderByOrderId(@Param("orderId") Long orderId);
    /**
     * 根据订单id查询拣货单
     * @param orderId
     * @return
     */
    PickOrderMqDTO selectPickOrderByOrderId(@Param("orderId") Long orderId);

    Integer orderCancelPickOrder(@Param("orderId") Long orderId);

    /**
     * 待拣货和拣货中的数量
     * @param list
     * @return
     */
    List<TaskNumDTO> pickNum(@Param("list") List<Long> list);

    MPage<PickNumReportListDTO> pickNumReportList(@Param("dto") PickNumReportDTO dto);

    Integer pickNumReprotSum(@Param("dto") PickNumReportDTO dto);

    int updateByOrderId(@Param("orderId") Long orderId);

    Integer queryPickOrderCount(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 查询有待拣货订单的门店，多个门店
     * @param shopIds
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Long> queryPickOrderList(@Param("shopIds") List<Long> shopIds, @Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("orderTypeList") List<Integer> orderTypeList);

    Integer queryDeliveryOrderCount(@Param("shopId") Long shopId, @Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("orderTypeList") List<Integer> orderTypeList);

    /**
     * 查询有带配送订单的门店，多个门店
     * @param shopIds
     * @param beginTime
     * @param endTime
     * @param orderTypeList
     * @return
     */
    List<Long> queryDeliveryOrder(@Param("shopIds") List<Long> shopIds, @Param("beginTime") String beginTime, @Param("endTime") String endTime,
                                  @Param("orderTypeList") List<Integer> orderTypeList, @Param("sourceTypeList") List<Integer> sourceTypeList);

    Integer queryNewOrderCount(@Param("shopId") Long shopId, @Param("deliveryDate") String deliveryDate);

    /**
     * 检测异常拣货单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectAbnormalPickOrder(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 检测自提异常单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectAbnormalSelfOrder(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 若拣货员名下存在未完成的拣货单，则同时清除对应拣货单的拣货员
     * @param employeeId
     * @return
     */
    Integer clearPickOrderEmployee(@Param("employeeId") Long employeeId);

    /**
     * 分区拣货员在当前分区下未完成的分区拣货子单数
     * @param employeeIdList
     * @return
     */
    List<DdPickAreaEmployeeODTO> queryPickOrderCountByEmployee(@Param("employeeIdList") List<Long> employeeIdList);

    List<PickOrder> listZoneWaitDistributePickOrderList(@Param("beginTime") String beginTime, @Param("endTime") String endTime
            , @Param("soonEndTime") String soonEndTime, @Param("deliveryEndTime") String deliveryEndTime, @Param("shopId") Long shopId);

    void updatePackingIdAndEndTimeByOrderId(@Param("orderId") Long orderId, @Param("packingId") Long packingId);
}
