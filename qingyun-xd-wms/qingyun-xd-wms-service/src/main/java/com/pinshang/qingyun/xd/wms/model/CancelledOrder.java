package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_xd_cancelled_order")
public class CancelledOrder extends BaseEntity {


    @ApiModelProperty(value = "默认0(目前只有饿了么)")
    private Integer sourceType;

    @ApiModelProperty(value = "前置仓id")
    private Long shopId;

    @ApiModelProperty(value = "取消单单号")
    private String cancelledOrderCode;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty(value = "订单时间")
    private Date orderTime;

    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;

    @ApiModelProperty(value = "入库时间")
    private Date stockInTime;

    @ApiModelProperty(value = "入库状态: 0 未入库   1 已入库")
    private Integer stockInStatus;

    @ApiModelProperty(value = "配送员id")
    private Long deliveryId;

    @ApiModelProperty(value = "配送员姓名")
    private String deliveryMan;

    @ApiModelProperty(value = "配送员电话")
    private String deliveryMobile;

    @ApiModelProperty(value = "取货号")
    private String pickCode;

    @ApiModelProperty(value = "收货人id")
    private Long receiveId;

    @ApiModelProperty(value = "收货人姓名")
    private String receiveMan;

}
