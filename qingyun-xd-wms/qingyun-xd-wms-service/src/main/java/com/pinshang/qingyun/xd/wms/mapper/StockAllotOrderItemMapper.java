package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.OrderSettleItemODTO;
import com.pinshang.qingyun.xd.wms.dto.StockAllotOrderDetailItemResult;
import com.pinshang.qingyun.xd.wms.dto.report.StockAllotDetailListIDTO;
import com.pinshang.qingyun.xd.wms.dto.report.StockAllotDetailListODTO;
import com.pinshang.qingyun.xd.wms.model.StockAllotOrderItem;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface StockAllotOrderItemMapper extends BaseMapper<StockAllotOrderItem> {
    Integer batchInsert(@Param("list") List<StockAllotOrderItem> list);

    List<StockAllotOrderDetailItemResult> selectAllotItemList(@Param("stockAllotId") Long stockAllotId);

    /**
     * 结算宽表需要  基于 调拨单ID 获取调拨单item详情
     * @param stockAllotIds
     * @return
     */
    List<OrderSettleItemODTO> findListByStockAllotIds(@Param("stockAllotIds") List<Long> stockAllotIds);

//    MPage<StockAllotDetailListODTO> selectAllotDetailList(@Param("dto") StockAllotDetailListIDTO dto);

//    MPage<StockAllotDetailListOfHeadODTO> selectAllotDetailListOfHead(@Param("dto") StockAllotDetailListOfHeadIDTO dto);

    /**
     * 调拨出库单明细
     * @param dto
     * @return
     */
    MPage<StockAllotDetailListODTO> allotOutDetailList(@Param("dto") StockAllotDetailListIDTO dto);

    /**
     * 调拨入库单明细
     * @param dto
     * @return
     */
    MPage<StockAllotDetailListODTO> allotInDetailList(@Param("dto") StockAllotDetailListIDTO dto);
}
