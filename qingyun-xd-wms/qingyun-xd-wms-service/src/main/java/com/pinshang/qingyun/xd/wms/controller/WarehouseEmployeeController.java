package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/warehouse/employee")
@Api(value = "仓库员工管理", tags = "WarehouseController")
public class WarehouseEmployeeController {

    @Autowired
    private WarehouseEmployeeService employeeService;

    @PostMapping("batchInsertWarehouseEmployee")
    @ApiOperation(value = "批量新增仓库员工", notes = "批量新增仓库员工")
    public Integer batchInsertWarehouseEmployee(@RequestBody List<WarehouseEmployeeDTO> dtoList) {
        return employeeService.batchInsert(dtoList);
    }

    @GetMapping("searchEmployeeByType")
    @ApiOperation(value = "根据职员类型查询员工列表(带分页)", notes = "根据职员类型查询员工列表(带分页)")
    public PageInfo<WarehouseEmployeeInfoDTO> searchEmployeeByType(SearchWarehouseEmployeeDTO searchDTO) {
        return employeeService.searchEmployeeByType(searchDTO);
    }

    @GetMapping("getEmployeeByType")
    @ApiOperation(value = "根据职员类型查询员工列表", notes = "根据职员类型查询员工列表")
    public List<WarehouseEmployeeInfoDTO> getEmployeeByType(WarehouseEmployeeTypeEnum typeEnum) {
        QYAssert.isTrue(typeEnum != null && WarehouseEmployeeTypeEnum.getTypeEnumByCode(typeEnum.getCode()) != null,
                "职员类型不能为空");
        SearchWarehouseEmployeeDTO searchDTO = new SearchWarehouseEmployeeDTO();
        searchDTO.setTypeEnum(typeEnum);
        return employeeService.getEmployeeByType(searchDTO);
    }


    @PostMapping("getEmployeeByTypeAndShop")
    @ApiOperation(value = "根据职员类型和门店id查询员工列表", notes = "根据职员类型和门店id查询员工列表-剔除已选")
    public List<WarehouseEmployeeInfoDTO> getEmployeeByTypeAndShop(@RequestBody GetEmployeeByTypeAndShopIDTO idto) {
        QYAssert.isTrue(idto.getTypeEnum() != null && WarehouseEmployeeTypeEnum.getTypeEnumByCode(idto.getTypeEnum().getCode()) != null,
                "职员类型不能为空");
        return employeeService.getEmployeeByTypeForCloud(idto);
    }

    @DeleteMapping("deleteByEmployeeId")
    @ApiOperation(value = "移除仓库员工", notes = "移除仓库员工")
    public Integer deleteByEmployeeId(DelWarehouseEmployee delWarehouseEmployee) {
        return employeeService.deleteByEmployeeId(delWarehouseEmployee);
    }

    @DeleteMapping("deleteByEmployeeIds")
    @ApiOperation(value = "批量移除仓库员工", notes = "批量移除仓库员工")
    public Integer deleteByEmployeeId(@RequestParam(value = "ids",required = false) List<Long> ids) {
        return employeeService.deleteByEmployeeIds(ids);
    }

    @GetMapping("queryEmployeeRealTimeMonitoring")
    @ApiOperation(value = "员工实时监控", notes = "员工实时监控")
    public PageInfo<WarehouseEmployeeInfoDTO> queryEmployeeRealTimeMonitoring(SearchWarehouseEmployeeDTO searchDTO){
        return employeeService.queryEmployeeWorkStatus(searchDTO);
    }


    @PutMapping("startPickWork")
    @ApiOperation(value = "开始拣货接单", notes = "开始拣货接单")
    public Boolean startPickWork(){
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        Long employeeId = FastThreadLocalUtil.getQY().getEmployeeId();
        employeeService.startPickWork(shopId, employeeId);
        return Boolean.TRUE;
    }

    @PutMapping("stopPickWork")
    @ApiOperation(value = "暂停拣货接单", notes = "暂停拣货接单")
    public Boolean stopPickWork(){
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        Long employeeId = FastThreadLocalUtil.getQY().getEmployeeId();
        employeeService.stopPickWork(shopId, employeeId);
        return Boolean.TRUE;
    }

    @PostMapping("refreshPickEmployee")
    @ApiOperation(value = "刷新拣货职员", notes = "刷新拣货职员")
    public Boolean refreshPickEmployee(){

        employeeService.refreshPickEmployee();
        return Boolean.TRUE;
    }

    @GetMapping("queryPicker4Me")
    @ApiOperation(value = "查询我的状态(拣货员)", notes = "查询我的状态(拣货员)")
    public WarehouseEmployee queryPicker4Me(){
        return employeeService.queryPicker4Me();
    }

    @GetMapping("updatePhoneByEmployeeId")
    @ApiOperation(value = "更新配送员手机号",notes = "更新配送员手机号")
    public void updatePhoneByEmployeeId(UpdateWarehouseEmployeeDTO updateDTO) {
        employeeService.updateByEmployeeId(updateDTO);
    }

    @GetMapping("queryEmployee")
    @ApiOperation(value = "查询我的状态(拣货员)", notes = "查询我的状态(拣货员)")
    public WarehouseEmployee queryEmployee(@RequestParam(value = "employeeId",required = false) Long employeeId, @RequestParam(value = "type",required = false) Integer type){
        return employeeService.queryEmployee(employeeId, type);
    }

    @PostMapping("updateWorkStatus")
    @ApiOperation(value = "批量更新员工的工作状态", notes = "批量更新员工的工作状态")
    public void updateWorkStatus(@RequestBody UpdateWorkStatusDTO updateWorkStatusDTO) {
        employeeService.updateWorkStatus(updateWorkStatusDTO);
    }

    @PostMapping("employeeList")
    @ApiOperation(value = "查询员工列表", notes = "查询员工列表")
    public MPage<WarehouseEmployeeResult> employeeList(@RequestBody QueryWarehouseEmployee dto) {
        return employeeService.employeeList(dto);
    }
}
