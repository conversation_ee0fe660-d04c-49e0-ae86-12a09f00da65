package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.exception.BusinessException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.shop.dto.ShopODTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityODTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityQueryIDTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.CommodityLimitIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.mapper.CommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallCommodityMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.StallMapper;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.ShopCommodityStock;
import com.pinshang.qingyun.xd.wms.model.bigShop.Stall;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodity;
import com.pinshang.qingyun.xd.wms.model.bigShop.StallCommodityStock;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.WeChatSendMessageService;
import com.pinshang.qingyun.xd.wms.service.XdSendLogService;
import com.pinshang.qingyun.xd.wms.service.redislock.RedisLockService;
import com.pinshang.qingyun.xd.wms.service.stock.ShopCommodityStockService;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import com.pinshang.qingyun.xd.wms.util.StallUtils;
import com.pinshang.qingyun.xd.wms.vo.bigShop.BigShopCommodityUpDownKafkaVo;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 档口商品库存  服务service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Slf4j
@Service
public class StallCommodityStockService extends ServiceImpl<StallCommodityStockMapper, StallCommodityStock> {

    @Autowired
    private StallCommodityStockMapper stallCommodityStockMapper;

    @Autowired
    private StallCommodityService stallCommodityService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private StallCommodityMapper stallCommodityMapper;

    @Autowired
    private XdSendLogService xdSendLogService;

    @Autowired
    private StallMapper stallMapper;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private ShopCommodityStockService shopCommodityStockService;

    @Autowired
    private UserStallServiceImpl userStallServiceImpl;

    @Autowired
    @Lazy
    private ShopCommodityService shopCommodityService;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Autowired
    private StallClient stallClient;


    @Transactional(rollbackFor = Exception.class)
    public int batchInsertStallCommodityStock(StallCommodityStockIDTO idto) {
        return stallCommodityStockMapper.batchInsertStallCommodityStock(StallCommodityStock.forInsert(idto));
    }

    /**
     * 查询单个档口商品库存
     */
    public StallCommodityStock queryOneStock(Long shopId, Long stallId, Long commodityId) {
        LambdaQueryWrapper<StallCommodityStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StallCommodityStock::getShopId, shopId)
                .eq(StallCommodityStock::getStallId, stallId)
                .eq(StallCommodityStock::getCommodityId, commodityId);
        return stallCommodityStockMapper.selectOne(queryWrapper);
    }

    /**
     * 查询单个档口商品库存
     */
    public List<StallCommodityStock> queryStallCommodityStockHasStock(Long shopId, Long stallId, List<Long> commodityIdList) {
        LambdaQueryWrapper<StallCommodityStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StallCommodityStock::getShopId, shopId)
                .eq(StallCommodityStock::getStallId, stallId)
                .gt(StallCommodityStock::getStockQuantity, BigDecimal.ZERO)
                .in(StallCommodityStock::getCommodityId, commodityIdList);
        return stallCommodityStockMapper.selectList(queryWrapper);
    }

    /**
     * 批量查询档口商品库存
     */
    public List<StallCommodityStock> batchQueryStock(List<StallCommodity> stallCommodityList) {
        LambdaQueryWrapper<StallCommodityStock> queryWrapper = new LambdaQueryWrapper<>();
        stallCommodityList.forEach(dto -> queryWrapper.or(
                wrapper1 -> wrapper1.eq(StallCommodityStock::getShopId, dto.getShopId())
                        .eq(StallCommodityStock::getStallId, dto.getStallId())
                        .eq(StallCommodityStock::getCommodityId, dto.getCommodityId())
        ));
        return this.baseMapper.selectList(queryWrapper);
    }


    public void increaseStockUnCheck(List<StockItemDTO> commodityList, Long warehouseId) {
        processStockUnCheck(commodityList, warehouseId);
    }

    public void processStockUnCheck(List<StockItemDTO> commodityList, Long warehouseId) {

        List<StockItemDTO> stallNotUpStatusList = Lists.newArrayList();

        for (StockItemDTO commodity : commodityList) {
            if (BigDecimal.ZERO.compareTo(commodity.getQuantity()) == 0) {
                continue;
            }

            DdStockInOutExtraVO ddStockInOutExtraVO = commodity.getDdStockInOutExtraVO();
            Long stallId = ddStockInOutExtraVO.getStallId();
            Integer storageArea = ddStockInOutExtraVO.getStorageArea();
            StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);

            goodsAllocationCommodityService.processGoodsAllocationCommodityStockUnCheck(warehouseId, ddStockInOutExtraVO, commodity);

            switch (storageAreaEnum) {
                case SHELF_AREA:
                    stallCommodityStockMapper.processShelveStockUnCheck(warehouseId, commodity, stallId);
                    break;
                case PROVISIONAL_AREA:
                    stallCommodityStockMapper.processProvisionalStockUnCheck(warehouseId, commodity, stallId);
                    break;
                case PICKING_AREA:
                    stallCommodityStockMapper.processPickingAreaStockUnCheck(warehouseId, commodity, stallId);
                    break;
                case WAREHOUSE_AREA:
                    stallCommodityStockMapper.processWarehouseAreaStockUnCheck(warehouseId, commodity, stallId);
                    break;
                default:
                    throw new BusinessException("Unknown storage area: " + storageArea);
            }

            dealStallCommodityAppOnStatus(warehouseId, commodity, stallId, stallNotUpStatusList, storageAreaEnum);

        }

        Collection<StockItemDTO> subtractCommodityList = CollectionUtils.subtract(commodityList, stallNotUpStatusList);
        dealStockMessage(warehouseId, subtractCommodityList);
    }

    /**
     * 按可用库存处理
     * 库存、冻结库存 都要调用
     */
    private void dealStockMessage(Long warehouseId, Collection<StockItemDTO> commodityList) {
        if (SpringUtil.isEmpty(commodityList)) {
            return;
        }
        shopCommodityService.dealStockMessage(warehouseId, commodityList);
    }

    public void increaseStock(List<StockItemDTO> commodityList, Long warehouseId) {
        processStock(commodityList, warehouseId);
    }

    public void processStock(List<StockItemDTO> commodityList, Long warehouseId) {

        List<StockItemDTO> stallNotUpStatusList = Lists.newArrayList();

        for (StockItemDTO commodity : commodityList) {
            if (BigDecimal.ZERO.compareTo(commodity.getQuantity()) == 0) {
                continue;
            }


            Long commodityId = commodity.getCommodityId();
            DdStockInOutExtraVO ddStockInOutExtraVO = commodity.getDdStockInOutExtraVO();
            Long stallId = ddStockInOutExtraVO.getStallId();

            goodsAllocationCommodityService.processGoodsAllocationCommodityStock(warehouseId, ddStockInOutExtraVO, commodity);

            Integer storageArea = ddStockInOutExtraVO.getStorageArea();
            StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);
            int resultCount = 0;
            switch (storageAreaEnum) {
                case SHELF_AREA:
                    resultCount = stallCommodityStockMapper.processShelveStock(warehouseId, commodity, stallId);
                    break;
                case PROVISIONAL_AREA:
                    resultCount = stallCommodityStockMapper.processProvisionalStock(warehouseId, commodity, stallId);
                    break;
                case PICKING_AREA:
                    resultCount = stallCommodityStockMapper.processPickingAreaStock(warehouseId, commodity, stallId);
                    break;
                case WAREHOUSE_AREA:
                    resultCount = stallCommodityStockMapper.processWarehouseAreaStock(warehouseId, commodity, stallId);
                    break;
                default:
                    QYAssert.isFalse("Unknown storage area: " + storageArea);
            }

            if (resultCount == 0 && commodity.getQuantity() != null && commodity.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
                Commodity comm = commodityMapper.selectById(commodityId);
                QYAssert.isFalse(comm.getCommodityCode() + comm.getCommodityName() + "库存不足,出库失败!");
            }

            dealStallCommodityAppOnStatus(warehouseId, commodity, stallId, stallNotUpStatusList, storageAreaEnum);

        }


        Collection<StockItemDTO> subtractCommodityList = CollectionUtils.subtract(commodityList, stallNotUpStatusList);
        dealStockMessage(warehouseId, subtractCommodityList);
    }

    private void dealStallCommodityAppOnStatus(Long warehouseId, StockItemDTO commodity, Long stallId, List<StockItemDTO> stallNotUpStatusList, StorageAreaEnum storageAreaEnum) {

        // 变更的是临时库，不需要维护门店商品库存
        if (StorageAreaEnum.PROVISIONAL_AREA.equals(storageAreaEnum)) {
            return;
        }

        // 维护门店商品库存
        Long commodityId = commodity.getCommodityId();
        List<StallCommodity> appOnStatus = stallCommodityService.filterStallCommodityAppOnStatus(warehouseId, stallId, Collections.singletonList(commodityId));
        if (SpringUtil.isNotEmpty(appOnStatus)) {
            shopCommodityService.updateStallAppOnStatusStock(warehouseId, commodityId, commodity.getQuantity());
        } else {
            stallNotUpStatusList.add(commodity);
        }
    }

    public void reduceStock(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO itemDTO : commodityList) {
            if (itemDTO.getQuantity() != null) {
                itemDTO.setQuantity(itemDTO.getQuantity().negate());
                if (itemDTO.getStockNumber() != null) {
                    itemDTO.setStockNumber(-itemDTO.getStockNumber());
                }
            }
        }

        processStock(commodityList, warehouseId);
    }

    public void reduceStockUnCheck(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO itemDTO : commodityList) {
            if (itemDTO.getQuantity() != null) {
                itemDTO.setQuantity(itemDTO.getQuantity().negate());
                if (itemDTO.getStockNumber() != null) {
                    itemDTO.setStockNumber(-itemDTO.getStockNumber());
                }
            }
        }

        processStockUnCheck(commodityList, warehouseId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void increaseQualityStock(List<StockItemDTO> commodityList, Long warehouseId) {
        processQualityStock(commodityList, warehouseId);
    }

    private void processQualityStock(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO commodity : commodityList) {
            DdStockInOutExtraVO ddStockInOutExtraVO = commodity.getDdStockInOutExtraVO();
            goodsAllocationCommodityService.processGoodsAllocationCommodityStock(warehouseId, ddStockInOutExtraVO, commodity);
            int resultCount = this.baseMapper.processProvisionalStock(warehouseId, commodity, ddStockInOutExtraVO.getStallId());
            QYAssert.isTrue(resultCount > 0, "临时库存不足,临时库出库失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void reduceQualityStock(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO itemDTO : commodityList) {

            if (itemDTO.getQuantity() != null) {
                itemDTO.setQuantity(itemDTO.getQuantity().negate());
            }
        }

        processQualityStock(commodityList, warehouseId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void reduceQualityStockUnCheck(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO itemDTO : commodityList) {

            if (itemDTO.getQuantity() != null) {
                itemDTO.setQuantity(itemDTO.getQuantity().negate());
            }
        }

        processQualityStockUnCheck(commodityList, warehouseId);
    }

    private void processQualityStockUnCheck(List<StockItemDTO> commodityList, Long warehouseId) {
        for (StockItemDTO commodity : commodityList) {
            DdStockInOutExtraVO ddStockInOutExtraVO = commodity.getDdStockInOutExtraVO();
            goodsAllocationCommodityService.processGoodsAllocationCommodityStockUnCheck(warehouseId, ddStockInOutExtraVO, commodity);
            this.baseMapper.processProvisionalStockUnCheck(warehouseId, commodity, ddStockInOutExtraVO.getStallId());
        }
    }

    @Deprecated
    public List<StallCommodityStock> queryStallCommodityStock(Long warehouseId, Long stallId, List<Long> commodityIdList) {

        LambdaQueryWrapper<StallCommodityStock> queryWrapper = new LambdaQueryWrapper<StallCommodityStock>()
                .eq(StallCommodityStock::getShopId, warehouseId)
                .eq(StallCommodityStock::getStallId, stallId)
                .in(StallCommodityStock::getCommodityId, commodityIdList);
        return this.baseMapper.selectList(queryWrapper);
    }

    public List<StallCommodityStock> queryStallCommodityStock(Long warehouseId, List<DdStockInOutExtraVO> ddStockInOutExtraVOList) {
        LambdaQueryWrapper<StallCommodityStock> wrapper = new LambdaQueryWrapper<>();

        ddStockInOutExtraVOList.forEach(data -> wrapper.or(
                wrapper1 -> wrapper1.eq(StallCommodityStock::getShopId, warehouseId)
                        .eq(StallCommodityStock::getStallId, data.getStallId())
                        .eq(StallCommodityStock::getCommodityId, data.getCommodityId())
        ));

        return this.list(wrapper);
    }

    /**
     * 设置预留库存
     *
     * @return
     */
    @Transactional
    public Boolean setReserveStock(Long id, BigDecimal reserveStock) {
        StallCommodityStock commodityStock = this.baseMapper.selectById(id);
        QYAssert.isTrue(null != commodityStock, "该数据不存在");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        this.baseMapper.updateReserveStock(id, reserveStock, tokenInfo.getUserId(), tokenInfo.getRealName());

        LambdaQueryWrapper<StallCommodity> query = new LambdaQueryWrapper<StallCommodity>()
                .eq(StallCommodity::getShopId, commodityStock.getShopId())
                .eq(StallCommodity::getStallId, commodityStock.getStallId())
                .eq(StallCommodity::getCommodityId, commodityStock.getCommodityId());
        StallCommodity stallCommodity = stallCommodityMapper.selectOne(query);
        // 上架，线上可售 更新门店商品表的预留库存
        if (YesOrNoEnums.NO.getCode().equals(stallCommodity.getAppStatus())) {
            CommodityReserveStockDTO dto = new CommodityReserveStockDTO(commodityStock.getCommodityId(), reserveStock);
            this.baseMapper.updateShopCommodityReserveStock(tokenInfo.getShopId(), commodityStock.getStallId(), tokenInfo.getUserId(), Arrays.asList(dto));
        }

        commodityStock = this.baseMapper.selectById(id);
        saveLog(Arrays.asList(commodityStock), 1, tokenInfo);

        this.dealStockMessage(commodityStock.getShopId(), Collections.singletonList(commodityStock.getCommodityId()));
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importReserveStock(Workbook wb, Long stallId) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        List<ImportReserveStockDTO> importDataList = new ArrayList<>();
        ImportReserveStockDTO importData = null;

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long shopId = tokenInfo.getShopId();

        int totalRow = sheet.getLastRowNum() + 1;
        QYAssert.isTrue(totalRow <= 1001, "每次最多导入1000行");

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "商品编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "线下预留库存*"), "模板不正确");
            }

            if (rowNum > 0) {
                if (row.getCell(0) == null || row.getCell(1) == null
                        || "".equals(row.getCell(0).getStringCellValue().trim()) || "".equals(row.getCell(1).getStringCellValue().trim())) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }
                try {
                    importData = new ImportReserveStockDTO();
                    importData.setCommodityCode(row.getCell(0).getStringCellValue().trim());
                    importData.setReserveStock(new BigDecimal(row.getCell(1).getStringCellValue()).setScale(3, RoundingMode.HALF_UP));
                    if (null != importData.getReserveStock() && (importData.getReserveStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || importData.getReserveStock().compareTo(BigDecimal.ZERO) < 0)) {
                        errorList.add((rowNum + 1) + "最小数量最大数量请输入0-99999之间的数字");
                        continue;
                    }
                    importDataList.add(importData);
                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }
            }
        }

        if (SpringUtil.isEmpty(errorList)) {
            QYAssert.isTrue(SpringUtil.isNotEmpty(importDataList), "导入数据不能为空");

            List<String> commodityCodes = importDataList.stream().map(ImportReserveStockDTO::getCommodityCode).collect(Collectors.toList());
            List<Commodity> commodities = commodityMapper.queryCommodityByCodeList(commodityCodes);
            Map<String, Long> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getCommodityCode, Commodity::getId));

            Map<Long, StallCommodityStock> stockMap = new HashMap<>();
            if (SpringUtil.isNotEmpty(commodities)) {
                List<Long> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
                List<StallCommodityStock> stallCommodityStocks = queryStallCommodityStock(shopId, stallId, commodityIds);
                stockMap = stallCommodityStocks.stream().collect(Collectors.toMap(StallCommodityStock::getCommodityId, e -> e));
            }

            List<StallCommodityStock> updateData = new ArrayList<>();
            List<CommodityReserveStockDTO> commodityList = new ArrayList<>();
            StallCommodityStock stallCommodityStock = null;
            CommodityReserveStockDTO commodityReserveStockDTO = null;
            List<Long> ids = new ArrayList<>();

            // 校验商品编码重复
            Map<String, Integer> repeatCount = new HashMap<>();

            for (ImportReserveStockDTO dto : importDataList) {

                if (repeatCount.containsKey(dto.getCommodityCode())) {
                    if (1 == repeatCount.get(dto.getCommodityCode())) {
                        errorList.add(dto.getCommodityCode() + "商品编码重复");
                    }
                    repeatCount.put(dto.getCommodityCode(), (repeatCount.get(dto.getCommodityCode()) + 1));
                    continue;
                }

                if (!commodityMap.containsKey(dto.getCommodityCode())) {
                    errorList.add(dto.getCommodityCode() + "商品编码不存在");
                    continue;
                }

                if (!stockMap.containsKey(commodityMap.get(dto.getCommodityCode()))) {
                    errorList.add(dto.getCommodityCode() + "商品所属档口不是所选档口");
                    continue;
                }

                stallCommodityStock = new StallCommodityStock();
                stallCommodityStock.setId(stockMap.get(commodityMap.get(dto.getCommodityCode())).getId());
                stallCommodityStock.setReserveStock(dto.getReserveStock());
                updateData.add(stallCommodityStock);

                commodityReserveStockDTO = new CommodityReserveStockDTO();
                commodityReserveStockDTO.setCommodityId(commodityMap.get(dto.getCommodityCode()));
                commodityReserveStockDTO.setReserveStock(dto.getReserveStock());
                commodityList.add(commodityReserveStockDTO);

                ids.add(stallCommodityStock.getId());

                repeatCount.put(dto.getCommodityCode(), 1);
            }

            if (SpringUtil.isEmpty(errorList) && SpringUtil.isNotEmpty(updateData)) {
                updateBatchById(updateData);

                // 上架，线上可售 更新门店商品表的预留库存
                this.baseMapper.updateShopCommodityReserveStock(tokenInfo.getShopId(), stallId, tokenInfo.getUserId(), commodityList);

                List<StallCommodityStock> stallCommodityStockList = this.baseMapper.selectBatchIds(ids);
                saveLog(stallCommodityStockList, 2, tokenInfo);

                List<Long> commodityIdList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
                this.dealStockMessage(tokenInfo.getShopId(), commodityIdList);
            }

        }

        return new ExcelResult(errorList, null);
    }

    private void dealStockMessage(Long shopId, List<Long> commodityIdList) {
        List<StockItemDTO> dealList = new ArrayList<>();
        for (Long commodityId : commodityIdList) {
            dealList.add(new StockItemDTO(commodityId, null, BigDecimal.ONE, null));
        }
        shopCommodityService.dealStockMessage(shopId, dealList);
    }

    public void saveLog(List<StallCommodityStock> list, Integer type, TokenInfo tokenInfo) {
        // 1设置预留库存、2导入预留库存

        // 查询商品信息
        List<Long> commodityIds = list.stream().map(StallCommodityStock::getCommodityId).collect(Collectors.toList());
        List<Commodity> commodities = commodityMapper.queryCommodityByIdList(commodityIds);
        Map<Long, Commodity> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, e -> e));

        // 档口信息
        Stall stall = stallMapper.selectById(list.get(0).getStallId());

        List<LogReserveStockDTO> logs = new ArrayList<>();
        LogReserveStockDTO dto = null;
        Commodity commodity = null;
        Date date = new Date();

        for (StallCommodityStock stallCommodityStock : list) {
            dto = new LogReserveStockDTO();
            dto.setType(type);
            dto.setShopId(stallCommodityStock.getShopId());
            dto.setStallId(stallCommodityStock.getStallId());
            dto.setStallName(stall.getStallName());
            dto.setCommodityId(stallCommodityStock.getCommodityId());
            commodity = commodityMap.containsKey(stallCommodityStock.getCommodityId()) ? commodityMap.get(stallCommodityStock.getCommodityId()) : new Commodity();
            dto.setCommodityCode(commodity.getCommodityCode());
            dto.setCommodityName(commodity.getCommodityName());
            dto.setCommoditySpec(commodity.getCommoditySpec());
            dto.setCommodityUnitName(commodity.getCommodityUnitName());
            dto.setReserveStock(stallCommodityStock.getReserveStock());

            dto.setCreateId(tokenInfo.getUserId());
            dto.setCreateCode(tokenInfo.getEmployeeNumber());
            dto.setCreateName(tokenInfo.getRealName());
            dto.setCreateTime(date);

            logs.add(dto);
        }

        xdSendLogService.sendLog(logs, "t_log_dd_reserve_stock");
    }

    public MPage<ReserveStockPageDTO> reserveStockPage(ReserveStockPageIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        // 查询当前用户拥有的档口权限
        //       List<Long> stallIds = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
        List<Long> stallIdList = new ArrayList<>();
        if (null == dto.getStallId()) {
            stallIdList = userStallServiceImpl.selectUserStallIdList(tokenInfo.getUserId(), tokenInfo.getShopId());
        } else {
            stallIdList = Arrays.asList(dto.getStallId());
        }
        dto.setStallIdList(stallIdList);

        return this.baseMapper.reserveStockPage(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void processFreezeStock(Long wareHouseId, StockItemDTO commodity, CommodityLimitIDTO commodityLimit, StallCommodity shopCommodityStall) {
        QYAssert.isTrue(Objects.nonNull(wareHouseId) && Objects.nonNull(commodity.getCommodityId()), "参数错误");

        Boolean commodityLimitFlag = Objects.isNull(commodityLimit) || !BooleanUtils.isTrue(commodityLimit.getLimitFlag()) ? Boolean.FALSE : Boolean.TRUE;

        int result = this.baseMapper.processFreezeStock(wareHouseId, shopCommodityStall.getStallId(), commodity, commodityLimitFlag);

        QYAssert.isTrue(Objects.equals(result, 1), "门店档口商品库存冻结或解冻失败");

    }

    /**
     * 监听 前置仓商品上下架 消息
     * 消费时，添加判断，非大店return，大店处理逻辑，门店 商品 到底在哪个档口(t_stall_commodity)是上架的
     * -> 初始化覆盖 门店库存  (t_stall_commodity_stock)
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealBigShopStallCommodityStock(List<BigShopCommodityUpDownKafkaVo> appStatusList) {
        // 过滤出上架的消息
        List<BigShopCommodityUpDownKafkaVo> validShopList = appStatusList.stream()
                .filter(vo -> Objects.nonNull(vo.getShopId())
                        && SpringUtil.isNotEmpty(vo.getCommodityIdList())
                        && Objects.equals(vo.getAppStatus(), 0))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validShopList)) {
            return;
        }

        List<Long> shopIdList = validShopList.stream()
                .map(BigShopCommodityUpDownKafkaVo::getShopId)
                .distinct()
                .collect(Collectors.toList());

        // 查询店铺信息
        List<ShopODTO> shopList = shopClient.getShopListById(shopIdList);
        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }

        Set<Long> bigShopIdSet = shopList.stream()
                .filter(shop -> StallUtils.isBigShop(shop.getManagementMode()))
                .map(ShopODTO::getId)
                .collect(Collectors.toSet());

        // 处理大店的覆盖 门店库存 操作
        validShopList.stream()
                .filter(x -> bigShopIdSet.contains(x.getShopId()))
                .forEach(this::processShopStock);
    }

    private void processShopStock(BigShopCommodityUpDownKafkaVo vo) {
        Long shopId = vo.getShopId();
        List<Long> commodityIdList = vo.getCommodityIdList();

        // 获取上架商品的档口信息
        StallCommodityQueryIDTO idto = new StallCommodityQueryIDTO();
        idto.setShopId(shopId);
        idto.setCommodityIdList(commodityIdList);
        List<StallCommodityODTO> stallCommodityODTOS = stallClient.queryStallCommodityInfo(idto);

        if (CollectionUtils.isEmpty(stallCommodityODTOS) ||
                commodityIdList.size() != stallCommodityODTOS.stream().filter(x -> Objects.equals(x.getAppStatus(), 0)).count()) {
            // 消息中的上架数量与pinshang库t_stall_commodity中的上架商品数量不一致
            log.warn("上架商品数量不一致,message:{},查询到的档口商品信息:{}", JSON.toJSONString(vo), JSON.toJSONString(stallCommodityODTOS));
            weChatSendMessageService.sendWeChatMessage("上架商品数量不一致");
            return;
        }

        // 批量处理商品库存
        List<StallCommodity> appOnStatusStallCommodityList = stallCommodityODTOS.stream()
                .filter(x -> Objects.equals(x.getAppStatus(), 0))
                .map(x -> BeanCloneUtils.copyTo(x, StallCommodity.class))
                .collect(Collectors.toList());

        updateStock(appOnStatusStallCommodityList);
    }

    private void updateStock(List<StallCommodity> appOnStatusStallCommodityList) {
        log.info("开始执行覆盖大店库存操作,appOnStatusStallCommodityList:{}", JSON.toJSONString(appOnStatusStallCommodityList));
        if (CollectionUtils.isEmpty(appOnStatusStallCommodityList)) {
            return;
        }
        // 批量查询档口商品库存
        List<StallCommodityStock> stallCommodityStocks = batchQueryStock(appOnStatusStallCommodityList);
        if (CollectionUtils.isEmpty(stallCommodityStocks)) {
            return;
        }

        Map<String, StallCommodityStock> stockMap = stallCommodityStocks.stream()
                .collect(Collectors.toMap(
                        stock -> generateStockKey(stock.getShopId(), stock.getStallId(), stock.getCommodityId()),
                        Function.identity()));

        List<ShopCommodityStock> stockUpdates = new ArrayList<>();
        for (StallCommodity stallCommodity : appOnStatusStallCommodityList) {
            StallCommodityStock stallCommodityStock = stockMap.get(generateStockKey(
                    stallCommodity.getShopId(), stallCommodity.getStallId(), stallCommodity.getCommodityId()));

            if (Objects.nonNull(stallCommodityStock)) {
                BigDecimal totalStock = stallCommodityStock.getStockQuantity()
                        .add(stallCommodityStock.getPickingAreaStock())
                        .add(stallCommodityStock.getWarehouseAreaStock());
                // 更新门店库存（档口商品的排面+拣货+存储区库存），冻结库存，预留库存
                ShopCommodityStock stockUpdate = new ShopCommodityStock();
                stockUpdate.setShopId(stallCommodity.getShopId());
                stockUpdate.setCommodityId(stallCommodity.getCommodityId());
                stockUpdate.setStockQuantity(totalStock);
                stockUpdate.setFreezeNumber(stallCommodityStock.getFreezeNumber());
                stockUpdate.setReserveStock(stallCommodityStock.getReserveStock());
                stockUpdates.add(stockUpdate);
            }
        }
        if (CollectionUtils.isNotEmpty(stockUpdates)) {
            shopCommodityStockService.batchUpdateStock(stockUpdates);
            log.info("覆盖大店库存操作完成,stockUpdates:{}", JSON.toJSONString(stockUpdates));
        }
    }

    private String generateStockKey(Long shopId, Long stallId, Long commodityId) {
        return shopId + "_" + stallId + "_" + commodityId;
    }

}
