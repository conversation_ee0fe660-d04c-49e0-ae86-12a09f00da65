package com.pinshang.qingyun.xd.wms.enums.bigshop;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生效方式: 1 不循环生效 2 每天循环生效
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EffectTypeEnum {
    NO_LOOP(1, "不循环生效"),
    DAY_LOOP(2, "每天循环生效")
    ;
    private final Integer code;
    private final String name;


    public static EffectTypeEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EffectTypeEnum typeEnum : EffectTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EffectTypeEnum typeEnum : EffectTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum.getName();
            }
        }
        return "";
    }
}
