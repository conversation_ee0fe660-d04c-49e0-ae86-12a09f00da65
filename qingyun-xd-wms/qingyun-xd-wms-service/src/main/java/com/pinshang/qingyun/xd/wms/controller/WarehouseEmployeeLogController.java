package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.QueryLog;
import com.pinshang.qingyun.xd.wms.dto.WarehouseEmployeeLogDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseEmployeeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/warehouse/employee/log")
@Api(value = "仓库员工管理日志", tags = "WarehouseEmployeeLogController")
public class WarehouseEmployeeLogController {

    @Autowired
    private WarehouseEmployeeLogService warehouseEmployeeLogService;

    @PostMapping("/list")
    @ApiOperation("日志列表查询")
    public MPage<WarehouseEmployeeLogDTO> logList(@RequestBody QueryLog queryLog) {
        return warehouseEmployeeLogService.logList(queryLog);
    }
}
