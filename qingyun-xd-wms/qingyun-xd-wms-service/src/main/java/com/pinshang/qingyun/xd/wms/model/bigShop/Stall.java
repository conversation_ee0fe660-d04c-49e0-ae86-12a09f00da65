package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@TableName("t_stall")
public class Stall extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 档口所属门店ID
     */
    private Long shopId;

    /**
     * 档口编码
     */
    private String stallCode;

    /**
     * 档口名称
     */
    private String stallName;

    /**
     * 档口服务商ID
     */
    private Long stallSpId;

    /**
     * 拣货分区ID
     */
    private Long pickAreaId;

    /**
     * 状态：0-停用、1-启用
     */
    private Integer status;


    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;
}
