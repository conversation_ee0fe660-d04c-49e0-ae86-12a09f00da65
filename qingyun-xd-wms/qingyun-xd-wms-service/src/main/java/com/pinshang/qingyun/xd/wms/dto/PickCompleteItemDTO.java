package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 拣货完成明细DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickCompleteItemDTO {

    @ApiModelProperty(value = "拣货单id")
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单明细id")
    private Long pickOrderItemId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQuantity;

    @ApiModelProperty(value = "拣货份数")
    private Integer pickNumber;

    @ApiModelProperty(value = "是否称重 0非称重 1称重")
    private Integer isWeight;
}