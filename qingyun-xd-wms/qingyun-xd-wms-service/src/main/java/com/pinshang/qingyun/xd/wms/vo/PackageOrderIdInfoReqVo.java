package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderIdInfoReqVo {

    @ApiModelProperty("包裹单Id")
    private Long orderId;
    @ApiModelProperty("包裹单明细Id")
    private Long orderItemId;
    @ApiModelProperty("包裹单编号")
    private String orderCode;

    public String buildKey(){
        return this.getOrderId()+"_"+this.getOrderItemId();
    }
}
