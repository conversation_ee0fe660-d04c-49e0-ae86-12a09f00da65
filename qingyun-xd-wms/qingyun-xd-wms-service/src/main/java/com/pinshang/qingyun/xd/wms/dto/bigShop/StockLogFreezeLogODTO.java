package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("StockLogFreezeLogODTO")
public class StockLogFreezeLogODTO implements Serializable {

    private static final long serialVersionUID = 2649748159322919087L;

    @ApiModelProperty("单据编号")
    private String referCode;

    @ApiModelProperty("冻结份数")
    private BigDecimal quantity;

    @ApiModelProperty("冻结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
