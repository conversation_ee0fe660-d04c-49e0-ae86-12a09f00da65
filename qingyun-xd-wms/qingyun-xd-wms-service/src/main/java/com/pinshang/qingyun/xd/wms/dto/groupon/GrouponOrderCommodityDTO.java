package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderCommodityDTO {

    @ApiModelProperty("收货人")
    private String receiveMan;

    @ApiModelProperty("收货人手机号")
    private String receiveMobile;

    @ApiModelProperty("提货商品列表")
    private List<GrouponOrderCommodityListDTO> list;

}
