package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

@NoArgsConstructor
@AllArgsConstructor
public class MackUpDTO {

    /**
     * topic
     */
    private String topic;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String tableName;

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public void checkData() {
        Assert.isTrue(topic != null, "topic不能为空");
        Assert.isTrue(beginTime != null, "beginTime不能为空");
        Assert.isTrue(endTime != null, "endTime不能为空");
    }
}