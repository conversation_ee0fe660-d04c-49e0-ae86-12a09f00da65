package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudDeliverListDTO {

    private Long orderId;

    private Long shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("门店到货日期")
    private Date arrivalTime;

    @ApiModelProperty("c端订单编号")
    private String orderCode;

    @ApiModelProperty("提货人手机号")
    private String receiveMobile;

    @ApiModelProperty("提货人手机号 带星号")
    private String receiveMobileStar;

    @ApiModelProperty("提货人")
    private String receiveMan;

    @ApiModelProperty("登录用户手机号")
    private String userMobile;

    @ApiModelProperty("登录用户手机号 带星号")
    private String userMobileStar;

    /**
     * 0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定
     */
    @ApiModelProperty("0=已取消, 2,3,4,5 待提货  6已提货" )
    private Integer orderStatus;

    /**
     * 0=普通订单 1=团购订单 2=云超订单
     */
    private Integer orderType;

    public void setReceiveMobile(String receiveMobile) {
        this.receiveMobile = receiveMobile;
        if (StringUtils.isNotEmpty(receiveMobile)) {
            this.receiveMobileStar = receiveMobile.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        }
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
        if (StringUtils.isNotEmpty(userMobile)) {
            this.userMobileStar =  userMobile.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        }
    }


}
