package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClouodCancelOrderListODTO {
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty(value = "订单", notes = "0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定")
    private Integer orderStatus;

    @ApiModelProperty("订单状态名称")
    private String orderStatusName;

    @ApiModelProperty("订单金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "收货人手机")
    private String receiveMobile;

    @ApiModelProperty(value = "下单人手机")
    private String orderMobile;

    @ApiModelProperty(value = "下单人xs_user_id")
    private Long xsUserId;

    @ApiModelProperty("预约到货日期")
    private String arriveTime;

    @ApiModelProperty("取消时间")
    private String cancelTime;

    @ApiModelProperty("取消操作人")
    private Long cancelUserId;

    @ApiModelProperty("取消操作人名称")
    private String cancelUserName;

    @ApiModelProperty("orderId")
    private Long orderId;

    @ApiModelProperty("sourceType")
    private Integer sourceType;
}
