package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.RegExpUtil;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWarehouseEmployeeDTO {

    @ApiModelProperty(value = "员工id,必填", required = true)
    private Long employeeId;

    @ApiModelProperty(value = "员工手机号，必填", required = true)
    private String employeePhone;

    @ApiModelProperty("配送员类型，1:拣货员 2:配送员,不传默认2配送员")
    private Integer type;

    public void checkData(){
        QYAssert.isTrue(employeeId != null, "员工id不能为空");
        QYAssert.isTrue(!StringUtils.isEmpty(employeePhone), "手机号码不能为空");
//        QYAssert.isTrue(RegExpUtil.checkPhone(employeePhone), "手机号码不合法");
        if (type != null) {
            QYAssert.isTrue(WarehouseEmployeeTypeEnum.getTypeEnumByCode(type) != null, "配送员类型不合法");
        } else {
            type = WarehouseEmployeeTypeEnum.DELIVERY.getCode();
        }
    }
}
