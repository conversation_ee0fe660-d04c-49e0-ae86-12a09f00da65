package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/23 10:47
 * 查询总部大仓给提货门店发货商品的明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDeliveryDetailsIDTO extends Pagination {
    @ApiModelProperty("提货门店id")
    private Long shopId;
    private List<Long> shopIdList;
    @ApiModelProperty("提货日期-开始 yyyy-MM-dd")
    private String orderTimeBegin;
    @ApiModelProperty("提货日期-结束 yyyy-MM-dd")
    private String orderTimeEnd;
    @ApiModelProperty("发货日期-开始 yyyy-MM-dd")
    private String createTimeBegin;
    @ApiModelProperty("发货日期-结束 yyyy-MM-dd")
    private String createTimeEnd;
    @ApiModelProperty(value = "C端订单编号")
    private Long orderId;
    @ApiModelProperty(value = "商品id")
    private Long commodityId;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "一级品类")
    private String commodityFirstKind;
    @ApiModelProperty(value = "二级品类")
    private String commoditySecondKind;
    @ApiModelProperty(value = "三级品类")
    private String commodityThirdKind;
    public void checkData() {
        boolean flag;
        QYAssert.isTrue(StringUtils.isNotBlank(createTimeBegin) && StringUtils.isNotBlank(createTimeEnd)
                || StringUtils.isNotBlank(orderTimeBegin) && StringUtils.isNotBlank(orderTimeEnd), "提货日期和发货日期必选其一");
        // 门店为空且商品、条码、品类都为空时，提货日期或者发货日期只能选择一天，
        // 门店不为空即选择一家门店时，提货日期或发货日期可以选择一个月
        // 商品，条码、品类输入其一时，提货日期或发货日期可以选择一个月
        if (StringUtils.isNotBlank(commodityFirstKind) || StringUtils.isNotBlank(commoditySecondKind) || StringUtils.isNotBlank(commodityThirdKind) || StringUtils.isNotBlank(barCode) || shopId != null || orderId != null || commodityId != null) {
            //范围在一个月
            flag = dateBetween(orderTimeBegin, orderTimeEnd, ChronoUnit.MONTHS) < 1L && dateBetween(createTimeBegin, createTimeEnd, ChronoUnit.MONTHS) < 1L;
        } else {
            //时间范围是一天
            flag = dateBetween(orderTimeBegin, orderTimeEnd, ChronoUnit.DAYS) < 1L && dateBetween(createTimeBegin, createTimeEnd, ChronoUnit.DAYS) < 1L;
        }
        QYAssert.isTrue(flag, "请精确查询条件");
    }

    public static long dateBetween(String startStr, String endStr, ChronoUnit chronoUnit) {
        if (StringUtils.isNotBlank(startStr) && StringUtils.isNotBlank(endStr)) {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDate startDate = LocalDate.parse(startStr, fmt);
            LocalDate endDate = LocalDate.parse(endStr, fmt);
            return chronoUnit.between(startDate, endDate);
        }
        return 0L;
    }
}
