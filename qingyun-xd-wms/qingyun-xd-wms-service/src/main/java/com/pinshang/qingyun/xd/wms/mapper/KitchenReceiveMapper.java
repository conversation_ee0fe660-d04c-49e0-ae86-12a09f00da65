package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageIDTO;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageODTO;
import com.pinshang.qingyun.xd.wms.model.KitchenReceive;

import java.util.List;

/**
 * @ClassName KitchenReceiveMapper
 * <AUTHOR>
 * @Date 2021/10/20 17:14
 * @Description KitchenReceiveMapper
 * @Version 1.0
 */
public interface KitchenReceiveMapper extends BaseMapper<KitchenReceive> {
    List<KitchenReceivePageODTO> page(KitchenReceivePageIDTO idto);
}
