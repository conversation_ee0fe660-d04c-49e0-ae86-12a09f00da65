package com.pinshang.qingyun.xd.wms.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SellOutExcelDTO {

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("条形码")
    private String barCode;

    @ExcelProperty("前台品类")
    private String cateName;

    @ExcelProperty("后台品类")
    private String commodityThirdKindName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("售罄时临时库存")
    private Integer qualityNumber;

    @ExcelProperty("售罄时冻结库存")
    private Integer freezeNumber;

    @ExcelProperty("最早售罄时间")
    private Date date;
}
