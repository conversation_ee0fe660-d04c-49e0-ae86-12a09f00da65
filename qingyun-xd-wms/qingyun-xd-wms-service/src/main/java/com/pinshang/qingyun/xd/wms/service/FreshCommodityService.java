package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.cms.service.CommodityEverydayFreshClient;
import com.pinshang.qingyun.shop.dto.xj.XjAppStatusByParamsDTO;
import com.pinshang.qingyun.shop.service.xj.XjShopCommodityClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.StockItemPageODTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryPageIDTO;
import com.pinshang.qingyun.xd.wms.mapper.CommodityStockMapper;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FreshCommodityService {

    @Autowired
    private XjShopCommodityClient xjShopCommodityClient;

    @Autowired
    private CommodityStockMapper commodityStockMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private CommodityEverydayFreshClient commodityEverydayFreshClient;

    @Autowired
    private SMMUserClient smmUserClient;

    public MPage<StockItemPageODTO> queryFreshStock(StockQueryPageIDTO dto) {
        Boolean shopCommodity = dto.getWarehouseId() == null && dto.getCommodityId() == null;
        QYAssert.isTrue(!shopCommodity,"门店或者商品至少选择一个");

        // 查询日日鲜商品idList
        List<Long> freshIdList = commodityEverydayFreshClient.getAllEverydayFresh();
        if(CollectionUtils.isEmpty(freshIdList)){
            return new Pagination<>();
        }

        if (null != dto.getAppStatus() && null != dto.getWarehouseId()) {
            List<Long> commodityIdList = xjShopCommodityClient.findShopCommodityStatus(dto.getWarehouseId(), dto.getAppStatus());
            if (null == commodityIdList || commodityIdList.size() == 0) {
                return new Pagination<>();
            }
            freshIdList.retainAll(commodityIdList);
            if(CollectionUtils.isEmpty(freshIdList)){
                return new Pagination<>();
            }
        }

        // 如果搜索条件选择商品
        if(null != dto.getCommodityId()){
            if(freshIdList.contains(dto.getCommodityId())){
                freshIdList = new ArrayList<>();
                freshIdList.add(dto.getCommodityId());
            }else {
                return new Pagination<>();
            }
        }
        dto.setCommodityIdList(freshIdList);

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (!CollectionUtils.isEmpty(shopIdList)) {
            dto.setShopIdList(shopIdList);
        } else {
            return new Pagination<>();
        }

        if (null != dto.getAppStatus() && null != dto.getCommodityId()) {
            List<Long> shopIds = xjShopCommodityClient.findShopByCommodity(dto.getCommodityId(), dto.getAppStatus());
            if (null == shopIds || shopIds.size() == 0) {
                return new Pagination<>();
            }
            shopIdList.retainAll(shopIds);
            if(CollectionUtils.isEmpty(shopIdList)){
                return new Pagination<>();
            }
        }
        // 如果搜索条件选择门店
        if(null != dto.getWarehouseId()){
            if(shopIdList.contains(dto.getWarehouseId())){
                shopIdList = new ArrayList<>();
                shopIdList.add(dto.getWarehouseId());
            }else {
                return new Pagination<>();
            }
        }
        dto.setShopIdList(shopIdList);

        MPage<StockItemPageODTO> page = commodityStockMapper.queryFreshStock(dto);
        if (!CollectionUtils.isEmpty(page.getList())) {
            List<Long> commodityIds = page.getList().stream().map(StockItemPageODTO::getCommodityId).collect(Collectors.toList());
            XjAppStatusByParamsDTO xjAppStatusByParamsDTO = new XjAppStatusByParamsDTO();
            xjAppStatusByParamsDTO.setCommodityIds(commodityIds);
            xjAppStatusByParamsDTO.setWarehouseId(dto.getWarehouseId());
            Map<String, Integer> map = xjShopCommodityClient.appstatusByParams(xjAppStatusByParamsDTO);

            Map<Long, List<String>> mapBarCode = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);

            page.getList().forEach(e -> {
                if (null != dto.getAppStatus()) {
                    e.setAppStatus(dto.getAppStatus());
                } else {
                    e.setAppStatus(map.get(e.getShopId().toString() + e.getCommodityId()));
                }

                if (mapBarCode.containsKey(e.getCommodityId())) {
                    e.setBarCodeList(mapBarCode.get(e.getCommodityId()));
                }
            });
        }
        return page;
    }
}
