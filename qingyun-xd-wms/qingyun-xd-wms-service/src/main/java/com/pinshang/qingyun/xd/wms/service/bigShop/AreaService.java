package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaIDTO;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.AreaMapper;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.GoodsAllocationMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.Area;
import com.pinshang.qingyun.xd.wms.model.bigShop.GoodsAllocation;
import com.pinshang.qingyun.xd.wms.model.bigShop.GoodsAllocationCommodity;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 大卖场区域管理
 */
@Service
public class AreaService {

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private GoodsAllocationMapper goodsAllocationMapper;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    /**
     * 添加区域
     * @param dto
     * @return
     */
    public Boolean addArea(AreaDTO dto) {
        dto.check();

        QYAssert.isTrue(null != dto.getShopId(), "门店不能为空");

        //判断名称和编码有没有重复的
        LambdaQueryWrapper<Area> query = new LambdaQueryWrapper<>();
        query.eq(Area::getShopId, dto.getShopId());
        query.and(wrapper -> wrapper.eq(Area::getAreaCode, dto.getAreaCode()).or().eq(Area::getAreaName, dto.getAreaName()) );

        List<Area> areaList = areaMapper.selectList(query);
        if (SpringUtil.isNotEmpty(areaList)) {
            for (Area area : areaList) {
                QYAssert.isTrue(!area.getAreaCode().equals(dto.getAreaCode()), "区域编码已存在");
                QYAssert.isTrue(!area.getAreaName().equals(dto.getAreaName()), "区域名称已存在");
            }
        }

        Area area = BeanCloneUtils.copyTo(dto, Area.class);

        areaMapper.insert(area);
        return Boolean.TRUE;
    }

    public MPage<AreaDTO> areaList(AreaIDTO dto) {
        MPage<AreaDTO> page = areaMapper.areaList(dto);
        if (!page.getRecords().isEmpty()) {
            List<AreaDTO> list = page.getList();
            List<Long> ids = list.stream().map(AreaDTO::getId).collect(Collectors.toList());
            //区域下面的货位数量，不区分状态和类型
            LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<GoodsAllocation>()
                    .in(GoodsAllocation::getAreaId, ids);
            List<GoodsAllocation> goodsAllocationList = goodsAllocationMapper.selectList(queryWrapper);
            if (SpringUtil.isNotEmpty(goodsAllocationList)) {
                Map<Long, Long> map = goodsAllocationList.stream().collect(Collectors.groupingBy(GoodsAllocation::getAreaId, Collectors.counting()));
                for (AreaDTO areaDTO : list) {
                    areaDTO.setNum(map.containsKey(areaDTO.getId()) ? map.get(areaDTO.getId()) : 0L );
                }
            }
        }
        return page;
    }

    /**
     * 修改区域
     * @param dto
     * @return
     */
    public Boolean update(AreaDTO dto) {
        dto.check();
        QYAssert.isTrue(null != dto.getId(), "ID不能为空");

        Area areaInfo = areaMapper.selectById(dto.getId());
        QYAssert.isTrue(null != areaInfo, "该区域不存在");

        //判断名称和编码有没有重复的
        LambdaQueryWrapper<Area> query = new LambdaQueryWrapper<>();

        query.ne(Area::getId, dto.getId());
        query.eq(Area::getShopId, areaInfo.getShopId());
        query.and(wrapper -> wrapper.eq(Area::getAreaCode, dto.getAreaCode()).or().eq(Area::getAreaName, dto.getAreaName()) );


        List<Area> areaList = areaMapper.selectList(query);
        if (SpringUtil.isNotEmpty(areaList)) {
            for (Area area : areaList) {
                QYAssert.isTrue(!area.getAreaCode().equals(dto.getAreaCode()), "区域编码已存在");
                QYAssert.isTrue(!area.getAreaName().equals(dto.getAreaName()), "区域名称已存在");
            }
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        areaInfo.setAreaCode(dto.getAreaCode());
        areaInfo.setAreaName(dto.getAreaName());
        areaInfo.setUpdateId(tokenInfo.getUserId());
        areaInfo.setUpdateTime(new Date());
        areaMapper.updateById(areaInfo);

        return Boolean.TRUE;
    }

    /**
     * 获取区域信息
     * @param areaId
     * @return
     */
    public Area areaInfo(Long areaId) {
        Area area = areaMapper.selectById(areaId);
        QYAssert.isTrue(null != area, "该区域不存在");
        return area;
    }

    /**
     * 删除区域信息
     * @param areaId
     * @return
     */
    public Boolean delete(Long areaId) {
        Area area = areaMapper.selectById(areaId);
        QYAssert.isTrue(null != area, "该区域不存在");

        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getAreaId, areaId);
        Integer count = goodsAllocationMapper.selectCount(queryWrapper);
        QYAssert.isTrue(count == 0, "货位数已发生变化，请刷新重试！");

        areaMapper.deleteById(areaId);
        return Boolean.TRUE;
    }

    /**
     * 删除区域，下面货位数量不为0的时候  先更换下面货位到最新的区域，然后再删除老的区域信息
     * 换区域信息
     * @param oldAreaId
     * @param newAreaId
     * @return
     */
    @Transactional
    public Boolean reallocation(Long oldAreaId, Long newAreaId) {

        QYAssert.isTrue(!Objects.equals(oldAreaId, newAreaId), "无效区域");

        List<Area> areaList = areaMapper.selectBatchIds(Arrays.asList(oldAreaId, newAreaId));
        QYAssert.isTrue(SpringUtil.isNotEmpty(areaList) && areaList.size() == 2, "区域不存在");

        //原来区域下面有多少货位
        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getAreaId, oldAreaId);
        List<GoodsAllocation> goodsAllocationList = goodsAllocationMapper.selectList(queryWrapper);
        if (SpringUtil.isNotEmpty(goodsAllocationList)) {
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            GoodsAllocation goodsAllocation = new GoodsAllocation();
            goodsAllocation.setAreaId(newAreaId);
            goodsAllocation.setUpdateId(tokenInfo.getUserId());
            goodsAllocation.setUpdateTime(new Date());
            goodsAllocationMapper.update(goodsAllocation, queryWrapper);

            List<Long> goodsAllocationIds = goodsAllocationList.stream().map(GoodsAllocation::getId).collect(Collectors.toList());
            LambdaQueryWrapper<GoodsAllocationCommodity> query = new LambdaQueryWrapper<GoodsAllocationCommodity>()
                    .in(GoodsAllocationCommodity::getGoodsAllocationId, goodsAllocationIds);
            List<GoodsAllocationCommodity> list = goodsAllocationCommodityService.list(query);
            //修改所属区域
            if (SpringUtil.isNotEmpty(list)) {
                list.forEach(e -> {
                    e.setAreaId(newAreaId);
                });
                goodsAllocationCommodityService.updateBatchById(list);
            }
        }
        areaMapper.deleteById(oldAreaId);
        return Boolean.TRUE;
    }

    /**
     * 根据关键字查询区域列表
     * @param keyWord
     * @return
     */
    public List<Area> getAreaListByKeyWord(Long shopId ,String keyWord) {
        return areaMapper.getAreaListByKeyWord(shopId, keyWord);
    }

}
