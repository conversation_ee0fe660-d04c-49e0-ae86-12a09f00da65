package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveStockPageIDTO extends Pagination<ReserveStockPageIDTO> {

    private Long shopId;

    private Long stallId;

    private Long commodityId;

    @ApiModelProperty("品类")
    private Long commodityThirdKindId;

    @ApiModelProperty("是否可售：1-是,0-否")
    private Integer commoditySaleStatus;

    @ApiModelProperty("APP状态：0-上架、1-下架")
    private Integer appStatus;

    private List<Long> stallIdList;


}
