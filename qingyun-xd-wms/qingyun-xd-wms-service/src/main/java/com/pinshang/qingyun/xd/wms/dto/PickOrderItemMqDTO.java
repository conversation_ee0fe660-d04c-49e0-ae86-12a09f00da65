package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 拣货单明细消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrderItemMqDTO {
    /**
     * 订单明细id
     * */
    private Long orderItemId;
    /**
     * 拣货单id
     */
    private Long pickOrderId;
    /**
     * 商品ID
     */
    private Long commodityId;
    /**
     * 商品数量
     */
    private BigDecimal quantity;

    /**
     * 商品份数
     */
    private Integer stockNumber;

    /**
     * 商品拣货数量
     */
    private BigDecimal pickQuantity;

    /**
     * 拣货份数
     */
    private Integer pickNumber;

    /**
     * 1=称重，0=非称重
     */
    private Integer isWeight;

    /**
     * 是否需要处理 0＝不加工，1＝加工(针对称重商品)
     */
    private Integer isProcess;

    private String originSubBizId;

    private Long stallId;
}