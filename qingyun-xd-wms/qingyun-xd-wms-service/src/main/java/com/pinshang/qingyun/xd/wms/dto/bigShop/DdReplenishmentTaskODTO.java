package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentFlagEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskSourceEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 大店补货任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@ToString
@ApiModel("DdReplenishmentTaskODTO")
public class DdReplenishmentTaskODTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    private String commodityUnitName;

    @ApiModelProperty("任务来源，1-系统预警，2-手动添加")
    private Integer taskSource;

    @ApiModelProperty("任务来源，1-系统预警，2-手动添加")
    private String taskSourceName;

    @ApiModelProperty("任务类型，1-排面补货，2-拣货区补货")
    private Integer taskType;

    @ApiModelProperty("任务类型，1-排面补货，2-拣货区补货")
    private String taskTypeName;

    @ApiModelProperty("建议补货数量")
    private BigDecimal suggestedQuantity;

    @ApiModelProperty("实际补货数量")
    private BigDecimal realQuantity;

    @ApiModelProperty("对应单据编号(t_dd_transfer_record)")
    private String transferCode;

    @ApiModelProperty("补货人ID")
    private Long replenishUserId;

    @ApiModelProperty(value = "补货人工号")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.employeeNumber, keyName = "replenishUserId")
    private String replenishEmployeeCode;

    @ApiModelProperty("补货人姓名")
    private String replenishUserName;

    @ApiModelProperty("补货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replenishTime;

    @ApiModelProperty("任务状态，1-生效中，2-已过期")
    private Integer status;

    @ApiModelProperty("任务状态，1-生效中，2-已过期")
    private String statusName;

    @ApiModelProperty("是否完成补货，0-未完成，1-已完成")
    private Integer replenishedStatus;

    @ApiModelProperty("是否完成补货，0-未完成，1-已完成")
    private String replenishedStatusName;

    @ApiModelProperty("补货操作标志，0-未点击完成，1-已点击完成")
    private Integer flag;

    @ApiModelProperty("补货操作标志，0-未点击完成，1-已点击完成")
    private String flagName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人ID")
    private Long createId;

    @ApiModelProperty("更新人ID")
    private Long updateId;

    @ApiModelProperty("任务生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("档口名称")
    private String stallName;

    @ApiModelProperty("档口编码")
    private String stallCode;

    @ApiModelProperty("移库单ID")
    private Long transferRecordId;

    public String getTaskSourceName() {
        return Objects.isNull(taskSource) ? "" : ReplenishmentTaskSourceEnum.getTypeEnumByCode(taskSource).getName();
    }

    public String getTaskTypeName() {
        return Objects.isNull(taskType) ? "" : ReplenishmentTaskTypeEnum.getTypeEnumByCode(taskType).getName();
    }

    public String getStatusName() {
        return Objects.isNull(status) ? "" : ReplenishmentTaskStatusEnum.getTypeEnumByCode(status).getName();
    }

    public String getReplenishedStatusName() {
        return Objects.isNull(replenishedStatus) ? "" : YesOrNoEnums.getByCode(replenishedStatus).getName();
    }

    public String getFlagName() {
        return Objects.isNull(flag) ? "" : ReplenishmentFlagEnum.getTypeEnumByCode(flag).getName();
    }
}