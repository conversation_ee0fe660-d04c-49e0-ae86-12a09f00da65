package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/31 14:48
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockOutCollectIDTO extends Pagination {
    private Long deptId;
    private Long shopId;
    private String commodityId;
    private String barCode;
    private String beginTime;
    private String endTime;
    private List<Long> shopIdList;
}
