package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.xd.wms.dto.bigShop.DdMdInventoryQueryIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdMdInventoryResponseODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockItemODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 大店查库存接口
 * @ClassName DdStockController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 18:07
 * @Version 1.0
 */
@RestController
@RequestMapping("/ddstock")
@Api(value = "品上生活查库存", tags = "DdStockController")
@Slf4j
public class DdStockController {
    @Autowired
    private DdStockService ddStockService;

    @PostMapping("/queryMdInventoryCommodityList")
    @ApiOperation(value = "大店盘点2.0获取商品", notes = " 大店盘点2.0获取商品")
    public List<DdMdInventoryResponseODTO> queryMdInventoryCommodityList(@RequestBody DdMdInventoryQueryIDTO queryIDTO) {
        return ddStockService.queryMdInventoryCommodityList(queryIDTO);
    }
}
