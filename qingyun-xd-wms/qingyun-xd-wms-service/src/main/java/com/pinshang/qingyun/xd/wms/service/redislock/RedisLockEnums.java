package com.pinshang.qingyun.xd.wms.service.redislock;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RedisLockEnums {

    BACK_WAREHOUSE_UP("BACK_WAREHOUSE_UP", 2000, -1, "后仓上架锁"),
    INIT_WAREHOUSE_AREA_COMMODITY("INIT_WAREHOUSE_AREA_COMMODITY", 3000, 5000, "初始化【存储区 临时库】商品信息"),

    PICK_ORDER_RUSH("PICK_ORDER_RUSH", 0, 4000, "分区待拣货抢单锁"),
    BEGIN_PICK_ORDER("BEGIN_PICK_ORDER", 2000, 3000, "开始拣货锁"),

    ACQUIRE_PACKING_STATION("ACQUIRE_PACKING_STATION", 3000, 5000, "订单占用打包口锁"),
    PDA_BATCH_SAVE_DD_DISPLAY("PDA_BATCH_SAVE_DD_DISPLAY", 3000, 5000, "pda批量商品绑定陈列位锁"),
    RELEASE_PACKING_STATION("RELEASE_PACKING_STATION", 5000, 8000, "释放打包口锁"),
    PICK_PARTITION_ORDER_DISTRIBUTE("PICK_PARTITION_ORDER_DISTRIBUTE", 0, 5 * 60 * 1000, "分配分区拣货单job-锁"),

    HANDOVER_DELIVERY("HANDOVER_DELIVERY", 20000, 40000, "完成交接锁"),
    HANDOVER_DELIVERY_SYNC("HANDOVER_DELIVERY_SYNC", 2000, 5000, "完成交接锁"),

    PARTITION_PICK_CONFIRM_OR_COMPLETE("PARTITION_PICK_CONFIRM_OR_COMPLETE", 3000, 5000, "确认拣货或完成拣货锁"),
    SINGLE_CONFIRM_PICK("SINGLE_CONFIRM_PICK", 0, 5000, "扫码拣货锁"),

    DISTRIBUTE_PARTITION_PICK_ORDER("DISTRIBUTE_PARTITION_PICK_ORDER", 0, 4000, "分配分区拣货单"),

    ACQUIRE_PACKING_PORT_V2("ACQUIRE_PACKING_PORT_V2", 3000, 5000, "分配打包口锁"),
    ADD_OR_UPDATE_PICKER("ADD_OR_UPDATE_PICKER", 3000, 5000, "维护拣货员队列锁"),
    ;

    private final String prefix;
    private final int waitTime;
    private final int ttl;
    private final String description;

}
