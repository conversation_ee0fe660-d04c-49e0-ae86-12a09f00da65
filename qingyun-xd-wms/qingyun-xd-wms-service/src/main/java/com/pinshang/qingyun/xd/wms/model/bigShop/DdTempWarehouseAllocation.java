package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName DdTempWarehouseAllocation
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/20 18:10
 * @Version 1.0
 */
@Data
@ToString
@TableName("t_dd_temp_warehouse_allocation")
public class DdTempWarehouseAllocation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    private Long shopId;

    private Long stallId;

    private Integer tempOperationType;

    private String goodsAllocationCode;

    private Long goodsAllocationId;

    /**
     * 修改人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    private Date updateTime;
}
