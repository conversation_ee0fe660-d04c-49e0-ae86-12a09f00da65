package com.pinshang.qingyun.xd.wms.vo.bigShop;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class DdDisplayPositionCommodityVO {

    private Long id;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 所属档口
     */
    private Long stallId;

    /**
     * 库区 1排面区 2拣货区 3存储区
     */
    private Integer storageArea;

    /**
     * 陈列位id
     */
    private Long displayPositionId;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 陈列位最小数量
     */
    private BigDecimal minStock;

    /**
     * 陈列位最大数量
     */
    private BigDecimal maxStock;

    /**
     * 货架id
     */
    private Long shelveId;

    /**
     * 陈列位
     */
    private String displayPositionName;

}
