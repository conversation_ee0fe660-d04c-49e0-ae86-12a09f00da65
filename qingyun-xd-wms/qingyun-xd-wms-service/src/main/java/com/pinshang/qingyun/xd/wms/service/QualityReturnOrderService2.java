package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pinshang.qingyun.base.api.Token;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.dto.order.SaleReturnAddIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnItemAddIDTO;
import com.pinshang.qingyun.order.dto.order.SaleReturnItemAddPicIDTO;
import com.pinshang.qingyun.order.service.SaleReturnOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.StallAddSaleReturnIDTO;
import com.pinshang.qingyun.xd.wms.mapper.QualityReturnOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.QualityReturnOrderPicMapper;
import com.pinshang.qingyun.xd.wms.model.QualityReturnOrder;
import com.pinshang.qingyun.xd.wms.model.QualityReturnOrderPic;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.util.StallUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/2/25
 */
@Service
@Transactional
public class QualityReturnOrderService2 {

    @Autowired
    private QualityReturnOrderMapper qualityReturnOrderMapper;
    @Autowired
    private SaleReturnOrderClient saleReturnOrderClient;
    @Autowired
    private CommodityBarCodeService commodityBarCodeService;
    @Autowired
    private QualityReturnOrderPicMapper qualityReturnOrderPicMapper;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;

    public MPage<QualityReturnOrderPage> page(QualityReturnOrderPageQuery qualityReturnOrderPageQuery) {
        qualityReturnOrderPageQuery.check();
        qualityReturnOrderPageQuery.notLimit();

        MPage<QualityReturnOrderPage> page = qualityReturnOrderMapper.page(qualityReturnOrderPageQuery);
        if(CollectionUtils.isNotEmpty(page.getList())){
            List<Long> commodityIdList = page.getList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            Map<Long,List<String>> commMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIdList);

            List<Long> idList = page.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
            List<QualityReturnOrderPic> qualityReturnPicList = qualityReturnOrderPicMapper.queryByReturnOrderList(idList);

            Map<Long, List<QualityReturnOrderPicODTO>> picListMap = new HashMap<>();
            Map<Long, String> videoUrlMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(qualityReturnPicList)){
                List<QualityReturnOrderPic> picList = qualityReturnPicList.stream().filter(saleReturnpic -> {return QualityReturnOrderPic.PicTypeEnums.PIC.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());
                List<QualityReturnOrderPic> videoList = qualityReturnPicList.stream().filter(saleReturnpic -> {return QualityReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());

                picList.forEach(pic -> {this.buildPicListMap(picListMap, pic);});
                videoUrlMap.putAll(videoList.stream().collect(Collectors.toMap(QualityReturnOrderPic::getQualityReturnOrderId, QualityReturnOrderPic::getPicUrl)));
            }
            for(QualityReturnOrderPage vo : page.getList()){
                vo.setBarCodeList(commMap.get(vo.getCommodityId()));

                vo.setPicList(picListMap.get(Long.valueOf(vo.getId())));
                vo.setVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(vo.getId()))? "": videoUrlMap.get(vo.getId()).trim());
                vo.setVisitVideoUrl(StringUtil.isNullOrEmpty(videoUrlMap.get(vo.getId()))? "": imgServerUrl + videoUrlMap.get(vo.getId()).trim());
                vo.setPicStatusName(SpringUtil.isEmpty(vo.getPicList())? "无": "有");
                vo.setVideoStatusName(StringUtil.isNullOrEmpty(vo.getVideoUrl())? "无": "有");
            }

        }
        return page;
    }

    private void buildPicListMap(Map<Long, List<QualityReturnOrderPicODTO>> picListMap, QualityReturnOrderPic quantityReturnPic) {
        if (null != quantityReturnPic) {
            Long qualityReturnOrderId = quantityReturnPic.getQualityReturnOrderId();
            List<QualityReturnOrderPicODTO> picList = null != picListMap.get(qualityReturnOrderId)? picListMap.get(qualityReturnOrderId): new ArrayList<>();;
            picList.add(new QualityReturnOrderPicODTO(quantityReturnPic.getPicUrl(),imgServerUrl + quantityReturnPic.getPicUrl()));
            picListMap.put(qualityReturnOrderId, picList);
        }
    }

    /**
     * 更新图片
     * @param ito
     * @return
     */
    @Transactional
    public int updateQualityReturnOrderPic(UpdateQualityReturnOrderPicIDTO ito){
        QYAssert.isTrue(null != ito, "参数有误!");
        QYAssert.isTrue(null != ito.getQualityReturnOrderId(), "质检退货id不能为空!");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(ito.getPicList()), "图片不能为空!");

        List<QualityReturnOrderPicODTO> picList = ito.getPicList();
        picList.removeIf(commodityTextPic -> (null == commodityTextPic || StringUtil.isNullOrEmpty(commodityTextPic.getPicUrl())));
        QYAssert.isTrue(picList.size() < 6, "商品图片最多支持5张!");

        // 删除旧图片，插入新图片
        LambdaQueryWrapper query = new LambdaQueryWrapper<QualityReturnOrderPic>()
                .eq(QualityReturnOrderPic::getQualityReturnOrderId, ito.getQualityReturnOrderId())
                .eq(QualityReturnOrderPic::getPicType, QualityReturnOrderPic.PicTypeEnums.PIC.getCode());
        qualityReturnOrderPicMapper.delete(query);

        for(QualityReturnOrderPicODTO picODO : ito.getPicList()){
            QualityReturnOrderPic qualityReturnPic = new QualityReturnOrderPic();
            qualityReturnPic.setQualityReturnOrderId(ito.getQualityReturnOrderId());
            qualityReturnPic.setPicType(QualityReturnOrderPic.PicTypeEnums.PIC.getCode());
            qualityReturnPic.setPicUrl(picODO.getPicUrl());
            qualityReturnPic.setCreateId(FastThreadLocalUtil.getQY().getUserId());
            qualityReturnPic.setCreateTime(new Date());
            qualityReturnOrderPicMapper.insert(qualityReturnPic);
        }
        return 1;
    }

    /**
     * 更新视频
     * @param ito
     * @return
     */
    @Transactional
    public int updateQualityReturnOrderVideo(UpdateQualityReturnOrderPicIDTO ito){
        QYAssert.isTrue(null != ito, "参数有误!");
        QYAssert.isTrue(null != ito.getQualityReturnOrderId(), "质检退货id不能为空!");
        QYAssert.isTrue(null != ito.getFilePath(), "视频不能为空!");

        // 删除旧视频，插入新视频
        LambdaQueryWrapper deleteQuery = new LambdaQueryWrapper<QualityReturnOrderPic>()
                .eq(QualityReturnOrderPic::getQualityReturnOrderId, ito.getQualityReturnOrderId())
                .eq(QualityReturnOrderPic::getPicType, QualityReturnOrderPic.PicTypeEnums.VIDEO.getCode());
        qualityReturnOrderPicMapper.delete(deleteQuery);

        if(StringUtils.isNotBlank(ito.getFilePath())){
            QualityReturnOrderPic qualityReturnPic = new QualityReturnOrderPic();
            qualityReturnPic.setQualityReturnOrderId(ito.getQualityReturnOrderId());
            qualityReturnPic.setPicType(QualityReturnOrderPic.PicTypeEnums.VIDEO.getCode());
            qualityReturnPic.setPicUrl(ito.getFilePath());
            qualityReturnPic.setCreateId(FastThreadLocalUtil.getQY().getUserId());
            qualityReturnPic.setCreateTime(new Date());
            qualityReturnOrderPicMapper.insert(qualityReturnPic);
        }
        return 1;
    }


    public void addSaleReturn(StallAddSaleReturnIDTO idto){

        List<Long> idList = idto.getIds();
        QYAssert.notEmpty(idList,"最少选择一项");
        List<QualityReturnOrderODTO> qualityReturnOrderODTOList = qualityReturnOrderMapper.queryQuantityReturnOrderByIds(idList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(qualityReturnOrderODTOList),"无记录，刷新重试");

        List<Long> qualityReturnOrderIdList = qualityReturnOrderODTOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<QualityReturnOrderPic> qualityReturnPicList = qualityReturnOrderPicMapper.queryByReturnOrderList(qualityReturnOrderIdList);

        Map<Long, List<QualityReturnOrderPic>> picListMap = new HashMap<>();
        Map<Long, List<QualityReturnOrderPic>> videoUrlMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(qualityReturnPicList)){
            List<QualityReturnOrderPic> picList = qualityReturnPicList.stream().filter(saleReturnpic -> {return QualityReturnOrderPic.PicTypeEnums.PIC.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());
            List<QualityReturnOrderPic> videoList = qualityReturnPicList.stream().filter(saleReturnpic -> {return QualityReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(saleReturnpic.getPicType());}).collect(Collectors.toList());

            picListMap = picList.stream().collect(Collectors.groupingBy(QualityReturnOrderPic::getQualityReturnOrderId));
            videoUrlMap = videoList.stream().collect(Collectors.groupingBy(QualityReturnOrderPic::getQualityReturnOrderId));
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        SaleReturnAddIDTO saleReturnAddIDTO = new SaleReturnAddIDTO();
        List<SaleReturnItemAddIDTO> saleReturnItemAddIDTOList = new ArrayList<>();
        for(QualityReturnOrderODTO qualityReturnOrder : qualityReturnOrderODTOList){
            SaleReturnItemAddIDTO addIDTO = new SaleReturnItemAddIDTO();
            addIDTO.setCommodityId(qualityReturnOrder.getCommodityId().toString());
            addIDTO.setReturnQuantity(new BigDecimal(qualityReturnOrder.getReturnNumber()).multiply(qualityReturnOrder.getCommodityPackageSpec()));
            addIDTO.setWarehouseId(tokenInfo.getShopId());
            addIDTO.setReturnReason(qualityReturnOrder.getReturnReason());
            List<QualityReturnOrderPic> picList = picListMap.get(qualityReturnOrder.getId());
            if(CollectionUtils.isNotEmpty(picList)){
                addIDTO.setPicList(BeanCloneUtils.copyTo(picList, SaleReturnItemAddPicIDTO.class));
            }
            QYAssert.isTrue(CollectionUtils.isNotEmpty(addIDTO.getPicList()),"商品:" + qualityReturnOrder.getCommodityName() + "图片不能为空");

            List<QualityReturnOrderPic> videoList = videoUrlMap.get(qualityReturnOrder.getId());
            if(CollectionUtils.isNotEmpty(videoList)){
                addIDTO.setVideoList(BeanCloneUtils.copyTo(videoList, SaleReturnItemAddPicIDTO.class));
            }
            saleReturnItemAddIDTOList.add(addIDTO);
        }

        saleReturnAddIDTO.setCreateId(tokenInfo.getUserId());
        saleReturnAddIDTO.setShopId(tokenInfo.getShopId());
        saleReturnAddIDTO.setSaleReturnItems(saleReturnItemAddIDTOList);
        saleReturnAddIDTO.setXdQuality(Boolean.TRUE); // 质检已经扣库存了，退货就不扣了
        TokenInfo qy = FastThreadLocalUtil.getQY();
        Boolean isBigShop = StallUtils.isBigShop(qy.getManagementMode());
        if(Objects.nonNull(isBigShop) && isBigShop.booleanValue()){
            saleReturnAddIDTO.setStallId(qy.getStallId());
        }
        saleReturnOrderClient.addSaleReturn(saleReturnAddIDTO);


        QualityReturnOrder pickOrder = new QualityReturnOrder();
        pickOrder.setStatus(YesOrNoEnums.YES.getCode());
        LambdaUpdateWrapper<QualityReturnOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(QualityReturnOrder::getId, idList);
        qualityReturnOrderMapper.update(pickOrder, updateWrapper);


    }
}
