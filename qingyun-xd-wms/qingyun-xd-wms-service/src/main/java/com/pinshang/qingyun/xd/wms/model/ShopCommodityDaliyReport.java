package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName ShopCommodityDaliyReport
 * <AUTHOR>
 * @Date 2022/11/10 11:13
 * @Description ShopCommodityDaliyReport
 * @Version 1.0
 */
@Data
@TableName("t_xd_shop_commodity_daliy_report")
public class ShopCommodityDaliyReport{
    @TableId
    private Long id;

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("在线商品数")
    private Integer commodityCountOnline;

    @ApiModelProperty("在线商品数_总部必售")
    private Integer commodityCountOnlineMustSell;

    @ApiModelProperty("在线商品数_门店自选")
    private Integer commodityCountOnlineSelfSelect;

    @ApiModelProperty("可售商品数")
    private Integer commodityCountOnSale;

    @ApiModelProperty("可售商品数_总部必售")
    private Integer commodityCountOnSaleMustSell;

    @ApiModelProperty("可售商品数_门店自选")
    private Integer commodityCountOnSaleSelfSelect;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private Long createId;

    @ApiModelProperty("加工时间")
    private Date processTime;

}
