package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Set;

@Data
@ToString
@ApiModel("GroupPickDetailIDTO")
public class GroupPickDetailIDTO implements Serializable {

    private static final long serialVersionUID = -3555729971442670385L;

    @ApiModelProperty("分区拣货单号列表")
    private Set<Long> pickPartitionOrderIdList;

    @ApiModelProperty("0-不检验 1-校验")
    private Integer checkPickStatus;

}
