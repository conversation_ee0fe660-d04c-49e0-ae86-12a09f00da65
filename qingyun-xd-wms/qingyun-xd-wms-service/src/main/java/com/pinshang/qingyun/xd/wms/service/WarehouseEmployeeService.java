package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.shop.dto.MdShopStatusODTO;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.smm.dto.employee.SelectShopEmployeeInfoListIDTO;
import com.pinshang.qingyun.smm.dto.employee.ShopEmployeeInfoODTO;
import com.pinshang.qingyun.smm.service.EmployeeServiceClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdPickAreaEmployeeODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdPickAreaEmployeePageIDTO;
import com.pinshang.qingyun.xd.wms.enums.OperateTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.WarehouseEmployeeTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.WorkStatusEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.DeliveryOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.ShopMapper;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseEmployeeMapper;
import com.pinshang.qingyun.xd.wms.model.Shop;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployee;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployeeLog;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickAreaEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDeque;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class WarehouseEmployeeService {

    @Autowired
    private WarehouseEmployeeMapper employeeMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private EmployeeServiceClient employeeServiceClient;

    @Autowired
    private WarehouseEmployeeLogService warehouseEmployeeLogService;

    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;

    @Autowired
    @Lazy
    private PickOrderService pickOrderService;

    @Lazy
    @Autowired
    private DdPickAreaEmployeeService pickAreaEmployeeService;

    @Autowired
    private ShopStatusClient shopStatusClient;

    @Autowired
    private DdPickAreaEmployeeService ddPickAreaEmployeeService;

    private static final String PICK_EMPLOYEE = "WMS:PICK_EMPLOYEE:";

    /**
     * 批量新增仓库员工
     *
     * @param dtoList
     * @return
     */
    public int batchInsert(List<WarehouseEmployeeDTO> dtoList) {
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        QYAssert.isTrue(!CollectionUtils.isEmpty(dtoList), "员工id不能为空");
        Map<Long, WarehouseEmployeeTypeEnum> empMap = dtoList.parallelStream().filter(Objects::nonNull)
                .collect(Collectors.toMap(WarehouseEmployeeDTO::getEmployeeId, WarehouseEmployeeDTO::getType));
        QYAssert.isTrue(!CollectionUtils.isEmpty(empMap), "员工id不能为空");
        List<WarehouseEmployee> employeeList = employeeMapper.getByEmployeeIds(empMap.keySet());
        if (!CollectionUtils.isEmpty(employeeList)){
            employeeList.forEach(item->{
                WarehouseEmployeeTypeEnum employeeTypeEnum = empMap.get(item.getEmployeeId());
                // 判断是否在当前门店中有同类型身份(配送员或者拣货员)
                QYAssert.isTrue(employeeTypeEnum == null ||
                                (item.getType().compareTo(employeeTypeEnum.getCode()) != 0
                                        || !item.getWarehouseId().equals(warehouseId))
                        , "员工已存在");
                // 如果有不是同一家门店的拣货员或者配送员记录 则停止拣货/配送, 并且删除该记录
                this.handlerOldWarehouseEmployeeInf(warehouseId, item);
            });
        }
        SelectShopEmployeeInfoListIDTO shopDTO = new SelectShopEmployeeInfoListIDTO();
        shopDTO.setShopId(warehouseId);
        List<ShopEmployeeInfoODTO> shopEmployeeInfoDTOS = employeeServiceClient.selectShopEmployeeInfoList(shopDTO);
        QYAssert.isTrue(!CollectionUtils.isEmpty(shopEmployeeInfoDTOS), "职员非当前前置仓职员");
        Map<Long, ShopEmployeeInfoODTO> shopMap = shopEmployeeInfoDTOS.parallelStream().filter(Objects::nonNull)
                .collect(Collectors.toMap(ShopEmployeeInfoODTO::getEmployeeId, v -> v));
        List<WarehouseEmployee> employees = new ArrayList<>();
        WarehouseEmployee employee;
        Date date = new Date();
        long userId = StockUtils.INSTANCE.userId();
        ShopEmployeeInfoODTO shop;
        for (WarehouseEmployeeDTO dto : dtoList) {
            dto.checkData();
            shop = shopMap.get(dto.getEmployeeId());
            QYAssert.isTrue(shop != null, "职员非当前前置仓职员");
            QYAssert.isTrue(shop.getEmployeeState() != null && shop.getEmployeeState() == 1, "非在职职员");
            QYAssert.isTrue(shop.getEmployeeAccountState() != null && shop.getEmployeeAccountState() == 1, "非已开通职员账号");
            employee = new WarehouseEmployee();
            employee.setEmployeeCode(shop.getEmployeeCode());
            employee.setEmployeeName(shop.getEmployeeName());
            employee.setEmployeePhone(dto.getEmployeePhone());
            employee.setEmployeeId(dto.getEmployeeId());
            employee.setType(dto.getType().getCode());
            employee.setWarehouseId(warehouseId);
            employee.setCreateId(userId);
            employee.setUpdateId(userId);
            employee.setCreateTime(date);
            employee.setUpdateTime(date);
            employees.add(employee);
        }
        employeeMapper.batchInsert(employees);
        //添加日志
        batchInsertLog(userId, OperateTypeEnum.INSERT.getCode(), employees);
        return employees.size();
    }

    /**
     * 如果有不是同一家门店的拣货员或者配送员记录 则停止拣货/配送, 并且删除该记录
     * @param newWarehouseId 当前新增拣货员/配送员的门店id
     * @param oldEmployeeInfo 该员工在t_xd_warehouse_employee表的旧记录
     */
    private void handlerOldWarehouseEmployeeInf(Long newWarehouseId, WarehouseEmployee oldEmployeeInfo){
        if(!oldEmployeeInfo.getWarehouseId().equals(newWarehouseId)){
            // 原有记录是拣货员, 停止拣货
            DelWarehouseEmployee delEmployee = new DelWarehouseEmployee();
            delEmployee.setEmployeeId(oldEmployeeInfo.getEmployeeId());
            if(oldEmployeeInfo.getType().equals(WarehouseEmployeeTypeEnum.PICK.getCode())){
                this.stopPickWork(newWarehouseId, oldEmployeeInfo.getEmployeeId());
            }
            delEmployee.setType(oldEmployeeInfo.getType());
            employeeMapper.deleteByEmployeeId(delEmployee);
            log.warn("删除拣货/配送员信息, 原门店id:{}, 员工id:{}, 类型:{}", oldEmployeeInfo.getWarehouseId(), oldEmployeeInfo.getEmployeeId(), oldEmployeeInfo.getType());
        }
    }

    /**
     * 批量添加增加日志
     * @param employees
     */
    private void batchInsertLog(long userId, int type, List<WarehouseEmployee> employees) {
        if (!CollectionUtils.isEmpty(employees)) {
            List<WarehouseEmployeeLog> logList = new ArrayList<>();
            WarehouseEmployeeLog warehouseEmployeeLog;
            for (WarehouseEmployee e : employees) {
                warehouseEmployeeLog = new WarehouseEmployeeLog();
                warehouseEmployeeLog.setEmployeeId(e.getEmployeeId());
                warehouseEmployeeLog.setEmployeeCode(e.getEmployeeCode());
                warehouseEmployeeLog.setEmployeeName(e.getEmployeeName());
                warehouseEmployeeLog.setType(e.getType());
                warehouseEmployeeLog.setWarehouseId(StockUtils.INSTANCE.warehouseId());
                warehouseEmployeeLog.setOperateType(type);
                warehouseEmployeeLog.setCreateId(userId);
                warehouseEmployeeLog.setCreateName(FastThreadLocalUtil.getQY().getRealName());
                warehouseEmployeeLog.setCreateTime(new Date());
                logList.add(warehouseEmployeeLog);
            }
            warehouseEmployeeLogService.batchInsert(logList);
        }
    }

    private void insertLog(int type, WarehouseEmployee data) {
        WarehouseEmployeeLog warehouseEmployeeLog = new WarehouseEmployeeLog();
        warehouseEmployeeLog.setEmployeeId(data.getEmployeeId());
        warehouseEmployeeLog.setEmployeeName(data.getEmployeeName());
        warehouseEmployeeLog.setEmployeeCode(data.getEmployeeCode());
        warehouseEmployeeLog.setType(data.getType());
        warehouseEmployeeLog.setWarehouseId(data.getWarehouseId());
        warehouseEmployeeLog.setOperateType(type);
        warehouseEmployeeLog.setCreateId(StockUtils.INSTANCE.userId());
        warehouseEmployeeLog.setCreateName(StockUtils.INSTANCE.realName());
        warehouseEmployeeLog.setCreateTime(new Date());
        warehouseEmployeeLogService.insert(warehouseEmployeeLog);
    }


    /**
     * 根据职员类型查询职员列表信息
     *
     * @param searchDTO
     * @return
     */
    public List<WarehouseEmployeeInfoDTO> getEmployeeByType(SearchWarehouseEmployeeDTO searchDTO) {
        SearchWarehouseEmployee warehouseEmployee = getSearchWarehouseEmployee(searchDTO);
        warehouseEmployee.setPageSize(999);
        List<WarehouseEmployeeInfoDTO> warehouseEmployeeInfoDTOS = employeeMapper.searchEmployeeByType(warehouseEmployee);
        warehouseEmployeeInfoDTOS = warehouseEmployeeInfoDTOS.parallelStream().filter(it->it != null && 1 == it.getWorkStatus())
                .collect(Collectors.toList());
        return warehouseEmployeeInfoDTOS;
    }


    /**
     * 根据职员类型查询职员列表信息
     *
     * @param idto
     * @return
     */
    public List<WarehouseEmployeeInfoDTO> getEmployeeByTypeForCloud(GetEmployeeByTypeAndShopIDTO idto) {
        int type = idto.getTypeEnum().getCode();
        idto.setType(type);
        List<WarehouseEmployeeInfoDTO> warehouseEmployeeInfoDTOS = employeeMapper.searchEmployeeByTypeForCloud(idto);
        warehouseEmployeeInfoDTOS = warehouseEmployeeInfoDTOS.parallelStream().filter(it->it != null && 1 == it.getWorkStatus())
                .collect(Collectors.toList());
        return warehouseEmployeeInfoDTOS;
    }


    private SearchWarehouseEmployee getSearchWarehouseEmployee(SearchWarehouseEmployeeDTO searchDTO) {
        QYAssert.isTrue(searchDTO != null, "搜索条件不能为空");
        searchDTO.checkData();
        int type = searchDTO.getTypeEnum().getCode();
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        QYAssert.notNull(warehouseId, "仓库id不能为空");
        SearchWarehouseEmployee warehouseEmployee = new SearchWarehouseEmployee();
        warehouseEmployee.setType(type);
        warehouseEmployee.setWarehouseId(warehouseId);
        warehouseEmployee.setKeyword(searchDTO.getKeyword());
        return warehouseEmployee;
    }

    /**
     * 根据职员类型查询职员列表信息（分页查询）
     * @param searchDTO
     * @return
     */
    public PageInfo<WarehouseEmployeeInfoDTO> searchEmployeeByType(SearchWarehouseEmployeeDTO searchDTO){
        SearchWarehouseEmployee warehouseEmployee = getSearchWarehouseEmployee(searchDTO);
        PageInfo<WarehouseEmployeeInfoDTO> pageInfo = PageHelper.startPage((int) searchDTO.getPageNo(), (int) searchDTO.getPageSize()).doSelectPageInfo(() -> {
            employeeMapper.searchEmployeeByType(warehouseEmployee);
        });
        return pageInfo;
    }

    /**
     * 移除仓库员工
     *
     * @param
     * @return
     */
    public int deleteByEmployeeId(DelWarehouseEmployee delWarehouseEmployee) {
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        delWarehouseEmployee.checkData();
        WarehouseEmployee employee = employeeMapper.getByEmployeeIdAndType(delWarehouseEmployee);
        // QYAssert.isTrue(employee==null || StringUtil.isBlank(employee.getEmployeeCode()),"员工处于工作状态不能移除");
        // QYAssert.isTrue(employee==null || StringUtil.isBlank(employee.getEmployeeCode()) || employee.getWorkStatus().equals(0),"员工处于工作状态不能移除");

        QYAssert.isTrue(!(employee == null || StringUtil.isBlank(employee.getEmployeeCode())), "员工不存在");
        QYAssert.isTrue(employee.getWorkStatus().equals(0), "员工处于工作状态不能移除");
        int i = employeeMapper.deleteByEmployeeId(delWarehouseEmployee);
        log.warn("移除仓库员工 {}操作了{}", userId, JsonUtil.java2json(delWarehouseEmployee));

        try {
            pickAreaEmployeeService.deleteByEmployeeId(delWarehouseEmployee.getEmployeeId());
        } catch (Exception e) {
            log.error("尝试删除大店分区拣货员 {}，employeeId{}", userId, delWarehouseEmployee.getEmployeeId(), e);
        }

        // 操作日志
        insertLog(OperateTypeEnum.MOVE.getCode(), employee);
        return i;
    }

    /**
     * 批量移除仓库员工
     *
     * @param
     * @return
     */
    public int deleteByEmployeeIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        //操作日志
        List<WarehouseEmployee> list =employeeMapper.getByEmployeeIds(new HashSet<>(ids));
        batchInsertLog(StockUtils.INSTANCE.userId(),OperateTypeEnum.MOVE.getCode(), list);
        return employeeMapper.deleteByEmployeeIds(ids);
    }

    /**
     * 分配拣货员
     *
     * @return
     */
    public Long distributePicker() {
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + warehouseId);
        if (pickers.size() == 0) {
            LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseEmployee>()
                    .eq(WarehouseEmployee::getWarehouseId, warehouseId)
                    .eq(WarehouseEmployee::getType, WarehouseEmployeeTypeEnum.PICK.getCode())
                    .eq(WarehouseEmployee::getWorkStatus, YesOrNoEnums.YES.getCode());
            List<WarehouseEmployee> employees = employeeMapper.selectList(query);
//            QYAssert.isTrue(SpringUtil.isNotEmpty(employees), "没有可用的拣货人员");
            if(SpringUtil.isEmpty(employees)){
                return null;
            }

            employees.forEach(it -> {
                pickers.addLast(it.getEmployeeId());
            });
        }

        Long employeeId = pickers.removeFirst();
        pickers.addLast(employeeId);
        return employeeId;
    }

    public PageInfo<WarehouseEmployeeInfoDTO> queryEmployeeWorkStatus(SearchWarehouseEmployeeDTO dto) {
        QYAssert.isTrue(dto != null && dto.getTypeEnum() != null, "职员类型不能为空");
        QYAssert.isTrue(WarehouseEmployeeTypeEnum.getTypeEnumByCode(dto.getTypeEnum().getCode()) != null,
                "职员类型不合法");
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        SearchWarehouseEmployee warehouseEmployee = new SearchWarehouseEmployee();
        warehouseEmployee.setType(dto.getTypeEnum().getCode());
        warehouseEmployee.setWarehouseId(warehouseId);
        PageInfo<WarehouseEmployeeInfoDTO> pageInfo =
                PageHelper.startPage((int) dto.getPageNo(), (int) dto.getPageSize()).doSelectPageInfo(() -> {
                    employeeMapper.searchEmployeeByType(warehouseEmployee);
                });
//        List<WarehouseEmployeeInfoDTO> dtos =
//        if (dtos != null) {
//        }
        return pageInfo;
    }

    public void startPickWork(Long shopId, Long employeeId) {
        QueryWrapper<WarehouseEmployee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("employee_id", employeeId);
        queryWrapper.eq("type", WarehouseEmployeeTypeEnum.PICK.getCode());
        WarehouseEmployee warehouseEmployee = employeeMapper.selectOne(queryWrapper);
        // 是停止捡货的状态才能进行开始捡货
        if (null != warehouseEmployee && warehouseEmployee.getWorkStatus().equals(YesOrNoEnums.NO.getCode())) {
            employeeMapper.doWorkStatus(employeeId, YesOrNoEnums.YES.getCode());

            // 添加日志
            insertLog(OperateTypeEnum.START.getCode(), warehouseEmployee);
            RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + shopId);
            if (pickers.contains(employeeId)) {
                return;
            }
            pickers.addLast(employeeId);
            log.warn("仓库员工开始拣货 {}-{}", shopId, employeeId);
        }

        //分区拣货同时维护
        pickAreaEmployeeService.pdaWorkOnOrOff(shopId, employeeId, YesOrNoEnums.YES.getCode());
    }

    public void stopPickWork(Long shopId, Long employeeId) {
        QueryWrapper<WarehouseEmployee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("employee_id", employeeId);
        queryWrapper.eq("type", WarehouseEmployeeTypeEnum.PICK.getCode());
        WarehouseEmployee warehouseEmployee = employeeMapper.selectOne(queryWrapper);
        // 是开始捡货的状态才能进行停止
        if (null != warehouseEmployee && warehouseEmployee.getWorkStatus().equals(YesOrNoEnums.YES.getCode())) {
            employeeMapper.doWorkStatus(employeeId, YesOrNoEnums.NO.getCode());

            // 添加日志
            insertLog(OperateTypeEnum.STOP.getCode(), warehouseEmployee);
            RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + shopId);
            pickers.clear();
            log.warn("仓库员工停止拣货 {}-{}", shopId, employeeId);
        }
        //分区拣货同时维护
        pickAreaEmployeeService.pdaWorkOnOrOff(shopId, employeeId, YesOrNoEnums.NO.getCode());
    }

    public void stopDeliverWork(Long shopId, Long employeeId) {
        QueryWrapper<WarehouseEmployee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("employee_id",employeeId);
        queryWrapper.eq("type",WarehouseEmployeeTypeEnum.DELIVERY.getCode());
        WarehouseEmployee warehouseDeliver = employeeMapper.selectOne(queryWrapper);
        //是工作的状态才能进行停止
        if (null != warehouseDeliver && warehouseDeliver.getWorkStatus().equals(YesOrNoEnums.YES.getCode())) {
            warehouseDeliver.setWorkStatus(YesOrNoEnums.NO.getCode());
            employeeMapper.updateById(warehouseDeliver);
        }
    }

    public void refreshPickEmployee(){
        LambdaQueryWrapper query = new LambdaQueryWrapper<Shop>()
                .in(Shop::getShopType, ShopTypeEnums.XS.getCode(), ShopTypeEnums.XD.getCode());
        List<Shop> xdShopList = shopMapper.selectList(query);
        if(SpringUtil.isNotEmpty(xdShopList)){
            for (Shop shop : xdShopList) {
                RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + shop.getId());
                pickers.clear();
            }
        }
    }

    public WarehouseEmployee queryPicker4Me() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, ApiErrorCodeEnum.TOKEN_EXPIRE);
        Long shopId = tokenInfo.getShopId();
        QYAssert.notNull(shopId, "门店id不能为空");
        Long employeeId = tokenInfo.getEmployeeId();

        Shop shop = shopMapper.selectById(shopId);
        if (ManagementModeEnums.档口分包.getCode().equals(shop.getManagementMode())) {
            return pickAreaEmployeeService.queryPicker4Me(shopId, employeeId);
        } else {
            LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseEmployee>()
                    .eq(WarehouseEmployee::getEmployeeId, employeeId)
                    .eq(WarehouseEmployee::getType, WarehouseEmployeeTypeEnum.PICK.getCode());
            return employeeMapper.selectOne(query);
        }
    }

    public int updateByEmployeeId(UpdateWarehouseEmployeeDTO updateDTO){
        QYAssert.isTrue(updateDTO != null, "员工id不能为空");
        updateDTO.checkData();
        List<WarehouseEmployee> employeeList =
                employeeMapper.getByEmployeeIds(Collections.singleton(updateDTO.getEmployeeId()));
        QYAssert.isTrue(!CollectionUtils.isEmpty(employeeList), "员工不存在");
        employeeList = employeeList.parallelStream().filter(item ->item.getType().equals(updateDTO.getType())).
                collect(Collectors.toList());
        QYAssert.isTrue(!CollectionUtils.isEmpty(employeeList), "员工不存在");
        WarehouseEmployee employee = new WarehouseEmployee();
        BeanUtils.copyProperties(updateDTO, employee);
        int res = employeeMapper.updateByEmployeeId(employee);
        insertLog(OperateTypeEnum.UPDATE.getCode(), employeeList.get(0));
        return res;
    }

    public WarehouseEmployee queryEmployee(Long employeeId, Integer type){
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseEmployee>()
                .eq(WarehouseEmployee::getEmployeeId, employeeId)
                .eq(WarehouseEmployee::getType, type);
        return employeeMapper.selectOne(query);
    }

    /**
     * 批量停止/开始工作
     * @param updateWorkStatusDTO
     */
    public void updateWorkStatus(UpdateWorkStatusDTO updateWorkStatusDTO) {
        updateWorkStatusDTO.checkData();
        List<WarehouseEmployee> employeeList = employeeMapper.selectBatchIds(updateWorkStatusDTO.getList());
        List<WarehouseEmployee> employees = employeeList.stream().filter(e -> !e.getWorkStatus().equals(updateWorkStatusDTO.getWorkStatus())).collect(Collectors.toList());

        if (SpringUtil.isEmpty(employeeList) || SpringUtil.isEmpty(employees)) {
            return;
        }

        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + shopId);

        List<Long> employeeIdList = new ArrayList<>(employees.size());
        if (!CollectionUtils.isEmpty(employees)) {
            WarehouseEmployee warehouseEmployee = null;
            for (WarehouseEmployee e: employees) {
                warehouseEmployee = new WarehouseEmployee();
                warehouseEmployee.setId(e.getId());
                warehouseEmployee.setWorkStatus(updateWorkStatusDTO.getWorkStatus());
                warehouseEmployee.setUpdateTime(new Date());
                employeeMapper.updateById(warehouseEmployee);
                if (updateWorkStatusDTO.getWorkStatus().equals(WorkStatusEnum.DISABLE.getCode())) {
                    pickers.remove(e.getEmployeeId());
                } else if (updateWorkStatusDTO.getWorkStatus().equals(WorkStatusEnum.ENABLE.getCode())) {
                    if(!pickers.contains(e.getEmployeeId())) {
                        pickers.addLast(e.getEmployeeId());
                    }
                }

                employeeIdList.add(e.getEmployeeId());
            }
            //添加日志
            Integer operateType = updateWorkStatusDTO.getWorkStatus() == WorkStatusEnum.DISABLE.getCode() ? OperateTypeEnum.STOP.getCode() : OperateTypeEnum.START.getCode();
            batchInsertLog(StockUtils.INSTANCE.userId(), operateType, employees);
        }

        // 同时维护分区拣货员
        ddPickAreaEmployeeService.updateWorkStatus(shopId, employeeIdList, updateWorkStatusDTO.getWorkStatus());
    }

    /**
     * 查询员工列表
     * @param dto
     * @return
     */
    public MPage<WarehouseEmployeeResult> employeeList(QueryWarehouseEmployee dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        MPage<WarehouseEmployeeResult> res = employeeMapper.employeeList(dto);
        List<WarehouseEmployeeResult> records = res.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<Long> delivery = records.stream().filter(e -> {
                        return e.getType().equals(WarehouseEmployeeTypeEnum.DELIVERY.getCode());
                    })
                    .map(WarehouseEmployeeResult::getEmployeeId).collect(Collectors.toList());
            List<Long> pick = records.stream().filter(e -> {
                        return e.getType().equals(WarehouseEmployeeTypeEnum.PICK.getCode());
                    })
                    .map(WarehouseEmployeeResult::getEmployeeId).collect(Collectors.toList());
            Map<Long, Integer> deliveryNum = null;
            if (!CollectionUtils.isEmpty(delivery)) {
                deliveryNum = deliveryOrderMapper.deliveryNum(delivery).stream().collect(Collectors.toMap(TaskNumDTO::getEmployeeId, TaskNumDTO::getNum));
            }

            Map<Long, Integer> pickNum = null;
            if (!CollectionUtils.isEmpty(pick)) {
                if (ManagementModeEnums.档口分包.getCode().equals(tokenInfo.getManagementMode())) {
                    MdShopStatusODTO shopStatus = shopStatusClient.getShopStatusByShopId(tokenInfo.getShopId());
                    if (Objects.nonNull(shopStatus) && PickingMethodEnum.ZONE_ORDER_PICKING.getCode().equals(shopStatus.getPickingMethod())) {
                        DdPickAreaEmployeePageIDTO ddPickAreaEmployeePageReq = buildDdPickAreaEmployeeReq(dto, tokenInfo);
                        PageInfo<DdPickAreaEmployeeODTO> ddPickAreaEmployeeODTOPageInfo = ddPickAreaEmployeeService.pickAreaEmployeePageList(ddPickAreaEmployeePageReq);
                        if (Objects.nonNull(ddPickAreaEmployeeODTOPageInfo) && SpringUtil.isNotEmpty(ddPickAreaEmployeeODTOPageInfo.getList())) {
                            pickNum = ddPickAreaEmployeeODTOPageInfo.getList().stream()
                                    .collect(Collectors.toMap(it -> Long.parseLong(it.getEmployeeId()), DdPickAreaEmployeeODTO::getTaskCount, (existing, replacement) -> existing));
                        }
                    } else {
                        pickNum = pickOrderService.pickNum(pick).stream().collect(Collectors.toMap(TaskNumDTO::getEmployeeId, TaskNumDTO::getNum));
                    }
                } else {
                    pickNum = pickOrderService.pickNum(pick).stream().collect(Collectors.toMap(TaskNumDTO::getEmployeeId, TaskNumDTO::getNum));
                }
            }

            for (WarehouseEmployeeResult e : records) {
                if (e.getType().equals(WarehouseEmployeeTypeEnum.PICK.getCode()) && pickNum != null) {
                    e.setTaskNum(pickNum.get(e.getEmployeeId()));
                } else if (e.getType().equals(WarehouseEmployeeTypeEnum.DELIVERY.getCode()) && deliveryNum != null) {
                    e.setTaskNum(deliveryNum.get(e.getEmployeeId()));
                }
            }
            res.setRecords(records);
        }

        return res;
    }

    private static DdPickAreaEmployeePageIDTO buildDdPickAreaEmployeeReq(QueryWarehouseEmployee dto, TokenInfo tokenInfo) {
        DdPickAreaEmployeePageIDTO ddPickAreaEmployeePageIDTO = new DdPickAreaEmployeePageIDTO();
        ddPickAreaEmployeePageIDTO.setPageNo(Long.valueOf(dto.getPageNo()).intValue());
        ddPickAreaEmployeePageIDTO.setPageSize(Long.valueOf(dto.getPageSize()).intValue());
        ddPickAreaEmployeePageIDTO.setShopId(tokenInfo.getShopId());
        ddPickAreaEmployeePageIDTO.setEmployeeCode(dto.getEmployeeCode());
        ddPickAreaEmployeePageIDTO.setWorkStatus(dto.getWorkStatus());
        return ddPickAreaEmployeePageIDTO;
    }

    /**
     * 获取用户信息map
     * @param userIdList
     * @return
     */
    public Map<Long,String> getEmployeeUserMapByIdList(List<Long> userIdList){
        Map<Long,String> employeeUserMap = new HashMap<>();
        if(CollectionUtils.isEmpty(userIdList)){
            return employeeUserMap;
        }
        List<WarehouseEmployeeInfoDTO> employeeInfoList = employeeMapper.getEmployeeUserByIdList(userIdList);
        if(!CollectionUtils.isEmpty(employeeInfoList)){
            employeeUserMap = employeeInfoList.stream().collect(Collectors.toMap(WarehouseEmployeeInfoDTO::getUserId,WarehouseEmployeeInfoDTO::getEmployeeName,(key1 , key2)-> key2));
        }
        return employeeUserMap;
    }
}
