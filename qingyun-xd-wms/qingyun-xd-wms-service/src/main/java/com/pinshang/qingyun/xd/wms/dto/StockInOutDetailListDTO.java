package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 库存出入库详情DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockInOutDetailListDTO {

    @ApiModelProperty(value = "商品code")
    private String commodityCode;

    @ApiModelProperty(value = "商品名")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

}