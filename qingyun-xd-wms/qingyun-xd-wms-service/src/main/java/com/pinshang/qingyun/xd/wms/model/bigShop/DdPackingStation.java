package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单打包口管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@ToString
@TableName("t_dd_packing_station")
public class DdPackingStation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属门店ID
     */
    private Long shopId;

    /**
     * 打包口
     */
    private String packingPort;

    /**
     * 状态，1-启用，0-停用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
