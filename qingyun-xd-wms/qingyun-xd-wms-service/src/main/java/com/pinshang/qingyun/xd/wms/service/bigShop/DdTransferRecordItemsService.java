package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdTransferRecordItemsMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdTransferRecordItems;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class DdTransferRecordItemsService extends ServiceImpl<DdTransferRecordItemsMapper, DdTransferRecordItems> {

    @Autowired
    private DdTransferRecordItemsMapper ddTransferRecordItemsMapper;

    /**
     * 根据移库单ID查询移库单明细
     */
    public List<DdTransferRecordItems> getItemsByTransferRecordId(Long transferRecordId) {
        if (Objects.isNull(transferRecordId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DdTransferRecordItems> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DdTransferRecordItems::getTransferRecordId, transferRecordId);
        return ddTransferRecordItemsMapper.selectList(wrapper);
    }

}
