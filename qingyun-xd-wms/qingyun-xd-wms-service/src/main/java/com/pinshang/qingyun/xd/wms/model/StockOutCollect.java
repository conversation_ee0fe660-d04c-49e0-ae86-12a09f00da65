package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liu zhen
 * @DateTime: 2023/4/3 15:49
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_xd_stock_out_collect")
public class StockOutCollect {
    @TableId
    private Long id;
    private Long  shopId;
    private Long commodityId;
    @ApiModelProperty("库存数量")
    private BigDecimal quantity;
    @ApiModelProperty("提取时间点")
    private Date collectTime;
    private Date createTime;
}
