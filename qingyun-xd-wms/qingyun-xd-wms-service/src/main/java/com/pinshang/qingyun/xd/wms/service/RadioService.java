package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.msg.dto.DynamicRadioDTO;
import com.pinshang.qingyun.msg.dto.RadioShopDTO;
import com.pinshang.qingyun.msg.service.RadioMsgClient;
import com.pinshang.qingyun.xd.wms.enums.RadioTemplateEnum;
import com.pinshang.qingyun.xd.wms.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RadioService {
    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private RadioMsgClient radioMsgClient;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 存在智能音响并且有待处理的及时达订单  和云超订单
     * @return
     */
    public Boolean waitingOrder() {
        List<RadioShopDTO> list = radioMsgClient.getAllRadioList();
        if (null != list && list.size() > 0) {
            List<Long> shopIds = list.stream().map(RadioShopDTO::getShopId).distinct().collect(Collectors.toList());
            //处理及时达的订单
            Map<Long, List<RadioShopDTO>> map = list.stream().collect(Collectors.groupingBy(RadioShopDTO::getShopId));
            List<Long> jsdShopIds = pickOrderService.queryWaitDealJsdOrder(shopIds);

            List<RadioShopDTO> radioList = new ArrayList<>();
            if (jsdShopIds.size() > 0) {
                for (Long shopId : jsdShopIds) {
                    radioList.addAll(map.get(shopId));
                }
            }

            if(radioList.size() > 0){
                List<DynamicRadioDTO> jsdList = new ArrayList<>();
                for (RadioShopDTO dto : radioList) {
                    jsdList.add(new DynamicRadioDTO(dto.getDeviceCode(), RadioTemplateEnum.WAITING_JSD_ORDER.getName()));
                }
                radioMsgClient.sendDynamicMsgList(jsdList);
            }

            radioList = new ArrayList<>();


            //查询待处理的云超订单
            List<Long> waitCloudShopIds = pickOrderService.waitCloudOrder(shopIds);
            if (null != waitCloudShopIds && waitCloudShopIds.size() > 0) {
                for (Long id : waitCloudShopIds) {
                    radioList.addAll(map.get(id));
                }
            }

            if(radioList.size() > 0){
                List<DynamicRadioDTO> ycList = new ArrayList<>();
                for (RadioShopDTO dto : radioList) {
                    ycList.add(new DynamicRadioDTO(dto.getDeviceCode(), RadioTemplateEnum.WAITING_YC_ORDER.getName()));
                }
                radioMsgClient.sendDynamicMsgList(ycList);
            }

            //查询待处理的第三方订单
            radioList = new ArrayList<>();
            List<Long> thirdShopIds = pickOrderService.queryThirdOrder(shopIds);
            if (null != thirdShopIds && thirdShopIds.size() > 0) {
                for (Long shopId : thirdShopIds) {
                    radioList.addAll(map.get(shopId));
                }
            }
            if(radioList.size() > 0){
                List<DynamicRadioDTO> thirdList = new ArrayList<>();
                for (RadioShopDTO dto : radioList) {
                    thirdList.add(new DynamicRadioDTO(dto.getDeviceCode(), RadioTemplateEnum.WAITING_THIRD_ORDER.getName()));
                }
                radioMsgClient.sendDynamicMsgList(thirdList);
            }

        }
        return Boolean.TRUE;
    }
}
