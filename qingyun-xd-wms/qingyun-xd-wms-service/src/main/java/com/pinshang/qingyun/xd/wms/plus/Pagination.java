package com.pinshang.qingyun.xd.wms.plus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class Pagination<T> implements MPage<T> {
    private long pageNo;

    private long pageSize;

    public long getPageNo() {
        return pageNo;
    }

    public void setPageNo(long pageNo) {
        this.pageNo = pageNo;
        this.setCurrent(pageNo);
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
        this.setSize(pageSize);
    }

    public void notLimit(){
        this.setSize(-1);
    }

    private static final long serialVersionUID = 8545996863226528798L;

    /**
     * 查询数据列表
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    @ApiModelProperty(hidden = true)
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    @ApiModelProperty(hidden = true)
    private long size = 20;

    /**
     * 当前页
     */
    @ApiModelProperty(hidden = true)
    private long current = 1;

    /**
     * 排序字段信息
     */
    @ApiModelProperty(hidden = true)
    private List<OrderItem> orders = new ArrayList<>();

    /**
     * 自动优化 COUNT SQL
     */
    @ApiModelProperty(hidden = true)
    private boolean optimizeCountSql = true;
    /**
     * 是否进行 count 查询
     */
    @ApiModelProperty(hidden = true)
    private boolean isSearchCount = true;


    /**
     * 是否存在上一页
     *
     * @return true / false
     */
    @ApiModelProperty(hidden = true)
    public boolean hasPrevious() {
        return this.current > 1;
    }

    /**
     * 是否存在下一页
     *
     * @return true / false
     */
    @ApiModelProperty(hidden = true)
    public boolean hasNext() {
        return this.current < this.getPages();
    }

    @Override
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    public IPage<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public long getTotal() {
        return this.total;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public IPage<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public long getSize() {
        return this.size;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public IPage<T> setSize(long size) {
        this.size = size;
        return this;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public long getCurrent() {
        return this.current;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public IPage<T> setCurrent(long current) {
        this.current = current;
        return this;
    }

    /**
     * 获取当前正序排列的字段集合
     * <p>
     * 为了兼容，将在不久后废弃
     *
     * @return 正序排列的字段集合
     * @see #getOrders()
     * @deprecated 3.2.0
     */
    @Nullable
    @Deprecated
    @ApiModelProperty(hidden = true)
    public String[] ascs() {
        return CollectionUtils.isNotEmpty(orders) ? mapOrderToArray(OrderItem::isAsc) : null;
    }

    /**
     * 查找 order 中正序排序的字段数组
     *
     * @param filter 过滤器
     * @return 返回正序排列的字段数组
     */
    @ApiModelProperty(hidden = true)
    private String[] mapOrderToArray(Predicate<OrderItem> filter) {
        List<String> columns = new ArrayList<>(orders.size());
        orders.forEach(i -> {
            if (filter.test(i)) {
                columns.add(i.getColumn());
            }
        });
        return columns.toArray(new String[0]);
    }

    /**
     * 移除符合条件的条件
     *
     * @param filter 条件判断
     */
    @ApiModelProperty(hidden = true)
    private void removeOrder(Predicate<OrderItem> filter) {
        for (int i = orders.size() - 1; i >= 0; i--) {
            if (filter.test(orders.get(i))) {
                orders.remove(i);
            }
        }
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂：
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    @ApiModelProperty(hidden = true)
    public IPage<T> addOrder(OrderItem... items) {
        orders.addAll(Arrays.asList(items));
        return this;
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂：
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    @ApiModelProperty(hidden = true)
    public IPage<T> addOrder(List<OrderItem> items) {
        orders.addAll(items);
        return this;
    }

    /**
     * 设置需要进行正序排序的字段
     * <p>
     * Replaced:{@link #addOrder(OrderItem...)}
     *
     * @param ascs 字段
     * @return 返回自身
     * @deprecated 3.2.0
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    public IPage<T> setAscs(List<String> ascs) {
        return CollectionUtils.isNotEmpty(ascs) ? setAsc(ascs.toArray(new String[0])) : this;
    }

    /**
     * 升序
     * <p>
     * Replaced:{@link #addOrder(OrderItem...)}
     *
     * @param ascs 多个升序字段
     * @deprecated 3.2.0
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    public IPage<T> setAsc(String... ascs) {
        // 保证原来方法 set 的语意
        removeOrder(OrderItem::isAsc);
        for (String s : ascs) {
            addOrder(OrderItem.asc(s));
        }
        return this;
    }

    /**
     * 获取需简要倒序排列的字段数组
     * <p>
     *
     * @return 倒序排列的字段数组
     * @see #getOrders()
     * @deprecated 3.2.0
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    public String[] descs() {
        return mapOrderToArray(i -> !i.isAsc());
    }

    /**
     * Replaced:{@link #addOrder(OrderItem...)}
     *
     * @param descs 需要倒序排列的字段
     * @return 自身
     * @deprecated 3.2.0
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    public IPage<T> setDescs(List<String> descs) {
        // 保证原来方法 set 的语意
        if (CollectionUtils.isNotEmpty(descs)) {
            removeOrder(item -> !item.isAsc());
            for (String s : descs) {
                addOrder(OrderItem.desc(s));
            }
        }
        return this;
    }

    /**
     * 降序，这方法名不知道是谁起的
     * <p>
     * Replaced:{@link #addOrder(OrderItem...)}
     *
     * @param descs 多个降序字段
     * @deprecated 3.2.0
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    public IPage<T> setDesc(String... descs) {
        setDescs(Arrays.asList(descs));
        return this;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public List<OrderItem> orders() {
        return getOrders();
    }

    @ApiModelProperty(hidden = true)
    public List<OrderItem> getOrders() {
        return orders;
    }

    @ApiModelProperty(hidden = true)
    public void setOrders(List<OrderItem> orders) {
        this.orders = orders;
    }

    @ApiModelProperty(hidden = true)
    @Override
    public boolean optimizeCountSql() {
        return optimizeCountSql;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public boolean isSearchCount() {
        if (total < 0) {
            return false;
        }
        return isSearchCount;
    }

    @ApiModelProperty(hidden = true)
    public IPage<T> setSearchCount(boolean isSearchCount) {
        this.isSearchCount = isSearchCount;
        return this;
    }

    @ApiModelProperty(hidden = true)
    public IPage<T> setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
        return this;
    }

    @NotNull
    @Override
    @ApiModelProperty(hidden = true)
    public List<T> getList() {
        return records;
    }

    @Override
    @ApiModelProperty(hidden = true)
    public boolean getHasNextPage() {
        return  this.current < this.getPages();
    }

    @Override
    @ApiModelProperty(hidden = true)
    public long getCurrentPage() {
        return this.current;
    }
}
