package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("PartitionPickOrderDetailODTO")
public class PartitionPickOrderDetailODTO implements Serializable {

    private static final long serialVersionUID = -2725589752367463636L;

    @ApiModelProperty(value = "拣货子单号")
    private String pickPartitionOrderCode;

    @ApiModelProperty(value = "拣货状态")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货状态名称")
    private String pickStatusName;

    @ApiModelProperty(value = "拣货人姓名")
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "pickId")
    private String pickEmployeeName;

    @ApiModelProperty(value = "拣货员Id")
    private Long pickId;

    @ApiModelProperty(value = "拣货子单商品信息")
    private List<PartitionPickOrderItemDetailODTO> partitionPickOrderItemDetails;

    public String getPickStatusName() {
        String remark = XdPickOrderStatusEnum.getByCode(pickStatus).getRemark();
        return XdPickOrderStatusEnum.CANCEL.getRemark().equals(remark) ? "已取消" : remark;
    }
}
