package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.enums.AbnormalHandles;
import com.pinshang.qingyun.xd.wms.service.AutoQualityOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: liuZhen
 * @DateTime: 2022/7/20 15:18
 */
@RequestMapping("autoQualityOrder")
@RestController
public class AutoQualityOrderController {
@Autowired
private AutoQualityOrderService qualityOrderService;
    @GetMapping("auto/{shopId}")
    public Boolean autoQuality(@PathVariable("shopId")Long shopId) {
        return qualityOrderService.auto(shopId ,AbnormalHandles.BACK);
    }
    @GetMapping("autoScrap/{shopId}")
    public Boolean autoScrapQuality(@PathVariable("shopId")Long shopId) {
        return qualityOrderService.auto(shopId , AbnormalHandles.SCRAP);
    }
}
