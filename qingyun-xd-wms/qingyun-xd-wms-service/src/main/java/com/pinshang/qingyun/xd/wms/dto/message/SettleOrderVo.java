package com.pinshang.qingyun.xd.wms.dto.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettleOrderVo {
    private Long sourceId;//订单ID，退货单ID 等

    private String sourceCode;//订单编码 退货单编码 退货编码等

    private Long storeId;// 客户ID

    private Date orderTime;//订单时间 或者 退货确认入库时间等

    private Date createTime;//数据产生日期，如 订单创建时间，出库单创建时间，确认退货单入库时间等

    private Long commodityId;// 商品ID

    private BigDecimal number;//数量

    private BigDecimal totalPrice;//总金额

    /** 实际数量 */
    private BigDecimal deliveryNumber;
    private BigDecimal deliveryTotalAmount;

    private Date toShopDate;

}
