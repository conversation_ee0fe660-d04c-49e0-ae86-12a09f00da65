package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTransferRecordDetailODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTransferRecordODTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTransferRecordPageIDTO;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTransferRecordService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdTransferRecordSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 移库
 * </p>
 *
 */
@Api(tags = "移库", description = "移库")
@RestController
@RequestMapping("/bigShop/transferRecord")
@RequiredArgsConstructor
public class DdTransferRecordController {

    private final DdTransferRecordService ddTransferRecordService;

    private final DdTokenShopIdService ddTokenShopIdService;

    /**
     * 大店补货数量（排面/拣货位）
     */
    @PostMapping("/save")
    @ApiOperation(value = "大店移库")
    public Boolean save(@RequestBody DdTransferRecordSaveVO vo) {
        ddTransferRecordService.saveTransferRecord(vo);
        return Boolean.TRUE;
    }

    /**
     * 移库单 列表
     */
    @PostMapping("/pageList")
    @MethodRender
    @ApiOperation(value = "移库单 列表", notes = "移库单 列表")
    public PageInfo<DdTransferRecordODTO> pageList(@RequestBody DdTransferRecordPageIDTO req) {
        ddTokenShopIdService.processReadDdTokenShopId(req.getShopId(), req.getStallId());
        return ddTransferRecordService.pageList(req);
    }

    /***
     * 移库单 明细
     */
    @MethodRender
    @ApiOperation(value = "移库单 明细", notes = "移库单 明细")
    @GetMapping("/itemList/{transferRecordId}")
    public DdTransferRecordDetailODTO queryDdTransferRecordItemList(@PathVariable("transferRecordId") Long transferRecordId) {
        return ddTransferRecordService.queryDdTransferRecordItemList(transferRecordId);
    }


}
