package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@ToString
@ApiModel("PrinterBindUnbundleVO")
public class PrinterBindUnbundleVO {

    /**
     * 绑定Id
     */
    @ApiModelProperty(value = "绑定Id")
    private Long printerBindId;


}
