package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickWorkOrderListDTO extends BaseEntity {

    @ApiModelProperty(value = "订单id" )
    private Long orderId;

    @ApiModelProperty(value = "拣货单id" )
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货单明细id" )
    private Long pickOrderItemId;

    @ApiModelProperty(value = "商品ID" )
    private Long commodityId;

    @ApiModelProperty(value = "处理标签ID" )
    private Long processId;

    @ApiModelProperty(value = "处理名称" )
    private String processName;

    @ApiModelProperty(value = "加工点ID" )
    private Long workId;

    @ApiModelProperty(value = "加工点名称" )
    private String workName;

    @ApiModelProperty(value = "加工人" )
    private Long workUserId;

    @ApiModelProperty(value = "加工点取货位" )
    private String shelfNo;

    @ApiModelProperty(value = "加工单状态(0=待处理，1＝已处理)" )
    private Integer workStatus;

    @ApiModelProperty(value = "完成时间")
    private Date completeTime;

    @ApiModelProperty(value = "加工人名称")
    private String employeeName;
}
