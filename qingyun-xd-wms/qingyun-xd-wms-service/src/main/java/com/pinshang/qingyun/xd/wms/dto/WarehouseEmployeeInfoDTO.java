package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseEmployeeInfoDTO {

    @ApiModelProperty("职员Id")
    private Long employeeId;

    @ApiModelProperty("员工编号/工号")
    private String employeeCode;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("职员状态：1-在职、4-离职")
    private Integer employeeState;

    @ApiModelProperty("职员手机号")
    private String employeePhone;

    @ApiModelProperty("工作状态：0停止工作，1开始工作")
    private Integer workStatus;

    /** 用户ID */
    private Long userId;
}
