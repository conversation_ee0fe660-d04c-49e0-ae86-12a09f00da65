package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.service.RadioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/radio")
@Api(value = "智能音箱", tags = "RadioController")
public class RadioController {

    @Autowired
    private RadioService radioService;

    @GetMapping(value = "/waitingOrder")
    @ApiOperation(value = "检测待处理的订单，job调用")
    public Boolean waitingOrder() {
        return radioService.waitingOrder();
    }
}
