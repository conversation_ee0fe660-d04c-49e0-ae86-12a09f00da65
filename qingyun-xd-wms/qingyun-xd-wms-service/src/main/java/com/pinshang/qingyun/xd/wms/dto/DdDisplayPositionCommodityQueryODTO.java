package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("DdDisplayPositionCommodityQueryODTO")
public class DdDisplayPositionCommodityQueryODTO {

    @ApiModelProperty("商品id")
    private String commodityId;

    @ApiModelProperty("商品条码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("型号规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commoditySpec, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    @ApiModelProperty("陈列位")
    private String displayPositionName;

    @ApiModelProperty("陈列位最小数量")
    private BigDecimal minStock;

    @ApiModelProperty("陈列位最大数量")
    private BigDecimal maxStock;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityUnit, keyName = "commodityId")
    private String commodityUnitName;

    @ApiModelProperty("商品包装")
    private String commodityPackageKind;
}
