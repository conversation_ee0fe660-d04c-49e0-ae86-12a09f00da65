package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PartitionPickOrderLogVO {

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 拣货单号
     */
    private String pickCode;

    /**
     * 分区拣货子单号
     */
    private String pickPartitionOrderCode;

    /**
     * 拣货分区
     */
    private Long pickAreaId;

    /**
     * 拣货分区名称
     */
    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA, fieldName = RenderFieldHelper.PickArea.pickAreaName, keyName = "pickAreaId")
    private String pickAreaName;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 分区拣货员ID
     */
    private Long pickId;

    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "pickId")
    private String pickEmployeeName;

    /**
     * 操作人工号
     */
    private String operateUserCode;

    /**
     * 操作人姓名
     */
    private String operateUserName;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人ID
     */
    private Long operateUserId;

}
