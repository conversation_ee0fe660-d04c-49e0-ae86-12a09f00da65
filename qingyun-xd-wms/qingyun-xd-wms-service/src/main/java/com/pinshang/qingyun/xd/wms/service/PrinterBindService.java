package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.EnableStatusEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.tiot.dto.TiotPrintClientIDTO;
import com.pinshang.qingyun.tiot.dto.TiotPrintClientODTO;
import com.pinshang.qingyun.tiot.service.TiotPrinterClient;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindQueryReqBO;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindSaveBO;
import com.pinshang.qingyun.xd.wms.bo.PrinterBindUnbundleBO;
import com.pinshang.qingyun.xd.wms.dto.PrinterBindODTO;
import com.pinshang.qingyun.xd.wms.dto.PrinterBindQueryIDTO;
import com.pinshang.qingyun.xd.wms.enums.PrinterDeptTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.PrinterTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.UserTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.PrinterBindMapper;
import com.pinshang.qingyun.xd.wms.model.PrinterBind;
import com.pinshang.qingyun.xd.wms.model.WarehouseWork;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPackingStation;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPackingStationService;
import com.pinshang.qingyun.xd.wms.vo.PrinterBindQueryRspVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PrinterBindService extends ServiceImpl<PrinterBindMapper, PrinterBind> {

    private final PrinterBindMapper printerBindMapper;
    private final WarehouseWorkService warehouseWorkService;
    private final DdPackingStationService ddPackingStationService;
    private final TiotPrinterClient tiotPrinterClient;

    @Transactional(rollbackFor = Exception.class)
    public void bind(PrinterBindSaveBO printerBindSaveBO) {

        QYAssert.isTrue(Objects.nonNull(UserTypeEnum.getTypeEnumByCode(printerBindSaveBO.getUserType())),"使用方类型只能为加工点和打包口");
        if(Objects.equals(printerBindSaveBO.getUserType(),UserTypeEnum.PROCESS_POINT.getCode())){
            //若使用方类型=加工点，使用方可选当前门店下的启用状态的加工点
            WarehouseWork warehouseWork = warehouseWorkService.queryWarehouseWorkById(printerBindSaveBO.getShopId(), printerBindSaveBO.getRealUserId());
            QYAssert.isTrue(Objects.nonNull(warehouseWork),"加工点不存在");
            QYAssert.isTrue(Objects.equals(EnableStatusEnums.ENABLE.getCode(),warehouseWork.getStatus()),"当前加工点未启用");
        }else{
            //如果使用方类型=打包口，使用方可选当前门店下的启用状态的打包口
            DdPackingStation ddPackingStation = ddPackingStationService.queryPackingStationById(printerBindSaveBO.getShopId(), printerBindSaveBO.getRealUserId());
            QYAssert.isTrue(Objects.nonNull(ddPackingStation),"打包口不存在");
            QYAssert.isTrue(Objects.equals(EnableStatusEnums.ENABLE.getCode(),ddPackingStation.getStatus()),"当前打包口未启用");
        }
        //检查前端上送的打印机编码是否是当前门店下的小票打印机
        TiotPrintClientIDTO tiotPrintClientIDTO = new TiotPrintClientIDTO();
        tiotPrintClientIDTO.setDeptType(PrinterDeptTypeEnum.SHOP.getCode());
        tiotPrintClientIDTO.setDeptId(printerBindSaveBO.getShopId());
        tiotPrintClientIDTO.setType(PrinterTypeEnum.VOUCHER.getCode());
        List<TiotPrintClientODTO> printerList = tiotPrinterClient.findPrinterListForClient(tiotPrintClientIDTO);
        if(SpringUtil.isEmpty(printerList)){
            QYAssert.isFalse("当前门店下无可用打印机");
        }

        Optional<TiotPrintClientODTO> printerOptional = printerList
                .stream()
                .filter(printer -> Objects.equals(printer.getCode(), printerBindSaveBO.getPrinterCode()))
                .findAny();
        if(!printerOptional.isPresent()){
            QYAssert.isFalse("当前打印机不在当前门店下，无法新增绑定");
        }

        //检查同一个使用方不可绑定多个打印机
        List<PrinterBind> list = this.list(new LambdaQueryWrapper<PrinterBind>().eq(PrinterBind::getRealUserId, printerBindSaveBO.getRealUserId()));
        QYAssert.isTrue(SpringUtil.isEmpty(list),"当前使用方已绑定过打印机");

        PrinterBind printerBind = BeanCloneUtils.copyTo(printerBindSaveBO, PrinterBind.class);
        printerBind.setCreateTime(new Date());
        printerBind.setPrinterMountingPosition(printerList.get(0).getLocation());
        this.save(printerBind);
    }

    @Transactional(rollbackFor = Exception.class)
    public void unbundle(PrinterBindUnbundleBO printerBindUnbundleBO) {

        Long printerBindId = printerBindUnbundleBO.getPrinterBindId();
        QYAssert.isTrue(Objects.nonNull(printerBindId),"解绑打印机不能为空");
        Long shopId = printerBindUnbundleBO.getShopId();
        List<PrinterBind> list = this.list(new LambdaQueryWrapper<PrinterBind>().eq(PrinterBind::getShopId, shopId).eq(PrinterBind::getId, printerBindId));
        QYAssert.isTrue(SpringUtil.isNotEmpty(list),"当前无绑定关系，无需解绑");
        this.remove(new LambdaQueryWrapper<PrinterBind>().eq(PrinterBind::getShopId,shopId).eq(PrinterBind::getId,printerBindId));

    }

    public MPage<PrinterBindQueryRspVO> bindPage(PrinterBindQueryReqBO printerBindQueryReqBO) {

        Integer userType = printerBindQueryReqBO.getUserType();
        if(Objects.nonNull(userType) && Objects.isNull(UserTypeEnum.getTypeEnumByCode(userType))){
            QYAssert.isFalse("使用方类型只能为加工点和打包口");
        }


        MPage<PrinterBindQueryRspVO> printerBindQueryRspVOMPage = printerBindMapper.bindPage(printerBindQueryReqBO);
        List<PrinterBindQueryRspVO> list = printerBindQueryRspVOMPage.getList();
        if(SpringUtil.isNotEmpty(list)){

            //加工点ids
            List<Long> processPointIds = list.stream().filter(printerBindQueryRspVO ->
                Objects.equals(printerBindQueryRspVO.getUserType(), UserTypeEnum.PROCESS_POINT.getCode()
            )).map(PrinterBindQueryRspVO::getRealUserId).collect(Collectors.toList());
            //打包口ids
            List<Long> packingStationIds = list.stream().filter(printerBindQueryRspVO ->
                Objects.equals(printerBindQueryRspVO.getUserType(), UserTypeEnum.PACKING_STATION.getCode())
            ).map(PrinterBindQueryRspVO::getRealUserId).collect(Collectors.toList());

            Map<Long,String> processPointIdNameMap = Collections.emptyMap();
            Map<Long,String> packingStationIdNameMap = Collections.emptyMap();

            if(SpringUtil.isNotEmpty(processPointIds)){
                processPointIdNameMap = warehouseWorkService.list(processPointIds).stream().collect(Collectors.toMap(WarehouseWork::getId,WarehouseWork::getWorkName));
            }

            if(SpringUtil.isNotEmpty(packingStationIds)){
                packingStationIdNameMap = ddPackingStationService.list(packingStationIds).stream().collect(Collectors.toMap(DdPackingStation::getId,DdPackingStation::getPackingPort));
            }


            //处理使用方名称展示
            Map<Long, String> finalProcessPointIdNameMap = processPointIdNameMap;
            Map<Long, String> finalPackingStationIdNameMap = packingStationIdNameMap;
            list.forEach(
                    printerBindQueryRspVO -> {
                        if(Objects.equals(printerBindQueryRspVO.getUserType(),UserTypeEnum.PROCESS_POINT.getCode())){
                            printerBindQueryRspVO.setRealUserName(finalProcessPointIdNameMap.get(printerBindQueryRspVO.getRealUserId()));
                        }else {
                            printerBindQueryRspVO.setRealUserName(finalPackingStationIdNameMap.get(printerBindQueryRspVO.getRealUserId()));
                        }
                    }
            );
        }

        return printerBindQueryRspVOMPage;

    }

    public Boolean isExistPrinterBind(String printerCode) {
        LambdaQueryWrapper<PrinterBind> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PrinterBind::getPrinterCode,printerCode);
        List<PrinterBind> list = this.list(queryWrapper);
        return SpringUtil.isNotEmpty(list) && list.size() > 0;
    }

    public PrinterBindODTO selectPrinterByParams(PrinterBindQueryIDTO idto) {
        QYAssert.isTrue(Objects.nonNull(idto),"查询参数不能为空");
        QYAssert.isTrue(Objects.nonNull(idto.getShopId()),"门店不能为空");
        QYAssert.isTrue(Objects.nonNull(idto.getUserType()),"使用方类型不能为空");
        QYAssert.isTrue(Objects.nonNull(idto.getRealUserId()),"使用方不能为空");

        List<PrinterBind> entityList = this.list(new LambdaQueryWrapper<PrinterBind>().eq(PrinterBind::getShopId, idto.getShopId())
                .eq(PrinterBind::getUserType, idto.getUserType())
                .eq(PrinterBind::getRealUserId, idto.getRealUserId()));

        if(SpringUtil.isNotEmpty(entityList)){
            PrinterBind printerBind = entityList.get(0);
            return BeanCloneUtils.copyTo(printerBind,PrinterBindODTO.class);
        }else{
            return null;
        }

    }
}
