package com.pinshang.qingyun.xd.wms.service.cloud;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.xd.wms.dto.DeliveryOrderKafkaDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CloudV2Service {

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Async
    public void sendDelivered(DeliveryOrderKafkaDTO deliveryOrderKafkaDTO) {
        log.info("开始时间={}", System.currentTimeMillis());
        try {
            Thread.sleep(4000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("结束时间={}", System.currentTimeMillis());
        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_DELIVERY_CHANGE_TOPIC.getTopic(),
                deliveryOrderKafkaDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_DELIVERY_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());

    }
}
