package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.PickingMethodEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.shop.dto.MdShopStatusODTO;
import com.pinshang.qingyun.shop.service.ShopStatusClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.WorkOrderStatusEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.PickWorkOrderMapper;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.PickWorkOrder;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPickPartitionOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import com.pinshang.qingyun.xd.wms.util.StallUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PickWorkOrderService extends ServiceImpl<PickWorkOrderMapper, PickWorkOrder> {

    @Autowired
    private PickWorkOrderMapper pickWorkOrderMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private PrinterBindService printerBindService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private ShopStatusClient shopStatusClient;

    @Autowired
    private PickOrderItemService pickOrderItemService;

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    /**
     * 查询加工单
     *
     * @param dto
     * @return
     */
    public MPage<PickWorkOrderResult> pickWorkOrderList(PickWorkOrderDTO dto) {
        dto.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        MPage<PickWorkOrderResult> page = pickWorkOrderMapper.pickWorkOrderList(dto);
        if (page != null && SpringUtil.isNotEmpty(page.getList())) {
            List<Long> commoditys = page.getList().stream().map(PickWorkOrderResult::getCommodityId).collect(Collectors.toList());
            //条形码
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commoditys);
            page.getList().stream().forEach(e -> {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
            });
        }
        return page;
    }

    /**
     * 打印加工单
     */
    public PrintWorkOrderrResult printWorkOrder(Long pickWorkOrderId) {
        return pickWorkOrderMapper.printWorkOrder(pickWorkOrderId);
    }

    /**
     * 加工完成
     *
     * @param pickWorkOrderId
     */
    @Transactional
    public void workComplete(Long pickWorkOrderId) {
        PickWorkOrder one = pickWorkOrderMapper.selectById(pickWorkOrderId);
        QYAssert.isTrue(null != one, "加工单不存在");
        QYAssert.isTrue(!one.getWorkStatus().equals(WorkOrderStatusEnum.FINISH.getCode()), "已经完成");
        QYAssert.isTrue(one.getWorkStatus().equals(WorkOrderStatusEnum.MIDDLE.getCode()), "未在处理中不能完成");
        PickWorkOrder pickWorkOrder = new PickWorkOrder();
        pickWorkOrder.setId(pickWorkOrderId);
        pickWorkOrder.setCompleteTime(new Date());
        pickWorkOrder.setWorkStatus(WorkOrderStatusEnum.FINISH.getCode());
        pickWorkOrder.setWorkUserId(StockUtils.INSTANCE.userId());
        pickWorkOrderMapper.updateById(pickWorkOrder);
    }

    public void distributeShelfNo(Long pickOrderId, String shelfNo) {
        pickWorkOrderMapper.distributeShelfNo(pickOrderId, shelfNo);
    }

    public List<PickWorkOrder> listByPickOrderIds(List<Long> pickOrderIdList) {
        if (SpringUtil.isEmpty(pickOrderIdList)) {
            return Collections.emptyList();
        }

        return pickWorkOrderMapper.selectList(
                new LambdaQueryWrapper<PickWorkOrder>()
                        .in(PickWorkOrder::getPickOrderId, pickOrderIdList)
        );
    }

    public JsonMsgBean tiotPrintWorkOrder(Long pickWorkOrderId) {
        //判断是否是档口分包
        TokenInfo token = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(Objects.nonNull(token),"请登录后打印");
        Integer managementMode = token.getManagementMode();
        QYAssert.isTrue(BooleanUtils.isTrue(StallUtils.isBigShop(managementMode)),"门店经营模式不为档口分包，无法使用网络打印");
        Long shopId = token.getShopId();

        return printWorkOrder(pickWorkOrderId,shopId);
    }

    public JsonMsgBean printWorkOrder(Long pickWorkOrderId, Long shopId) {
        JsonMsgBean result = new JsonMsgBean().initSuccess();
        PickWorkOrderResult pickWorkOrderResult = pickWorkOrderMapper.pickWorkOrderById(pickWorkOrderId, shopId);
        QYAssert.isTrue(Objects.nonNull(pickWorkOrderResult), "加工单不存在");

        PrinterBindQueryIDTO idto = new PrinterBindQueryIDTO();
        idto.setShopId(pickWorkOrderResult.getShopId());
        idto.setUserType(1);
        idto.setRealUserId(pickWorkOrderResult.getWorkId());
        PrinterBindODTO printerBindODTO = printerBindService.selectPrinterByParams(idto);

        if (Objects.isNull(printerBindODTO)) {
            log.warn("打包口:[{}]未绑定网络打印机", idto);
            result.initFailure("打包口未绑定网络打印机");
            return result;
        }

        String printerCode = printerBindODTO.getPrinterCode();

        ShopProcessOrderPrintDTO shopProcessOrderPrintDTO = new ShopProcessOrderPrintDTO();
        TiotShopProcessOrderPrintDTO tiotShopProcessOrderPrintDTO = BeanCloneUtils.copyTo(pickWorkOrderResult, TiotShopProcessOrderPrintDTO.class);

        tiotShopProcessOrderPrintDTO.setId(pickWorkOrderResult.getOrderId());
        tiotShopProcessOrderPrintDTO.setNumber(BigDecimal.valueOf(pickWorkOrderResult.getStockNumber()));
        //查询门店经营模式是否为分区按单拣货，拣货单号替换为分区拣货单号
        Integer shopPickingMethod = getShopPickingMethod(shopId);
        if(Objects.equals(PickingMethodEnum.ZONE_ORDER_PICKING.getCode(),shopPickingMethod)){
            Long pickOrderItemId = pickWorkOrderResult.getPickOrderItemId();
            PickOrderItem pickOrderItem = pickOrderItemService.getById(pickOrderItemId);
            if(Objects.nonNull(pickOrderItem)){
                DdPickPartitionOrder ddPickPartitionOrder = ddPickPartitionOrderService.getById(pickOrderItem.getPickPartitionOrderId());
                if(Objects.nonNull(ddPickPartitionOrder)){
                    tiotShopProcessOrderPrintDTO.setPickCode(ddPickPartitionOrder.getPickPartitionOrderCode());
                }
            }
        }
        shopProcessOrderPrintDTO.setTiotShopProcessOrderPrintDTO(tiotShopProcessOrderPrintDTO);
        shopProcessOrderPrintDTO.setPrinterTemplateNo("MB0009");
        shopProcessOrderPrintDTO.setPrinterCode(printerCode);




        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.TIOT_PRINT_TOPIC.getTopic(),
                shopProcessOrderPrintDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SHOP_PROCESS_ORDER_PRINT.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());

        return result;
    }


    private Integer getShopPickingMethod(Long shopId) {
        MdShopStatusODTO shopStatusByShopId = shopStatusClient.getShopStatusByShopId(shopId);
        return Optional.ofNullable(shopStatusByShopId).map(MdShopStatusODTO::getPickingMethod).orElse(null);
    }
}
