package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * @ClassName TempOperationTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/20 18:26
 * @Version 1.0
 */
public enum TempOperationTypeEnum {
    POS_RETURN(0, "POS退货入库"),
    APP_RETURN(1, "APP退货入库"),
    DISCOUNT_RETURN(2, "折扣特价码入库"),
    STORE_RETURN_CANCEL(3, "门店退货取消"),
    ;

    private Integer code;
    private String name;


    TempOperationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (TempOperationTypeEnum tempOperationTypeEnum : TempOperationTypeEnum.values()) {
            if (tempOperationTypeEnum.getCode().equals(code)) {
                return tempOperationTypeEnum.getName();
            }
        }
        return null;
    }
}
