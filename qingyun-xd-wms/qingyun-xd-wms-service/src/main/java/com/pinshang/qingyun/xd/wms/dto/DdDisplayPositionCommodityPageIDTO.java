package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel("DdDisplayPositionCommodityPageIDTO")
public class DdDisplayPositionCommodityPageIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("货架id")
    private Long shelveId;

    @ApiModelProperty("陈列位")
    private Long displayPositionId;

    private List<Long> shopIdList;

    @ApiModelProperty("商品")
    private Long commodityId;

    @ApiModelProperty("pos是否可售, 1-是, 0-否")
    private Integer commoditySaleStatus;

    @ApiModelProperty("1-仅看未绑定陈列位的商品, 0-全部")
    private Integer bindType;

    @ApiModelProperty("1-仅看未设置陈列位安全库存的商品, 0-全部")
    private Integer safeStockType;

    @ApiModelProperty("品类id")
    private Long cateId;
}