package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.shop.dto.stock.SelectWeightPriceListIDTO;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockSnapshotCommodityTotalMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockSnapshotCommodityWeeklyMapper;
import com.pinshang.qingyun.xd.wms.model.StockSnapshotCommodityWeekly;
import com.pinshang.qingyun.xd.wms.vo.StockSnapshotVO;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-06-01-9:58
 */
@Service
@Slf4j
public class StockSnapshotTransactionService {
    @Autowired
    private ShopStockClient shopStockClient;

    @Autowired
    private StockSnapshotCommodityWeeklyMapper stockSnapshotCommodityWeeklyMapper;

    @Autowired
    private StockSnapshotCommodityTotalMapper stockSnapshotCommodityTotalMapper;

    @Autowired
    private StockLogMapper stockLogMapper;

    public void insertTotal(Long shopId){
        List<StockSnapshotVO> stockSnapshotVOList = stockSnapshotCommodityTotalMapper.selectStockInfoByshopId(shopId, null);
        Map<Long, BigDecimal> weightPrice = shopStockClient.selectWeightPriceTotal(shopId);
        for(int i = 0 ; i< stockSnapshotVOList.size(); i++){
            StockSnapshotVO item = stockSnapshotVOList.get(i);
            item.setWeightPrice(weightPrice.get(item.getCommodityId()));
            // 防止反复物理删减id增大到超出上限
            item.setId( i + 1);
        }
        this.batchInsertTotal(stockSnapshotVOList, shopId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertWeekly(String endTime, String beginTime, Long shopId){
        // 2. 查询门店下活跃商品
        List<Long> activeCommodityList = stockSnapshotCommodityWeeklyMapper.getActiveCommodityByShopId(beginTime, endTime, shopId);
        if(SpringUtil.isEmpty(activeCommodityList)){
            return ;
        }
        // 3. 查询活跃商品的库存快照
        List<StockSnapshotVO> stockSnapshotVOList = stockSnapshotCommodityTotalMapper.selectStockInfoByshopId(shopId, activeCommodityList);
        // 4. 构建并分批保存周快照
        Date now = new Date();
        List<StockSnapshotCommodityWeekly> newSnapshot = stockSnapshotVOList.parallelStream()
                .map(it -> {
                    StockSnapshotCommodityWeekly weekly = new StockSnapshotCommodityWeekly();
                    weekly.setShopId(shopId);
                    weekly.setNumber(it.getStockNum());
                    weekly.setCommodityId(it.getCommodityId());
                    weekly.setQuantity(it.getStockQuantity());
                    return weekly;
                }).collect(Collectors.toList());
        this.batchInsertWeekly(newSnapshot, DateUtil.get4yMd(now), now, 1L);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertHistoryWeekly(String endTime, String beginTime, Long shopId){
        // 3. 查询活跃商品的库存快照
        List<StockSnapshotVO> stockSnapshotVOList = stockLogMapper.selectStockByDate(shopId,beginTime, endTime);
        // 4. 构建并分批保存周快照
        Date now = new Date();
        List<StockSnapshotCommodityWeekly> newSnapshot = stockSnapshotVOList.parallelStream()
                .map(it -> {
                    StockSnapshotCommodityWeekly weekly = new StockSnapshotCommodityWeekly();
                    weekly.setShopId(shopId);
                    weekly.setNumber(it.getStockNum());
                    weekly.setCommodityId(it.getCommodityId());
                    weekly.setQuantity(it.getStockQuantity());
                    return weekly;
                }).collect(Collectors.toList());
        this.batchInsertWeekly(newSnapshot, endTime, now, 1L);
    }



    private void batchInsertTotal( List<StockSnapshotVO> list, Long shopId){
        int space = 5000;
        int shopLen = list.size() /space;
        shopLen += list.size() % space > 0 ? 1: 0;
        int from;
        int end;
        Date now = new Date();
        for(int i = 0; i < shopLen; i++){
            from = i * space;
            end =  from + space;
            end = Math.min(end, list.size());
            stockSnapshotCommodityTotalMapper.batchInsert(shopId, list.subList(from, end), now);
        }
    }


    /**
     * 批量插入每周快照
     * @param list
     * @param shotDate
     * @param createTime
     * @param createId
     */
    private void batchInsertWeekly(List<StockSnapshotCommodityWeekly> list, String shotDate, Date createTime, Long createId){
        int space = 5000;
        int shopLen = list.size() /space;
        shopLen += list.size() % space > 0 ? 1: 0;
        int from;
        int end;
        for(int i = 0; i < shopLen; i++){
            from = i * space;
            end =  from + space;
            end = Math.min(end, list.size());
            stockSnapshotCommodityWeeklyMapper.batchInsert( list.subList(from, end), shotDate, createTime, createId);
        }
    }


}
