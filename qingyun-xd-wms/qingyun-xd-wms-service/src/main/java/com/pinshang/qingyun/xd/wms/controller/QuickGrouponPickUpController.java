package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.quick.QuickOrderPickUpDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.QuickPickUpSubmitDTO;
import com.pinshang.qingyun.xd.wms.dto.quick.WarnCodeDTO;
import com.pinshang.qingyun.xd.wms.service.groupon.QuickGrouponPickUpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/quickGroupon/")
@Api(value = "清美团团核销", tags = "QuickGrouponPickUpController")
public class QuickGrouponPickUpController {

    @Autowired
    private QuickGrouponPickUpService quickGrouponPickUpService;

    @ApiOperation(value = "清美团团核销，查询订单信息")
    @GetMapping("cancelAfterVerification")
    public QuickOrderPickUpDTO cancelAfterVerification(@RequestParam(value = "pickupCode", required = false) String pickupCode) {
        return quickGrouponPickUpService.cancelAfterVerification(pickupCode);
    }

    @ApiOperation(value = "开始核销，锁定该订单")
    @GetMapping("startWritingOff")
    public WarnCodeDTO startWritingOff(@RequestParam(value = "orderId", required = false) Long orderId) {
        return quickGrouponPickUpService.startWritingOff(orderId);
    }

    @ApiOperation(value = "提交核销")
    @PostMapping("submitWritingOff")
    public Boolean submitWritingOff(@RequestBody List<QuickPickUpSubmitDTO> submitList) {
        return quickGrouponPickUpService.submitWritingOff(submitList);
    }

    @ApiOperation(value = "取消订单的锁定")
    @GetMapping("unlock")
    public Boolean unlock(@RequestParam(value = "orderId", required = false) Long orderId) {
        return quickGrouponPickUpService.unlock(orderId);
    }
}
