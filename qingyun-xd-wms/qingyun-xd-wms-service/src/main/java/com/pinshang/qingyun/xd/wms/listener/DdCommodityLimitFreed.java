package com.pinshang.qingyun.xd.wms.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdCommodityLimitFeedDTO;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.DdCommodityLimitMapper;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdCommodityLimit;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdCommodityLimitService;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Component
@Slf4j
public class DdCommodityLimitFreed implements RedisDelayQueueHandle<String> {

    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private DdCommodityLimitService ddCommodityLimitService;
    @Autowired
    private DdCommodityLimitMapper ddCommodityLimitMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute(String occupyCode) {
        log.info("大店商品限量, occupyCode : {}" , occupyCode);

        DdCommodityLimitFeedDTO req = JsonUtil.json2java(occupyCode, DdCommodityLimitFeedDTO.class);
        boolean isEmpty = (req.getShopId() == null
                            || req.getStallId() == null
                            || req.getCommodityId() == null
                            || req.getLimitNumber() == null
                            || req.getEffectType() == null
                            || req.getLoopTime() == null
                            || req.getUserId() == null);
        if(isEmpty){
            log.warn("大店商品限量, 参数错误, occupyCode : {}" , occupyCode);
            return;
        }

        LambdaQueryWrapper query = new LambdaQueryWrapper<DdCommodityLimit>()
                .eq(DdCommodityLimit::getShopId, req.getShopId())
                .eq(DdCommodityLimit::getStallId, req.getStallId())
                .eq(DdCommodityLimit::getCommodityId, req.getCommodityId());
        List<DdCommodityLimit> ddCommodityLimitList = ddCommodityLimitMapper.selectList(query);
        if(CollectionUtils.isNotEmpty(ddCommodityLimitList)){
            // 原始的商品限量
            List<DdCommodityLimit> oldList = BeanCloneUtils.copyTo(ddCommodityLimitList, DdCommodityLimit.class);

            DdCommodityLimit commodityLimit = ddCommodityLimitList.get(0);
            // 如果消息过来了和数据库里面的不一致就return
            boolean isSame = (req.getEffectType().equals(commodityLimit.getEffectType())
                            && req.getLoopTime().equals(commodityLimit.getLoopTime())
                            && req.getLimitNumber().equals(commodityLimit.getLimitNumber()));

            if(!isSame) {
                log.warn("大店商品限量循环设置,和数据库值不一致，直接返回。 occupyCode {}", occupyCode);
                return;
            }

            commodityLimit.setPurchaseNumber(0);
            commodityLimit.setUpdateId(req.getUserId());
            commodityLimit.setUpdateTime(new Date());
            commodityLimit.setEffectBeginTime(new Date());
            ddCommodityLimitMapper.updateById(commodityLimit);

            // 发送库存消息(库存变动才发消息，库存由无到有或者库存由有到无)
            List<DdCommodityLimit> newList = ddCommodityLimitService.queryDdCommodityLimitList(req.getShopId(), req.getStallId(), Collections.singletonList(req.getCommodityId()));

            ddCommodityLimitService.dealStockMessage(req.getShopId(), newList);

            // 根据loopTime重新计算加入延迟队列里面的时间
            Long delayTime = DdUtils.getDelayTime(req.getLoopTime());
            DelayMsgIDTO idto = new DelayMsgIDTO(RedisDelayQueueEnum.DD_COMMODITY_LIMIT_SET.getCode(), JsonUtil.java2json(req), TimeUnit.MILLISECONDS.toSeconds(delayTime), TimeUnit.SECONDS);
            delayMsgClient.addDelayQueue(idto);

        }else {
            log.warn("大店商品限量, 获取商品限量为空, occupyCode : {}" , occupyCode);
        }
    }
}
