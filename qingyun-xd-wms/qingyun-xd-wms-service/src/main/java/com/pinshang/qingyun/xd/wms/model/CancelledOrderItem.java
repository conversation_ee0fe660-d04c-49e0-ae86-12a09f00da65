package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("t_xd_cancelled_order_item")
public class CancelledOrderItem extends BaseEntity {


    @ApiModelProperty(value = "取消单id")
    private  Long cancellecOrderId;

    @ApiModelProperty(value = "商品id")
    private  Long commodityId;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "订单数")
    private Integer orderNumber;

    @ApiModelProperty(value = "发货数")
    private Integer  deliverNumber;

    @ApiModelProperty(value = "'正常品入库份数")
    private Integer  normalNumber;

    @ApiModelProperty(value = "异常品入库份数")
    private Integer abnormalNumber;

}
