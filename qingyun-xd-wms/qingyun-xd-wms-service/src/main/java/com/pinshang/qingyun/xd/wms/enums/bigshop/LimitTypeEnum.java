package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 限量计算方式 1=从0计算  2=累加计算
 *
 * <AUTHOR>
 */
public enum LimitTypeEnum {
    FROM_ZERO(1, "从0计算"),
    SUMMATION(2, "累加计算")
    ;
    private Integer code;
    private String name;

    LimitTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static LimitTypeEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LimitTypeEnum typeEnum : LimitTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LimitTypeEnum typeEnum : LimitTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum.getName();
            }
        }
        return "";
    }
}
