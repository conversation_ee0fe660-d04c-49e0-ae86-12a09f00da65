package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.xd.wms.dto.WorkCommodityLogDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityLogListDTO;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseWorkCommodityLogMapper;
import com.pinshang.qingyun.xd.wms.model.WarehouseWorkCommodityLog;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WarehouseWorkCommodityLogService {

    @Autowired
    private WarehouseWorkCommodityLogMapper warehouseWorkCommodityLogMapper;

    /**
     * 批量添加
     * @param list
     */
    public void batchInsert(List<WarehouseWorkCommodityLog> list) {
        warehouseWorkCommodityLogMapper.batchInsert(list);
    }


    /**
     * 查询日志列表
     * @param dto
     * @return
     */
    public MPage<WorkCommodityLogListDTO> workCommodityLogList(WorkCommodityLogDTO dto) {
        dto.setWarehouseId(StockUtils.INSTANCE.warehouseId());

        if (!StringUtils.isEmpty(dto.getStartTime())) {
            dto.setStartTime(dto.getStartTime()+" 00:00:00");
        }

        if (!StringUtils.isEmpty(dto.getEndTime())) {
            dto.setEndTime(dto.getEndTime()+ " 23:59:59");
        }

        return warehouseWorkCommodityLogMapper.workCommodityLogList(dto);
    }
}
