package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@ApiModel("OrderPackingODTO")
public class OrderPackingODTO implements Serializable {

    private static final long serialVersionUID = 4582366980640444252L;

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "收货人")
    private String receiveMan;

    @ApiModelProperty(value = "收货人电话")
    private String receiveMobile;

    @ApiModelProperty(value = "收货地址")
    private String receiveAddress;

    @ApiModelProperty(value = "打包口")
    private String packingPort;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @JsonIgnore
    private Integer packingStatus;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStatusName;

    @ApiModelProperty(value = "是否显示打包完成")
    private Boolean showPackingComplete = Boolean.FALSE;

    @ApiModelProperty(value = "是否显示打印小票")
    private Boolean showPrintTicket = Boolean.FALSE;

    @JsonIgnore
    private Long pickOrderId;

    @ApiModelProperty(value = "拣货份数")
    private Integer pickNumber;

    private Date orderDeliveryBeginTime;

    private Date orderDeliveryEndTime;

    @ApiModelProperty(value = "预约时间")
    private String orderDeliveryTime;

    private Long packingId;

    @ApiModelProperty(value = "打包员工名称")
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "packingId")
    private String packingEmployeeName;

    public String getOrderStatusName() {
        return XdOrderStatusEnum.getName(orderStatus);
    }

    public String getOrderDeliveryTime() {
        if (orderDeliveryBeginTime == null || orderDeliveryEndTime == null) {
            return null;
        }
        // 创建日期格式器
        SimpleDateFormat datePart = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat timePart = new SimpleDateFormat("HH:mm");

        return datePart.format(orderDeliveryBeginTime) + " - " + timePart.format(orderDeliveryEndTime);
    }
}
