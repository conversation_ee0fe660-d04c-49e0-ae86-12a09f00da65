package com.pinshang.qingyun.xd.wms.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配送批次
 * <AUTHOR>
 */
public enum DeliveryBatchEnum {
    NO_BATCH(0,"无需批次配送"),
    ONE_BATCH(1,"1配"),
    TWO_BATCH(2,"2配"),
    THREE_BATCH(3,"3配"),
    TEMP_BATCH(9,"临时批次")
    ;
    private Integer code;
    private String name;

    DeliveryBatchEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (DeliveryBatchEnum es : DeliveryBatchEnum.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public static List toList() {
        List list = new ArrayList();
        for (DeliveryBatchEnum deliveryBatchEnum : DeliveryBatchEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", deliveryBatchEnum.getCode());
            map.put("name", deliveryBatchEnum.getName());
            list.add(map);
        }
        return list;
    }
}
