package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.enums.shop.ShopCommodityBusinessTypeEnum;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.shop.dto.shopCommodity.SaveShopCommodityIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommoditySaveDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityTransferInventoryIDTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.shopCommodity.SaveShopCommodityClientClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/12/18
 */
@Slf4j
@Service
public class StockAllotOrderApplyInitService {

    @Autowired
    private ShopCommodityClient shopCommodityClient;

    @Autowired
    private SaveShopCommodityClientClient saveShopCommodityClientClient;
    /**
     * 初始化调入门店商品信息
     * @param userId
     * @param inShopId
     * @param outShopId
     * @param commodityIdList
     * @return
     */
    @Async
    public Boolean initShopCommodityTransferInventory(String code ,Long userId, Long inShopId, Long outShopId, List<Long> commodityIdList){
        List<ShopCommoditySaveDTO> shopCommodityList = new ArrayList<>();
        commodityIdList.forEach(item->{
            ShopCommoditySaveDTO shopCommoditySaveDTO = new ShopCommoditySaveDTO();
            shopCommoditySaveDTO.setShopId(inShopId);
            shopCommoditySaveDTO.setOutShopId(outShopId);
            shopCommoditySaveDTO.setCommodityId(item);
            shopCommodityList.add(shopCommoditySaveDTO);
        });
        SaveShopCommodityIDTO init = SaveShopCommodityIDTO.init(ShopCommodityBusinessTypeEnum.SHOP_ALLOCATE_AND_TRANSFER.getCode(), code,
                shopCommodityList, userId, null, null);
        log.warn("调拨调入门店商品信息初始化, 入参 {}", JsonUtil.java2json(init));
        saveShopCommodityClientClient.asyncSaveShopCommodity(init);
//        ShopCommodityTransferInventoryIDTO idto = new ShopCommodityTransferInventoryIDTO();
//        idto.setUserId(userId);
//        idto.setCallInShopId(inShopId);
//        idto.setOutShopId(outShopId);
//        idto.setCommodityIdList(commodityIdList);
//
//        log.warn("调拨调入门店商品信息初始化, 入参 {}", JsonUtil.java2json(idto));
//        shopCommodityClient.initShopCommodityTransferInventory(idto);
        return true;
    }
}
