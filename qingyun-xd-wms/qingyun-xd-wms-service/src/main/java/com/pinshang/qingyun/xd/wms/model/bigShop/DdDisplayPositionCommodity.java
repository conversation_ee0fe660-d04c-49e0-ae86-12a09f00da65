package com.pinshang.qingyun.xd.wms.model.bigShop;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 门店商品绑定陈列位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@TableName("t_dd_display_position_commodity")
public class DdDisplayPositionCommodity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 所属档口
     */
    private Long stallId;

    /**
     * 库区 1排面区 2拣货区 3存储区
     */
    private Integer storageArea;

    /**
     * 陈列位id
     */
    private Long displayPositionId;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 陈列位最小数量
     */
    private BigDecimal minStock;

    /**
     * 陈列位最大数量
     */
    private BigDecimal maxStock;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    private Date updateTime;

}
