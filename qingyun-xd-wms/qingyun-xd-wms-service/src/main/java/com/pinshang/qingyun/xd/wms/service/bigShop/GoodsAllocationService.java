package com.pinshang.qingyun.xd.wms.service.bigShop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.bigShop.*;
import com.pinshang.qingyun.xd.wms.model.bigShop.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.XdSendLogService;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import com.pinshang.qingyun.xd.wms.vo.bigShop.LogDdGoodsAllocationVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 货位管理
 */
@Service
public class GoodsAllocationService extends ServiceImpl<GoodsAllocationMapper, GoodsAllocation> {


    @Autowired
    private GoodsAllocationMapper goodsAllocationMapper;

    @Autowired
    private XdSendLogService xdSendLogService;

    @Autowired
    private GoodsAllocationCommodityService goodsAllocationCommodityService;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private StallMapper stallMapper;

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private GoodsAllocationCommodityMapper goodsAllocationCommodityMapper;

    @Autowired
    private DdTempWarehouseAllocationMapper ddTempWarehouseAllocationMapper;

    /**
     * 添加货位
     * @param dto
     * @return
     */
    @Transactional
    public Boolean add(GoodsAllocationDTO dto) {
        dto.check();

        LambdaQueryWrapper<GoodsAllocation> query = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getShopId, dto.getShopId())
                        .eq(GoodsAllocation::getGoodsAllocationCode, dto.getGoodsAllocationCode());
        Integer num = goodsAllocationMapper.selectCount(query);
        QYAssert.isTrue(num == 0 , "货位号已存在");

        Area area = areaMapper.selectById(dto.getAreaId());
        QYAssert.isTrue(null != area, "区域不存在");

        GoodsAllocation goodsAllocation = BeanCloneUtils.copyTo(dto, GoodsAllocation.class);

        //只有拣货区才需要排序
        if (StorageAreaEnum.PICKING_AREA.getCode().equals(dto.getStorageArea())) {
            Integer maxSortNum = goodsAllocationMapper.getMaxSortNum(dto.getShopId());
            if (null == maxSortNum) {
                goodsAllocation.setSortNum(1);
            } else {
                goodsAllocation.setSortNum(maxSortNum + 1);
            }
        }

        goodsAllocation.setStatus(StatusEnum.ENABLE.getCode());

        goodsAllocationMapper.insert(goodsAllocation);

        //保存日志
        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        saveLog(Arrays.asList(goodsAllocation.getId()), 1, tokenInfo);
        return Boolean.TRUE;
    }

    /**
     * 启用或者停用
     * @param id
     * @param status
     * @return
     */
    @Transactional
    public Boolean startOrStop(Long id, Integer status) {

        GoodsAllocation goodsAllocation = goodsAllocationMapper.selectById(id);
        QYAssert.isTrue(null != goodsAllocation, "货位不存在");

        QYAssert.isTrue(!Objects.equals(goodsAllocation.getStatus(), status), "货位状态不对，请刷新");

        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        Integer type = null;
        //启用
        if (StatusEnum.ENABLE.getCode().equals(status)) {
            type = 2;
        } else if (StatusEnum.DISABLE.getCode().equals(status)) {
            //停用 需要校验库存
            List<GoodsAllocationCommodity> list = goodsAllocationCommodityService.getGoodsAllocationList(tokenInfo.getShopId(), Arrays.asList(id));
            if (SpringUtil.isNotEmpty(list)) {
                List<GoodsAllocationCommodity> stockList = list.stream().filter(e -> null != e.getStock() && e.getStock().compareTo(BigDecimal.ZERO) != 0 ).collect(Collectors.toList());
                QYAssert.isTrue(SpringUtil.isEmpty(stockList), "此货位的库存不为0，不可停用");

                //删除绑定记录
                List<Long> longs = list.stream().map(GoodsAllocationCommodity::getId).collect(Collectors.toList());
                goodsAllocationCommodityService.removeByIds(longs);

                //拣货位和商品绑定记录删除需要记录日志
                List<GoodsAllocationCommodity> deleteDate = list.stream().filter(e -> StorageAreaEnum.PICKING_AREA.getCode().equals(e.getStorageArea())).collect(Collectors.toList());
                if (SpringUtil.isNotEmpty(deleteDate)) {
                    goodsAllocationCommodityService.saveLog(deleteDate, 3, tokenInfo);
                }
            }
            LambdaQueryWrapper<DdTempWarehouseAllocation> ddTempWrapper = new LambdaQueryWrapper<>();
            ddTempWrapper.eq(DdTempWarehouseAllocation::getGoodsAllocationId, goodsAllocation.getId());
            List<DdTempWarehouseAllocation> ddTempWarehouseAllocations= ddTempWarehouseAllocationMapper.selectList(ddTempWrapper);
            if(CollectionUtils.isNotEmpty(ddTempWarehouseAllocations)){
                throw new BizLogicException(ddTempWarehouseAllocations.get(0).getGoodsAllocationCode()+"货位已被绑定业务类型"+ TempOperationTypeEnum.getNameByCode(ddTempWarehouseAllocations.get(0).getTempOperationType())+"的默认货位");
            }

            type = 3;
        }

        goodsAllocation.setStatus(status);
        goodsAllocation.setUpdateId(tokenInfo.getUserId());
        goodsAllocation.setUpdateTime(new Date());
        updateById(goodsAllocation);

        //记录货位启用/停用日志
        saveLog(Arrays.asList(id), type, tokenInfo);

        return Boolean.TRUE;
    }

    /**
     * 分配档口
     */
    @Transactional
    public Boolean distributionStall(Long goodsAllocationId, Long stallId) {

        GoodsAllocation goodsAllocation = goodsAllocationMapper.selectById(goodsAllocationId);
        QYAssert.isTrue(null != goodsAllocation, "货位信息不存在");

        QYAssert.isTrue(StatusEnum.ENABLE.getCode().equals(goodsAllocation.getStatus()), "货位号已停用");

        QYAssert.isTrue(!stallId.equals(goodsAllocation.getStallId()), "此货位当前已分配档口等于所选档口");

        Stall stall = stallMapper.selectById(stallId);
        QYAssert.isTrue(null != stall, "所选档口信息不存在");
        QYAssert.isTrue(stall.getShopId().equals(goodsAllocation.getShopId()), "所属档口和货位号不在同一个门店");

        LambdaQueryWrapper<DdTempWarehouseAllocation> ddTempWrapper = new LambdaQueryWrapper<>();
        ddTempWrapper.eq(DdTempWarehouseAllocation::getGoodsAllocationId, goodsAllocationId);
        List<DdTempWarehouseAllocation> ddTempWarehouseAllocations= ddTempWarehouseAllocationMapper.selectList(ddTempWrapper);
        if(CollectionUtils.isNotEmpty(ddTempWarehouseAllocations)){
            throw new BizLogicException("此货位已被绑定业务类型"+ TempOperationTypeEnum.getNameByCode(ddTempWarehouseAllocations.get(0).getTempOperationType())+"的默认货位，不可修改所属档口");
        }

        List<GoodsAllocationCommodity> list = goodsAllocationCommodityService.getGoodsAllocationList(goodsAllocation.getShopId(), Arrays.asList(goodsAllocation.getId()));
        if (StorageAreaEnum.PICKING_AREA.getCode().equals(goodsAllocation.getStorageArea())) {
            //拣货位判断是否绑定了商品
            QYAssert.isTrue(SpringUtil.isEmpty(list), "货位是拣货位且已绑定商品，请先解绑");
        } else if (StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(goodsAllocation.getStorageArea()) || StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(goodsAllocation.getStorageArea())) {
            //存储位判断库存是否为0
            BigDecimal stock = list.stream().map(GoodsAllocationCommodity::getStock).reduce(BigDecimal.ZERO, BigDecimal::add);
            QYAssert.isTrue(stock.compareTo(BigDecimal.ZERO) == 0, "此货位库存不为0，不可变更所属档口");
        }
        goodsAllocation.setStallId(stallId);
        goodsAllocationMapper.updateById(goodsAllocation);

        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        goodsAllocationCommodityMapper.updateStallId(tokenInfo.getShopId(), goodsAllocation.getId(), stallId, tokenInfo.getUserId());

        //保存日志
        saveLog(Arrays.asList(goodsAllocationId), 4, tokenInfo);
        return Boolean.TRUE;
    }

    /**
     * 门店货位列表
     * @param dto
     * @return
     */
    public MPage<GoodsAllocationPageDTO> page(GoodsAllocationPageIDTO dto) {
        return goodsAllocationMapper.page(dto);
    }

    /**
     * 门店拣货路径列表
     * @param dto
     * @return
     */
    public MPage<PickPathDTO> pathPage(PickPathIDTO dto) {
        return goodsAllocationMapper.pathPage(dto);
    }

    /**
     * 导入货位
     * @param wb
     */
    @Transactional(rollbackFor = Exception.class)
    public ExcelResult importGoodsAllocation(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        int totalRow = sheet.getLastRowNum() + 1;
        QYAssert.isTrue(totalRow <= 1001, "每次最多导入1000行");

        Pattern pattern = Pattern.compile("^[a-zA-Z0-9-]+$");

        GoodsAllocationImportDTO dto = null;
        List<GoodsAllocationImportDTO> list = new ArrayList<>();
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货位号*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "所属库区*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 2, "区域编号*"), "模板不正确");
            }

            if (rowNum > 0) {

                if (row.getCell(0) == null || row.getCell(1) == null || row.getCell(2) == null
                || "".equals(row.getCell(0).getStringCellValue().trim()) || "".equals(row.getCell(1).getStringCellValue().trim())
                || "".equals(row.getCell(2).getStringCellValue().trim()) ) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }
                try {
                    String goodsAllocationCode = row.getCell(0).getStringCellValue().trim();
                    if (goodsAllocationCode.length() > 20) {
                        errorList.add(goodsAllocationCode + "货位号最长20个字");
                        continue;
                    }

                    if (!goodsAllocationCode.matches("^[A-Z].*")) {
                        errorList.add(goodsAllocationCode + "货位号必须大写字母开头");
                        continue;
                    }

                    if (!pattern.matcher(goodsAllocationCode).matches()) {
                        errorList.add(goodsAllocationCode + "货位号仅支持字母数字及中划线");
                        continue;
                    }

                    String storageAreaName = row.getCell(1).getStringCellValue().trim();
                    if (!storageAreaName.equals(StorageAreaEnum.PICKING_AREA.getName()) && !storageAreaName.equals(StorageAreaEnum.WAREHOUSE_AREA.getName())
                            && !storageAreaName.equals(StorageAreaEnum.PROVISIONAL_AREA.getName())) {
                        errorList.add("所属库区"+storageAreaName+"不存在");
                        continue;
                    }

                    String areaCode = row.getCell(2).getStringCellValue().trim();
                    dto = new GoodsAllocationImportDTO();
                    dto.setGoodsAllocationCode(goodsAllocationCode);
                    dto.setStorageAreaName(storageAreaName);
                    dto.setAreaCode(areaCode);
                    list.add(dto);

                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }

            }

        }

        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();

        if (SpringUtil.isEmpty(errorList)) {
            QYAssert.isTrue(SpringUtil.isNotEmpty(list), "导入数据不能为空");

            //验证货位号是否重复
            List<String> goodsAllocationCodeList = list.stream().map(GoodsAllocationImportDTO::getGoodsAllocationCode).collect(Collectors.toList());
            LambdaQueryWrapper<GoodsAllocation> query = new LambdaQueryWrapper<GoodsAllocation>()
                    .eq(GoodsAllocation::getShopId, tokenInfo.getShopId())
                    .in(GoodsAllocation::getGoodsAllocationCode, goodsAllocationCodeList);
            List<GoodsAllocation> goodsAllocationList = goodsAllocationMapper.selectList(query);
            if (SpringUtil.isNotEmpty(goodsAllocationList)) {
                for (GoodsAllocation goodsAllocation : goodsAllocationList) {
                    errorList.add(goodsAllocation.getGoodsAllocationCode() + "货位号已存在");
                }
            }

            //验证区域编码是否正确
            List<String> areaCodeList = list.stream().map(GoodsAllocationImportDTO::getAreaCode).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<Area> areaLambdaQueryWrapper = new LambdaQueryWrapper<Area>()
                    .eq(Area::getShopId, tokenInfo.getShopId())
                    .in(Area::getAreaCode, areaCodeList);
            List<Area> areaList = areaMapper.selectList(areaLambdaQueryWrapper);
            List<String> exitAreaCode = areaList.stream().map(Area::getAreaCode).collect(Collectors.toList());
            areaCodeList.removeAll(exitAreaCode);
            if (SpringUtil.isNotEmpty(areaCodeList)) {
                areaCodeList.forEach(e -> {
                    errorList.add(e + "区域编号不存在");
                });
            }

            //验证重复数据
            List<String> duplicates = goodsAllocationCodeList.stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                    .entrySet()
                    .stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            if (SpringUtil.isNotEmpty(duplicates)) {
                duplicates.forEach(e -> {
                    errorList.add(e + "货位号存在重复");
                });
            }

            //没有错误信息，添加数据
            if (SpringUtil.isEmpty(errorList)) {

                Integer maxSortNum = goodsAllocationMapper.getMaxSortNum(tokenInfo.getShopId());
                if (null == maxSortNum) {
                    maxSortNum = 1;
                } else {
                    maxSortNum++;
                }

                Map<String, Long> map = areaList.stream().collect(Collectors.toMap(Area::getAreaCode, Area::getId));

                List<GoodsAllocation> goodsAllocations = new ArrayList<>();
                GoodsAllocation goodsAllocation = null;
                for (GoodsAllocationImportDTO importDto : list) {
                    goodsAllocation = new GoodsAllocation();
                    goodsAllocation.setShopId(tokenInfo.getShopId());
                    goodsAllocation.setGoodsAllocationCode(importDto.getGoodsAllocationCode());
                    goodsAllocation.setAreaId(map.get(importDto.getAreaCode()));
                    goodsAllocation.setStorageArea(StorageAreaEnum.getCodeByName(importDto.getStorageAreaName()));
                    //只有拣货区才需要排序
                    if (StorageAreaEnum.PICKING_AREA.getCode().equals(goodsAllocation.getStorageArea())) {
                        goodsAllocation.setSortNum(maxSortNum);
                        maxSortNum++;
                    }
                    goodsAllocation.setStatus(StatusEnum.ENABLE.getCode());
                    goodsAllocations.add(goodsAllocation);
                }
                saveBatch(goodsAllocations, 1000);

                //保存日志
                List<Long> ids = goodsAllocations.stream().map(GoodsAllocation::getId).collect(Collectors.toList());
                saveLog(ids, 5, tokenInfo);
            }

        }

        return new ExcelResult(errorList, null);
    }

    /**
     * 批量分配档口
     * @param wb
     * @return
     */
    @Transactional
    public ExcelResult batchDistributionStall(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        int totalRow = sheet.getLastRowNum() + 1;
        QYAssert.isTrue(totalRow <= 501, "每次最多导入500行");

        List<BatchDistributionStallDTO> batchDistributionStallDTOS = new ArrayList<>();
        BatchDistributionStallDTO batchDistributionStallDTO = null;
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货位号*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "档口编码*"), "模板不正确");
            }
            if (rowNum > 0) {
                if (row.getCell(0) == null || row.getCell(1) == null || "".equals(row.getCell(0).getStringCellValue().trim())
                || "".equals(row.getCell(1).getStringCellValue().trim())) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }

                try {
                    batchDistributionStallDTO = new BatchDistributionStallDTO();
                    batchDistributionStallDTO.setGoodsAllocationCode(row.getCell(0).getStringCellValue().trim());
                    batchDistributionStallDTO.setStallCode(row.getCell(1).getStringCellValue().trim());
                    batchDistributionStallDTOS.add(batchDistributionStallDTO);
                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }
            }
        }

        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        List<String> goodsAllocationCodeList = batchDistributionStallDTOS.stream().map(BatchDistributionStallDTO::getGoodsAllocationCode).collect(Collectors.toList());
        //判断重复的货位号
        List<String> duplicates = goodsAllocationCodeList.stream().filter(i -> Collections.frequency(goodsAllocationCodeList, i) > 1)
                .distinct()
                .collect(Collectors.toList());
        if (SpringUtil.isNotEmpty(duplicates)) {
            duplicates.forEach(e -> {
                errorList.add("货位号"+e+"重复");
            });
        }

        //不存在格式问题，继续判断数据
        if (SpringUtil.isEmpty(errorList)) {

            QYAssert.isTrue(SpringUtil.isNotEmpty(batchDistributionStallDTOS), "导入数据不能为空");

            //判断不存在的货位
            List<GoodsAllocation> goodsAllocationList = goodsAllocationByCodesList(goodsAllocationCodeList, tokenInfo.getShopId());
            List<String> exitGoodsAllocations = goodsAllocationList.stream().map(GoodsAllocation::getGoodsAllocationCode).distinct().collect(Collectors.toList());
            goodsAllocationCodeList.removeAll(exitGoodsAllocations);
            if (SpringUtil.isNotEmpty(goodsAllocationCodeList)) {
                goodsAllocationCodeList.forEach(e -> {
                    errorList.add(e + "货位号不存在");
                });
            }
            if(CollectionUtils.isNotEmpty(exitGoodsAllocations)){
                LambdaQueryWrapper<DdTempWarehouseAllocation> ddTempWrapper = new LambdaQueryWrapper<>();
                ddTempWrapper.in(DdTempWarehouseAllocation::getGoodsAllocationCode, exitGoodsAllocations);
                List<DdTempWarehouseAllocation> ddTempWarehouseAllocations= ddTempWarehouseAllocationMapper.selectList(ddTempWrapper);
                if(CollectionUtils.isNotEmpty(ddTempWarehouseAllocations)){
                    ddTempWarehouseAllocations.forEach(d -> errorList.add(d.getGoodsAllocationCode() + "此货位已被绑定业务类型"+ TempOperationTypeEnum.getNameByCode(d.getTempOperationType())+"的默认货位"));
                }
            }

            if (SpringUtil.isNotEmpty(goodsAllocationList)) {

                //判断货位库存
                List<Long> goodsAllocationIds = goodsAllocationList.stream().map(GoodsAllocation::getId).collect(Collectors.toList());
                List<GoodsAllocationCommodity> getGoodsAllocationList = goodsAllocationCommodityService.getGoodsAllocationList(tokenInfo.getShopId(), goodsAllocationIds);
                Map<Long, BigDecimal> map = getGoodsAllocationList.stream().collect(Collectors.groupingBy(GoodsAllocationCommodity::getGoodsAllocationId, Collectors.reducing(BigDecimal.ZERO, GoodsAllocationCommodity::getStock, BigDecimal::add)));

                for (GoodsAllocation goodsAllocation : goodsAllocationList) {
                    //拣货位是否绑定商品
                    if (StorageAreaEnum.PICKING_AREA.getCode().equals(goodsAllocation.getStorageArea()) && map.containsKey(goodsAllocation.getId()) ) {
                        errorList.add(goodsAllocation.getGoodsAllocationCode() + "拣货位且已绑定商品");
                    } else if ((StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(goodsAllocation.getStorageArea())
                            || StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(goodsAllocation.getStorageArea()))
                            && map.containsKey(goodsAllocation.getId())
                            && map.get(goodsAllocation.getId()).compareTo(BigDecimal.ZERO) > 0) {
                        //存储位库存是否为零
                        errorList.add(goodsAllocation.getGoodsAllocationCode() + "货位的库存不为0");
                    }
                }

                List<String> stallCode = batchDistributionStallDTOS.stream().map(BatchDistributionStallDTO::getStallCode).collect(Collectors.toList());
                LambdaQueryWrapper<Stall> query = new LambdaQueryWrapper<Stall>()
                        .eq(Stall::getShopId, tokenInfo.getShopId())
                        .in(Stall::getStallCode, stallCode);
                List<Stall> stallList = stallMapper.selectList(query);
                List<String> exitStallCode = stallList.stream().map(Stall::getStallCode).collect(Collectors.toList());
                stallCode.removeAll(exitStallCode);
                stallCode.forEach(e -> {
                    errorList.add(e + "档口编码不存在");
                });

                if (SpringUtil.isEmpty(errorList)) {

                    Map<String, Long> goodsAllocationMap = goodsAllocationList.stream().collect(Collectors.toMap(GoodsAllocation::getGoodsAllocationCode, GoodsAllocation::getId));

                    Map<String, Long> stallMap = stallList.stream().collect(Collectors.toMap(Stall::getStallCode, Stall::getId));
                    List<GoodsAllocation> updateData = new ArrayList<>();
                    GoodsAllocation update = null;

                    for (BatchDistributionStallDTO distributionStallDTO : batchDistributionStallDTOS) {
                        update = new GoodsAllocation();
                        update.setId(goodsAllocationMap.get(distributionStallDTO.getGoodsAllocationCode()));
                        update.setStallId(stallMap.get(distributionStallDTO.getStallCode()));
                        updateData.add(update);
                    }
                    updateBatchById(updateData, 500);

                    //保存日志
                    List<Long> ids = goodsAllocationList.stream().map(GoodsAllocation::getId).collect(Collectors.toList());
                    saveLog(ids, 6, tokenInfo);
                }

            }

        }
        return new ExcelResult(errorList, null);
    }

    /**
     * 导入拣货路径
     * @param wb
     * @return
     */
    @Transactional
    public ExcelResult importPickPath(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        List<String> errorList = new ArrayList<>();

        List<String> importData = new ArrayList<>();

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "货位号*"), "模板不正确");
            }

            if (rowNum > 0) {
                if (row.getCell(0) == null || "".equals(row.getCell(0).getStringCellValue().trim())) {
                    errorList.add((rowNum + 1) + "行未填写完整");
                    continue;
                }

                try {
                    importData.add(row.getCell(0).getStringCellValue().trim());
                } catch (Exception e) {
                    errorList.add((rowNum + 1) + "行数据格式不正确");
                    continue;
                }
            }
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        if (SpringUtil.isEmpty(errorList)) {

            QYAssert.isTrue(SpringUtil.isNotEmpty(importData), "导入数据不能为空");

            LambdaQueryWrapper<GoodsAllocation> query = new LambdaQueryWrapper<GoodsAllocation>()
                    .eq(GoodsAllocation::getShopId, tokenInfo.getShopId())
                    .eq(GoodsAllocation::getStorageArea, StorageAreaEnum.PICKING_AREA.getCode());
            query.orderByAsc(GoodsAllocation::getSortNum);
            List<GoodsAllocation> goodsAllocations = goodsAllocationMapper.selectList(query);
            Map<String, GoodsAllocation> goodsAllocationMap = goodsAllocations.stream().collect(Collectors.toMap(GoodsAllocation::getGoodsAllocationCode, e -> e));

            List<GoodsAllocation> updateData = new ArrayList<>();
            GoodsAllocation goodsAllocation = null;
            Integer sortNum = 1;
            Date date = new Date();

            Map<String, Integer> repeatCount = new HashMap<>();

            //导入的货位号排在前面
            for (String goodsAllocationCode : importData) {

                //重复的货位号 只提醒一次
                if (repeatCount.containsKey(goodsAllocationCode)) {
                    if (1 == repeatCount.get(goodsAllocationCode)) {
                        errorList.add(goodsAllocationCode + "货位号重复");
                    }
                    repeatCount.put(goodsAllocationCode, (repeatCount.get(goodsAllocationCode) + 1));
                    continue;
                }

                if (!goodsAllocationMap.containsKey(goodsAllocationCode)) {
                    errorList.add(goodsAllocationCode + "拣货位不存在");
                    continue;
                }

                goodsAllocation = new GoodsAllocation();
                goodsAllocation.setId(goodsAllocationMap.get(goodsAllocationCode).getId());
                goodsAllocation.setSortNum(sortNum);
                goodsAllocation.setUpdateId(tokenInfo.getUserId());
                goodsAllocation.setUpdateTime(date);
                updateData.add(goodsAllocation);
                sortNum++;
                goodsAllocationMap.remove(goodsAllocationCode);
                repeatCount.put(goodsAllocationCode, 1);
            }
            //剩下的拣货位号依次往后排
            if (!goodsAllocationMap.isEmpty()) {
                List<GoodsAllocation> goodsAllocationList = goodsAllocationMap.values().stream().collect(Collectors.toList());
                goodsAllocationList.sort(Comparator.comparing(GoodsAllocation::getSortNum));

                for (GoodsAllocation goodsAllocation1 : goodsAllocationList) {
                    goodsAllocation = new GoodsAllocation();
                    goodsAllocation.setId(goodsAllocation1.getId());
                    goodsAllocation.setSortNum(sortNum);
                    goodsAllocation.setUpdateId(tokenInfo.getUserId());
                    goodsAllocation.setUpdateTime(date);
                    updateData.add(goodsAllocation);
                    sortNum++;
                }
            }

            if (SpringUtil.isEmpty(errorList) && SpringUtil.isNotEmpty(updateData)) {
                updateBatchById(updateData);
            }

        }

        return new ExcelResult(errorList, null);
    }

    public List<GoodsAllocation> goodsAllocationByCodesList(List<String> codes, Long shopId) {
        LambdaQueryWrapper<GoodsAllocation> query = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getShopId, shopId)
                .in(GoodsAllocation::getGoodsAllocationCode, codes);
        return goodsAllocationMapper.selectList(query);
    }




    public void saveLog(List<Long> ids, Integer type, TokenInfo tokenInfo) {
        //1新增 2启用  3停用 4分配档口 5批量导入 6批量分配
        List<GoodsAllocation> goodsAllocations = goodsAllocationMapper.selectBatchIds(ids);
        //查询档口信息
        List<Long> stallIds = goodsAllocations.stream().map(GoodsAllocation::getStallId).distinct().collect(Collectors.toList());
        List<Stall> stallList = stallMapper.selectBatchIds(stallIds);
        Map<Long, String> stringMap = stallList.stream().collect(Collectors.toMap(Stall::getId, Stall::getStallName));

        List<LogDdGoodsAllocationVO> voList = new ArrayList<>();
        LogDdGoodsAllocationVO vo = null;
        for (GoodsAllocation goodsAllocation : goodsAllocations) {
            vo = new LogDdGoodsAllocationVO();
            vo.setShopId(goodsAllocation.getShopId());
            vo.setType(type);
            vo.setGoodsAllocationId(goodsAllocation.getId());
            vo.setGoodsAllocationCode(goodsAllocation.getGoodsAllocationCode());
            vo.setStorageArea(goodsAllocation.getStorageArea());
            vo.setStallId(goodsAllocation.getStallId());

            if (null != goodsAllocation.getStallId()) {
                vo.setStallName(stringMap.get(goodsAllocation.getStallId()));
            }
            vo.setStatus(goodsAllocation.getStatus());
            vo.setCreateId(tokenInfo.getUserId());
            vo.setCreateCode(tokenInfo.getEmployeeNumber());
            vo.setCreateName(tokenInfo.getRealName());
            vo.setCreateTime(DateUtil.get4yMdHms(new Date()));
            voList.add(vo);
        }

        xdSendLogService.sendLog(voList, "t_log_dd_goods_allocation");
    }


    public GoodsAllocation getGoodsAllocationIdByCode(Long shopId, Long stallId, String goodsAllocationCode) {
        LambdaQueryWrapper<GoodsAllocation> query = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getShopId, shopId)
                .eq(GoodsAllocation::getStallId, stallId)
                .eq(GoodsAllocation::getGoodsAllocationCode, goodsAllocationCode);
        return goodsAllocationMapper.selectOne(query);
    }

    public List<GoodsAllocation> listByGoodAllocationIds(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GoodsAllocation::getId,ids);
        return goodsAllocationMapper.selectList(queryWrapper);
    }

    public List<GoodsAllocation> listByGoodAllocationCodesAndShopId(List<String> codes,Long shopId){
        if (CollectionUtils.isEmpty(codes) || Objects.isNull(shopId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GoodsAllocation::getGoodsAllocationCode,codes);
        queryWrapper.eq(GoodsAllocation::getShopId,shopId);
        return goodsAllocationMapper.selectList(queryWrapper);
    }


    /**
     * PDA 查询可选货位
     *
     * @param req 搜索条件
     * @return 可选货位
     */
    public List<GoodsAllocationEnableListODTO> queryGoodsAllocationPdaList(GoodsAllocationListIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getStallId(), "档口id不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品id不能为空");
        List<Integer> storageAreaList = req.getStorageAreaList();
        QYAssert.notEmpty(storageAreaList, "库区不能为空");

        List<GoodsAllocationEnableListODTO> list = getGoodsAllocationEnableListODTOS(req, storageAreaList);

        QYAssert.notEmpty(list, "未查询到货位");

        // 按照入参 req.getStorageAreaList() 的顺序排序
        list.sort(Comparator.comparing(item -> storageAreaList.indexOf(item.getStorageArea())));
        return list;
    }

    @NotNull
    private List<GoodsAllocationEnableListODTO> getGoodsAllocationEnableListODTOS(GoodsAllocationListIDTO req, List<Integer> storageAreaList) {
        List<GoodsAllocationEnableListODTO> list = new ArrayList<>();

        // 根据单个档口商品 查询库存
        List<StallCommodityStock> stallCommodityStocks = stallCommodityStockService.queryStallCommodityStock(req.getShopId(), req.getStallId(),
                Collections.singletonList(req.getCommodityId()));

        if (CollectionUtils.isNotEmpty(stallCommodityStocks)) {
            // 获取商品绑定的排面区，拣货区 库存
            addStockToList(stallCommodityStocks.get(0), storageAreaList, list);
        }

        // 筛选出需要查询的多货位库区类型
        List<Integer> multiAllocationAreas = Stream.of(StorageAreaEnum.WAREHOUSE_AREA.getCode(), StorageAreaEnum.PROVISIONAL_AREA.getCode())
                .filter(storageAreaList::contains)
                .collect(Collectors.toList());

        // 查询多货位库区的货位和库存
        if (CollectionUtils.isNotEmpty(multiAllocationAreas)) {
            addMultiAllocationStockToList(req, multiAllocationAreas, list);
        }
        return list;
    }

    /**
     * 品鲜 查询货位列表
     *
     * @param req
     * @return
     */
    public List<GoodsAllocationListODTO> queryGoodsAllocationWebList(GoodsAllocationListIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getStallId(), "档口id不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品id不能为空");
        List<GoodsAllocationListODTO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(req.getStorageAreaList())) {
            List<GoodsAllocationEnableListODTO> listArea = getGoodsAllocationEnableListODTOS(req, req.getStorageAreaList());
            for (GoodsAllocationEnableListODTO goodsAllocationEnableListODTO : listArea) {
                List<GoodsAllocationListODTO> goodsAllocationListODTOS = BeanCloneUtils.copyTo(goodsAllocationEnableListODTO.getItems(), GoodsAllocationListODTO.class);
                list.addAll(goodsAllocationListODTOS);
            }
        } else {
            if (StorageAreaEnum.SHELF_AREA.getCode().equals(req.getStorageArea())) {
                return new ArrayList<>();
            } else if (StorageAreaEnum.PICKING_AREA.getCode().equals(req.getStorageArea())) {
                //获取商品绑定的拣货位
                getPickShelf(req, list);
            } else if (StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(req.getStorageArea()) ||
                    (StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(req.getStorageArea()))) {
                //查询所属档口的存储位
                getStoreShelf(req, list);
            }
        }
        return list;
    }

    private void addStockToList(StallCommodityStock stallCommodityStock, List<Integer> storageAreaList, List<GoodsAllocationEnableListODTO> list) {
        BigDecimal stockQuantity = stallCommodityStock.getStockQuantity() != null ? stallCommodityStock.getStockQuantity() : BigDecimal.ZERO;
        BigDecimal pickingAreaStock = stallCommodityStock.getPickingAreaStock() != null ? stallCommodityStock.getPickingAreaStock() : BigDecimal.ZERO;

        addGoodsAllocationToList(StorageAreaEnum.SHELF_AREA.getCode(), stockQuantity, stallCommodityStock, storageAreaList, list);
        addGoodsAllocationToList(StorageAreaEnum.PICKING_AREA.getCode(), pickingAreaStock, stallCommodityStock, storageAreaList, list);
    }

    private void addGoodsAllocationToList(Integer storageArea, BigDecimal stockQuantity, StallCommodityStock req, List<Integer> storageAreaList, List<GoodsAllocationEnableListODTO> list) {
        if (CollectionUtils.isEmpty(storageAreaList) || !storageAreaList.contains(storageArea)) {
            return;
        }
        GoodsAllocationEnableListODTO allocationListODTO = new GoodsAllocationEnableListODTO();
        allocationListODTO.setStorageArea(storageArea);
        GoodsAllocationItemListODTO item = new GoodsAllocationItemListODTO();
        item.setStock(stockQuantity.stripTrailingZeros().toPlainString());
        item.setStorageArea(storageArea);

        // 如果是拣货位，先处理货位号逻辑
        if (Objects.equals(StorageAreaEnum.PICKING_AREA.getCode(), storageArea)) {
            GoodsAllocationCommodity pickingGoodsAllocationCommodity = goodsAllocationCommodityService
                    .getPickingGoodsAllocationCommodity(req.getShopId(), req.getStallId(), req.getCommodityId());

            // 如果拣货位信息为空，则直接添加一个空集合，返回
            if (Objects.isNull(pickingGoodsAllocationCommodity)) {
                allocationListODTO.setItems(Collections.emptyList());
            } else {
                // 如果有拣货位信息，则正常设置货位号和货位码
                item.setGoodsAllocationId(pickingGoodsAllocationCommodity.getGoodsAllocationId());
                item.setGoodsAllocationCode(pickingGoodsAllocationCommodity.getGoodsAllocationCode());
                allocationListODTO.setItems(Collections.singletonList(item));
            }
        } else {
            // 非拣货位的逻辑，正常处理库存和添加列表
            allocationListODTO.setItems(Collections.singletonList(item));
        }
        list.add(allocationListODTO);
    }

    private void addMultiAllocationStockToList(GoodsAllocationListIDTO req, List<Integer> areasToQuery, List<GoodsAllocationEnableListODTO> list) {
        if (CollectionUtils.isEmpty(areasToQuery)) {
            return;
        }

        // 一次性查询所有符合条件的货位
        List<GoodsAllocation> goodsAllocations = goodsAllocationMapper.selectList(
                new LambdaQueryWrapper<GoodsAllocation>()
                        .eq(GoodsAllocation::getShopId, req.getShopId())
                        .eq(GoodsAllocation::getStallId, req.getStallId())
                        .in(GoodsAllocation::getStorageArea, areasToQuery)
                        .eq(GoodsAllocation::getStatus, StatusEnum.ENABLE.getCode())
        );
        if (CollectionUtils.isEmpty(goodsAllocations)) {
            return;
        }

        // 一次性查询所有符合条件的商品库存
        List<GoodsAllocationCommodity> commodityList = goodsAllocationCommodityService.listByStorageAreas(
                req.getShopId(), req.getStallId(), req.getCommodityId(), areasToQuery);

        Map<Long, GoodsAllocationCommodity> commodityMap = commodityList.stream()
                .collect(Collectors.toMap(GoodsAllocationCommodity::getGoodsAllocationId, Function.identity(), (v1, v2) -> v1));

        // 按库区类型分组
        Map<Integer, List<GoodsAllocation>> groupedAllocations = goodsAllocations.stream()
                .collect(Collectors.groupingBy(GoodsAllocation::getStorageArea));

        // 为每个库区创建结果
        for (Integer areaCode : areasToQuery) {
            List<GoodsAllocation> areaAllocations = groupedAllocations.getOrDefault(areaCode, Collections.emptyList());
            if (CollectionUtils.isEmpty(areaAllocations)) {
                continue;
            }

            GoodsAllocationEnableListODTO odto = new GoodsAllocationEnableListODTO();
            odto.setStorageArea(areaCode);
            List<GoodsAllocationItemListODTO> items = new ArrayList<>();

            for (GoodsAllocation goodsAllocation : areaAllocations) {
                GoodsAllocationItemListODTO item = createGoodsAllocationItem(goodsAllocation, commodityMap);
                item.setStorageArea(areaCode);
                items.add(item);
            }

            odto.setItems(items);
            list.add(odto);
        }
    }

    private GoodsAllocationItemListODTO createGoodsAllocationItem(GoodsAllocation goodsAllocation,
                                                                  Map<Long, GoodsAllocationCommodity> commodityMap) {
        GoodsAllocationItemListODTO item = new GoodsAllocationItemListODTO();
        item.setGoodsAllocationId(goodsAllocation.getId());
        item.setGoodsAllocationCode(goodsAllocation.getGoodsAllocationCode());

        GoodsAllocationCommodity goodsAllocationCommodity = commodityMap.get(goodsAllocation.getId());
        BigDecimal stock = Objects.nonNull(goodsAllocationCommodity)
                ? goodsAllocationCommodity.getStock()
                : BigDecimal.ZERO;
        item.setStock(stock.stripTrailingZeros().toPlainString());
        item.setStorageArea(StorageAreaEnum.WAREHOUSE_AREA.getCode());
        return item;
    }

    /**
     * 查询商品存储位
     * @param req
     * @param list
     */
    private void getStoreShelf(GoodsAllocationListIDTO req, List<GoodsAllocationListODTO> list) {
        LambdaQueryWrapper<GoodsAllocation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GoodsAllocation::getShopId, req.getShopId())
                .eq(GoodsAllocation::getStallId, req.getStallId())
                .eq(GoodsAllocation::getStorageArea, req.getStorageArea())
                .eq(GoodsAllocation::getStatus, StatusEnum.ENABLE.getCode());
        List<GoodsAllocation> goodsAllocations = goodsAllocationMapper.selectList(wrapper);
        List<GoodsAllocationCommodity> commodityList = goodsAllocationCommodityService.list(req.getShopId(), req.getStallId(), req.getCommodityId(), req.getStorageArea());
        Map<Long, GoodsAllocationCommodity> commodityMap = commodityList.stream().collect(Collectors.toMap(GoodsAllocationCommodity::getGoodsAllocationId, Function.identity()));
        for (GoodsAllocation goodsAllocation : goodsAllocations) {
            GoodsAllocationListODTO odto = new GoodsAllocationListODTO();
            odto.setGoodsAllocationId(goodsAllocation.getId());
            odto.setGoodsAllocationCode(goodsAllocation.getGoodsAllocationCode());
            odto.setStorageArea(goodsAllocation.getStorageArea());
            // 获取库存，若不存在则设为 "0"
            GoodsAllocationCommodity goodsAllocationCommodity = commodityMap.get(goodsAllocation.getId());
            BigDecimal stock = Objects.nonNull(goodsAllocationCommodity)
                    ? goodsAllocationCommodity.getStock()
                    : BigDecimal.ZERO;

            odto.setStock(stock.stripTrailingZeros().toPlainString());
            list.add(odto);
        }
    }

    /**
     * 查询商品拣货位
     * @param req
     * @param list
     */
    private void getPickShelf(GoodsAllocationListIDTO req, List<GoodsAllocationListODTO> list) {
        GoodsAllocationCommodity pickingGoodsAllocationCommodity = goodsAllocationCommodityService.getPickingGoodsAllocationCommodity(req.getShopId(), req.getStallId(), req.getCommodityId());
        if (Objects.nonNull(pickingGoodsAllocationCommodity)){
            GoodsAllocationListODTO odto = new GoodsAllocationListODTO();
            odto.setGoodsAllocationId(pickingGoodsAllocationCommodity.getGoodsAllocationId());
            odto.setGoodsAllocationCode(pickingGoodsAllocationCommodity.getGoodsAllocationCode());
            odto.setStorageArea(pickingGoodsAllocationCommodity.getStorageArea());
            odto.setStock(pickingGoodsAllocationCommodity.getStock().stripTrailingZeros().toPlainString());
            list.add(odto);
        }
    }

    public List<GoodsAllocation> queryGoodsAllocationByIdList(List<Long> goodsAllocationIdList) {
        QYAssert.notEmpty(goodsAllocationIdList, "货位id不能为空");

        LambdaQueryWrapper<GoodsAllocation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GoodsAllocation::getId, goodsAllocationIdList);
        return goodsAllocationMapper.selectList(wrapper);
    }

    /**
     * 校验货位号
     */
    public CheckGoodsAllocationIDTO checkGoodsAllocation(CheckGoodsAllocationIDTO req) {
        QYAssert.notNull(req, "参数不能为空");
        QYAssert.notNull(req.getShopId(), "门店id不能为空");
        QYAssert.notNull(req.getStallId(), "档口id不能为空");
        QYAssert.notNull(req.getCommodityId(), "商品id不能为空");
        QYAssert.notNull(req.getGoodsAllocationCode(), "货位号不能为空");
        GoodsAllocation goodsAllocation = this.getGoodsAllocationIdByCode(req.getShopId(), req.getStallId(), req.getGoodsAllocationCode());
        QYAssert.isTrue(Objects.nonNull(goodsAllocation), "货位号不存在或当前档口不可用");

        //存储位校验属于当前档口即可，拣货位校验是否和商品绑定
        if (Objects.equals(goodsAllocation.getStorageArea(), StorageAreaEnum.PICKING_AREA.getCode())) {
            Boolean isValid = goodsAllocationCommodityService.checkGoodsAllocationId(req.getShopId(), req.getStallId(), req.getCommodityId(), goodsAllocation.getId());
            QYAssert.isTrue(isValid, "货位号不存在或当前档口不可用");
        }

        return BeanCloneUtils.copyTo(req, CheckGoodsAllocationIDTO.class);
    }


    public List<GoodAllocationPrintForPDAODTO> printForPDA(Long areaId, Integer storageArea, String goodsAllocationCode){
        LambdaQueryWrapper<GoodsAllocation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(null != areaId){
            lambdaQueryWrapper.eq(GoodsAllocation::getAreaId, areaId);
        }
        if(null != storageArea){
            lambdaQueryWrapper.eq(GoodsAllocation::getStorageArea, storageArea);
        }
        if(StringUtils.isNotEmpty(goodsAllocationCode)){
            lambdaQueryWrapper.like(GoodsAllocation::getGoodsAllocationCode, goodsAllocationCode);
        }
        lambdaQueryWrapper.eq(GoodsAllocation::getStatus, YesOrNoEnums.YES.getCode()).orderByAsc(GoodsAllocation::getGoodsAllocationCode)
                .last("limit 100");
        List<GoodsAllocation> goodsAllocation = goodsAllocationMapper.selectList(lambdaQueryWrapper);
        if(SpringUtil.isEmpty(goodsAllocation)){
            return new ArrayList<>();
        }else{
            return goodsAllocation.stream().map(it ->{
                GoodAllocationPrintForPDAODTO odto = new GoodAllocationPrintForPDAODTO();
                odto.setStorageArea(it.getStorageArea());
                odto.setGoodsAllocationCode(it.getGoodsAllocationCode());
                odto.setStorageAreaStr(StorageAreaEnum.getTypeEnumByCode(it.getStorageArea()).getName());
                return odto;
            }).collect(Collectors.toList());
        }
    }

    public List<GoodsAllocation> queryGoodsAllocation(QueryGoodsAllocationIDTO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (null == dto.getShopId()) {
            dto.setShopId(tokenInfo.getShopId());
        }
        return goodsAllocationMapper.queryGoodsAllocation(dto);
    }

    public List<GoodsAllocation> queryGoodsAllocationDropDownList(GoodsAllocationDropDownIDTO dto) {

        if (StorageAreaEnum.SHELF_AREA.getCode().equals(dto.getStorageArea()) || StorageAreaEnum.PROVISIONAL_AREA.getCode().equals(dto.getStorageArea())) {
            return Collections.emptyList();
        }

        QYAssert.notNull(dto.getShopId(), "门店不能为空");
        QYAssert.notNull(dto.getStallId(), "档口不能为空");

        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getShopId, dto.getShopId())
                .eq(GoodsAllocation::getStallId, dto.getStallId())
                .eq(Objects.nonNull(dto.getStorageArea()), GoodsAllocation::getStorageArea, dto.getStorageArea())
                .like(SpringUtil.hasText(dto.getGoodsAllocationCode()), GoodsAllocation::getGoodsAllocationCode, dto.getGoodsAllocationCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    public List<String> getTempLocationNumbers(Long stallId){
        QYAssert.notNull(stallId, "档口id不能为空");
        LambdaQueryWrapper<GoodsAllocation> queryWrapper = new LambdaQueryWrapper<GoodsAllocation>()
                .eq(GoodsAllocation::getStallId, stallId)
                .eq(GoodsAllocation::getStorageArea, StorageAreaEnum.PROVISIONAL_AREA.getCode());
        return goodsAllocationMapper.selectList(queryWrapper).stream().map(GoodsAllocation::getGoodsAllocationCode).collect(Collectors.toList());
    }
}
