package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DdStockItemODTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/31 18:17
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DdStockItemODTO implements Serializable {
    private static final long serialVersionUID = -4927148250381154599L;
    @ApiModelProperty(value = "商品Id")
    private Long commodityId;
    @Getter
    @ApiModelProperty(value = "份数")
    private Integer stockNumber;
    public Integer getStockNumber() {
        return this.stockNumber == null || this.stockNumber <= 0 ? 0 : this.stockNumber;
    }
}
