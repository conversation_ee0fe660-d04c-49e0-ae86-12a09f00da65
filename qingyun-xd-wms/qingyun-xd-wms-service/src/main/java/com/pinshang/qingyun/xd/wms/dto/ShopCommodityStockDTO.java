package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityStockDTO {

    @ApiModelProperty("门店Id")
    private Long shopId;

    @ApiModelProperty(value = "档口id")
    private Long stallId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "库存数量(大店排面数量)")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "拣货区库存(大店)")
    private BigDecimal pickingAreaStock;
    @ApiModelProperty(value = "存储区库存(大店)")
    private BigDecimal warehouseAreaStock;
    @ApiModelProperty(value = "临时库存(大店)")
    private BigDecimal stockProvisional;
    @ApiModelProperty(value = "冻结库存(大店)")
    private BigDecimal freezeQuantity;
    @ApiModelProperty(value = "预留库存(大店)")
    private BigDecimal reserveStock;

    @ApiModelProperty(value = "冻结份数")
    private Integer freezeNumber;

    @ApiModelProperty(value = "质检份数")
    private Integer qualityNumber;

    @ApiModelProperty(value = "质检数量")
    private BigDecimal qualityQuantity;

    private String commodityCode;

    private String commodityName;

    @ApiModelProperty(value = "包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty(value = "前置仓销售箱规")
    private BigDecimal xdSalesBoxCapacity;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "app状态：0-上架，1-下架")
    private Integer appStatus;

    @ApiModelProperty(value = "加权平均价(大店)")
    private BigDecimal weightPrice;
}
