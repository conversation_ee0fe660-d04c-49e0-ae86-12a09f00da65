package com.pinshang.qingyun.xd.wms.controller.bigShop;


import com.pinshang.qingyun.base.annotations.RepeatSubmitAnno;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdReplenishmentService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


/**
 * <p>
 * 大店补货
 * </p>
 *
 */
@Api(tags = "大店补货", description = "大店补货")
@RestController
@RequestMapping("/bigShop/replenishment")
@RequiredArgsConstructor
public class DdReplenishmentController {

    private final DdReplenishmentService ddReplenishmentService;
    private final RedissonClient redissonClient;
    private final DdTokenShopIdService ddTokenShopIdService;

    /**
     * 大店补货数量（排面/拣货位）
     */
    @PostMapping("/quantity")
    @ApiOperation(value = "大店补货数量（排面/拣货位）")
    public ReplenishmentTaskQuantityVO quantity() {
        return ddReplenishmentService.quantity();
    }

    /**
     * 排面补货/拣货位补货 我的任务/全部任务
     */
    @PostMapping("/query/task")
    @ApiOperation(value = "排面补货/拣货位补货 我的任务/全部任务")
    public ApiResponse<DdReplenishmentTaskRspVO> queryTask(@RequestBody DdReplenishmentTaskReqVO reqVO, HttpServletResponse response) {
        QYAssert.notNull(reqVO.getStallId(),"档口不能为空");
        ddTokenShopIdService.processReadDdTokenShopId(reqVO.getShopId(),reqVO.getStallId());
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        return ApiResponse.convert(ddReplenishmentService.queryTask(reqVO));
    }


    /**
     * 排面补货全部任务 点击商品 / 添加商品 ->确认添加任务
     */
    @PostMapping("/addTaskCommodity")
    @ApiOperation(value = "排面补货全部任务 点击商品 / 添加商品 ->确认添加任务")
    public AddTaskCommodityRspVO addTaskCommodity(@RequestBody AddTaskCommodityReqVO vo) {
        ddTokenShopIdService.processDdTokenShopId(FastThreadLocalUtil.getQY().getShopId(),vo.getStallId());
        return ddReplenishmentService.addTaskCommodity(vo);

    }

    /**
     * 左滑删除 解绑补货人
     */
    @PostMapping("/unbind/{taskId}")
    @ApiOperation(value = "左滑删除 解绑补货人")
    public Boolean unbindTask(@PathVariable("taskId") Long taskId) {

        return ddReplenishmentService.unbindTask(taskId);

    }


    /**
     * 我的任务完成补货
     */
    @PostMapping("/completeReplenishmentAll")
    @ApiOperation(value = "我的任务完成补货")
    public Boolean completeReplenishmentAll(Long stallId) {
        Long userId = FastThreadLocalUtil.getQY().getUserId();

        return ddReplenishmentService.completeReplenishmentAll(stallId,userId);

    }

    /**
     * 我的任务点击商品完成补货 展示信息
     */
    @PostMapping("/completeReplenishmentDetail")
    @ApiOperation(value = "我的任务点击商品完成补货 展示信息")
    @MethodRender
    public CompleteReplenishmentCommodityRspVO completeReplenishmentDetail(@RequestBody CompleteReplenishmentCommodityReqVO vo) {

        return ddReplenishmentService.completeReplenishmentDetail(vo);

    }

    /**
     * 我的任务点击商品完成补货
     */
    @PostMapping("/completeReplenishment/{taskId}")
    @ApiOperation(value = "我的任务点击商品完成补货")
    public Boolean completeReplenishment(@PathVariable("taskId") Long taskId) {

        return ddReplenishmentService.completeReplenishment(taskId);

    }


    /**
     * 我的任务点击商品执行补货 展示信息
     */
    @PostMapping("/executeReplenishmentDetail")
    @ApiOperation(value = "我的任务点击商品执行补货 展示信息")
    @MethodRender
    public ExecuteReplenishmentCommodityRspVO executeReplenishmentDetail(@RequestBody ExecuteReplenishmentCommodityReqVO vo) {

        return ddReplenishmentService.executeReplenishmentDetail(vo);

    }

    /**
     * 我的任务点击商品执行补货
     */
    @PostMapping("/executeReplenishment")
    @ApiOperation(value = "我的任务点击商品执行补货")
    @MethodRender
    public Boolean executeReplenishment(@RequestBody ExecuteReplenishmentCommodityReqVO vo) {

        RLock lock = redissonClient.getLock("xsWms:executeReplenishment");
        if (lock.tryLock()) {
            try {
                return ddReplenishmentService.executeReplenishment(vo);
            } finally {
                lock.unlock();
            }
        } else {
            QYAssert.isFalse("系统繁忙,请勿频繁操作!");
        }

        return Boolean.FALSE;
    }

}
