package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.pinshang.qingyun.xd.order.dto.XdMultipleCommodityApplyReturnIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ApplyCloudReturnOrderIODTO
 * <AUTHOR>
 * @Date 2021/6/24 17:15
 * @Description ApplyCloudReturnOrderIODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudApplyReturnOrderIDTO {
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("退多个商品集合，整单退时不用传(小程序)")
    private List<XdMultipleCommodityApplyReturnIDTO> multipleCommodity;
}
