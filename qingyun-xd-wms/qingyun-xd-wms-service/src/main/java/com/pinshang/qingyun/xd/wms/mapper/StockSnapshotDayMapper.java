package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayIDTO;
import com.pinshang.qingyun.xd.wms.model.StockSnapshotDay;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface StockSnapshotDayMapper extends BaseMapper<StockSnapshotDay> {

    /**
     *门店库存快照列表
     * @param dto
     * @return
     */
    List<StockSnapshotDayDTO> stockList(@Param("dto") StockSnapshotDayIDTO dto);

    Integer deleteByStockDate(@Param("date") String date);

}
