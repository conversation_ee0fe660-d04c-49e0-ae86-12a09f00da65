package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/12
 * @Version 1.0
 */
@Data
public class StallCommodityStockPageODTO {
    @ExcelIgnore
    private Long shopId;

    @ApiModelProperty("部门")
    @ExcelProperty("部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String deptName;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP, fieldName = RenderFieldHelper.Shop.shopName, keyName = "shopId")
    private String shopName;

    @ExcelIgnore
    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ExcelProperty("档口")
    @ApiModelProperty("档口")
    private String stallName;

    @ExcelIgnore
    @ApiModelProperty("档口Id")
    private Long stallId;

    @ExcelProperty("库区")
    @ApiModelProperty("库区")
    private String storageAreaStr;

    @ExcelIgnore
    @ApiModelProperty("库区-1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("货位id")
    @ExcelIgnore
    private Long goodsAllocationId;

    @ApiModelProperty("货位")
    @ExcelProperty("货位")
    private String goodsAllocationName;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.barCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityName, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("前台品名")
    @ExcelProperty("前台品名")
    private String commodityAppName;

    @ExcelIgnore
    @ApiModelProperty("t_stall_commodity是否可售：1-是,0-否")
    private Integer commoditySaleStatus;

    @FieldRender(fieldName = RenderFieldHelper.Commodity.boxCapacity, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty(value = "采购箱规")
    @ExcelProperty("采购箱规")
    private String boxCapacity;

    @ExcelProperty("是否可售")
    @ApiModelProperty("t_stall_commodity是否可售：1-是,0-否")
    private String commoditySaleStatusStr;

    @ApiModelProperty("规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commoditySpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("包装规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityPackageSpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("库存总数量")
    @ApiModelProperty("库存总数量=正常库数量+临时库数量")
    private BigDecimal totalQuantity;

    @ExcelProperty("排面区数量")
    @ApiModelProperty("排面区数量")
    private BigDecimal stockQuantity;

    @ExcelProperty("拣货区数量")
    @ApiModelProperty("拣货区数量")
    private BigDecimal pickingAreaStock;

    @ExcelProperty("存储区库存")
    @ApiModelProperty("存储区库存")
    private BigDecimal warehouseAreaStock;

    @ExcelProperty("临时库存")
    @ApiModelProperty("临时库存")
    private BigDecimal stockProvisional;

    @ExcelProperty("冻结库存")
    @ApiModelProperty("冻结库存")
    private Integer freezeNumber;

    /**
     * 上下架状态：0-上架，1-下架
     */
    @ApiModelProperty("上架状态")
    @ExcelIgnore
    private Integer appStatus;

    @ApiModelProperty("上架状态")
    @ExcelProperty("上架状态")
    private String appStatusStr;

    @ExcelProperty(value = "后台一级品类")
    @ApiModelProperty("后台一级品类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
    private String commodityFirstName;

    @ExcelProperty(value = "后台二级品类")
    @ApiModelProperty("后台二级品类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
    private String commoditySecondName;

    @ExcelProperty(value = "后台三级品类")
    @ApiModelProperty("后台三级品类")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
    private String commodityThirdKindName;

    @ExcelIgnore
    private BigDecimal stock;

    @ExcelProperty(value = "是否必售")
    @ApiModelProperty(value = "是否必售")
    private String saleStatusName;

    public String getCommoditySaleStatusStr() {
        return null != this.commoditySaleStatus && this.commoditySaleStatus == 1 ? "可售" : "不可售";
    }

    public String getStorageAreaStr(){
        return Optional.ofNullable(StorageAreaEnum.getTypeEnumByCode(storageArea)).map(StorageAreaEnum::getName).orElse("");
    }

    public String getAppStatusStr() {
        return null != this.appStatus  && this.appStatus == 0 ? "上架" : "下架";
    }

    public BigDecimal getStock() {
        if(null != stockQuantity){
            return stockQuantity;
        }else if(null != pickingAreaStock){
            return pickingAreaStock;
        }else if(null != warehouseAreaStock){
            return warehouseAreaStock;
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getStockQuantity() {
        return null == stockQuantity? BigDecimal.ZERO : stockQuantity ;
    }

    public BigDecimal getPickingAreaStock() {
        return null == pickingAreaStock? BigDecimal.ZERO : pickingAreaStock;
    }

    public BigDecimal getWarehouseAreaStock() {
        return null == warehouseAreaStock? BigDecimal.ZERO : warehouseAreaStock;
    }

    public BigDecimal getStockProvisional() {
        return null == stockProvisional ? BigDecimal.ZERO : stockProvisional;
    }

    public Integer getFreezeNumber() {
        return null == freezeNumber ? 0 : freezeNumber;
    }
}
