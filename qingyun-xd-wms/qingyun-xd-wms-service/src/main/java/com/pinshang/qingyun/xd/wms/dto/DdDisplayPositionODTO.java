package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 排面陈列位管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@ToString
@ApiModel("DdDisplayPositionODTO")
public class DdDisplayPositionODTO {

    private Long id;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("货架id")
    private Long shelveId;

    @ApiModelProperty("档口code")
    private String stallCode;

    @ApiModelProperty("档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "stallId")
    private String stallName;

    @ApiModelProperty("排面货架编码")
    private String shelveCode;

    @ApiModelProperty("排面货架名称")
    private String shelveName;

    @ApiModelProperty("陈列位")
    private String displayPositionName;

    @ApiModelProperty("商品数")
    private Integer commodityCount;

}