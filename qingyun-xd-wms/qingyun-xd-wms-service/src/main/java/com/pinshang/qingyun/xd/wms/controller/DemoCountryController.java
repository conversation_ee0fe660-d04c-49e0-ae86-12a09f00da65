
package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.xd.wms.model.DemoWms;
import com.pinshang.qingyun.xd.wms.service.DemoWmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(value = "鲜到仓储Demo", tags = "DemoCountryController",description ="鲜到仓储Demo" )
@Slf4j
public class DemoCountryController {
    @Autowired
    private DemoWmsService demoWmsService;

    @RequestMapping("/test")
    @ApiOperation(value = "鲜到配送Demo", notes = "测试请求")
    public String restTemplate(){
        IdWorker.getId();
        log.info(com.baomidou.mybatisplus.core.toolkit.IdWorker.getIdStr());
        log.info(com.baomidou.mybatisplus.core.toolkit.IdWorker.getIdStr());
        log.info(com.baomidou.mybatisplus.core.toolkit.IdWorker.getIdStr());
        DemoWms country =  demoWmsService.getById();
        return country.getName();
    }
}
