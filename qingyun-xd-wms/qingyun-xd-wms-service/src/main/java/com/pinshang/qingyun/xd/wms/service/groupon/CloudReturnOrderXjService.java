package com.pinshang.qingyun.xd.wms.service.groupon;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.msg.AppMessageTypeEnum;
import com.pinshang.qingyun.base.enums.settlement.SettleCompaintSourceTypeEnum;
import com.pinshang.qingyun.base.enums.shop.ShopCommodityBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.xd.*;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.msg.dto.zsmd.MessageDTO;
import com.pinshang.qingyun.msg.service.zsmd.ZSMessageClient;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.product.service.CommodityBarCodeClient;
import com.pinshang.qingyun.shop.dto.ShopCommodityPriceODTO;
import com.pinshang.qingyun.shop.dto.ShopEmployeeODTO;
import com.pinshang.qingyun.shop.dto.price.SyncCommodityToShopCommodityIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.SaveShopCommodityIDTO;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommoditySaveDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptIDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptItemIDTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopEmployeeClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.shop.service.shopCommodity.SaveShopCommodityClientClient;
import com.pinshang.qingyun.storage.dto.QueryShopPackageOrderInfoIDTO;
import com.pinshang.qingyun.storage.dto.QueryShopPackageOrderInfoODTO;
import com.pinshang.qingyun.storage.service.PackageOrderWebClient;
import com.pinshang.qingyun.xd.order.dto.XdApplyReturnIDTO;
import com.pinshang.qingyun.xd.order.dto.XdCloudApplyReturnODTO;
import com.pinshang.qingyun.xd.order.dto.XdMultipleCommodityApplyReturnIDTO;
import com.pinshang.qingyun.xd.order.dto.XdOrderODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.order.service.XdReturnOrderClient;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.cloud.CloudOrderCancelIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.dto.message.AllotItemMessage;
import com.pinshang.qingyun.xd.wms.dto.message.AllotMessage;
import com.pinshang.qingyun.xd.wms.dto.message.SettleOrderVo;
import com.pinshang.qingyun.xd.wms.enums.CloudTakeGoodErrorCodeEnum;
import com.pinshang.qingyun.xd.wms.enums.XdRefundTypeEnums;
import com.pinshang.qingyun.xd.wms.mapper.*;
import com.pinshang.qingyun.xd.wms.model.*;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.vo.CommodityPriceVO;
import com.pinshang.qingyun.xd.wms.vo.InventoryInitialItemVO;
import com.pinshang.qingyun.xd.wms.vo.InventoryInitialVO;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xsuser.dto.UserInfoODTO;
import com.pinshang.qingyun.xsuser.dto.member.SelectUserMemberInfoIDTO;
import com.pinshang.qingyun.xsuser.dto.member.UserMemberInfoODTO;
import com.pinshang.qingyun.xsuser.service.XSUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * @ClassName CloudXjReturnOrderService
 * <AUTHOR>
 * @Date 2021/6/17 15:32
 * @Description CloudXjReturnOrderService
 * @Version 1.0
 */
@Slf4j
@Service
public class CloudReturnOrderXjService {

    @Autowired
    private XdReturnOrderMapper xdReturnOrderMapper;

    @Autowired
    private XdReturnOrderItemMapper xdReturnOrderItemMapper;
    @Autowired
    private ShopStockClient shopStockClient;
    @Autowired
    private StockServiceAdapter stockServiceAdapter;
    @Autowired
    private ShopCommodityService shopCommodityService;
    @Autowired
    private GrouponMapper grouponMapper;
    @Autowired
    private XdReturnOrderClient xdReturnOrderClient;

    @Autowired
    private CommodityBarCodeClient commodityBarCodeClient;
    @Autowired
    private ShopCommodityClient shopCommodityClient;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private DcShopPackageOrderMapper dcShopPackageOrderMapper;
    @Autowired
    private DcShopPackageOrderItemMapper dcShopPackageOrderItemMapper;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private PackageOrderWebClient packageOrderWebClient;
    @Autowired
    private ShopEmployeeClient shopEmployeeClient;
    @Autowired
    private ZSMessageClient zSMessageClient;
    @Autowired
    private BackSettingMapper backSettingMapper;
    @Autowired
    private XSUserClient xsUserClient;
    @Autowired
    private IMqSenderComponent mqSenderComponent;
    @Autowired
    private XdOrderClient xdOrderClient;
    @Autowired
    private SaveShopCommodityClientClient saveShopCommodityClientClient;


    @Transactional(rollbackFor = Exception.class)
    public Boolean returnOrder(CloudApplyReturnOrderIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        Date nowDate = new Date();

        //1. 调用生成订单, 并返回退单信息XdReturnOrderEntity
        XdApplyReturnIDTO xdApplyReturnIDTO = new XdApplyReturnIDTO();
        xdApplyReturnIDTO.setOrderId(idto.getOrderId());
        xdApplyReturnIDTO.setRefundType(XdRefundTypeEnums.COMMDITY_RETURN.getCode());
        xdApplyReturnIDTO.setReturnOrderFlag(0);
        xdApplyReturnIDTO.setReturnReasonId(9131078242860602861L);
        xdApplyReturnIDTO.setMultipleCommodity(idto.getMultipleCommodity());
        XdCloudApplyReturnODTO xdReturnOrderEntity = xdReturnOrderClient.cloudApplyReturn(xdApplyReturnIDTO);
        QYAssert.isTrue(null != xdReturnOrderEntity, "生成退货单失败, 原订单号: {}", idto.getOrderId() + "");

        // 2. 更新退货单审核
        Long returnOrderId = Long.parseLong(xdReturnOrderEntity.getReturnOrderId());
        XdReturnOrderEntity auditUpdateEntity = new XdReturnOrderEntity();
        auditUpdateEntity.setId(returnOrderId);
        auditUpdateEntity.setStatus(XdReturnOrderStatusEnum.AUDITED.getCode());
        auditUpdateEntity.setUpdateId(userId);
        auditUpdateEntity.setUpdateTime(nowDate);
        xdReturnOrderMapper.updateById(auditUpdateEntity);
        log.info("更新退货单审核, 退单号: ${}", returnOrderId);

        // 3. 取货完成 发送消息, 无实际取货单
        DeliveryOrderKafkaRecCloudVo deliveryOrder = new DeliveryOrderKafkaRecCloudVo();
        deliveryOrder.setOrderId(returnOrderId);
        deliveryOrder.setDeliveryType(XdDeliveryOrderTypeEnum.PICKUP.getCode() );
        deliveryOrder.setDeliveryStatus(XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode());
        deliveryOrder.setOrderCode(xdReturnOrderEntity.getReturnOrderCode());
        deliveryOrder.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
        deliveryOrder.setSourceType(OrderSourceTypeEnum.MINI);
        Long shopId = xdReturnOrderMapper.selectShopIdByReturnOrderId(returnOrderId);
        deliveryOrder.setWarehouseId(shopId);
        // 4. 库存期初
        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_DELIVERY_CHANGE_TOPIC.getTopic(),
                deliveryOrder, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_DELIVERY_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
        return true;
    }

    /**
     *  校验是否所有商品都有供货价
     */
    public Boolean checkStorePriceForReturn(CloudApplyReturnOrderIDTO idto){
        Order orderInfoShop = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(Order::getShopId).eq(Order::getId, idto.getOrderId()));
        List<Long> itemIdList = idto.getMultipleCommodity().stream().map(XdMultipleCommodityApplyReturnIDTO::getOrderItemId).collect(Collectors.toList());
        List<XdOrderItem> orderItemsCommodities = orderItemMapper.selectList(new LambdaQueryWrapper<XdOrderItem>().select(XdOrderItem::getCommodityId)
                .in(XdOrderItem::getId, itemIdList));
        return checkHasStockPrice(orderInfoShop.getShopId(), orderItemsCommodities.stream().map(XdOrderItem::getCommodityId).collect(Collectors.toList()));
    }

    /**
     * 取消订单期初库存
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cloudCancelIni(CloudCancelIniIDTO idto){
        QYAssert.isTrue(SpringUtil.isNotEmpty(idto.getOrderIdList()), "订单id不能为空");
        for(Long orderId : idto.getOrderIdList()) {
            List<DcShopPackageOrder> dcShopPackageList = dcShopPackageOrderMapper.selectList(new LambdaQueryWrapper<DcShopPackageOrder>()
                    .eq(DcShopPackageOrder::getReferId, orderId));
            if(SpringUtil.isEmpty(dcShopPackageList)){
                continue;
            }
            List<Long> dcShopPackageIdList = dcShopPackageList.stream().map(DcShopPackageOrder::getId).collect(Collectors.toList());

            InventoryInitialVO vo = new InventoryInitialVO();
            List<DcShopPackageOrderItem> dcShopPackageItemList = dcShopPackageOrderItemMapper.selectList(
                    new LambdaQueryWrapper<DcShopPackageOrderItem>().in(DcShopPackageOrderItem::getPackOrderId, dcShopPackageIdList));

            vo.setReferOrderId(dcShopPackageList.get(0).getReferId());
            vo.setReferOrderCode(dcShopPackageList.get(0).getReferCode());
            List<InventoryInitialItemVO> voItemList = new ArrayList<>(dcShopPackageItemList.size());
            for (DcShopPackageOrderItem item : dcShopPackageItemList) {
                InventoryInitialItemVO voItem = new InventoryInitialItemVO();
                voItem.setCommodityId(item.getCommodityId());
                voItem.setQuantity(item.getPackageQuantity());
                voItem.setNumber(new BigDecimal(item.getPackageNumber()));
                voItemList.add(voItem);
            }
            vo.setItemList(voItemList);
            vo.setStockInOutTypeEnums(StockInOutTypeEnums.IN_CLOUD_CANCEL_NORMAL);
            Shop shop = shopMapper.selectOne(new LambdaQueryWrapper<Shop>().select(Shop::getStoreId).eq(Shop::getId, dcShopPackageList.get(0).getShopId()));
            vo.setShopId(dcShopPackageList.get(0).getShopId());
            vo.setStoreId(shop.getStoreId());
            vo.setUserId(idto.getUserId());
            inventoryInitial(vo);
        }
        return true;
    }


    /**
     * 获取总部给门店的商品价格(不考虑特价), 无结果返回null
     * @param storeId
     * @param commodityIdList
     */
    public Map<String, BigDecimal> getStockInPrice(Long storeId, List<Long> commodityIdList){
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId+"");
        idto.setCommodityIdListAll(commodityIdList);
        idto.setPageSize(commodityIdList.size());
        PageInfo<CommodityResultODTO> storeCommodity = productPriceModelClient.findStoreCommodityListByPage(idto);
        if(SpringUtil.isEmpty(storeCommodity.getList())){
            return null;
        }
        return storeCommodity.getList().stream().collect(Collectors.toMap(CommodityResultODTO::getCommodityId, CommodityResultODTO::getCommodityPrice, (t1,t2)->t2));
    }


    /**
     * 根据订单code查询订单信息
     */
    public CloudOrderForReturnODTO queryOrderInfoForReturn(String orderCode){
        Long shopId = FastThreadLocalUtil.getQY().getShopId();
        CloudOrderForReturnDTO orderInfo= grouponMapper.queryOrderForCloudReturn(orderCode);

        checkCloudOrderInfo(orderInfo, shopId);

        CloudOrderForReturnODTO odto = new CloudOrderForReturnODTO();
        odto.setOrderId(orderInfo.getOrderId());
        odto.setOrderCode(orderInfo.getOrderCode());
        odto.setArrivalTime(orderInfo.getArrivalTime());
        odto.setUserMobile(orderInfo.getUserMobile());
        List<CloudOrderForReturnItemODTO> itemList = grouponMapper.queryOrderItemForCloudReturn(orderInfo.getOrderId(), shopId);
        if(SpringUtil.isEmpty(itemList)){
            throw new BizLogicException("此单没有可退商品!", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        List<Long> commodityIdList = new ArrayList<>(itemList.size());
        List<Long> orderItemIdList = new ArrayList<>(itemList.size());
        for(CloudOrderForReturnItemODTO item : itemList){
            commodityIdList.add(item.getCommodityId());
            orderItemIdList.add(item.getOrderItemId());
        }
        Map<Long, XdReturnOrderItemEntity> returnOrderItemMap = selectReturnRecordByOid(orderItemIdList);
        Map<Long, List<String>> barCodeMap = commodityBarCodeClient.selectBarCodeByCid(commodityIdList);
        for (CloudOrderForReturnItemODTO oDto : itemList) {
            oDto.setBarCodeList(barCodeMap.get(oDto.getCommodityId()));
            if(SpringUtil.isNotEmpty(returnOrderItemMap) && null != returnOrderItemMap.get(oDto.getOrderItemId())){
                oDto.setHasReturn(1);
            }else{
                oDto.setHasReturn(0);
            }
        }
        odto.setItemList(itemList);
        return odto;
    }

    /**
     * 校验是否可以取消, 只校验已打包的商品
     * @return 期初失败订单
     */
    public List<String> checkForInventoryInitial(List<String> orderCodeList){
        List<String> failOrderList = new ArrayList<>();
        for(String orderCode : orderCodeList) {
            List<DcShopPackageOrder> dcShopPackageList = dcShopPackageOrderMapper.selectList(new LambdaQueryWrapper<DcShopPackageOrder>()
                    .eq(DcShopPackageOrder::getReferCode, orderCode));
            if(SpringUtil.isEmpty(dcShopPackageList)){
                continue;
            }
            List<Long> dcShopPackageIdList = dcShopPackageList.stream().map(DcShopPackageOrder::getId).collect(Collectors.toList());
            List<DcShopPackageOrderItem> dcShopPackageItemList = dcShopPackageOrderItemMapper.selectList(
                    new LambdaQueryWrapper<DcShopPackageOrderItem>().in(DcShopPackageOrderItem::getPackOrderId, dcShopPackageIdList));
            if(SpringUtil.isEmpty(dcShopPackageItemList)){
                continue;
            }

            List<Long> commodityIdList = dcShopPackageItemList.stream().map(DcShopPackageOrderItem::getCommodityId).collect(Collectors.toList());
            if(!checkHasStockPrice(dcShopPackageList.get(0).getShopId(), commodityIdList)){
                failOrderList.add(orderCode);
            }
        }
        return failOrderList;
    }
    public Boolean cancelOrder(Long orderId){
        LockUtils.checkLock(LockUtils.PICK_CREATE, String.valueOf(orderId), 10, TimeUnit.SECONDS);

        Long userId= FastThreadLocalUtil.getQY().getUserId();
        Order order = orderMapper.selectById(orderId);
        QYAssert.isTrue(null != order, "订单不存在");
        QYAssert.isTrue(XdOrderStatusEnum.OUT_STOCKING.getCode()==order.getOrderStatus() || XdOrderStatusEnum.WAITING_DELIVERY.getCode() == order.getOrderStatus(),
                "仅出库中、待配送2种状态的订单，可以“取消”");

        PickOrderMqDTO mqDTO = new PickOrderMqDTO();
        mqDTO.setOrderId(order.getId());
        mqDTO.setOrderCode(order.getOrderCode());
        mqDTO.setPickStatus(XdPickOrderStatusEnum.CANCEL.getCode());
        mqDTO.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
        mqDTO.setOperatorId(userId);
        mqDTO.setCancelType(XdOrderCancelReasonEnum.CUSTOM_CANCEL.getCode());
        sendMessage(mqDTO);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error("云超取消订单 ", e);
        }
        //目前只有云超普通的单子发送这个消息
        // 大仓已经出库完成了（出库完成时刻，发正向消息），后来客服取消了订单
        //取消时刻，发负向消息; 判断大仓是否已经出库完成
        QueryShopPackageOrderInfoIDTO dto = new QueryShopPackageOrderInfoIDTO();
        dto.setOrderId(orderId);
        QueryShopPackageOrderInfoODTO dcPackage = packageOrderWebClient.queryShopPackageOrderInfo(dto);
        if(dcPackage != null  && dcPackage.getIsCloudOrderFinish()){
            sendCancelSettleMessage(order.getId());
        }

        //如果是顺丰配送，需要发消息通知顺丰
        if (XdDeliveryModeEnum.SF_DELIVERY.getCode() == order.getDeliveryMode()) {
            ThirdDeliveryKafkaDTO kafkaDTO = new ThirdDeliveryKafkaDTO();
            kafkaDTO.setOrderIds(Arrays.asList(orderId));
            kafkaDTO.setOperateType(2);
            mqSenderComponent.send(
                    QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.SF_CANCEL_ORDER_TOPIC.getTopic(),
                    kafkaDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SF_CANCEL_ORDER_TOPIC.name(),
                    KafkaMessageOperationTypeEnum.UPDATE.name());
        }
        return true;
    }

    /**
     *  呼叫系统对接 云超订单取消
     */
    public Boolean cancelCloudOrderEx(CloudOrderCancelIDTO cancelIDTO){

        Order order = orderMapper.selectById(cancelIDTO.getOrderId());
        QYAssert.isTrue(null != order, "订单不存在");

        PickOrderMqDTO mqDTO = new PickOrderMqDTO();
        mqDTO.setOrderId(order.getId());
        mqDTO.setOrderCode(order.getOrderCode());
        mqDTO.setPickStatus(XdPickOrderStatusEnum.CANCEL.getCode());
        mqDTO.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
        mqDTO.setOperatorId(cancelIDTO.getUserId());
        mqDTO.setReasonOptionId(cancelIDTO.getReasonOptionId());
        mqDTO.setReason(cancelIDTO.getReason());
        sendMessage(mqDTO);

        // 大仓已经出库完成了（出库完成时刻，发正向消息），后来客服取消了订单
        //取消时刻，发负向消息,并且判断大仓已经出库完成
        QueryShopPackageOrderInfoIDTO dto = new QueryShopPackageOrderInfoIDTO();
        dto.setOrderId(order.getId());
        QueryShopPackageOrderInfoODTO doPackage = packageOrderWebClient.queryShopPackageOrderInfo(dto);
        if(doPackage != null && doPackage.getIsCloudOrderFinish()){
            sendCancelSettleMessage(order.getId());
        }
        return true;
    }

    /**
     * 大仓已经出库完成了（出库完成时刻，发正向消息），后来客服取消了订单
     * 取消时刻，发负向消息
     */
    public void sendCancelSettleMessage(Long orderId) {
        List<SettleOrderVo> list = orderMapper.querySettleOrderList(orderId);
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        SettleOrderVo settleOrderVo = list.get(0);
        List<AllotItemMessage> items = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal deliveryTotalAmount = BigDecimal.ZERO;
        for(SettleOrderVo vo : list){
            AllotItemMessage itemMessage = getAllotItemMessage(vo, settleOrderVo);
            items.add(itemMessage);
            totalAmount = totalAmount.add(itemMessage.getTotalPrice());
            deliveryTotalAmount = deliveryTotalAmount.add(itemMessage.getDeliveryTotalPrice());
        }
        List<AllotMessage> sendData = new ArrayList<>();
        AllotMessage allotMessage = new AllotMessage();
        BeanUtils.copyProperties(settleOrderVo,allotMessage);
        allotMessage.setItems(items);
        allotMessage.setSourceType(SettleCompaintSourceTypeEnum.RT_YC_CANCEL_ORDER.name());
        allotMessage.setSendMqTime(DateUtil.getDateFormate(new Date(), DateUtil.DEFAULT_DATE_FORMAT));
        allotMessage.setDeliveryTime(settleOrderVo.getToShopDate());
        allotMessage.setTotalAmount(totalAmount);
        allotMessage.setDeliveryTotalAmount(deliveryTotalAmount);
        sendData.add(allotMessage);
        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.SETTLE_ORDER_ALL_TOPIC.getTopic(),
                sendData, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.SETTLE_ORDER_SYNC.name(),
                KafkaMessageOperationTypeEnum.INSERT.name());
    }

    @NotNull
    private static AllotItemMessage getAllotItemMessage(SettleOrderVo vo, SettleOrderVo settleOrderVo) {
        AllotItemMessage itemMessage = new AllotItemMessage();
        itemMessage.setSourceId(settleOrderVo.getSourceId() + "");
        itemMessage.setItemId(vo.getCommodityId());
        itemMessage.setCommodityId(vo.getCommodityId());
        itemMessage.setNumber(vo.getNumber());
        itemMessage.setUnitPrice(vo.getTotalPrice().divide(vo.getNumber(),2,BigDecimal.ROUND_HALF_UP));
        itemMessage.setTotalPrice(vo.getTotalPrice());

        // 如果发货数量大于订单数量，则发货数量=订单数量.
        itemMessage.setDeliveryUnitPrice(vo.getDeliveryTotalAmount().divide(vo.getDeliveryNumber(),2,BigDecimal.ROUND_HALF_UP));
        if(vo.getDeliveryNumber().compareTo(vo.getNumber()) > 0){
            itemMessage.setDeliveryNumber(vo.getNumber());
        }else {
            itemMessage.setDeliveryNumber(vo.getDeliveryNumber());
        }
        itemMessage.setDeliveryTotalPrice(itemMessage.getDeliveryUnitPrice().multiply(itemMessage.getDeliveryNumber()).setScale(2,BigDecimal.ROUND_HALF_UP));
        return itemMessage;
    }

    public Boolean distributionWarnByShopId(Long shopId){
        // 总部登陆shopId为null, 此时不提醒
        if(null == shopId){
            return false;
        }
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        List<CloudDistributionWarnVO> orderList = selectDistributionWarnOrder(shopId);
        if(CollectionUtils.isEmpty(orderList)){
            return false;
        }
        List<Long> storeIdList = orderList.stream().map(CloudDistributionWarnVO::getStoreId).distinct().collect(Collectors.toList());
        List<ShopEmployeeODTO> shopInfoList = shopEmployeeClient.getShopEmployeeListByStoreIdList(storeIdList);
        if(shopInfoList.isEmpty()){
            return false;
        }
        List<Long> userIds = shopInfoList.stream().map(ShopEmployeeODTO::getUserId).collect(Collectors.toList());
        if(userIds.contains(userId)){
            return !CollectionUtils.isEmpty(orderList);
        }
        return false;
    }

    public Boolean distributionWarnForPDA(){
        List<CloudDistributionWarnVO> orderList = selectDistributionWarnOrder(null);;
        if(null == orderList || orderList.isEmpty()){
            return true;
        }
        List<Long> storeIdList = orderList.stream().map(CloudDistributionWarnVO::getStoreId).distinct().collect(Collectors.toList());
        List<ShopEmployeeODTO> shopInfoList = shopEmployeeClient.getShopEmployeeListByStoreIdList(storeIdList);
        // 检测无联系人的门店
        List<Long> shopIdList = shopInfoList.stream().map(ShopEmployeeODTO::getShopId).distinct().collect(Collectors.toList());
        List<Long> noUserShopId = orderList.stream().map(CloudDistributionWarnVO::getShopId).distinct().filter(shopId -> !shopIdList.contains(shopId)).collect(Collectors.toList());
        if(!noUserShopId.isEmpty()){
            log.info("以下门店没有联系人:{}", noUserShopId);
        }

        List<Long> warmUserId =  shopInfoList.stream().map(ShopEmployeeODTO::getUserId).distinct().collect(Collectors.toList());
        if(!warmUserId.isEmpty()){
            sendPDAMessage(warmUserId);
        }
        return true;
    }

    public PageInfo<ClouodCancelOrderListODTO> cancelOrderList(ClouodCancelOrderListIDTO idto){
        idto.setOrderBeginTime(idto.getOrderBeginTime()+" 00:00:00");
        idto.setOrderEndTime(idto.getOrderEndTime()+" 23:59:59");

        if(StringUtils.isNotBlank(idto.getArriveBeginTime()) && StringUtils.isNotBlank(idto.getArriveEndTime()) ) {
            idto.setArriveBeginTime(idto.getArriveBeginTime() + " 00:00:00");
            idto.setArriveEndTime(idto.getArriveEndTime() + " 23:59:59");
        }

        // 普通订单和云超订单, 下单人手机号需要查询xs_user表获取, 如果不存在该用户, 则置为-1
        if(StringUtils.isNotBlank(idto.getOrderMobile())){
            UserMemberInfoODTO xsUserInfo = selectUserMemberInfoByPhone(idto.getOrderMobile());
            if(null!= xsUserInfo){
                idto.setXsUserId(xsUserInfo.getXsUserId());
            }else{
                idto.setXsUserId(-1L);
            }
        }

        PageInfo<ClouodCancelOrderListODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.cancelOrderList(idto);
        });
        if(null== pageInfo.getList() || pageInfo.getList().isEmpty()){
            return pageInfo;
        }
        // 获取组装下单人手机号
        List<Long> xsUserIdList = pageInfo.getList().stream().map(ClouodCancelOrderListODTO::getXsUserId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, String> userInfoMap = selectUserInfoListByUserIdList(xsUserIdList);

        for(ClouodCancelOrderListODTO it :  pageInfo.getList()){
            it.setOrderStatusName(XdOrderStatusEnum.getByCode(it.getOrderStatus()).getRemark());
            if(XdOrderStatusEnum.CANCEL.getCode() != it.getOrderStatus()){
                it.setCancelTime(null);
                it.setCancelUserId(null);
                it.setCancelUserName("");
            }
            if(!OrderSourceTypeEnum.GROUP_BUY.getCode().equals(it.getSourceType())){
                it.setOrderMobile(userInfoMap.get(it.getXsUserId()));
            }
        }
        return pageInfo;
    }

    public QueryReturnInfoForCloudTakeGoodODTO queryReturnInfoForCloudTakeGood(String returnCode, Long shopId){
        QueryReturnInfoForCloudTakeGoodODTO odto = new QueryReturnInfoForCloudTakeGoodODTO();
        odto.setStatus(YesOrNoEnums.YES.getCode());
        LambdaQueryWrapper queryOrder= new LambdaQueryWrapper<XdReturnOrderEntity>()
                .eq(XdReturnOrderEntity::getReturnCode, returnCode);
        XdReturnOrderEntity returnInfo = xdReturnOrderMapper.selectOne(queryOrder);
        if(null == returnInfo || null == returnInfo.getId()){
            odto.setStatus(CloudTakeGoodErrorCodeEnum.ERROR_RETURN_CODE.getCode());
            return odto;
        }
        if(XdReturnOrderStatusEnum.AUDITED.getCode() != returnInfo.getStatus()){
            odto.setStatus(CloudTakeGoodErrorCodeEnum.ERROR_RETURN_CODE.getCode());
            return odto;
        }
        if( !shopId.equals(returnInfo.getShopId())){
            odto.setStatus(CloudTakeGoodErrorCodeEnum.ERROR_RETURN_SHOP.getCode());
            return odto;
        }

        XdOrderODTO xdOrderODTO = xdOrderClient.findByOrderId(returnInfo.getOrderId());
        boolean judge = (XdOrderTypeEnum.CLOUDXJ.getCode() == xdOrderODTO.getOrderType().getCode() || XdOrderTypeEnum.CLOUD_GROUP.getCode() == xdOrderODTO.getOrderType().getCode())
                && xdOrderODTO.getDeliveryMode() == XdDeliveryModeEnum.SHOP_SELF_DELIVERY.getCode();
        QYAssert.isTrue(judge, "仅支持云超自提订单");


        odto.setId(returnInfo.getId());
        odto.setReturnCode(returnInfo.getReturnCode());
        odto.setTotalAmount(returnInfo.getTotalAmount());
        List<QueryReturnInfoForCloudTakeGoodItemODTO> itemList = xdReturnOrderItemMapper.selectItemListForCloudReturnTakeSelf(returnInfo.getId());
        QYAssert.isTrue(SpringUtil.isNotEmpty(itemList), "退货单异常");
        odto.setItemList(itemList);
        return odto;
    }

    /**
     * 云超自提核销
     * */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeReturnInfoForCloudTakeGood(Long returnOrderId){
        XdReturnOrderEntity returnOrder = xdReturnOrderMapper.selectById(returnOrderId);
        if(null == returnOrder || null == returnOrder.getId()){
            throw new BizLogicException(CloudTakeGoodErrorCodeEnum.ERROR_RETURN_CODE.getDesc());
        }
        if(XdReturnOrderStatusEnum.AUDITED.getCode() != returnOrder.getStatus()){
            throw new BizLogicException(CloudTakeGoodErrorCodeEnum.ERROR_RETURN_CODE.getDesc());
        }
        // 3. 取货完成 发送消息, 无实际取货单
        DeliveryOrderKafkaRecCloudVo deliveryOrder = new DeliveryOrderKafkaRecCloudVo();
        deliveryOrder.setOrderId(returnOrderId);
        deliveryOrder.setDeliveryType(XdDeliveryOrderTypeEnum.PICKUP.getCode() );
        deliveryOrder.setDeliveryStatus(XdDeliveryOrderStatusEnum.DELIVERY_COMPLETED.getCode());
        deliveryOrder.setOrderCode(returnOrder.getReturnCode());
        deliveryOrder.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
        deliveryOrder.setSourceType(OrderSourceTypeEnum.getEnumByCode(returnOrder.getSourceType()));
        deliveryOrder.setWarehouseId(returnOrder.getShopId());

        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_DELIVERY_CHANGE_TOPIC.getTopic(),
                deliveryOrder, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_DELIVERY_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
        return true;
    }


    private List<CloudDistributionWarnVO> selectDistributionWarnOrder(Long shopId){
        BackSetting cloudSetting = backSettingMapper.selectById(1L);
        if(null == cloudSetting || null == cloudSetting.getCloudDistributionWarnTime()){
            return null;
        }
        Date orderTimePre = DateUtil.addDay(new Date(), -7);
        Date warnTime = DateUtil.addMinute(new Date(), cloudSetting.getCloudDistributionWarnTime());
        String orderTimePreStr = DateUtil.get4yMd(orderTimePre);
        return orderMapper.selectWarnOrder(shopId, warnTime,  orderTimePreStr);
    }

    private void sendPDAMessage(List<Long> userIds){
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTypeEnum(AppMessageTypeEnum.ORDER);
        messageDTO.setSendScope(1);
        messageDTO.setTitle("订单超时");
        messageDTO.setContent("DISTRIBUTION-ORDER");
        messageDTO.setUserIds(userIds);
        messageDTO.setTargetUrl("您有即将超时未分配的云超配送任务,请及时处理! ");
        zSMessageClient.sendMessage(messageDTO);
    }

    /**
     * 发送取消订单消息
     * @param mqDTO
     */
    private void sendMessage(PickOrderMqDTO mqDTO){

        mqSenderComponent.send(
                QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XD_PICK_ORDER_CHANGE_TOPIC.getTopic(),
                mqDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XD_PICK_ORDER_CHANGE_TYPE.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());

        log.info("发送订单取消变更通知, orderCode:{}, pickStatus:{} ", mqDTO.getOrderCode(), XdOrderStatusEnum.CANCEL);
    }

    private boolean checkHasStockPrice(Long shopId, List<Long> commodityIdList){
        Shop shop = shopMapper.selectOne(new LambdaQueryWrapper<Shop>().select(Shop::getStoreId).eq(Shop::getId, shopId));
        Map<String, BigDecimal> stockCommodityPrice = getStockInPrice(shop.getStoreId(), commodityIdList);
        if (SpringUtil.isEmpty(stockCommodityPrice)){
            return false;
        }
        for(Long commodityId : commodityIdList){
            if(null == stockCommodityPrice.get(commodityId+"")){
                return false;
            }
        }
        return true;
    }

    /**
     * 云超退货获取原单信息校验
     * 不通过直接抛业务异常
     * @param orderInfo
     */
    private void checkCloudOrderInfo (CloudOrderForReturnDTO orderInfo, Long shopId){
        if(null == orderInfo || null == orderInfo.getOrderId()){
            throw new BizLogicException("没有匹配到云超订单, 请输入正确的订单号!", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        if(XdOrderStatusEnum.CANCEL.getCode() == orderInfo.getOrderStatus()){
            throw new BizLogicException("此订单已取消, 无法退货!", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        if(XdOrderStatusEnum.DELIVERED.getCode() != orderInfo.getOrderStatus()){
            throw new BizLogicException("此订单未提货, 无法退货!", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }
        if(!shopId.equals(orderInfo.getShopId())){
            throw new BizLogicException("提货门店："+orderInfo.getShopName()+"门店！本店无法退货！", ApiErrorCodeEnum.PDA_GOT_IT_WARN);
        }

    }

    public void cloudInventoryInitial(InventoryInitialVO vo) {
        List<Long> commodityIdList = vo.getItemList().stream().map(InventoryInitialItemVO::getCommodityId).distinct().collect(Collectors.toList());
        //查询剩余库存
        List<ShopStockDTO> finishedStockList = shopCommodityService.queryShopStock(vo.getShopId(), commodityIdList);
        //库存不存在，或者有部分商品库存不存在  期初库存
        if (SpringUtil.isEmpty(finishedStockList) || finishedStockList.size() < commodityIdList.size() ) {
            String code = vo.getReferOrderCode();
            if(ShopCommodityBusinessTypeEnum.CLOUD_DELIVERY_FAILED.equals(vo.getBusinessTypeEnum())){
                code = vo.getOrderCode();
            }
            finishedStockList = initCommodityAndPrice(commodityIdList, vo.getShopId(),vo.getBusinessTypeEnum(),code);
            if ( SpringUtil.isEmpty(finishedStockList) || finishedStockList.size() < commodityIdList.size() ) {
                //如果依旧还是为空，提示期初失败
                throw new BizLogicException("期初失败，请重试", ApiErrorCodeEnum.UNKNOW_EXCEPTION);
            }
        }
        Map<Long, BigDecimal> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

        List<ShopStockReceiptItemIDTO> commodityList = new ArrayList<>(commodityIdList.size());
        ShopStockReceiptIDTO shopStockReceipt = new ShopStockReceiptIDTO();
        shopStockReceipt.setShopId(vo.getShopId());

        Shop shop = shopMapper.selectById(QYApplicationContext.cloudShopId);

        Map<String, BigDecimal> stockCommodityPrice = getStockInPrice(shop.getStoreId(), commodityIdList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(stockCommodityPrice) , "此单中部分商品，在该门店的产品价格方案中缺失！");

        for(InventoryInitialItemVO item: vo.getItemList()){
            ShopStockReceiptItemIDTO stockReceiptItem = new ShopStockReceiptItemIDTO();
            stockReceiptItem.setCommodityId(item.getCommodityId());
            stockReceiptItem.setQuantity(item.getQuantity());
            BigDecimal price = stockCommodityPrice.get(item.getCommodityId()+"");
            if(null == price){
                throw new BizLogicException("此单中部分商品，在该门店的产品价格方案中缺失！");
            }
            stockReceiptItem.setPrice(price);
            //保存三位小数，为了计算weight_price的精准度
            stockReceiptItem.setTotalPrice(price.multiply(item.getQuantity()).setScale(3, RoundingMode.HALF_UP));
            stockReceiptItem.setExistStockQuantity(stockCollect.get(item.getCommodityId()));
            commodityList.add(stockReceiptItem);
        }
        shopStockReceipt.setCommodityList(commodityList);

        // 5. 退货入库
        returnStock(vo, shopStockReceipt);

    }

    public List<ShopStockDTO> initCommodityAndPrice(List<Long> commodityIds, Long shopId,ShopCommodityBusinessTypeEnum businessTypeEnum,String code) {
        log.info("期初库存shopId={},商品ids={}", shopId, commodityIds);
        List<ShopStockDTO> finishedStockList = new ArrayList<>();
        //期初零售价
        Map<Long, ShopCommodityPriceODTO> shopCommodityPriceMap = shopCommodityClient.queryShopCommodityPrice(QYApplicationContext.cloudShopId, commodityIds);
        List<ShopCommoditySaveDTO> shopCommodityList = new ArrayList<>(commodityIds.size());
        for (Long commodityId : commodityIds) {
            ShopCommoditySaveDTO shopCommoditySaveDTO = ShopCommoditySaveDTO.initCommodityPrice(shopId, commodityId, shopCommodityPriceMap.get(commodityId).getCommodityPrice());
            shopCommodityList.add(shopCommoditySaveDTO);
        }
        // shopCommodityClient.initCommodityAndPriceToShopCommodityListByCloud(dto);
        SaveShopCommodityIDTO saveShopCommodityIDTO = new SaveShopCommodityIDTO();
        saveShopCommodityIDTO.setBusinessType(businessTypeEnum.getCode());
        saveShopCommodityIDTO.setShopCommodityList(shopCommodityList);
        saveShopCommodityIDTO.setBusinessBillCode(code);
        saveShopCommodityClientClient.saveShopCommodity(saveShopCommodityIDTO);

        int size = commodityIds.size();
        //最多循环五次，查询不出来也要继续进行
        for (int i = 0; i < 30; i++) {
            log.info("循环次数={}", i);
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
            }
            //期初的时候走了异步，这里要新开一个线程才能查询到数据
            finishedStockList = shopCommodityService.queryShopStockCloudInit(shopId, commodityIds);
            log.info("finishedStockList.size={}", finishedStockList.size());
            if (finishedStockList.size() == size) {
                break;
            }
        }
        log.info("云超期初结束");
        return finishedStockList;
    }


    /**
     * 该方法已经要废弃，不要在用了，换成 cloudInventoryInitial
     * 取消或者退货时期初库存和进货价
     *
     * @param vo
     */
    public void inventoryInitial(InventoryInitialVO vo){
        //期初零售价
        SyncCommodityToShopCommodityIDTO dto = new SyncCommodityToShopCommodityIDTO();
        dto.setShopId(vo.getShopId());
        List<ShopCommoditySaveDTO> shopCommodityList =new ArrayList<>(vo.getCommodityPriceList().size());
        for (CommodityPriceVO commodityPriceVO : vo.getCommodityPriceList()) {
            ShopCommoditySaveDTO shopCommoditySaveDTO = ShopCommoditySaveDTO.initCommodityPrice(vo.getShopId(), commodityPriceVO.getCommodityId(), commodityPriceVO.getCommodityPrice());
            shopCommodityList.add(shopCommoditySaveDTO);
        }
        SaveShopCommodityIDTO saveShopCommodityIDTO = new SaveShopCommodityIDTO();
        saveShopCommodityIDTO.setBusinessType(ShopCommodityBusinessTypeEnum.CLOUD_ORDER_CANCEL.getCode());
        saveShopCommodityIDTO.setShopCommodityList(shopCommodityList);
        saveShopCommodityIDTO.setBusinessBillCode(vo.getReferOrderCode());
        saveShopCommodityClientClient.saveShopCommodity(saveShopCommodityIDTO);
        // 4. 门店成本为0, 成本维护
        List<Long> commodityIdList = vo.getItemList().stream().map(InventoryInitialItemVO::getCommodityId).collect(Collectors.toList());
        //获取成本价位0的记录
        // 获取剩余库存
        List<ShopStockDTO> finishedStockList = shopCommodityService.queryShopStock(vo.getShopId(), commodityIdList);
        if(SpringUtil.isEmpty(finishedStockList)){
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                log.error("Thread.sleep(2000L)", e);
            }
            finishedStockList = shopCommodityService.queryShopStock(vo.getShopId(), commodityIdList);
        }
        Map<Long, BigDecimal> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockQuantity));

        List<ShopStockReceiptItemIDTO> commodityList = new ArrayList<>(commodityIdList.size());
        ShopStockReceiptIDTO shopStockReceipt = new ShopStockReceiptIDTO();
        shopStockReceipt.setShopId(vo.getShopId());

        Map<String, BigDecimal> stockCommodityPrice = getStockInPrice(vo.getStoreId(), commodityIdList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(stockCommodityPrice) , "此单中部分商品，在该门店的产品价格方案中缺失！");

        for (InventoryInitialItemVO item : vo.getItemList()) {
            ShopStockReceiptItemIDTO stockReceiptItem = new ShopStockReceiptItemIDTO();
            stockReceiptItem.setCommodityId(item.getCommodityId());
            stockReceiptItem.setQuantity(item.getQuantity());
            BigDecimal price = stockCommodityPrice.get(item.getCommodityId() + "");
            if (null == price) {
                throw new BizLogicException("此单中部分商品，在该门店的产品价格方案中缺失！");
            }
            stockReceiptItem.setPrice(price);
            //保存三位小数，为了计算weight_price的精准度
            stockReceiptItem.setTotalPrice(price.multiply(item.getQuantity()).setScale(3, RoundingMode.HALF_UP));
            stockReceiptItem.setExistStockQuantity(stockCollect.get(item.getCommodityId()));
            commodityList.add(stockReceiptItem);
        }
        shopStockReceipt.setCommodityList(commodityList);

        // 5. 退货入库
        returnStock(vo, shopStockReceipt);
    }

    private void returnStock(InventoryInitialVO vo, ShopStockReceiptIDTO shopStockReceipt) {
        Pair<Long,String> idAndCode = new ImmutablePair<>( vo.getReferOrderId(), vo.getReferOrderCode());
        List<StockItemDTO> stockItemDTOList =  vo.getItemList().stream()
                .map( it -> new StockItemDTO(it.getCommodityId(),it.getNumber().intValue(), it.getQuantity(),null))
                .collect(Collectors.toList());

        try{
            if(SpringUtil.isNotEmpty(shopStockReceipt.getCommodityList())) {
                shopStockClient.stockReceipt(shopStockReceipt);
            }
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, vo.getStockInOutTypeEnums(),stockItemDTOList, vo.getShopId(), vo.getUserId());
            stockServiceAdapter.stockInOut(stockInOutVO);
        }catch(Exception e){
            log.error("门店成本维护失败/退货入库失败", e);
        }
    }


    /**
     * 根据OrderItemId获取ReturnOrderItem信息
     * @param orderItemIdList
     * @return
     */
    private Map<Long, XdReturnOrderItemEntity> selectReturnRecordByOid(List<Long> orderItemIdList ){
        List<XdReturnOrderItemEntity> returnOrderItemList = xdReturnOrderItemMapper.selectList(
                new LambdaQueryWrapper<XdReturnOrderItemEntity>().in(XdReturnOrderItemEntity::getOrderItemId, orderItemIdList));
        return SpringUtil.isEmpty(returnOrderItemList) ? null :
                returnOrderItemList.stream().collect(Collectors.toMap(XdReturnOrderItemEntity::getOrderItemId, it -> it, (t1, t2) -> t2));
    }

    /**
     * 根据手机号查询xs用户信息
     * @param phoneNum
     * @return
     */
    private UserMemberInfoODTO selectUserMemberInfoByPhone(String phoneNum){
        SelectUserMemberInfoIDTO idto = new SelectUserMemberInfoIDTO();
        idto.setType(1);
        idto.setUserName(phoneNum);
        return xsUserClient.selectUserMemberInfo(idto);
    }

    /**
     * 根据手机号查询xs用户信息
     * @param userIdList
     * @return
     */
    private  Map<Long, String> selectUserInfoListByUserIdList(List<Long> userIdList){
        List<UserInfoODTO> userInfoList = xsUserClient.selectUserNameByIdList(userIdList);
        Map<Long, String> userInfoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(userInfoList)){
            userInfoMap = userInfoList.stream().collect(Collectors.toMap(UserInfoODTO::getId, UserInfoODTO::getUserName));
        }
        return userInfoMap;
    }
}
