package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.ShelfTypeEnum;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelf;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 仓库货位管理
 */

@RestController
@RequestMapping("/warehouse/shelf")
@Api(value = "仓库货位管理", tags = "WarehouseShelfController")
public class WarehouseShelfController {

    @Autowired
    private WarehouseShelfService warehouseShelfService;

    @PostMapping("/insertWarehouseShelf")
    @ApiOperation(value = "添加仓库货位", notes = "添加仓库货位")
    public Integer batchInsertWarehouseShelf(@RequestBody List<WarehouseShelfDTO> list) {
        return warehouseShelfService.batchInsertWarehouseShelf(list);
    }

    /**
     *仓库货位状态更改
     * @return
     */
    @PostMapping("/updateWarehouseShelfStatus")
    @ApiOperation(value = "仓库货位状态更改", notes = "仓库货位状态更改")
    public Integer updateWarehouseShelfStatus(@RequestBody WarehouseShelfDTO warehouseShelfDTO) {
        return warehouseShelfService.updateWarehouseShelfStatus(warehouseShelfDTO);
    }

    /**
     * 查询仓库货位列表，分页
     * @param queryWarehouseShelfDTO
     * @return
     */
    @PostMapping("/queryWarehouseShelf")
    @ApiOperation(value = "查询仓库货位表", notes = "查询仓库货位表")
    public MPage<QueryWarehouseShelfResult> queryWarehouseShelf(@RequestBody QueryWarehouseShelfDTO queryWarehouseShelfDTO) {
        return warehouseShelfService.queryWarehouseShelf(queryWarehouseShelfDTO);
    }

    /**
     * 根据条件查询仓库货位，不分页
     * @param queryWarehouseShelfDTO
     * @return
     */
    @PostMapping("/queryShelfNotPage")
    @ApiOperation(value = "查询仓库货位", notes = "查询仓库货位")
    public List<QueryWarehouseShelfResult> queryShelfNotPage(@RequestBody QueryWarehouseShelfDTO queryWarehouseShelfDTO) {
        return warehouseShelfService.queryShelfNotPage(queryWarehouseShelfDTO);
    }

    @GetMapping("/queryPickShelf")
    @ApiOperation("查询所有可以用的拣货位")
    public List<WarehouseShelfListDTO> queryPickShelf(@RequestParam(value = "shelfNo", required = false) String shelfNo) {
        return warehouseShelfService.queryPickShelf(shelfNo);
    }

    @GetMapping("/queryShelfList/{warehouseId}")
    @ApiOperation(value = "根据仓库id查询空闲货位", notes = "根据仓库id查询空闲货位")
    public List<QueryWarehouseShelfResult> queryShelfList(@PathVariable("warehouseId") Long warehouseId) {
        return warehouseShelfService.queryShelfList(warehouseId);
    }


    @PostMapping("/queryShelfFree")
    @ApiOperation(value = "查询取货位/拣货位", notes = "查询取货位/拣货位")
    public MPage<ShelfFreeResult> queryShelfFree(@RequestBody ShelfFreeDTO shelfFreeDTO) {
        return warehouseShelfService.queryShelfFree(shelfFreeDTO);
    }

    @GetMapping("/shelfListByType")
    @ApiOperation(value = "根据类型查询货位列表", notes = "根据类型查询货位列表")
    public MPage<WarehouseShelfListDTO> shelfListByType(@RequestParam(value = "type",required = false)ShelfTypeEnum type, @RequestParam(value = "warehouseId", required = false) Long warehouseId
            , @RequestParam(value = "keyword", required = false) String keyword) {
        return warehouseShelfService.shelfListByType(type, warehouseId, keyword);
    }


    @ApiOperation(value = "根据仓库id查询货位列表", notes = "根据仓库id查询货位列表")
    @GetMapping("/shelfListByWarehouseId/{warehouseId}")
    public List<WarehouseShelf> shelfListByWarehouseId(@PathVariable("warehouseId") Long warehouseId) {
        return warehouseShelfService.shelfListByWarehouseId(warehouseId);
    }

    @PostMapping("/shelfImport")
    @ApiOperation(value = "货位导入", notes = "货位导入")
    public void shelfImport(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        warehouseShelfService.shelfImport(wb);
    }

    @GetMapping("/shelfSort")
    @ApiOperation(value = "货位导入", notes = "货位导入")
    public void shelfSort(@RequestParam(value = "id",required = false) Long id, @RequestParam(value = "sort",required = false) Long sort) {
        warehouseShelfService.shelfSort(id, sort);
    }

    @GetMapping("/initializeShelf")
    @ApiOperation(value = "货位初始化")
    public Boolean initializeShelf(@RequestParam(value = "shopId",required = false) Long shopId) {
        return warehouseShelfService.initializeShelf(shopId);
    }
}
