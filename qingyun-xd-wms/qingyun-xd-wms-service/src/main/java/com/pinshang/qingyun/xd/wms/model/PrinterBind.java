package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="PrinterBind对象", description="门店打印机关系绑定表")
@TableName("t_xd_printer_bind")
public class PrinterBind extends BaseEntity {

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    /**
     * 打印机编码
     */
    @ApiModelProperty(value = "打印机编码")
    private String printerCode;


    /**
     * 打印机安装位置
     */
    @ApiModelProperty(value = "打印机安装位置")
    private String printerMountingPosition;

    /**
     * 使用方类型
     * @see com.pinshang.qingyun.xd.wms.enums.UserTypeEnum
     */
    @ApiModelProperty(value = "使用方类型(1-加工点 2-打包口)")

    private Integer userType;

    /**
     * 使用方(如果使用方类型为加工点，取当前门店下的加工点，使用方类型为打包口，使用方选当前门店的打包口)
     */
    @ApiModelProperty(value = "使用方(如果使用方类型为加工点，取当前门店下的加工点，使用方类型为打包口，使用方选当前门店的打包口)")
    private Long realUserId;

}
