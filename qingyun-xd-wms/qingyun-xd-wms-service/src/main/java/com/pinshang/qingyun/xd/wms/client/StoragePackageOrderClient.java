package com.pinshang.qingyun.xd.wms.client;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xd.wms.client.dto.PackageIDTO;
import com.pinshang.qingyun.xd.wms.client.dto.QueryBoxCodeByPackageIdIDTO;
import com.pinshang.qingyun.xd.wms.client.dto.QueryBoxCodeByPackageIdODTO;
import com.pinshang.qingyun.xd.wms.client.hystrix.StoragePackageOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2022/3/21
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_STORAGE_SERVICE, fallbackFactory = StoragePackageOrderClientHystrix.class,configuration = FeignClientConfiguration.class)
public interface StoragePackageOrderClient {

    /**
     * 根据箱码获取t_dc_shop_package_order.id
     * @param boxCode
     * @return
     */
    @RequestMapping(value = "/package/web/getPackageOrderIdByBoxCode", method = RequestMethod.GET)
    List<Long> getPackageOrderIdByBoxCode(@RequestParam(value = "boxCode", required = false) String boxCode);

    /**
     * 获取箱码
     * key : packageOrderId
     * value : 箱码 List<String>
     * @param packageIDTO
     * @return
     */
    @RequestMapping(value = "/package/web/getPackageOrderBoxCode", method = RequestMethod.POST)
    Map<Long, List<String>> getPackageOrderBoxCode(@RequestBody PackageIDTO packageIDTO);

    /**
     * 获取箱码和类型
     * @param idto
     * @return
     */
    @RequestMapping(value = "/package/api/queryBoxCodeByPackageId", method = RequestMethod.POST)
    List<QueryBoxCodeByPackageIdODTO> queryBoxCodeByPackageId(@RequestBody QueryBoxCodeByPackageIdIDTO idto);
}
