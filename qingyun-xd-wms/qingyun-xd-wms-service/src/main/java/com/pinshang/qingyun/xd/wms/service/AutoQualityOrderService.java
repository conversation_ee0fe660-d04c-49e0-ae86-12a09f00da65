package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xd.wms.enums.AbnormalHandles;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.mapper.ShopMapper;
import com.pinshang.qingyun.xd.wms.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: liuZhen
 * @DateTime: 2022/7/20 15:21
 */
@Slf4j
@Service
public class AutoQualityOrderService {
    @Autowired
    private QualityOrderService service;
    @Autowired
    private ShopCommodityStockMapper shopCommodityStockMapper;
    @Autowired
    private  StockInventoryOrderService stockInventoryOrderService;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private ShopMapper shopMapper;

    public Boolean auto(Long shopId, AbnormalHandles abnormalHandles) {
        Long minShopId = 0L;
        //按门店查询
        LambdaQueryWrapper<Shop> queryShop = new LambdaQueryWrapper<Shop>();
       // queryShop.gt(Shop::getId,minShopId);
        if (shopId > minShopId) {
            queryShop.eq(Shop::getId, shopId);
        }

        //质检回正常库 新增大店不参与自动质检到临时库 --与运维确认 自动质检报废job生产上没有启动
        queryShop.notIn(Shop::getShopType,ShopTypeEnums.XSJM.getCode(),ShopTypeEnums.GF.getCode());

        List<Shop> shopList = shopMapper.selectList(queryShop);
        log.debug("需要自动质检的门店数量：{}", shopList.size());
        for (Shop shop : shopList) {
            autoByShopId(shop,abnormalHandles);
        }
        return Boolean.TRUE;
    }

    private void autoByShopId(Shop shop,AbnormalHandles abnormalHandles) {
        //查询需要自动质检的
        LambdaQueryWrapper<ShopCommodityStock> query = new LambdaQueryWrapper<ShopCommodityStock>()
                .gt(ShopCommodityStock::getQualityQuantity, "0").eq(ShopCommodityStock::getShopId, shop.getId());
        //数量会不会太多，要不要分页
        List<ShopCommodityStock> shopCommodityStocks = shopCommodityStockMapper.selectList(query);
        //获取锁库的列表
        List<Long> lockedList = stockInventoryOrderService.lockedInventoryCommodityId(shop.getId());
        log.debug("门店：{}，需要自动质检的商品数量{}", shop.getId(), shopCommodityStocks.size());

        StockQualityOrder qualityOrder = new StockQualityOrder();
        List<StockQualityOrderItem> qualityOrderItems = new ArrayList<>(shopCommodityStocks.size());
        for (ShopCommodityStock shopCommodityStock : shopCommodityStocks) {
            if (SpringUtil.isNotEmpty(lockedList)&&lockedList.contains(shopCommodityStock.getCommodityId())){
                log.info("门店:{}商品{}在锁库状态不进行自动质检",shop.getId(),shopCommodityStock.getCommodityId());
                continue;
            }
            StockQualityOrderItem item = new StockQualityOrderItem();
            item.setAbnormalDeal(abnormalHandles.getCode());
            item.setCommodityId(shopCommodityStock.getCommodityId());
            item.setNumber(shopCommodityStock.getQualityNumber());
            item.setQuantity(shopCommodityStock.getQualityQuantity());
            qualityOrderItems.add(item);
        }
        qualityOrder.setId(IdWorker.getId());
        qualityOrder.setQualityOrderCode(codeClient.createCode("QUALITY_ORDER_CODE"));
        StockUtils.INSTANCE.warehouseId();
        TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
        if (tokenInfo==null){
            tokenInfo = new TokenInfo();
            tokenInfo.setShopId(shop.getId());
            tokenInfo.setUserId(-1L);
            tokenInfo.setRealName("系统");
            FastThreadLocalUtil.setQY(tokenInfo);
        }
        //自动质检
        try{
            service.qualityHandleStockBack(qualityOrderItems, qualityOrder);
        }catch (Exception e){
            log.error("自动质检异常 shopId;{} 异常信息:{}",shop.getId(), e);
        }
        FastThreadLocalUtil.removeQY();
    }

}
