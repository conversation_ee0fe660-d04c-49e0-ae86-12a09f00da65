package com.pinshang.qingyun.xd.wms.dto.bigShop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预留库存日志
 */
@Data
public class LogReserveStockDTO {

    private Integer type;

    private Long shopId;

    private Long stallId;

    private String stallName;

    private Long commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String commodityUnitName;

    private BigDecimal reserveStock;

    /**
     *是否可售：1-是,0-否
     */
    private Integer commoditySaleStatus;

    /**
     * APP状态：0-上架、1-下架
     */
    private Integer appStatus;

    private Long createId;

    private String createCode;

    private String createName;

    private Date createTime;

}
