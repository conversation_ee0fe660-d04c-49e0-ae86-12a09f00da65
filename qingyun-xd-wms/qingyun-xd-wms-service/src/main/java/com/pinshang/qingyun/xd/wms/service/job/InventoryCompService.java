package com.pinshang.qingyun.xd.wms.service.job;

import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportIDTO;
import com.pinshang.qingyun.report.dto.pos.InventoryCompPosReportODTO;
import com.pinshang.qingyun.report.service.ReportClient;
import com.pinshang.qingyun.xd.wms.dto.InventoryCompStockOutLogODTO;
import com.pinshang.qingyun.xd.wms.dto.StockLogODTO;
import com.pinshang.qingyun.xd.wms.mapper.ShopCommodityStockMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockLogMapper;
import com.pinshang.qingyun.xd.wms.model.ShopCommodityStock;
import com.pinshang.qingyun.xd.wms.service.WeChatSendMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class InventoryCompService {

    private final WeChatSendMessageService weChatSendMessageService;

    private final DictionaryClient dictionaryClient;

    private  final StockLogMapper stockLogMapper;
    
    private final ReportClient reportClient;


    public Boolean doInventoryComp(String dateTime) {

        if(StringUtils.isBlank(dateTime)){
            return Boolean.FALSE;
        }

        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("JXCDB");
        if(dictionaryODTO != null && StringUtils.isNotBlank(dictionaryODTO.getOptionValue())) {

            String optionValue = dictionaryODTO.getOptionValue();
            List<String> commodityFirstKindNameList = Arrays.asList(optionValue.split(","));

            // 2. 获取处理后的stockLog数据
            List<InventoryCompStockOutLogODTO> stockLogODTOList = stockLogMapper.selectXdStockLog(
                    dateTime + " 00:00:00",
                    dateTime + " 23:59:59",
                    commodityFirstKindNameList
            );

            if(SpringUtil.isEmpty(stockLogODTOList)){
                log.info("日期{}没有需要比对的进销存数据",dateTime);
                return Boolean.TRUE;
            }

            InventoryCompPosReportIDTO idto = new InventoryCompPosReportIDTO();
            idto.setDate(dateTime);
            idto.setCommodityFirstKindNameList(commodityFirstKindNameList);
            List<InventoryCompPosReportODTO> inventoryCompPosReportODTOS = reportClient.queryPosReportShopCommoditySumQuantity(idto);
            if(SpringUtil.isEmpty(inventoryCompPosReportODTOS)){
                log.warn("日期{}下没有对应pos流水记录",dateTime);
                return Boolean.TRUE;
            }

            // 3. 获取库存数据并构建快速查询Map
            Map<String, InventoryCompPosReportODTO> inventoryCompPosReportODTOMap = inventoryCompPosReportODTOS
                    .stream()
                    .collect(Collectors.toMap(
                            inventoryCompPosReportODTO -> buildCacheKey(inventoryCompPosReportODTO.getShopId(), inventoryCompPosReportODTO.getCommodityCode()),
                            inventoryCompPosReportODTO -> inventoryCompPosReportODTO
                    ));

            // 4. 对比数据
            stockLogODTOList.forEach(stockLog -> {
                String key = buildCacheKey(stockLog.getShopId(), stockLog.getCommodityCode());
                InventoryCompPosReportODTO inventoryCompPosReportODTO = inventoryCompPosReportODTOMap.get(key);

                if (inventoryCompPosReportODTO == null) {
                    log.warn("pos流水表中不存在记录: shopId={}, commodityCode={}",
                            stockLog.getShopId(), stockLog.getCommodityCode());
                    weChatSendMessageService.sendWeChatMessage("pos流水表中不存在记录: shopId=["+stockLog.getShopId()+"], commodityCode=["+stockLog.getCommodityCode()+"]");
                    return;
                }

                boolean qtyMatch = compareBigDecimal(Optional.ofNullable(stockLog.getSumChangeQuantity()).orElse(BigDecimal.ZERO).abs(), Optional.ofNullable(inventoryCompPosReportODTO.getSumQuantity()).orElse(BigDecimal.ZERO).abs());

                if (!qtyMatch) {
                    log.warn("流水不一致 - 门店:商品 {}-{}, 日志数量: {}, pos数量: {}",
                            stockLog.getShopId(), stockLog.getCommodityCode(),
                            stockLog.getSumChangeQuantity(), inventoryCompPosReportODTO.getSumQuantity());
                    weChatSendMessageService.sendWeChatMessage("流水不一致 - 门店: ["+stockLog.getShopId()+"],商品：["+stockLog.getCommodityCode()+"],日志数量：["+stockLog.getSumChangeQuantity()+"],pos数量：["+inventoryCompPosReportODTO.getSumQuantity()+"]");

                }
            });

            Map<String, InventoryCompStockOutLogODTO> inventoryCompStockOutLogODTOMap = stockLogODTOList
                    .stream()
                    .collect(Collectors.toMap(
                            stockLogODTO -> buildCacheKey(stockLogODTO.getShopId(), stockLogODTO.getCommodityCode()),
                            stockLogODTO -> stockLogODTO
                    ));

            inventoryCompPosReportODTOS.forEach(posReportODTO -> {
                String key = buildCacheKey(posReportODTO.getShopId(), posReportODTO.getCommodityCode());
                InventoryCompStockOutLogODTO inventoryCompStockOutLogODTO = inventoryCompStockOutLogODTOMap.get(key);

                if (inventoryCompStockOutLogODTO == null) {
                    log.warn("进销存流水表中不存在记录: shopId={}, commodityCode={}",
                            posReportODTO.getShopId(), posReportODTO.getCommodityCode());
                    weChatSendMessageService.sendWeChatMessage("进销存流水表中不存在记录: shopId=["+posReportODTO.getShopId()+"], commodityCode=["+posReportODTO.getCommodityCode()+"]");
                }
            });
            return Boolean.TRUE;
        }

        log.info("未设置库存进销存比对告警的商品");
        return Boolean.TRUE;

    }

    // 构建缓存键
    private String buildCacheKey(Long shopId, String commodityCode) {
        return shopId + "|" + commodityCode;
    }

    // 安全比较BigDecimal（处理null和精度）
    private boolean compareBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        return a.compareTo(b) == 0;
    }




}


