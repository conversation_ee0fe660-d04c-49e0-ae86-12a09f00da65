package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * @Author: sk
 * @Date: 2024/10/17
 */
public enum FreezeStatusEnum {

    FREEZE(0,"冻结"),
    UN_FREEZE(1,"解冻"),
    ;

    private Integer code;
    private String desc;

    private FreezeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
