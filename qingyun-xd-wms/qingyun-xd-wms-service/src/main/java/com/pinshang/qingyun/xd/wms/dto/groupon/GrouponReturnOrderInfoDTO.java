package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponReturnOrderInfoDTO {

    @ApiModelProperty("001:此单已全部退完，没有可退商品了 002:此单已取消，无法退货 " +
            "003:此单未提货，无法退货 004:此单预约在：xxxxxx店，本店不能退货" +
            "005:此单已超过退货时效！")
    private String code;

    private String msg;

    @ApiModelProperty("order_item主键，即退货码")
    private Long itemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品code列表")
    private List<String> barCodeList;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品计量单位")
    private String commodityUnitName;

    /**
     * todo 测试环境实际的下单数据，在注意一下是用哪一个realQuantity
     */
//    private BigDecimal realQuantity;
    @ApiModelProperty("最多可退数量(下单数量-已退数量)")
    private Integer ableReturnNumber;

    /**
     * 目前团购不存在用积分，优惠券支付的情况。也不存在除不尽的情况
     * total_amount/quantity
     */
    @ApiModelProperty("商品单价")
    private BigDecimal price;

    @ApiModelProperty("提货门店id")
    private Long shopId;

    @ApiModelProperty("0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，5＝配送中，6＝配送成功，7＝配送失败")
    private Integer orderStatus;

    /*
    * todo 后续需要确认，是不是订单提货时间
    * */
    @ApiModelProperty("订单实际完成时间")
    private Date orderCompleteDate;



}
