package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCommodityResult {

    @ApiModelProperty(value = "加工商品")
    private List<OrderCommodityList> processCommodity;

    @ApiModelProperty(value = "非加工商品")
    private List<OrderCommodityList> noProcessCommodity;
}
