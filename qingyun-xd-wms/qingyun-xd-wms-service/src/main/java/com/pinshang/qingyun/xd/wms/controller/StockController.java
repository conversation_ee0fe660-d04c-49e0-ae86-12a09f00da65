
package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.dto.volcano.VolcanoStockIDTO;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.StockQualityOrderService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfService;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存
 * Created by chenqi on 2019/11/21.
 */
@RestController
@RequestMapping("/stock")
@Api(value = "鲜到库存", tags = "StockController")
@Slf4j
public class StockController {
    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private WarehouseShelfService shelfService;

    @Autowired
    private StockQualityOrderService stockQualityOrderService;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @PostMapping("/stockFreeze")
    @ApiOperation(value = "冻结库存", notes = "冻结库存")
    public Integer stockFreeze(@RequestBody StockIDTO stockIDTO) {
        //检验数据
        stockIDTO.checkFreezeData();

        LockUtils.checkLock(LockUtils.FREEZE_OPERATE, stockIDTO.getReferCode());
        return shopCommodityService.stockFreeze(stockIDTO.getReferId(),stockIDTO.getReferCode(),stockIDTO.getWarehouseId()
                , stockIDTO.getCommodityList(), stockIDTO.getUserId(), stockIDTO.getCommodityLimitIDTOList());
    }

    @PostMapping("/stockUnFreeze")
    @ApiOperation(value = "解冻库存", notes = "解冻库存")
    public Integer stockUnFreeze(@RequestBody StockIDTO stockIDTO) {
        //检验数据
        stockIDTO.checkFreezeData();

        LockUtils.checkLock(LockUtils.UN_FREEZE_OPERATE, stockIDTO.getReferCode());
        return shopCommodityService.stockUnFreeze(stockIDTO.getReferId(),stockIDTO.getReferCode(),stockIDTO.getWarehouseId());
    }

    @PostMapping("/queryStockList")
    @ApiOperation(value = "APP:库存查询[实时]", notes = "库存查询")
    public List<StockItemDTO> queryStockList(@RequestBody StockQueryIDTO stockQueryIDTO) {
        //检验数据
        stockQueryIDTO.checkData();
        if(SpringUtil.isEmpty(stockQueryIDTO.getCommodityList())){
            return new ArrayList<>();
        }
        List<StockItemDTO> returnList = shopCommodityService.queryRealStockList(stockQueryIDTO.getWarehouseId(), stockQueryIDTO.getCommodityList());
        stockQueryIDTO.dealReturnList(returnList);
        return returnList;
    }

    @PostMapping("/queryPlatformStockList")
    @ApiOperation(value = "APP:库存查询[第三方]", notes = "库存查询")
    public List<StockItemDTO> queryPlatformStockList(@RequestBody StockQueryIDTO stockQueryIDTO) {
        //检验数据
        stockQueryIDTO.checkData();
        List<StockItemDTO> returnList = shopCommodityService.queryRealStockList(stockQueryIDTO.getWarehouseId(), stockQueryIDTO.getCommodityList());
        int syncRate = 100;
        try {
            DictionaryODTO dictionary = dictionaryClient.getDictionaryById(DictionaryEnums.STOCK_SYNC_RATE.getId());
            syncRate = Integer.valueOf(dictionary.getOptionValue());
        } catch (Exception e) {
            log.error("获取库存同步比例失败");
        }

        for (StockItemDTO dto : returnList) {
            if(dto.getStockNumber() > 0){
                dto.setStockNumber(dto.getStockNumber() * syncRate / 100);
            }else{
                dto.setStockNumber(0);
            }
        }
        return returnList;
    }

    @PostMapping("/queryShopStockList")
    @ApiOperation(value = "库存查询[实际库存数量]", notes = "库存查询[实际库存数量]")
    public List<StockItemDTO> queryShopStockList(@RequestBody StockQueryIDTO stockQueryIDTO) {
        //检验数据
        stockQueryIDTO.checkData();
        if(SpringUtil.isEmpty(stockQueryIDTO.getCommodityList())){
            return new ArrayList<>();
        }
        List<StockItemDTO> returnList = shopCommodityService.queryStockList(stockQueryIDTO.getWarehouseId(), stockQueryIDTO.getCommodityList());
        return returnList;
    }

    @GetMapping("/queryStockPage")
    @ApiOperation(value = "库存查询", notes = "库存查询")
    public MPage<StockItemPageODTO> queryStockPage(StockQueryPageIDTO stockQueryPageIDTO) {
        //stockQueryPageIDTO.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        return shopCommodityService.queryStockPage(stockQueryPageIDTO);
    }

    @GetMapping("/exportStockList")
    @ApiOperation(value = "库存导出", notes = "库存导出")
    @FileCacheQuery(bizCode = "SHOP_STOCK_EXPORT")
    public void exportStockList(StockQueryPageIDTO stockQueryPageIDTO, HttpServletResponse response) throws IOException {
        stockQueryPageIDTO.notLimit();
        MPage<StockItemPageODTO> res = shopCommodityService.queryStockPage(stockQueryPageIDTO);
        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "库存报表" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("部门");
        tableHeader.add("门店");
        tableHeader.add("客户编码");
        tableHeader.add("商品编码");
        tableHeader.add("条形码");
        tableHeader.add("商品名称");
        tableHeader.add("前台品名");
        tableHeader.add("是否必售");
        tableHeader.add("规格");
        tableHeader.add("包装规格");
        tableHeader.add("计量单位");
        tableHeader.add("库存份数");
        tableHeader.add("库存数量");
        tableHeader.add("临时库份数");
        tableHeader.add("临时库数量");
        tableHeader.add("冻结库份数");
        tableHeader.add("上架状态");
        tableHeader.add("货位");
        tableHeader.add("是否预警");
        tableHeader.add("最低库存");
        tableHeader.add("最高库存");
        tableHeader.add("后台一级品类");
        tableHeader.add("前台一级品类");
        tableHeader.add("前台二级品类");
        StringBuilder stringBuilder = new StringBuilder();
        List<List<String>> dataList = new ArrayList<>();
        if(res!=null && SpringUtil.isNotEmpty(res.getList())) {
            for(StockItemPageODTO e : res.getList()) {
                stringBuilder.setLength(0);
                List<String> row = new ArrayList<>();
                row.add(e.getDeptName());
                row.add(e.getShopName());
                row.add(e.getStoreCode());
                row.add(e.getCommodityCode());
                row.add(e.getBarCode());
                row.add(e.getCommodityName());
                row.add(e.getCommodityAppName());
                row.add(e.getSaleStatusName());
                row.add(e.getCommoditySpec());
                row.add(e.getCommodityPackageSpec()+"");
                row.add(e.getCommodityUnitName());
                row.add(e.getStockNumber()+"");
                row.add(e.getStockQuantity()+"");
                row.add(e.getQualityNumber()+"");
                row.add(e.getQualityQuantity()+"");
                row.add(e.getFreezeNumber()+"");
                if (e.getAppStatus() != null) {
                    if (e.getAppStatus().equals(0)) {
                        row.add("上架");
                    } else if(e.getAppStatus().equals(1)) {
                        row.add("下架");
                    }
                } else {
                    row.add("");
                }

                row.add(e.getShelfNo());
                if (e.getIfWarn() != null ) {
                    if (e.getIfWarn().equals(1)) {
                        row.add("是");
                    } else {
                        row.add("否");
                    }
                } else {
                    row.add("");
                }

               // dataList.add(row);
                if (e.getMinStock() != null) {
                    row.add(e.getMinStock().toString());
                } else {
                    row.add(null);
                }
                if (e.getMaxStock() != null) {
                    row.add(e.getMaxStock().toString());
                } else {
                    row.add(null);
                }
                row.add(e.getCateName());
                row.add(e.getFirstCategoryName());
                row.add(e.getSecondCategoryName());
                dataList.add(row);
            }
        }

        //覆盖文件名, 无需扩展名
        fileName = "库存报表" + sdf.format(new Date());
        ExcelUtil.setFileNameAndHead(response,  fileName);
        List<List<String>> excelHead = tableHeader.stream().map(Collections::singletonList).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet("数据").doWrite(dataList);

        /* 已重构, 后续稳定后删除
        SXSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbookS(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcelS(response,fileName,xb);
        */
    }



    @GetMapping("/queryStock/{barCode}")
    @ApiOperation(value = "手持：库存查询[扫码]", notes = "库存查询[扫码]")
    public StockItemPageODTO queryStockByBarCode(@PathVariable("barCode") String barCode,
                                                 @RequestParam(value = "shopId",required = false)Long shopId,
                                                 @RequestParam(value = "stallId",required = false) Long stallId) {
        //大店商品查询、手持库存调整废弃，不会调用此查询接口(如果调用此接口会报错未上送shopId)，只有手持质检会调用此查询接口且会上送shopId,和stallId
        ddTokenShopIdService.processReadDdTokenShopId(shopId,stallId);
        return shopCommodityService.queryStockByBarCode(barCode,shopId,stallId);
    }


    @GetMapping("/queryStockOffLine/{barCode}")
    @ApiOperation(value = "手持：库存查询[扫码]-非线上", notes = "库存查询[扫码]-非线上")
    public StockItemPageODTO queryStockOffLineByBarCode(@PathVariable("barCode") String barCode) {
        return shopCommodityService.queryStockOffLineByBarCode(barCode);
    }

    @GetMapping("/queryStockInOutPage")
    @ApiOperation(value = "库存出入库查询", notes = "库存出入库查询")
    public MPage<StockInOutItemODTO> queryStockInOutPage(StockInOutPageIDTO stockInOutPageIDTO) {
        stockInOutPageIDTO.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        return shopCommodityService.queryStockInOutPage(stockInOutPageIDTO);
    }

    @GetMapping("/queryStockInOutDetail")
    @ApiOperation(value = "库存出入库查询", notes = "库存出入库查询")
    public StockInOutDetailODTO queryStockInOutDetail(StockInOutDetailIDTO stockInOutDetailIDTO) {
        return shopCommodityService.queryStockInOutDetail(stockInOutDetailIDTO);
    }

    @GetMapping("/queryShopStock")
    @ApiOperation(value = "门店库存查询", notes = "门店库存查询")
    public List<ShopStockDTO> queryShopStock(@RequestParam(value = "shopId",required = false) Long shopId) {
        QYAssert.isTrue(shopId != null, "门店Id不能为空");
        return shopCommodityService.queryShopStock(shopId, new ArrayList<>());
    }

    @GetMapping("/queryShopCommodityStock")
    @ApiOperation(value = "查询门店商品库存信息", notes = "查询门店商品库存、零售价信息")
    public List<ShopCommodityStockDTO> queryShopCommodityStock(@RequestParam(value = "shopId",required = false) Long shopId, @RequestParam(value = "commodityIdList",required = false) List<Long> commodityIdList) {
        QYAssert.isTrue(shopId != null, "门店Id不能为空");
        QYAssert.isTrue(commodityIdList != null, "商品IdList不能为空");
        return shopCommodityService.queryShopCommodityStock(shopId,commodityIdList);
    }

    @PostMapping("/queryShopCommodityStock2")
    @ApiOperation(value = "查询门店商品库存信息", notes = "查询门店商品库存、零售价信息")
    public List<ShopCommodityStockDTO> queryShopCommodityStock2(@RequestBody StockQueryIDTO dto) {
        QYAssert.isTrue(dto.getWarehouseId() != null, "门店Id不能为空");
        QYAssert.isTrue(dto.getCommodityList() != null, "商品IdList不能为空");
        return shopCommodityService.queryShopCommodityStock(dto.getWarehouseId(),dto.getCommodityList());
    }

    @PostMapping("/queryBigShopCommodityStock")
    @ApiOperation(value = "查询大店库存信息", notes = "查询大店库存信息")
    public List<ShopCommodityStockDTO> queryBigShopCommodityStock(@RequestBody StockQueryIDTO dto) {
        QYAssert.isTrue(dto.getWarehouseId() != null, "门店Id不能为空");
        QYAssert.isTrue(dto.getCommodityList() != null, "商品IdList不能为空");
        return shopCommodityService.queryBigShopCommodityStock(dto.getWarehouseId(), dto.getStallIdList(), dto.getStallId(), dto.getCommodityList());
    }

    @GetMapping("/queryShopCommodityStockByCommodityId")
    @ApiOperation(value = "根据commodityId查询门店商品库存信息", notes = "根据commodityId查询门店商品库存、零售价信息")
    public List<ShopCommodityStockDTO> queryShopCommodityStockByCommodityId(@RequestParam(value = "commodityId",required = false)  Long commodityId) {
        QYAssert.isTrue(commodityId != null, "门店Id不能为空");
        return shopCommodityService.queryShopCommodityStockByCommodityId(commodityId);
    }

    @GetMapping("/queryStockNumZeroCommodityIds")
    @ApiOperation(value = "查询库存为0的商品", notes = "查询库存为0的商品")
    public List<Long> queryStockNumZeroCommodityIds(@RequestParam(value = "shopId",required = false) Long shopId) {
        QYAssert.isTrue(shopId != null, "门店Id不能为空");
        return shopCommodityService.queryStockNumZeroCommodityIds(shopId);
    }

    @PostMapping("/stockReceipt")
    @ApiOperation(value = "收货", notes = "收货")
    public Boolean stockReceipt(@RequestBody StockReceiptIDTO stockReceiptIDTO) {
        log.info("收货数据={}", stockReceiptIDTO);
        //检验数据
        stockReceiptIDTO.checkData();
        stockServiceAdapter.stockReceipt(stockReceiptIDTO);

        return Boolean.TRUE;
    }

    @PostMapping("/stockShopReturn")
    @ApiOperation(value = "门店退货", notes = "门店退货总部")
    public Boolean stockShopReturn(@RequestBody StockIDTO stockIDTO) {
        //检验数据
        stockIDTO.checkData();

        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(stockIDTO.getWarehouseId());
        tokenInfo.setUserId(stockIDTO.getUserId());
        FastThreadLocalUtil.setQY(tokenInfo);

        stockServiceAdapter.checkStockReturn(stockIDTO);
        stockServiceAdapter.stockShopReturn(stockIDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/stockShopReturnList")
    @ApiOperation(value = "门店退货", notes = "门店退货总部")
    public Boolean stockShopReturnList(@RequestBody List<StockIDTO> stockIDTOList) {
        StockIDTO stock = stockIDTOList.get(0);
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setShopId(stock.getWarehouseId());
        tokenInfo.setUserId(stock.getUserId());
        FastThreadLocalUtil.setQY(tokenInfo);
        return stockServiceAdapter.stockShopReturnList(stockIDTOList);
    }

    @PostMapping("/posCouponCode")
    @ApiOperation(value = "pos折扣特价码(库存)", notes = "pos折扣特价码(库存)")
    public Boolean posCouponCode(@RequestBody StockIDTO stockIDTO) {
        //检验数据
        stockIDTO.checkData();

        ImmutablePair idAndCode = new ImmutablePair(stockIDTO.getReferId(), stockIDTO.getReferCode());
        StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.OUT_POS_CODE_NORMAL
                , stockIDTO.getCommodityList(), stockIDTO.getWarehouseId(), stockIDTO.getUserId());
        stockServiceAdapter.stockInOut(stockInOutVO);
        return Boolean.TRUE;
    }

    @PostMapping("/queryShopShelf")
    @ApiOperation(value = "门店货位查询", notes = "门店货位查询")
    public List<ShopShelfResDTO> queryShopShelf(@RequestBody ShopShelfReqDTO reqDTO) {
        reqDTO.checkData();
        return shelfService.queryShopShelf(reqDTO);
    }

    @PostMapping("/queryShopAbnormal")
    @ApiOperation(value = "门店损耗查询", notes = "门店损耗查询")
    public List<ShopAbnormalResDTO> queryShopAbnormal(@RequestBody ShopAbnormalReqDTO reqDTO) {
        reqDTO.checkData();
        return stockQualityOrderService.queryShopAbnormal(reqDTO);
    }


    /**
     * 定时同步饿了么库存
     */
    @PostMapping("/shopStockSync")
    @ApiOperation(value = "库存同步", notes = "库存同步")
    public Boolean shopStockSync(){
        shopCommodityService.shopStockSync();
        return Boolean.TRUE;
    }

    /**
     * 手动同步第三方库存
     * @param shopId
     * @param commodityIdList
     */
    @RequestMapping(value = "/hand/shopStockSync", method = RequestMethod.POST)
    public Boolean handShopStockSync(@RequestParam(value = "shopId",required = false) Long shopId, @RequestBody List<Long> commodityIdList){
        shopCommodityService.doStockSync(shopId, commodityIdList);
        return Boolean.TRUE;
    }

    @ApiOperation(value = "库存预警列表", notes = "库存预警列表")
    @RequestMapping(value = "/queryCommodityStockWarn", method = RequestMethod.POST)
    public MPage<StockWarnListDTO> queryCommodityStockWarn(@RequestBody StockWarnDTO stockWarnDTO) {
        return shopCommodityService.queryCommodityStockWarn(stockWarnDTO);
    }

    @ApiOperation(value = "库存预警设置", notes = "库存预警设置")
    @RequestMapping(value = "/stockWarnSet", method = RequestMethod.POST)
    public Boolean stockWarnSet(@RequestBody StockWarnSetDTO stockWarnSetDTO) {
        shopCommodityService.stockWarnSet(stockWarnSetDTO);
        return Boolean.TRUE;
    }

    /**
     * 期初门店库存信息
     * @param list
     * @return
     */
    @RequestMapping(value = "/initShopCommodityStock", method = RequestMethod.POST)
    public int initShopCommodityStock(@RequestBody List<ShopCommodityStockInitIDTO> list){
        return stockServiceAdapter.initShopCommodityStock(list);
    }

    /**
     * 门店盘点查询指定更新时间后的商品库存，减少每次操作数据
     * @param stockQueryIDTO
     * @return
     */
    @PostMapping("/queryAssignShopStockList")
    @ApiOperation(value = "库存查询[实际库存数量]", notes = "库存查询[实际库存数量]")
    public List<StockItemDTO> queryAssignShopStockList(@RequestBody StockQueryIDTO stockQueryIDTO) {
        QYAssert.isTrue(stockQueryIDTO.getWarehouseId() != null, "仓库id不能为空");
        List<StockItemDTO> returnList = shopCommodityService.queryAssignShopStockList(stockQueryIDTO);
        return returnList;
    }

    /**
     * 加工-库存
     * @param stockProcessIDTO
     * @return
     */
    @PostMapping("/stockProcessing")
    @ApiOperation(value = "加工-库存", notes = "加工-库存")
    public Boolean stockProcessing(@RequestBody StockProcessIDTO stockProcessIDTO) {
        //检验数据
        stockProcessIDTO.checkData();
        stockServiceAdapter.stockProcessing(stockProcessIDTO);

        return Boolean.TRUE;
    }

    /**
     * 库存处理
     * @param stockIDTO
     * @return
     */
    @PostMapping(value = "/modifyStock")
    public Boolean modifyStock(@RequestBody StockIDTO stockIDTO){
        stockServiceAdapter.modifyStock(stockIDTO);
        return Boolean.TRUE;
    }

    /**
     * 处理volcano 餐饮库存
     * @param stockIDTO
     * @return
     */
    @PostMapping(value = "/modifyVolcanoStock")
    public Boolean modifyVolcanoStock(@RequestBody VolcanoStockIDTO stockIDTO){
        //检验数据
        stockIDTO.checkData();
        stockServiceAdapter.modifyVolcanoStock(stockIDTO);
        return Boolean.TRUE;
    }

    @GetMapping("/queryNegativeShopStockList")
    @ApiOperation(value = "根据shopId查询库存<= 0的", notes = "根据shopId查询库存<= 0的")
    public List<StockItemDTO> queryNegativeShopStockList(@RequestParam(value = "shopId",required = false) Long shopId) {
        return shopCommodityService.queryNegativeShopStockList(shopId);
    }

    @PostMapping("/queryShopCommodityStockByCateId")
    @ApiOperation(value = "根据大类id查询门店商品库存信息", notes = "根据大类id查询门店商品库存信息")
    public List<ShopCommodityStockByCateIdODTO> queryShopCommodityStockByCateId(@RequestBody ShopCommodityStockByCateIdIDTO dto ) {
        QYAssert.isTrue(dto.getShopId() != null, "门店Id不能为空");
        return shopCommodityService.queryShopCommodityStockByCateId(dto);
    }

    @PostMapping("/transportQuantityAdd")
    @ApiOperation("在途库存添加")
    public Boolean transportQuantityAdd() {
        stockServiceAdapter.transportQuantityAdd();
        return Boolean.TRUE;
    }

    @RequestMapping(value = "/stockFreezeReservation/{date}", method = RequestMethod.GET)
    @ApiOperation("冻结t+1预订单库存")
    public Boolean stockFreezeReservation(@PathVariable("date") String date){
        return shopCommodityService.stockFreezeReservation(date);
    }

    @PostMapping("/xiaoeTongStock")
    @ApiOperation("小鹅通库存处理")
    public XiaoeTongStockODTO xiaoeTongStockProcess(@RequestBody XiaoeTongStockIDTO req) {
        req.checkData();
        stockServiceAdapter.processXiaoeTongStock(req);
        return new XiaoeTongStockODTO();
    }
}
