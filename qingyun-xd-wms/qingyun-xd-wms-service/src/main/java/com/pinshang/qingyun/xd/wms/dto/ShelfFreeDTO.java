package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShelfFreeDTO extends Pagination<ShelfFreeResult> {

    @ApiModelProperty(value = "货位编号")
    private Long shelfId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "货位类型 1-拣货位,2-配送取货位,3-加工点取货位")
    private Integer type;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    private String keyword;
}
