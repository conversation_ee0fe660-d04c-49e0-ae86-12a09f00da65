/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.xd.wms.util;

/**
 * <p>
 * 字符串工具类
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/10/11 14:37
 */
public class StrUtil {

    /**
     * 判断字符串是否包含中文字符
     *
     * @param str 待检测的字符串
     * @return 如果字符串包含中文字符，则返回 true；否则返回 false
     */
    public static boolean containsChinese(String str) {
        return str.chars().anyMatch(c -> c >= 0x4e00 && c <= 0x9fff);
    }
}