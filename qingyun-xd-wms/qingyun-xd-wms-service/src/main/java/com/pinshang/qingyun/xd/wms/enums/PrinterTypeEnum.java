package com.pinshang.qingyun.xd.wms.enums;

/**
 * 打印机类型
 * <AUTHOR>
 */
public enum PrinterTypeEnum {
    VOUCHER(1,"小票打印机"),
    DESKTOP(2,"台式打印机"),
    LABEL(3,"标签打印机"),

    ;
    private Integer code;
    private String name;

    PrinterTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PrinterTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (PrinterTypeEnum typeEnum : PrinterTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
