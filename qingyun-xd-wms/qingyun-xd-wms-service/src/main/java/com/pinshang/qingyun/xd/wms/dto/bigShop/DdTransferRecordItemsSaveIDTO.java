package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("DdTransferRecordItemsSaveIDTO")
public class DdTransferRecordItemsSaveIDTO {

    /**
     * 移出库区 1拣货区  2存储区 3排面区 4临时库
     */
    private Integer outStoreArea;

    /**
     * 移出货位code
     */
    private String outGoodsAllocationCode;


    /**
     * 移入库区 1拣货区  2存储区 3排面区 4临时库
     */
    private Integer inStoreArea;

    /**
     * 移入货位code
     */
    private String inGoodsAllocationCode;

    /**
     * 移库数量
     */
    private BigDecimal moveQuantity;


}
