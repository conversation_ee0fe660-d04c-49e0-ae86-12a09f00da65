package com.pinshang.qingyun.xd.wms.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.xd.wms.dto.bigShop.PartitionPickConfirmOrCompleteIDTO;
import com.pinshang.qingyun.xd.wms.model.bigShop.DdPickPartitionOrder;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.HandoverDeliveryListenerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
@OnlineSwitchWatcher
public class DdPartitionPickListener {

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    @KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_HANDOVER_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_HANDOVER_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_HANDOVER_TOPIC
            },
            containerFactory = "kafkaListenerContainerFactory",
            errorHandler = "kafkaConsumerErrorHandler")
    public void handoverDeliveryListen(String message) {
        log.warn("topic:" + KafkaTopicConstant.DD_PARTITION_PICK_HANDOVER_TOPIC + " ====================message==" + message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        Long pickOrderId;
        List<DdPickPartitionOrder> ddPickPartitionOrders = Collections.emptyList();
        try {
            // 历史 消息内容只有一个拣货单id
            pickOrderId = Long.valueOf(messageWrapper.getData().toString());
        } catch (Exception e) {
            // 新消息内容包含拣货单id和拣货单信息
            HandoverDeliveryListenerVO msg = JSON.parseObject(messageWrapper.getData().toString(), HandoverDeliveryListenerVO.class);
            pickOrderId = msg.getPickOrderId();
            ddPickPartitionOrders = msg.getDdPickPartitionOrders();
        }
        ddPickPartitionOrderService.finishPickOrderIfAllPartitionPickOrderFinish(pickOrderId, ddPickPartitionOrders, null);
    }

    @KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE_TOPIC + "xdWmsKafkaListenerContainerFactory",
            topics = {
                    "${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE_TOPIC,
                    "${spring.application.name}-Retry-${application.name.switch}" + KafkaTopicConstant.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE_TOPIC
            },
            containerFactory = "kafkaListenerContainerFactory",
            errorHandler = "kafkaConsumerErrorHandler")
    public void confitmOrCompletePickListen(String message) {
        log.warn("topic:" + KafkaTopicConstant.DD_PARTITION_PICK_CONFIRM_OR_COMPLETE_TOPIC + " ====================message==" + message);
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        PartitionPickConfirmOrCompleteIDTO msg = JSON.parseObject(messageWrapper.getData().toString(), PartitionPickConfirmOrCompleteIDTO.class);

        if (PartitionPickConfirmOrCompleteIDTO.CONFIRM.equals(msg.getOperateType())) {
            // ddPickPartitionOrderService.processConfirmPickMsg(msg);
        } else {
            ddPickPartitionOrderService.processCompletePickMsg(msg);
        }
    }


}
