package com.pinshang.qingyun.xd.wms.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/31 14:16
 * @Description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOutCollectODTO {
    @ApiModelProperty("部门")
    @ExcelProperty("部门")
    private String deptName;
    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    private String shopName;
    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("条形码")
    @ExcelProperty("条形码")
    private String barCode;
    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("前台名称")
    @ExcelProperty("前台名称")
    private String commodityAppName;
    @ApiModelProperty("规格")
    @ExcelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("计量单位")
    @ExcelProperty("计量单位")
    private String commodityUnitName;
    @ApiModelProperty("是否称重 0非称重 1称重")
    @ExcelProperty("是否称重")
    private String isWeightStr;
    @ApiModelProperty("库存数量")
    @ExcelProperty("库存数量")
    private BigDecimal stockQuantity;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("提取时间点")
    @ExcelProperty("提取时间点")
    private Date collectTime;
    @ApiModelProperty("是否称重 0非称重 1称重")
    @ExcelIgnore
    private Integer isWeight;
    @ExcelIgnore
    private Long shopId;
    @ExcelIgnore
    private Long commodityId;
    @ExcelIgnore
    private List<String> barCodeList;

    public void setIsWeight(Integer isWeight) {
        this.isWeightStr = YesOrNoEnums.YES.getCode().equals(isWeight) ? "是" : "否";
    }
}
