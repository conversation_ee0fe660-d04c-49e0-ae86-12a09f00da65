package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.StockAllotOrderStatusEnums;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.StockAllotOrderService;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/stock/allot")
@Api(value = "库存调拨管理", tags = "StockAllotOrderController")
@RequiredArgsConstructor
public class StockAllotOrderController {


    private final StockAllotOrderService stockAllotOrderService;
    private final DdTokenShopIdService ddTokenShopIdService;
    private final IRenderService renderService;


    @PostMapping("apply")
    @ApiOperation(value = "提交调拨申请", notes = "提交调拨申请")
    public Boolean apply(@RequestBody StockAllotOrderApplyDTO stockAllotOrderApplyDTO) {
        LockUtils.checkLock(LockUtils.STOCK_ALLOT_APPLY, StockUtils.INSTANCE.warehouseId() + "_" + stockAllotOrderApplyDTO.getAllotType());

        stockAllotOrderService.apply(stockAllotOrderApplyDTO);
        return Boolean.TRUE;
    }

    @GetMapping("/detail/{stockAllotId}")
    @ApiOperation(value = "根据id查询申请单详情", notes = "根据id查询申请单详情")
    @MethodRender
    public StockAllotOrderDetailResult detailById(@PathVariable("stockAllotId") Long stockAllotId) {
        return stockAllotOrderService.detailById(stockAllotId);
    }

    @PostMapping("allotInList")
    @ApiOperation(value = "调拨入库列表", notes = "调拨入库列表")
    public MPage<StockAllotOrderListDTO> allotInList(@RequestBody StockAllotOrderQueryDTO dto) {
        dto.setInShopId(StockUtils.INSTANCE.warehouseId());
        ddTokenShopIdService.processReadDdTokenShopId(dto.getInShopId(),dto.getInStallId(),true);
        dto.setUserId(FastThreadLocalUtil.getQY().getUserId());
        MPage<StockAllotOrderListDTO> page = stockAllotOrderService.allotInList(dto);
        renderService.render(page.getList(),"/stock/allot/allotInList");
        return page;
    }

    @GetMapping("allotOutListPda")
    @ApiOperation(value = "pda调拨出库列表", notes = "pda调拨出库列表")
    @MethodRender
    public List<StockAllotOrderListDTO> allotOutListPda() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        StockAllotOrderQueryDTO dto = new StockAllotOrderQueryDTO();
        dto.setOutShopId(tokenInfo.getShopId());
        dto.setUserId(tokenInfo.getUserId());
        //只查询审核通过的
        dto.setStatus(StockAllotOrderStatusEnums.AUDIT_SUCCESS.getCode());
        dto.notLimit();
        return stockAllotOrderService.allotOutListPda(dto).getList();
    }

    @PostMapping("allotOutList")
    @ApiOperation(value = "调拨出库列表", notes = "调拨出库列表")
    public MPage<StockAllotOrderListDTO> allotOutList(@RequestBody StockAllotOrderQueryDTO dto) {
        dto.setOutShopId(StockUtils.INSTANCE.warehouseId());
        ddTokenShopIdService.processReadDdTokenShopId(dto.getOutShopId(),dto.getOutStallId(),true);
        dto.setUserId(FastThreadLocalUtil.getQY().getUserId());
        MPage<StockAllotOrderListDTO> page = stockAllotOrderService.allotOutListPda(dto);
        renderService.render(page.getList(),"/stock/allot/allotOutList");
        return page;
    }

    @PostMapping("allotList")
    @ApiOperation(value = "总部：出入库调拨单列表", notes = "总部：出入库调拨单列表")
    public MPage<StockAllotOrderListDTO> allotList(@RequestBody StockAllotOrderQueryDTO dto) {
        MPage<StockAllotOrderListDTO> page = stockAllotOrderService.allotList(dto);
        renderService.render(page.getList(),"/stock/allot/allotList");
        return page;
    }

    @PostMapping("singleAllotList")
    @ApiOperation(value = "单门店出入库调拨单列表", notes = "单门店出入库调拨单列表")
    public MPage<StockAllotOrderListDTO> singleAllotList(@RequestBody StockAllotSingleQueryDTO dto) {
        dto.setOutShopId(StockUtils.INSTANCE.warehouseId());
        dto.setInShopId(StockUtils.INSTANCE.warehouseId());
        MPage<StockAllotOrderListDTO> page = stockAllotOrderService.singleAllotList(dto);
        renderService.render(page.getList(),"/stock/allot/singleAllotList");
        return page;
    }


    @PostMapping("/audit/{stockAllotId}")
    @ApiOperation(value = "审核通过", notes = "审核通过")
    public Boolean audit(@PathVariable("stockAllotId") Long stockAllotId) {
        LockUtils.checkLock(LockUtils.STOCK_ALLOT, stockAllotId.toString());
        stockAllotOrderService.audit(stockAllotId);
        return Boolean.TRUE;
    }

    @PostMapping("/auditRejected/{stockAllotId}")
    @ApiOperation(value = "审核驳回", notes = "审核驳回")
    public Boolean auditRejected(@PathVariable("stockAllotId") Long stockAllotId) {
        LockUtils.checkLock(LockUtils.STOCK_ALLOT, stockAllotId.toString());

        stockAllotOrderService.auditRejected(stockAllotId);
        return Boolean.TRUE;
    }

    @PostMapping("/allotCancel/{stockAllotId}")
    @ApiOperation(value = "调拨取消", notes = "调拨取消")
    public Boolean allotCancel(@PathVariable("stockAllotId") Long stockAllotId) {
        LockUtils.checkLock(LockUtils.STOCK_ALLOT, stockAllotId.toString());

        stockAllotOrderService.allotCancel(stockAllotId);
        return Boolean.TRUE;
    }

    @PostMapping("/allotOut")
    @ApiOperation(value = "调拨出库", notes = "调拨出库")
    public Boolean allotOut(@RequestBody StockAllotOutDTO stockAllotOutDTO){
        stockAllotOutDTO.checkData();
        LockUtils.checkLock(LockUtils.STOCK_ALLOT, stockAllotOutDTO.getStockAllotId().toString());

        stockAllotOrderService.allotOut(stockAllotOutDTO);
        return Boolean.TRUE;
    }

    @PostMapping("/allotIn/{stockAllotId}")
    @ApiOperation(value = "调拨入库", notes = "调拨入库")
    public Boolean allotIn(@PathVariable("stockAllotId") Long stockAllotId) {
        LockUtils.checkLock(LockUtils.STOCK_ALLOT, stockAllotId.toString());

        stockAllotOrderService.allotIn(stockAllotId);
        return Boolean.TRUE;
    }

    @RequestMapping(value = "/findSettleOrderItemListByStockAllotIds",method = RequestMethod.POST)
    @ApiOperation(value = "获取调拨明细", notes = "获取调拨明细")
    public List<OrderSettleItemODTO> findSettleOrderItemListByStockAllotIds(@RequestBody OrderSettleIDTO orderSettleIDTO){
        return stockAllotOrderService.findListByStockAllotIds(orderSettleIDTO.getIds());
    }

    @RequestMapping(value =  "/findInSettleOrderListByStoreIds",method = RequestMethod.POST)
    @ApiOperation(value = "获取调拨", notes = "获取调拨")
    public List<OrderSettleODTO> findInSettleOrderListByStoreIds(@RequestBody OrderSettleIDTO orderSettleIDTO){
        return stockAllotOrderService.findInSettleOrderListByStoreIds(orderSettleIDTO);
    }

    @RequestMapping(value =  "/findOutSettleOrderListByStoreIds",method = RequestMethod.POST)
    @ApiOperation(value = "获取调拨", notes = "获取调拨")
    public List<OrderSettleODTO> findOutSettleOrderListByStoreIds(@RequestBody OrderSettleIDTO orderSettleIDTO){
        return stockAllotOrderService.findOutSettleOrderListByStoreIds(orderSettleIDTO);
    }

    @RequestMapping(value =  "/jobAllotIn",method = RequestMethod.POST)
    @ApiOperation(value = "跑JOB，超过48小时没有入库的，自动入库")
    public Boolean jobAllotIn() {
        return stockAllotOrderService.jobAllotIn();
    }

    @ApiOperation(value = "调拨单据支持 to纪锋")
    @RequestMapping(value =  "/stockAllotOrderSupportPage",method = RequestMethod.POST)
    public PageInfo<StockAllotOrderSupportDTO> stockAllotOrderSupportPage(@RequestBody StockAllotOrderSupportIDTO dto) {
        return stockAllotOrderService.stockAllotOrderSupportPage(dto);
    }

}
