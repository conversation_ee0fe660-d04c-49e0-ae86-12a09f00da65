package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickOrderDTO extends Pagination<PickOrderListResult> {

    @ApiModelProperty(value = "拣货单code")
    private String pickCode;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "拣货单状态(0=待拣货，1＝拣货中，2＝拣货完成，3＝已取消")
    private Integer pickStatus;

    @ApiModelProperty(value = "拣货单状态可以查询多个")
    private List<Integer> pickStatusList;

    @ApiModelProperty(value = "拣货人")
    private Long pickId;

    @ApiModelProperty(value = "拣货人姓名")
    private String employeeName;

    @ApiModelProperty(value = "预约开始时间")
    private String orderDeliveryBeginTime;

    @ApiModelProperty(value = "预约结束时间")
    private String orderDeliveryEndTime;

    @ApiModelProperty(value = "是否缺货 1缺货 0不缺货")
    private Integer stockOutStatus;

    /**
     * 手持端专用
     */
    @ApiModelProperty(value = "1表示手持段")
    private Integer type;

    /**
     * 0=普通订单 1=团购订单 2=云超订单
     */
    private Integer orderType;

    private List<Integer> orderTypeList;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty(value = "打包口")
    private String packingPort;

}
