package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/10/15
 */
@Data
public class DdCommodityODTO {

    private String commodityId;


    @ApiModelProperty("商品编码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityCode, keyName = "commodityId")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commoditySpec, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("条码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barcode;

    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityUnit, keyName = "commodityId")
    private String commodityUnitName;


    @ApiModelProperty("限量销售份数")
    private Integer limitNumber;

}
