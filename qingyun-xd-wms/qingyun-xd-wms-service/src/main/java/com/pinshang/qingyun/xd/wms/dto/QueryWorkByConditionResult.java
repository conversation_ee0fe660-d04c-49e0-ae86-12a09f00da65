package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkByConditionResult {

    @ApiModelProperty(value = "加工点id")
    private Long id;

    @ApiModelProperty(value = "加工点编号")
    private String workNo;

    @ApiModelProperty(value = "加工点名字")
    private String workName;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "状态 1启用 0停用")
    private Integer status;

    @ApiModelProperty(value = "加工点商品数量")
    private Integer count;
}
