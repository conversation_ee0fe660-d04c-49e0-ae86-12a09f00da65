package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xd.wms.dto.OrderCommodityList;
import com.pinshang.qingyun.xd.wms.dto.OrderCommodityResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseShelfnoCommodityDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.PartitionOrderCommodityResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.PickPartitionOrderListResult;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderItemMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockFreezeLogMapper;
import com.pinshang.qingyun.xd.wms.mapper.WarehouseShelfCommodityMapper;
import com.pinshang.qingyun.xd.wms.model.PickOrder;
import com.pinshang.qingyun.xd.wms.model.PickOrderItem;
import com.pinshang.qingyun.xd.wms.model.StockFreezeLog;
import com.pinshang.qingyun.xd.wms.model.bigShop.PickNumberSummary;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class PickOrderItemService extends ServiceImpl<PickOrderItemMapper, PickOrderItem> {

    @Autowired
    private PickOrderItemMapper pickOrderItemMapper;

    @Autowired
    private WarehouseShelfCommodityMapper warehouseShelfCommodityMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private StockFreezeLogMapper stockFreezeLogMapper;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;

    @Autowired
    private IRenderService renderService;

    /**
     * 后端：根据拣货单id查询拣货详情
     *
     * @param pickOrderId
     * @return
     */
    public OrderCommodityResult orderCommodityDetail(Long pickOrderId) {
        return buildOrderCommodity(pickOrderId);
    }

    /**
     * 手持：根据拣货单id查询拣货商品列表
     *
     * @param pickOrderId
     * @return
     */
    public List<OrderCommodityList> orderCommodityList(Long pickOrderId) {
        List<OrderCommodityList> res = new ArrayList<>();
        OrderCommodityResult orderCommodityResult = buildOrderCommodity(pickOrderId);
        res.addAll(orderCommodityResult.getNoProcessCommodity());
        res.addAll(orderCommodityResult.getProcessCommodity());
        return res;
    }

    //1加工商品，0非加工商品
    private OrderCommodityResult buildOrderCommodity(Long pickOrderId) {
        OrderCommodityResult res = null;
        List<OrderCommodityList> list = pickOrderItemMapper.orderCommodityList(pickOrderId);
        if (SpringUtil.isNotEmpty(list)) {
            PickOrder pickOrder = pickOrderMapper.selectById(pickOrderId);
            //查询冻结数量
            LambdaQueryWrapper query = new LambdaQueryWrapper<StockFreezeLog>()
                    .eq(StockFreezeLog::getReferCode, pickOrder.getOrderCode())
                    .ge(StockFreezeLog::getQuantity, 0);
            List<StockFreezeLog> freezeLogs = stockFreezeLogMapper.selectList(query);
            Map<Long, BigDecimal> freezeMap = null;
            if (!CollectionUtils.isEmpty(freezeLogs)) {
                //          freezeMap = freezeLogs.stream().collect(Collectors.groupingBy(StockFreezeLog::getCommodityId,Collectors.reducing(BigDecimal.ZERO,StockFreezeLog::getQuantity,BigDecimal::add)));
                freezeMap = freezeLogs.stream().collect(Collectors.groupingBy(StockFreezeLog::getCommodityId, Collectors.reducing(BigDecimal.ZERO, StockFreezeLog::getQuantity, BigDecimal::add)));
            }

            res = new OrderCommodityResult();
            List<OrderCommodityList> processCommodity = new ArrayList();
            List<OrderCommodityList> noProcessCommodity = new ArrayList();
            List<Long> commoditys = list.stream().map(OrderCommodityList::getCommodityId).collect(Collectors.toList());
            List<WarehouseShelfnoCommodityDTO> shelfnoCommodityDTOList = warehouseShelfCommodityMapper.queryShelfnoByCommodity(StockUtils.INSTANCE.warehouseId(), commoditys);
            Map<Long, String> map = shelfnoCommodityDTOList.stream().collect(Collectors.toMap(WarehouseShelfnoCommodityDTO::getCommodityId, WarehouseShelfnoCommodityDTO::getShelfNo));
            //条形码
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commoditys);
            Integer freezeNumber = null;
            Integer diff = null;
            for (OrderCommodityList e : list) {
                //计算缺货份数(老数据没有freezeLogs,默认为不缺货)
                e.setStockOutNumber(0);
                if (null != freezeMap) {
                    freezeNumber = freezeMap.get(e.getCommodityId()) != null ? freezeMap.get(e.getCommodityId()).intValue() : null;
                    if (null != freezeNumber && null != e.getStockNumber()) {
                        //仓库拣货单明细表份数标记  份数改数量
                        diff = freezeNumber - e.getStockNumber();
                        e.setStockOutNumber(diff > -1 ? 0 : -diff);
                        freezeMap.put(e.getCommodityId(), diff > -1 ? new BigDecimal(diff) : BigDecimal.ZERO);
                    }
                }
                if (null == e.getWorkOrderId()) {
                    //非加工商品
                    e.setShelfNo(map.get(e.getCommodityId()));
                    e.setBarCodeList(longListMap.get(e.getCommodityId()));
                    noProcessCommodity.add(e);
                } else {
                    //加工商品
                    e.setBarCodeList(longListMap.get(e.getCommodityId()));
                    processCommodity.add(e);
                }
            }
            res.setProcessCommodity(processCommodity);
            res.setNoProcessCommodity(noProcessCommodity);
        }
        return res;
    }

    @Transactional
    public List<PickOrderItem> listByPickPartitionOrderIdList(List<Long> pickPartitionOrderIdList) {

        if (SpringUtil.isEmpty(pickPartitionOrderIdList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<PickOrderItem> queryWrapper = new LambdaQueryWrapper<PickOrderItem>()
                .in(PickOrderItem::getPickPartitionOrderId, pickPartitionOrderIdList);
        return pickOrderItemMapper.selectList(queryWrapper);
    }

    public List<PickOrderItem> listByPickPartitionOrderIdListAndCommodityId(Map<Long, List<Long>> pickPartitionOrderIdMapItemIds, Long commodityId) {

        if (SpringUtil.isEmpty(pickPartitionOrderIdMapItemIds) || Objects.isNull(commodityId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PickOrderItem> wrapper = new LambdaQueryWrapper<>();

        pickPartitionOrderIdMapItemIds.forEach((pickPartitionOrderId, pickOrderItemIds) -> wrapper.or(
                wrapper1 -> wrapper1.eq(PickOrderItem::getPickPartitionOrderId, pickPartitionOrderId)
                        .in(PickOrderItem::getId, pickOrderItemIds)
                        .eq(PickOrderItem::getCommodityId, commodityId)
        ));

        return pickOrderItemMapper.selectList(wrapper);
    }

    public List<PickOrderItem> listProcessByPickPartitionOrderIdList(Long pickPartitionOrderId, Long pickOrderItemId, Long commodityId) {

        if (Objects.isNull(pickPartitionOrderId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<PickOrderItem> queryWrapper = new LambdaQueryWrapper<PickOrderItem>()
                .eq(PickOrderItem::getPickPartitionOrderId, pickPartitionOrderId)
                .eq(Objects.nonNull(pickOrderItemId), PickOrderItem::getId, pickOrderItemId)
                .eq(Objects.nonNull(commodityId), PickOrderItem::getCommodityId, commodityId)
                .eq(PickOrderItem::getIsProcess, YesOrNoEnums.YES.getCode());
        return pickOrderItemMapper.selectList(queryWrapper);
    }


    public List<PickOrderItem> listByPickOrderId(Long pickOrderId) {
        QYAssert.notNull(pickOrderId, "拣货单id不能为空");

        return pickOrderItemMapper.selectList(
                new LambdaQueryWrapper<PickOrderItem>()
                        .eq(PickOrderItem::getPickOrderId, pickOrderId)
        );
    }

    public PartitionOrderCommodityResult partitionOrderCommodityDetail(Long pickPartitionOrderId) {

        PickPartitionOrderListResult pickPartitionOrderListResult = ddPickPartitionOrderService.partitionPickOrderById(pickPartitionOrderId);
        QYAssert.isTrue(Objects.nonNull(pickPartitionOrderListResult), "分区拣货子单不存在");

        PartitionOrderCommodityResult result = new PartitionOrderCommodityResult();
        renderService.render(pickPartitionOrderListResult, "/pick/order/item/partitionOrderCommodityDetail/{pickPartitionOrderId}");
        result.setPickPartitionOrderListResult(pickPartitionOrderListResult);


        List<OrderCommodityList> list = pickOrderItemMapper.partitionOrderCommodityList(pickPartitionOrderId);
        if (SpringUtil.isNotEmpty(list)) {

            List<OrderCommodityList> processCommodity = new ArrayList();
            List<OrderCommodityList> noProcessCommodity = new ArrayList();
            List<Long> commoditys = list.stream().map(OrderCommodityList::getCommodityId).collect(Collectors.toList());
            //条形码
            Map<Long, List<String>> longListMap = commodityBarCodeService.queryCommodityBarCodeList(commoditys);
            for (OrderCommodityList e : list) {
                e.setBarCodeList(longListMap.get(e.getCommodityId()));
                if (null == e.getWorkOrderId()) {
                    //非加工商品
                    noProcessCommodity.add(e);
                } else {
                    //加工商品
                    processCommodity.add(e);
                }
            }
            result.setProcessCommodity(processCommodity);
            result.setNoProcessCommodity(noProcessCommodity);
        }

        return result;
    }

    public List<PickNumberSummary> selectPickNumberSummaryByPickOrderIds(List<Long> pickOrderIds) {
        return pickOrderItemMapper.selectPickNumberSummaryByPickOrderIds(pickOrderIds);
    }

}
