package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2019/12/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityODTO {

    /**商品ID**/
    private Long commodityId;

    private String barCode;

    private String commodityName;
    private String commodityCode;

    /** 商品状态:1-正常,0-淘汰(t_xs_shop_commodity) */
    private Integer commodityStatus;
    /** 淘汰状态:0-淘汰,1-正常(t_commodity) */
    private Integer status;

    /** 是否可售:1-是,0-否(t_xs_shop_commodity) */
    private Integer commoditySaleStatus;
    /** 是否可采:1-可采,0-否,不可采(t_xs_shop_commodity_purchase_status) */
    private Integer commodityPurchaseStatus;

    /** 可采状态:1-可采，0-不可采(t_commodity) */
    private Integer purchaseStatus;
}
