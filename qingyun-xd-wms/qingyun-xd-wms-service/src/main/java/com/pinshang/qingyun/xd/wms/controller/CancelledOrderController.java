package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderIDTO;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderItemODTO;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderODTO;
import com.pinshang.qingyun.xd.wms.dto.CancelledOrderReceiveIDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.CancelledOrderService;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@RestController
@RequestMapping("/cancelledOrder")
@Api(value = "取消单", tags = "CancelledOrderController")
public class CancelledOrderController {

    @Autowired
    private CancelledOrderService cancelledOrderService;

    /**
     * 查询取消单列表，分页
     * @param cancelledOrderIDTO
     * @return
     */
    @PostMapping("/queryCancelledOrderPage")
    @ApiOperation(value = "查询取消单列表，分页", notes = "查询取消单列表，分页")
    public MPage<CancelledOrderODTO> queryCancelledOrderPage(@RequestBody CancelledOrderIDTO cancelledOrderIDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        cancelledOrderIDTO.setShopId(tokenInfo.getShopId());
        return cancelledOrderService.queryCancelledOrderPage(cancelledOrderIDTO);
    }

    /**
     * 查询收货页面
     * @param orderCode
     * @return
     */
    @GetMapping("/queryCancelledOrderItemList")
    @ApiOperation(value = "查询收货页面", notes = "查询收货页面")
    public List<CancelledOrderItemODTO> queryCancelledOrderItemList(@RequestParam(value = "orderCode",required = false) String orderCode) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return cancelledOrderService.queryCancelledOrderItemList(orderCode,tokenInfo.getRealName());
    }

    /**
     * 取消单收货操作
     * @param idto
     * @return
     */
    @PostMapping("/cancelledOrderReceive")
    @ApiOperation(value = "取消单收货操作", notes = "取消单收货操作")
    public Boolean cancelledOrderReceive(@RequestBody CancelledOrderReceiveIDTO idto) {
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getOrderCode()), "订单编码不能为空");

        LockUtils.checkLock(LockUtils.ELM_CANCELLED, idto.getOrderCode());

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setReceiveId(tokenInfo.getUserId());
        idto.setReceiveMan(tokenInfo.getRealName());
        return cancelledOrderService.cancelledOrderReceive(idto);
    }
}
