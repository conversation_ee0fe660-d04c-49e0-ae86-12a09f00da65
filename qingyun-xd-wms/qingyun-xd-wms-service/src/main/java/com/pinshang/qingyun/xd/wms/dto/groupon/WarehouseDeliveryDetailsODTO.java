package com.pinshang.qingyun.xd.wms.dto.groupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2021/6/23 10:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDeliveryDetailsODTO {
    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    @ApiModelProperty(value = "客户编号")
    private String userCode;
    @ApiModelProperty(value = "提货门店")
    private String shopName;
    @ApiModelProperty(value = "提货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;
    @ApiModelProperty(value = "发货日期")//包裹创建时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty(value = "C端订单编号")
    private Long orderId;
    @ApiModelProperty(value = "一级品类")
    private String commodityFirstKindName;
    @ApiModelProperty(value = "二级品类")
    private String commoditySecondKindName;
    @ApiModelProperty(value = "三级品类")
    private String commodityThirdKindName;
    @ApiModelProperty(value = "条形码") //主码
    private String barCode;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "规格")
    private String commoditySpec;
    @ApiModelProperty(value = "计量单位")
    private String commodityUnitName;
    @ApiModelProperty(value = "订货数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "打包数量")
    private BigDecimal packageQuantity;
    @ApiModelProperty(value = "结算金额")
    private BigDecimal  totalPrice;
    @ApiModelProperty(value ="客户id")
    private Long storeId;
    @ApiModelProperty(value = "商品id")
    private Long  commodityId;
    @ApiModelProperty(value = "条形子码列表")
    private List<String> barCodeList;
    private void setPackageQuantity(BigDecimal packageQuantity){
        this.packageQuantity=packageQuantity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    private void setTotalPrice(BigDecimal totalPrice){
        this.totalPrice=totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    private void setQuantity(BigDecimal quantity){
        this.quantity=quantity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    private void setPrice(BigDecimal price){
        this.price=price.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
