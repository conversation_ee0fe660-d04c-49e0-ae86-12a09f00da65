package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-05-24-15:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotListForStockAndPriceODTO {
    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("成本价")
    private BigDecimal weightPrice;

    @ApiModelProperty("份数")
    private Integer number;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("商品总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("快照日期")
    private Date shotDate;

    @ApiModelProperty("成本价")
    private String weightPriceStr;

    @ApiModelProperty("商品总金额")
    private String totalPriceStr;

}
