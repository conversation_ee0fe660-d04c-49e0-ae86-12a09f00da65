package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TransferTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 移库单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
@ApiModel("DdTransferRecordODTO")
public class DdTransferRecordODTO {

    @ApiModelProperty("移库单ID")
    private Long id;

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("档口编码")
    private String stallCode;

    @ApiModelProperty("档口名称")
    private String stallName;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("计量单位")
    private String commodityUnitName;

    @ApiModelProperty("移库单号")
    private String transferCode;

    @ApiModelProperty("类型1-排面补货、2-拣货位补货、3-后仓上架、4-移库")
    private Integer type;

    @ApiModelProperty("类型1-排面补货、2-拣货位补货、3-后仓上架、4-移库")
    private String typeName;

    @ApiModelProperty("创建人ID")
    private Long createId;

    @ApiModelProperty(value = "操作人姓名")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.realName, keyName = "createId")
    private String createName;

    @ApiModelProperty(value = "操作人工号")
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.employeeNumber, keyName = "createId")
    private String employeeCode;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public String getTypeName() {
        TransferTypeEnum transferTypeEnum = TransferTypeEnum.getTypeEnumByCode(this.type);
        return null == transferTypeEnum ? null : transferTypeEnum.getName();
    }

    public String getStallCode() {
        return this.stallCode + "_" + this.stallName;
    }

}