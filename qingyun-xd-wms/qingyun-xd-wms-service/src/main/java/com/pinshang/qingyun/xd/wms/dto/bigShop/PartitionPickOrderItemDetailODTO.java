package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("PartitionPickOrderItemDetailODTO")
public class PartitionPickOrderItemDetailODTO implements Serializable {

    private static final long serialVersionUID = -3596623167100286864L;

    @ApiModelProperty("商品Id")
    private Long commodityId;

    @ApiModelProperty("商品条码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.barCode, keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityName, keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("型号规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commoditySpec, keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("称重")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.isWeight, keyName = "commodityId")
    private Integer isWeight;

    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY, fieldName = RenderFieldHelper.Commodity.commodityUnit, keyName = "commodityId")
    private String commodityUnit;

    @ApiModelProperty("包装规格")
    private String commodityPackageKind;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "商品份数")
    private Integer stockNumber;

    @ApiModelProperty(value = "商品拣货数量")
    private BigDecimal pickQuantity = BigDecimal.ZERO;

    @ApiModelProperty(value = "拣货份数")
    private Integer pickNumber = 0;
}
