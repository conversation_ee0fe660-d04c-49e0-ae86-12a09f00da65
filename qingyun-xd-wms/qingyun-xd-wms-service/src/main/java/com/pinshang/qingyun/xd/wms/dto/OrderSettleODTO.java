package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSettleODTO {

    private Long kid;
    private String orderCode;
    private String sourceId;
    private String sourceType;
    private Date deliveryTime;
    private Date outTime;
    private Long storeId;
    private BigDecimal totalPrice;

}