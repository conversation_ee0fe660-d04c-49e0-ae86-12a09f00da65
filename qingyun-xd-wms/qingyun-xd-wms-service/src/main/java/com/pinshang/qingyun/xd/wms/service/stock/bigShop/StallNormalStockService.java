package com.pinshang.qingyun.xd.wms.service.stock.bigShop;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdTempWarehouseDefaultDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.TempOperationTypeEnum;
import com.pinshang.qingyun.xd.wms.service.StockInventoryOrderService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdTempWarehouseAllocationService;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import com.pinshang.qingyun.xd.wms.util.LockUtils;
import com.pinshang.qingyun.xd.wms.vo.StockInOutVO;
import com.pinshang.qingyun.xd.wms.vo.bigShop.DdStockInOutExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StallNormalStockService {

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Autowired
    private StockInventoryOrderService stockInventoryOrderService;

    @Autowired
    private StockServiceAdapter stockServiceAdapter;

    @Autowired
    private DdTempWarehouseAllocationService ddTempWarehouseAllocationService;

    public Pair<Long, String> stockInOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum
            , List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        if (StockInOutEnum.IN.getCode() == typeEnum.getInOutType()) {
            stockIn(idAndCode, typeEnum, commodityList, warehouseId, userId);
        } else if (StockInOutEnum.OUT.getCode() == typeEnum.getInOutType()) {
            stockOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }

        return null;
    }

    private Pair<Long, String> stockIn(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList
            , Long warehouseId, Long userId) {

        if (!StockInOutTypeEnums.IN_INVENTORY_PLUS.equals(typeEnum)) {
            LockUtils.checkLock(LockUtils.STOCK_INOUT, "stockIn:" + typeEnum.toString() + +idAndCode.getLeft() + idAndCode.getRight());

            // 冻结检测
            checkStockLock(commodityList, warehouseId);
        }

        // 处理库存+
        if (YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())) {
            stallCommodityStockService.increaseStock(commodityList, warehouseId);
        } else {
            stallCommodityStockService.increaseStockUnCheck(commodityList, warehouseId);
        }

        return null;
    }

    private Pair<Long, String> stockOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList
            , Long warehouseId, Long userId) {

        if (!StockInOutTypeEnums.OUT_INVENTORY_LOSS.equals(typeEnum)) {

            if (StockInOutTypeEnums.DD_TRANSPORT_OUT.equals(typeEnum)) {
                DdStockInOutExtraVO ddStockInOutExtraVO = commodityList.get(0).getDdStockInOutExtraVO();
                LockUtils.checkLock(LockUtils.STOCK_INOUT, "stockOut:" + typeEnum + idAndCode.getLeft() + idAndCode.getRight() + "_"
                        + ddStockInOutExtraVO.getStorageArea() + ddStockInOutExtraVO.getGoodsAllocationId());
            } else {
                LockUtils.checkLock(LockUtils.STOCK_INOUT, "stockOut:" + typeEnum.toString() + idAndCode.getLeft() + idAndCode.getRight());
            }
            // 冻结检测
            checkStockLock(commodityList, warehouseId);
        }

        // 库存调整出库，需要直接临时库入库 历史处理方式
        // 现改为 “正常库转临” 返回给调用者临时库入库code
        if (StockInOutTypeEnums.OUT_POS_CODE_NORMAL.equals(typeEnum)) {
            // POS折扣特价码出库 直接 临时库入库
            List<StockItemDTO> transferedCommodityList = transferProvisionalArea(commodityList);
            StockInOutVO stockInOutVO = StockInOutVO.buildStockInOutVO(idAndCode, StockInOutTypeEnums.IN_POS_CODE_QUALITY, transferedCommodityList, warehouseId, userId);
            stockServiceAdapter.stockInOut(stockInOutVO);
        }

        if (YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())) {
            stallCommodityStockService.reduceStock(commodityList, warehouseId);
        } else {
            stallCommodityStockService.reduceStockUnCheck(commodityList, warehouseId);
        }


        return null;
    }

    private List<StockItemDTO> transferProvisionalArea(List<StockItemDTO> commodityList) {
        List<StockItemDTO> stockItemDTOS = BeanCloneUtils.copyTo(commodityList, StockItemDTO.class);

        Set<Long> stallIdSet = commodityList.stream()
                .map(item -> item.getDdStockInOutExtraVO().getStallId())
                .collect(Collectors.toSet());

        Map<Long, DdTempWarehouseDefaultDTO> stallIdProvisionalAreaGoodsAllocationMap =
                ddTempWarehouseAllocationService.getStallIdProvisionalAreaGoodsAllocationMap(stallIdSet, TempOperationTypeEnum.DISCOUNT_RETURN);
        if (SpringUtil.isEmpty(stallIdProvisionalAreaGoodsAllocationMap)) {
            return stockItemDTOS;
        }

        for (StockItemDTO stockItemDTO : stockItemDTOS) {
            DdStockInOutExtraVO ddStockInOutExtraVO = stockItemDTO.getDdStockInOutExtraVO();
            Long stallId = ddStockInOutExtraVO.getStallId();
            DdTempWarehouseDefaultDTO ddTempWarehouseDefaultDTO = stallIdProvisionalAreaGoodsAllocationMap.get(stallId);
            Long goodsAllocationId = Optional.ofNullable(ddTempWarehouseDefaultDTO).map(DdTempWarehouseDefaultDTO::getGoodsAllocationId).orElse(null);
            String goodsAllocationCode = Optional.ofNullable(ddTempWarehouseDefaultDTO).map(DdTempWarehouseDefaultDTO::getGoodsAllocationCode).orElse(null);
            DdStockInOutExtraVO transfer = DdStockInOutExtraVO.buildProvisionaAreaDdStockInOutExtraVO(stockItemDTO.getCommodityId()
                    , stallId, goodsAllocationId, goodsAllocationCode);
            stockItemDTO.setDdStockInOutExtraVO(transfer);
        }
        return stockItemDTOS;
    }

    /**
     * @param commodityList
     */
    private void checkStockLock(List<StockItemDTO> commodityList, Long warehouseId) {
        List<Long> todoList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        // 获取已锁库的所有明细
        List<Long> lockedList = stockInventoryOrderService.lockedInventoryCommodityId(warehouseId);

        lockedList.retainAll(todoList);
        if (SpringUtil.isNotEmpty(lockedList)) {
            throw new BizLogicException("商品正在锁库状态，无法进行库存操作");
        }
    }

}
