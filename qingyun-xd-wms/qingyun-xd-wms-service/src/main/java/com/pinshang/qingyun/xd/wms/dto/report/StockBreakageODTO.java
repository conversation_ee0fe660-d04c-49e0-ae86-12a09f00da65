package com.pinshang.qingyun.xd.wms.dto.report;

import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/4/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockBreakageODTO{

    private String shopCode;

    /** 门店名称 */
    private String shopName;

    /** 商品编码 */
    private String commodityCode;
    /** 商品名称 */
    private String commodityName;
    /** 规格 */
    private String commoditySpec;


    /** 报损数量 */
    private BigDecimal breakageQuantity;

    /** 单价 */
    private BigDecimal price;

    /** 金额 */
    private BigDecimal totalPrice;

    /** 报损原因 */
    private String breakageReasonName;

    /** 报损日期 */
    private Date createTime;

    public String getCreateTimeStr() {
        if(null != createTime ){
            return DateUtil.getDateFormate(createTime,"yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }
}
