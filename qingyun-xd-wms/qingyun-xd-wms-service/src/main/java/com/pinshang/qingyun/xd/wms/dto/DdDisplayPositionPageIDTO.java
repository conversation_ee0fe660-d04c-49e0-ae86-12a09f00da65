package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel("DdDisplayPositionPageIDTO")
public class DdDisplayPositionPageIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("货架id")
    private Long shelveId;

    @ApiModelProperty("陈列位")
    private Long displayPositionId;

    private List<Long> shopIdList;
}