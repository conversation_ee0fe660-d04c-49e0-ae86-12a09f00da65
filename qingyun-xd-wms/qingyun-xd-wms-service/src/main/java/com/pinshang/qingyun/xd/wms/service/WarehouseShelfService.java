package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.ShelfStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.ShelfTypeEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.*;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelf;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelfCommodity;
import com.pinshang.qingyun.xd.wms.model.WarehouseWork;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RDeque;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WarehouseShelfService {

    @Autowired
    private WarehouseShelfMapper warehouseShelfMapper;

    @Autowired
    private WarehouseShelfCommodityMapper warehouseShelfCommodityMapper;

    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;

    @Autowired
    private PickWorkOrderMapper pickWorkOrderMapper;

    @Autowired
    private WarehouseWorkMapper warehouseWorkMapper;

    @Autowired
    private RedissonClient redissonClient;

    private static final String WAREHOUSE_SHELF_DELIVERY = "WMS:WAREHOUSE_SHELF_DELIVERY:";

    private static final String WAREHOUSE_SHELF_WORK = "WMS:WAREHOUSE_SHELF_WORK:";

    private static final String WAREHOUSE_SHELF_DELIVERY_USE = "WMS:WAREHOUSE_SHELF_DELIVERY_USE:";

    private static final String WAREHOUSE_SHELF_WORK_USE = "WMS:WAREHOUSE_SHELF_WORK_USE:";

    public static final String SHELF_DELIVERY_LOCK = "WMS:LOCK:SHELF_DELIVERY:";

    /**
     * 批量添加仓库货位
     * @param list
     * @return
     */
    @Transactional
    public Integer batchInsertWarehouseShelf(List<WarehouseShelfDTO> list) {
        long userId = StockUtils.INSTANCE.userId();
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        Date date = new Date();
        List<WarehouseShelf> shelfList = new ArrayList<>();
        WarehouseShelf warehouseShelf = null;
        for (WarehouseShelfDTO e : list) {
            e.checkData();
            WarehouseShelf one = warehouseShelfMapper.selectOne(e.getShelfNo(), warehouseId, e.getType());
            QYAssert.isTrue(one == null, "货位号已经存在");
            warehouseShelf = new WarehouseShelf();
            BeanUtils.copyProperties(e, warehouseShelf);
            warehouseShelf.setWarehouseId(warehouseId);
            warehouseShelf.setUpdateId(userId);
            warehouseShelf.setCreateId(userId);
            warehouseShelf.setUpdateTime(date);
            warehouseShelf.setCreateTime(date);
            shelfList.add(warehouseShelf);
        }
//        list.stream().forEach(e->{
//            e.checkData();
//            WarehouseShelf one = warehouseShelfMapper.selectOne(e.getShelfNo(), warehouseId, e.getType());
//            QYAssert.isTrue(one == null, "货位号已经存在");
//            e.setWarehouseId(warehouseId);
//            e.setUpdateId(userId);
//            e.setCreateId(userId);
//            e.setUpdateTime(date);
//            e.setCreateTime(date);
//        });

        int result = warehouseShelfMapper.batchInsertWarehouseShelf(shelfList);

        for (WarehouseShelfDTO shelf : list) {
            if(YesOrNoEnums.YES.getCode().equals(shelf.getStatus())){
                addDistributeShelf(warehouseId, shelf.getType(), shelf.getShelfNo());
            }
        }
        return result;
    }

    /**
     * 货位状态更改
     * @return
     */
    @Transactional
    public Integer updateWarehouseShelfStatus(WarehouseShelfDTO dto) {
        QYAssert.isTrue(dto.getId() != null, "货位id不能为空");
        QYAssert.isTrue(dto.getStatus() != null, "货位状态不能为空");
        
        WarehouseShelf warehouseShelf = warehouseShelfMapper.selectById(dto.getId());
        QYAssert.notNull(warehouseShelf, "仓库货位不存在");
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        //如果停用先去判断是否已经绑定商品
        if (dto.getStatus().equals(ShelfStatusEnum.DISABLE.getCode())) {
            //拣货位
            if (warehouseShelf.getType().equals(ShelfTypeEnum.PICK.getCode())) {
                WarehouseShelfCommodity warehouseShelfCommodity = warehouseShelfCommodityMapper.queryShelfCommodityByShelfid(dto.getId());
                QYAssert.isNull(warehouseShelfCommodity, "已经绑定商品不可以停用");
            } else if (warehouseShelf.getType().equals(ShelfTypeEnum.TAKE.getCode())) {
                //配送取货位
                Integer countShelf =  deliveryOrderMapper.countShelf(warehouseShelf.getShelfNo(), StockUtils.INSTANCE.warehouseId());
                QYAssert.isTrue(countShelf <= 0, "配送取货位已经占用，不可以停用");

                freedDistributeShelf(warehouseId, ShelfTypeEnum.TAKE, warehouseShelf.getShelfNo());
            } else if (warehouseShelf.getType().equals(ShelfTypeEnum.PROCESS.getCode())) {
                //加工点取货位
                Integer shelfOccupyCount = pickWorkOrderMapper.shelfOccupyCount(warehouseShelf.getShelfNo(), StockUtils.INSTANCE.warehouseId());
                QYAssert.isTrue(shelfOccupyCount <= 0, "加工点取货位已经占用，不可以停用");

                freedDistributeShelf(warehouseId, ShelfTypeEnum.PROCESS, warehouseShelf.getShelfNo());
            }
        }
        WarehouseShelf updateData = new WarehouseShelf();
        updateData.setId(dto.getId());
        updateData.setStatus(dto.getStatus());
        return warehouseShelfMapper.updateById(updateData);
    }

    /**
     * 根据条件查询货位列表
     * @param queryWarehouseShelfDTO
     * @return
     */
    public MPage<QueryWarehouseShelfResult> queryWarehouseShelf(QueryWarehouseShelfDTO queryWarehouseShelfDTO) {
        queryWarehouseShelfDTO.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        return warehouseShelfMapper.queryWarehouseShelf(queryWarehouseShelfDTO);
    }

    /**
     * 根据条件查询货位(不分页)
     * @param queryWarehouseShelfDTO
     * @return
     */
    public List<QueryWarehouseShelfResult> queryShelfNotPage(QueryWarehouseShelfDTO queryWarehouseShelfDTO) {
        queryWarehouseShelfDTO.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        queryWarehouseShelfDTO.notLimit();
        return warehouseShelfMapper.queryWarehouseShelf(queryWarehouseShelfDTO).getList();
    }

    /**
     * 查询所有可以用的拣货位
     * @param shelfNo
     * @return
     */
    public List<WarehouseShelfListDTO> queryPickShelf(String shelfNo) {
        return warehouseShelfMapper.queryPickShelf(shelfNo, StockUtils.INSTANCE.warehouseId());
    }

    public List<QueryWarehouseShelfResult> queryShelfList(Long warehouseId) {
        QueryWarehouseShelfDTO param = new QueryWarehouseShelfDTO();
        param.setWarehouseId(warehouseId);
        param.setStatus(ShelfStatusEnum.ENABLE.getCode());
        param.setType(ShelfTypeEnum.PICK.getCode());
        param.setOccupy(2);
        param.notLimit();
        return warehouseShelfMapper.queryWarehouseShelf(param).getList();
    }

    /**
     * 查询货位
     * @param dto
     * @return
     */
    public MPage<ShelfFreeResult> queryShelfFree(ShelfFreeDTO dto) {
        long warehouseId = StockUtils.INSTANCE.warehouseId();
        dto.setWarehouseId(warehouseId);
        MPage<ShelfFreeResult> res = warehouseShelfMapper.shelfByCondition(dto);

        ShelfTypeEnum typeEnum = dto.getType().equals(ShelfTypeEnum.TAKE.getCode()) ? ShelfTypeEnum.TAKE : ShelfTypeEnum.PROCESS;
        res.getList().stream().forEach(e->{
            e.setIsFree(checkShelfFree(warehouseId, e.getShelfNo(), typeEnum));
        });
        return res;
    }

    public MPage<WarehouseShelfListDTO> shelfListByType(ShelfTypeEnum type, Long warehouseId, String keyword) {
        if (warehouseId == null || warehouseId.equals(0)) {
            warehouseId = StockUtils.INSTANCE.warehouseId();
        }
        ShelfFreeDTO dto = new ShelfFreeDTO();
        dto.setWarehouseId(warehouseId);
        dto.setType(type.getCode());
        dto.setKeyword(keyword);
        return warehouseShelfMapper.shelfListByType(dto);
    }


    /**
     * 分配配送取货位
     * @return
     */
    public String distributeShelfDelivery(){
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        RDeque<String> deliveryShelf = getDistributeShelf(warehouseId, WAREHOUSE_SHELF_DELIVERY, ShelfTypeEnum.TAKE);
        if(SpringUtil.isEmpty(deliveryShelf)){
            return null;
        }

        String shelfNo = deliveryShelf.removeFirst();
        deliveryShelf.addLast(shelfNo);

        RAtomicLong shelfUseCount = redissonClient.getAtomicLong(WAREHOUSE_SHELF_DELIVERY_USE + warehouseId + shelfNo);
        shelfUseCount.incrementAndGet();
        return shelfNo;
    }

    /**
     * 完成配送取货位
     * @return
     */
    public void completeShelfDelivery(Long orderId, String shelfNo){
        //未分配货位 就取消
        if(StringUtil.isEmpty(shelfNo)){
            return;
        }
        RAtomicLong shelfDeliveryLock = redissonClient.getAtomicLong(SHELF_DELIVERY_LOCK + orderId);
        long lock = shelfDeliveryLock.incrementAndGet();
        if(lock == 1) {
            shelfDeliveryLock.expire(1, TimeUnit.DAYS);
        }
        else if(lock > 1){
            return;
        }

        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        RAtomicLong shelfUseCount = redissonClient.getAtomicLong(WAREHOUSE_SHELF_DELIVERY_USE + warehouseId + shelfNo);
        shelfUseCount.decrementAndGet();
    }

    /**
     * 分配加工点取货位
     * @return
     */
    public String distributeShelfWork(){
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        RDeque<String> workShelf = getDistributeShelf(warehouseId, WAREHOUSE_SHELF_WORK, ShelfTypeEnum.PROCESS);
        if(SpringUtil.isEmpty(workShelf)){
            return null;
        }

        String shelfNo = workShelf.removeFirst();
        workShelf.addLast(shelfNo);

        RAtomicLong shelfUseCount = redissonClient.getAtomicLong(WAREHOUSE_SHELF_WORK_USE + warehouseId + shelfNo);
        shelfUseCount.incrementAndGet();
        return shelfNo;
    }

    /**
     * 完成加工点取货位
     * @return
     */
    public void completeShelfWork(String shelfNo){
        //未分配货位 就取消
        if(StringUtil.isEmpty(shelfNo)){
            return;
        }

        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        RAtomicLong shelfUseCount = redissonClient.getAtomicLong(WAREHOUSE_SHELF_WORK_USE + warehouseId + shelfNo);
        shelfUseCount.decrementAndGet();
    }

    /**
     * 查询指定仓库的商品货位
     * @param dto
     * @return
     */
    public List<ShopShelfResDTO> queryShopShelf(ShopShelfReqDTO dto) {
        return warehouseShelfMapper.queryShopShelf(dto);
    }

    /**
     * 检查仓库货位是否空闲
     * @param warehouseId
     * @param shelfNo
     * @param typeEnum
     * @return 空闲返回True 占用返回False
     */
    public Boolean checkShelfFree(Long warehouseId, String shelfNo, ShelfTypeEnum typeEnum){
        String warehouseShelfUse = getWarehouseShelfUse(typeEnum);
        RAtomicLong shelfUseCount = redissonClient.getAtomicLong(warehouseShelfUse + warehouseId + shelfNo);

        return shelfUseCount.get() == 0;
    }

    private String getWarehouseShelf(ShelfTypeEnum typeEnum) {
        String warehouseShelf = "";
        if(ShelfTypeEnum.PICK.getCode() == typeEnum.getCode()){
            QYAssert.isTrue(false, "货位类型异常");
        }else if(ShelfTypeEnum.TAKE.getCode() == typeEnum.getCode()){
            warehouseShelf = WAREHOUSE_SHELF_DELIVERY;
        }else if(ShelfTypeEnum.PROCESS.getCode() == typeEnum.getCode()){
            warehouseShelf = WAREHOUSE_SHELF_WORK;
        }
        return warehouseShelf;
    }

    private String getWarehouseShelfUse(ShelfTypeEnum typeEnum) {
        String warehouseShelf = "";
        if(ShelfTypeEnum.PICK.getCode() == typeEnum.getCode()){
            QYAssert.isTrue(false, "货位类型异常");
        }else if(ShelfTypeEnum.TAKE.getCode() == typeEnum.getCode()){
            warehouseShelf = WAREHOUSE_SHELF_DELIVERY_USE;
        }else if(ShelfTypeEnum.PROCESS.getCode() == typeEnum.getCode()){
            warehouseShelf = WAREHOUSE_SHELF_WORK_USE;
        }
        return warehouseShelf;
    }

    /**
     * 获取货位
     * @param warehouseId
     * @param warehouseShelf
     * @param typeEnum
     * @return
     */
    private RDeque<String> getDistributeShelf(Long warehouseId, String warehouseShelf, ShelfTypeEnum typeEnum) {
        RDeque<String> workShelf = redissonClient.getDeque(warehouseShelf + warehouseId);
        if (workShelf.size() == 0) {
            LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseShelf>()
                    .eq(WarehouseShelf::getWarehouseId, warehouseId)
                    .eq(WarehouseShelf::getType, typeEnum.getCode())
                    .eq(WarehouseShelf::getStatus, YesOrNoEnums.YES.getCode());
            List<WarehouseShelf> shelfList = warehouseShelfMapper.selectList(query);
//            QYAssert.isTrue(SpringUtil.isNotEmpty(shelfList), "没有可用的" + typeEnum.getDesc());
            if(SpringUtil.isNotEmpty(shelfList)){
                log.info("获取货位list -> {}", JsonUtil.java2json(shelfList));

                //无限轮
                shelfList.forEach(it -> {
                    workShelf.add(it.getShelfNo());
                });
            }
        }
        return workShelf;
    }

    /**
     * 添加货位
     * @param warehouseId
     * @param shelfType
     * @param shelfNo
     */
    private void addDistributeShelf(Long warehouseId, Integer shelfType, String shelfNo) {
        ShelfTypeEnum typeEnum = null;
        if (shelfType.equals(ShelfTypeEnum.PICK.getCode())){
            return;
        }else if (shelfType.equals(ShelfTypeEnum.TAKE.getCode())){
            typeEnum = ShelfTypeEnum.TAKE;
        }else if (shelfType.equals(ShelfTypeEnum.PROCESS.getCode())) {
            typeEnum = ShelfTypeEnum.PROCESS;
        }
        String warehouseShelf = getWarehouseShelf(typeEnum);
        RDeque<String> workShelf = redissonClient.getDeque(warehouseShelf + warehouseId);
        workShelf.addLast(shelfNo);
    }

    /**
     * 释放货位
     * @param warehouseId
     * @param typeEnum
     * @param shelfNo
     */
    private void freedDistributeShelf(Long warehouseId, ShelfTypeEnum typeEnum, String shelfNo) {
        String warehouseShelf = getWarehouseShelf(typeEnum);
        RDeque<String> workShelf = redissonClient.getDeque(warehouseShelf + warehouseId);
        workShelf.remove(shelfNo);
    }

    public List<WarehouseShelf> shelfListByWarehouseId(Long warehouseId) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseShelf>()
                .eq(WarehouseShelf::getWarehouseId, warehouseId)
                .eq(WarehouseShelf::getType, ShelfTypeEnum.PICK.getCode());
        return warehouseShelfMapper.selectList(query);
    }

    public void shelfImport(Workbook wb) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        List<WarehouseShelf> insertData = new ArrayList<>();
        long userId = StockUtils.INSTANCE.userId();
        Date date = new Date();
        WarehouseShelf warehouseShelf = null;
        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(checkRowNumZero(row, 0, "排序"), "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 1, "货位号"), "模板不正确");
            }
            //从第二行开始
            if (rowNum > 0) {
                warehouseShelf = new WarehouseShelf();
                Cell c0 = row.getCell(0);
                if (c0 != null) {
                    QYAssert.isTrue(!c0.getCellType().name().equals("STRING"), "序号只能是正整数");
                    Double cellValue = new Double(c0.getNumericCellValue());
                    if (cellValue.longValue() != cellValue ) {
                        QYAssert.isTrue(false, "序号只能是整数");
                    }
                    if (cellValue > 999999 || cellValue < 1) {
                        QYAssert.isTrue(false, "序号是1到999999之间");
                    }
                    warehouseShelf.setSort(new Double(c0.getNumericCellValue()) != null ? new Double(c0.getNumericCellValue()).longValue(): null);
                }
                Cell c1 = row.getCell(1);
                QYAssert.notNull(c1, "货位号不能为空");
                QYAssert.isTrue(!c1.getStringCellValue().trim().equals(""), "货位号不能为空");
                QYAssert.isTrue(c1.getStringCellValue().trim().length() <= 32, "货位长度最多32位");
                warehouseShelf.setShelfNo(c1.getStringCellValue().trim());
                warehouseShelf.setWarehouseId(warehouseId);
                warehouseShelf.setStatus(ShelfStatusEnum.ENABLE.getCode());
                warehouseShelf.setType(ShelfTypeEnum.PICK.getCode());
                warehouseShelf.setCreateId(userId);
                warehouseShelf.setUpdateId(userId);
                warehouseShelf.setCreateTime(date);
                warehouseShelf.setUpdateTime(date);
                insertData.add(warehouseShelf);
            }
        }
        QYAssert.notEmpty(insertData, "模板数据不能为空哦！");
        saveShelfs(insertData);
    }

    private void saveShelfs(List<WarehouseShelf> insertData) {
        List<String> shelfNos = insertData.stream().map(WarehouseShelf::getShelfNo).
                distinct().collect(Collectors.toList());
        if (shelfNos.size() < insertData.size()) {
            QYAssert.isTrue(false, "货位编号存在重复");
        }
        //是否存在重复的货位
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseShelf>()
                .eq(WarehouseShelf::getWarehouseId, StockUtils.INSTANCE.warehouseId())
                .in(WarehouseShelf::getShelfNo, shelfNos);
        List<WarehouseShelf> shelfList = warehouseShelfMapper.selectList(query);
        if (!CollectionUtils.isEmpty(shelfList)) {
            StringBuffer sb = new StringBuffer();
            shelfList.forEach(it->{
                sb.append(it.getShelfNo());
                sb.append(",");
            });
            sb.append("已经存在喽！");
            QYAssert.isTrue(false, sb.toString());
        }
        warehouseShelfMapper.batchInsertWarehouseShelf(insertData);

    }

    /**
     * 判断模板格式：根据Excel表头来判断
     * @param row
     * @param index
     * @param cellName
     * @return
     */
    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
        if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
            result = false;
        }
        return result;
    }

    /**
     * 更改货位排序
     * @param id
     * @param sort
     */
    public void shelfSort(Long id, Long sort) {
        if (sort > 999999 || sort < 1) {
            QYAssert.isTrue(false, "货位编号在1到999999之间");
        }
        WarehouseShelf warehouseShelf = new WarehouseShelf();
        warehouseShelf.setId(id);
        warehouseShelf.setSort(sort);
        warehouseShelfMapper.updateById(warehouseShelf);
    }

    /**
     * 初始化货位
     * @param shopId
     * @return
     */
    @Transactional
    public Boolean initializeShelf(Long shopId) {
        //拣货位，加工点取货位，配送取货位
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseShelf>().
                eq(WarehouseShelf::getWarehouseId, shopId).
                groupBy(WarehouseShelf::getType);
        List<WarehouseShelf> shelf = warehouseShelfMapper.selectList(query);
        List<Integer> logList = shelf.stream().map(WarehouseShelf::getType).collect(Collectors.toList());
        List<WarehouseShelf> shelfList = new ArrayList<>();
        Date date = new Date();
        WarehouseShelf warehouseShelf = null;
        for (ShelfTypeEnum e: ShelfTypeEnum.values()) {
            if (logList.contains(e.getCode())) {
                continue;
            }
            warehouseShelf = new WarehouseShelf();
            warehouseShelf.setShelfNo("0001");
            warehouseShelf.setWarehouseId(shopId);
            warehouseShelf.setStatus(ShelfStatusEnum.ENABLE.getCode());
            warehouseShelf.setCreateId(0L);
            warehouseShelf.setUpdateId(0L);
            warehouseShelf.setUpdateTime(date);
            warehouseShelf.setCreateTime(date);
            warehouseShelf.setType(e.getCode());
            shelfList.add(warehouseShelf);
        }
        if (!CollectionUtils.isEmpty(shelfList)) {
            warehouseShelfMapper.batchInsertWarehouseShelf(shelfList);
        }
        //加工点
        LambdaQueryWrapper queryWork = new LambdaQueryWrapper<WarehouseWork>().
                eq(WarehouseWork::getWarehouseId, shopId);
        List<WarehouseWork> workList = warehouseWorkMapper.selectList(queryWork);
        if (CollectionUtils.isEmpty(workList)) {
            WarehouseWork warehouseWork = new WarehouseWork();
            warehouseWork.setWorkNo("0001");
            warehouseWork.setWorkName("0001");
            warehouseWork.setWarehouseId(shopId);
            warehouseWork.setStatus(ShelfStatusEnum.ENABLE.getCode());
            warehouseWorkMapper.insert(warehouseWork);
        }
        return Boolean.TRUE;
    }

}
