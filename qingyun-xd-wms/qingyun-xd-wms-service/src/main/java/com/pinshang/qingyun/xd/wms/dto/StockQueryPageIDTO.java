package com.pinshang.qingyun.xd.wms.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 库存查询IDTO
 */
@NoArgsConstructor
@AllArgsConstructor
public class StockQueryPageIDTO extends Pagination<StockItemPageODTO> {
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty(value = "最小数量")
    private Integer startNum;

    @ApiModelProperty(value = "最大数量")
    private Integer endNum;

    @ApiModelProperty(value = "app状态：0-上架，1-下架")
    private Integer appStatus;

    @ApiModelProperty(value = "是否预警：1是，0否")
    private Integer ifWarn;

    @ApiModelProperty(value = "门店类型")
    private Integer shopType;

    @ApiModelProperty(value = "后台品类id")
    private Long cateId;

    @ApiModelProperty(value = "前台品名")
    private String commodityAppName;

    @ApiModelProperty(value = "最大份数")
    private Integer maxStockNum;

    @ApiModelProperty(value = "最小份数")
    private Integer minStockNum;

    private List<Long> commodityIdList;

    private List<Long> shopIdList;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("供销商id")
    private Long consignmentId;

    @JsonIgnore
    @ApiModelProperty("代销商门店下的商品id")
    private List<Long> consignmentCommodityList;

    @JsonIgnore
    @ApiModelProperty("包含该商品的代销商门店id")
    private List<Long> consignmentShopIdList;

    public Integer getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(Integer isWeight) {
        this.isWeight = isWeight;
    }

    public List<Long> getCommodityIdList() {
        return commodityIdList;
    }

    public void setCommodityIdList(List<Long> commodityIdList) {
        this.commodityIdList = commodityIdList;
    }

    public Integer getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(Integer appStatus) {
        this.appStatus = appStatus;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getStartNum() {
        return startNum;
    }

    public void setStartNum(Integer startNum) {
        this.startNum = startNum;
    }

    public Integer getEndNum() {
        return endNum;
    }

    public void setEndNum(Integer endNum) {
        this.endNum = endNum;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public List<Long> getShopIdList() {
        return shopIdList;
    }

    public void setShopIdList(List<Long> shopIdList) {
        this.shopIdList = shopIdList;
    }

    public Long getCateId() {
        return cateId;
    }

    public void setCateId(Long cateId) {
        this.cateId = cateId;
    }

    @Override
    public String toString() {
        return "StockQueryPageIDTO{" +
                "warehouseId=" + warehouseId +
                ", commodityId='" + commodityId + '\'' +
                ", barCode='" + barCode + '\'' +
                '}';
    }

    public Integer getIfWarn() {
        return ifWarn;
    }

    public void setIfWarn(Integer ifWarn) {
        this.ifWarn = ifWarn;
    }

    public String getCommodityAppName() {
        return commodityAppName;
    }

    public void setCommodityAppName(String commodityAppName) {
        this.commodityAppName = commodityAppName;
    }

    public Integer getMaxStockNum() {
        return maxStockNum;
    }

    public void setMaxStockNum(Integer maxStockNum) {
        this.maxStockNum = maxStockNum;
    }

    public Integer getMinStockNum() {
        return minStockNum;
    }

    public void setMinStockNum(Integer minStockNum) {
        this.minStockNum = minStockNum;
    }

    public Long getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(Long consignmentId) {
        this.consignmentId = consignmentId;
    }

    public List<Long> getConsignmentCommodityList() {
        return consignmentCommodityList;
    }

    public void setConsignmentCommodityList(List<Long> consignmentCommodityList) {
        this.consignmentCommodityList = consignmentCommodityList;
    }

    public List<Long> getConsignmentShopIdList() {
        return consignmentShopIdList;
    }

    public void setConsignmentShopIdList(List<Long> consignmentShopIdList) {
        this.consignmentShopIdList = consignmentShopIdList;
    }
}