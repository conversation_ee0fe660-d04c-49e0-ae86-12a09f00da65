package com.pinshang.qingyun.xd.wms.client.hystrix;

import com.pinshang.qingyun.xd.wms.client.StoragePackageOrderClient;
import com.pinshang.qingyun.xd.wms.client.dto.PackageIDTO;
import com.pinshang.qingyun.xd.wms.client.dto.QueryBoxCodeByPackageIdIDTO;
import com.pinshang.qingyun.xd.wms.client.dto.QueryBoxCodeByPackageIdODTO;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2022/3/21
 */
@Component
public class StoragePackageOrderClientHystrix implements FallbackFactory<StoragePackageOrderClient> {
    @Override
    public StoragePackageOrderClient create(Throwable cause) {
        return new StoragePackageOrderClient() {

            @Override
            public List<Long> getPackageOrderIdByBoxCode(String boxCode) {
                return null;
            }

            @Override
            public Map<Long, List<String>> getPackageOrderBoxCode(PackageIDTO packageIDTO) {
                return null;
            }

            @Override
            public List<QueryBoxCodeByPackageIdODTO> queryBoxCodeByPackageId(QueryBoxCodeByPackageIdIDTO idto) {
                return null;
            }

        };
    }
}
