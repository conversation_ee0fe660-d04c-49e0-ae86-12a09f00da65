package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockOutTypeEnums;
import com.pinshang.qingyun.base.enums.xd.StockQualityInTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 库存处理IDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockIDTO {
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "关联单号id")
    private Long referId;

    @ApiModelProperty(value = "关联单号code")
    private String referCode;

    @ApiModelProperty(value = "入库枚举")
    private StockQualityInTypeEnums inTypeEnum;

    @ApiModelProperty(value = "出库枚举")
    private StockOutTypeEnums outTypeEnum;

    @ApiModelProperty(value = "出入库枚举")
    private StockInOutTypeEnums stockEnums;

    @ApiModelProperty(value = "商品list")
    private List<StockItemDTO> commodityList;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "库存冻结商品限量销售信息")
    private List<CommodityLimitIDTO> commodityLimitIDTOList;

    public void checkData() {
        QYAssert.isTrue(warehouseId != null, "仓库id不能为空");
        QYAssert.isTrue(referId != null, "关联单号id不能为空");
        QYAssert.isTrue(referCode != null, "关联单号code不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");
    }

    public void checkFreezeData() {
        QYAssert.isTrue(warehouseId != null, "仓库id不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品list不能为空");
        QYAssert.isTrue(userId != null, "用户id不能为空");
    }
}