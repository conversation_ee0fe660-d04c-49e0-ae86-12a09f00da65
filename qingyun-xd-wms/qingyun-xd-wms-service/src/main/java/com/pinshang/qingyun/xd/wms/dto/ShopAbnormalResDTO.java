package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询门店损耗结果对象")
public class ShopAbnormalResDTO {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("损耗总数")
    private Integer abnormalNum1;

    @ApiModelProperty("损耗(过保&临保)")
    private Integer abnormalNum2;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ShopAbnormalResDTO that = (ShopAbnormalResDTO) o;
        return Objects.equals(shopId, that.shopId) &&
                Objects.equals(commodityId, that.commodityId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(shopId, commodityId);
    }
}
