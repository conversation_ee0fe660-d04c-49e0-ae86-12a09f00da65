package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.StallCommodityStockIDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/9 9:21
 * <p>
 * 档口商品表
 */
@TableName("t_stall_commodity_stock")
@Data
@NoArgsConstructor
public class StallCommodityStock extends BasePO {

    /***
     * 档口所属门店ID
     */
    private Long shopId;

    /***
     * 档口ID
     */
    private Long stallId;
    /***
     * 商品ID
     */
    private Long commodityId;

    /***
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 拣货区库存数量
     */
    private BigDecimal pickingAreaStock;

    /**
     * 存储区库存数量
     */
    private BigDecimal warehouseAreaStock;

    /**
     * 临时库存数量
     */
    private BigDecimal qualityQuantity;


    /***
     * 冻结库存
     */
    private Integer freezeNumber;

    /**
     * 预留库存
     */
    private BigDecimal reserveStock;

    /***
     * 创建人名称
     */
    private String createName;
    /***
     * 更新人名称
     */
    private String updateName;


    public StallCommodityStock(Long shopId, Long stallId, Long commodityId) {
        this.shopId = shopId;
        this.stallId = stallId;
        this.commodityId = commodityId;
    }

    public StallCommodityStock initBasic(Long userId, String userName, Date date) {
        setCreateId(userId);
        setCreateName(userName);
        setCreateTime(date);
        setUpdateId(userId);
        setUpdateName(userName);
        setUpdateTime(date);
        return this;
    }

    public static List<StallCommodityStock> forInsert(StallCommodityStockIDTO idto) {

        List<StallCommodityStock> list = new ArrayList<>();
        Date date = new Date();
        idto.getCommodityIdSet().forEach(c -> {
            StallCommodityStock stallCommodityStock = new StallCommodityStock(idto.getShopId(), idto.getStallId(), c);
            stallCommodityStock.setStockQuantity(BigDecimal.ZERO);
            stallCommodityStock.setPickingAreaStock(BigDecimal.ZERO);
            stallCommodityStock.setWarehouseAreaStock(BigDecimal.ZERO);
            stallCommodityStock.setQualityQuantity(BigDecimal.ZERO);
            stallCommodityStock.setFreezeNumber(0);
            stallCommodityStock.setReserveStock(BigDecimal.ZERO);
            stallCommodityStock.initBasic(idto.getUserId(), idto.getUserName(), date);
            list.add(stallCommodityStock);
        });
        return list;
    }
}
