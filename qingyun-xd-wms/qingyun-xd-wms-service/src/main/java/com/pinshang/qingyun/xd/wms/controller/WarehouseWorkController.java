package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseWorkDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseWorkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/warehouse/work")
@Api(value = "加工点管理管理", tags = "WarehouseWorkController")
public class WarehouseWorkController {

    @Autowired
    private WarehouseWorkService warehouseWorkService;

    @PostMapping("/insertWarehouseWork")
    @ApiOperation(value = "添加加工点", notes = "添加加工点")
    public Integer insertWarehouseWork(@RequestBody WarehouseWorkDTO warehouseDTO) {
        return warehouseWorkService.insertWarehouseWork(warehouseDTO);
    }

    @PostMapping("/updateWarehouseWork")
    @ApiOperation(value = "修改加工点", notes = "修改加工点")
    public Integer updateWarehouseWork(@RequestBody WarehouseWorkDTO warehouseDTO) {
        return warehouseWorkService.updateWarehouseWork(warehouseDTO);
    }

    @PostMapping("/updateWarehouseWorkStatus")
    @ApiOperation(value = "修改状态", notes = "修改状态")
    public Integer updateWarehouseWorkStatus(@RequestBody WarehouseWorkDTO warehouseDTO) {
        return warehouseWorkService.updateWarehouseWorkStatus(warehouseDTO);
    }

    @GetMapping("/warehouseWorkByStatus/{status}")
    @ApiOperation(value = "根据状态查询加工点", notes = "根据状态查询加工点")
    public List<WarehouseWorkDTO> warehouseWorkByStatus(@PathVariable(value = "status") Integer status) {
        return warehouseWorkService.warehouseWorkByStatus(status);
    }

    @PostMapping("/queryWorByCondition")
    @ApiOperation(value = "查询加工点列表", notes = "查询加工点列表")
    public MPage<QueryWorkByConditionResult> queryWorByCondition(@RequestBody QueryWorkByConditionDTO data) {
        return warehouseWorkService.queryWorByCondition(data);
    }
}
