package com.pinshang.qingyun.xd.wms.vo;


import com.pinshang.qingyun.xd.wms.enums.UserTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Optional;

@Data
@ToString
@ApiModel("PrinterUserTypeQueryRspVO")
public class PrinterUserTypeQueryRspVO {

    /**
     * 使用方id
     */
    @ApiModelProperty(value = "使用方id")
    private Long  realUserId;

    /**
     * 使用方
     */
    @ApiModelProperty(value = "使用方名称")
    private String realUserName;

}
