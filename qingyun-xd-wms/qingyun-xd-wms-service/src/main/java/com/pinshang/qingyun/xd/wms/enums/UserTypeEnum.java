package com.pinshang.qingyun.xd.wms.enums;

/**
 * 使用方类型（1-加工点 2-打包口）
 */
public enum UserTypeEnum {
    PROCESS_POINT(1,"加工点"),
    PACKING_STATION(2,"打包口"),
    ;
    private Integer code;
    private String name;

    UserTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static UserTypeEnum getTypeEnumByCode(Integer code){
        if (code == null) {
            return null;
        }
        for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
