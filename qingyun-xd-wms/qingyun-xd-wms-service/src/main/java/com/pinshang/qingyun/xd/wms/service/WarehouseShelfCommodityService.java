package com.pinshang.qingyun.xd.wms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.enums.ShelfStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.ShelfTypeEnum;
import com.pinshang.qingyun.xd.wms.ext.StockUtils;
import com.pinshang.qingyun.xd.wms.mapper.*;
import com.pinshang.qingyun.xd.wms.model.Commodity;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelf;
import com.pinshang.qingyun.xd.wms.model.WarehouseShelfCommodity;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class WarehouseShelfCommodityService {

    @Autowired
    private WarehouseShelfCommodityMapper warehouseShelfCommodityMapper;

    @Autowired
    private WarehouseShelfMapper warehouseShelfMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private StockInventoryOrderService stockInventoryOrderService;

    @Autowired
    private StockInventoryOrderItemMapper stockInventoryOrderItemMapper;

    @Autowired
    private PickOrderMapper pickOrderMapper;

    @Autowired
    private CommodityBarCodeService commodityBarCodeService;

    @Autowired
    private CommodityMapper commodityMapper;

    /**
     * 添加仓库货位和商品的绑定
     * 货位可以重复绑定
     */
    @Transactional
    public Integer insertWarehouseShelfCommodity(WarehouseShelfCommodityDTO warehouseShelfCommodityDTO) {
        warehouseShelfCommodityDTO.checkData();
        //检验货位信息
        checkWarehouseShelf(warehouseShelfCommodityDTO);
        //查看商品信息
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        WarehouseShelfCommodity queryShelfCommodityByCommodityid = warehouseShelfCommodityMapper.queryShelfCommodityByCommodityid(warehouseShelfCommodityDTO.getCommodityId(), warehouseId);
        QYAssert.isTrue(null == queryShelfCommodityByCommodityid, "商品已经绑定到其他的货位");

        WarehouseShelfCommodity insertData = new WarehouseShelfCommodity();
        BeanUtils.copyProperties(warehouseShelfCommodityDTO, insertData);
        return warehouseShelfCommodityMapper.insert(insertData);
    }

    /**
     * 检验货位信息
     * 货位可以重复绑定
     */
    private void checkWarehouseShelf(WarehouseShelfCommodityDTO warehouseShelfCommodityDTO) {
        WarehouseShelf warehouseShelf =  warehouseShelfMapper.selectById(warehouseShelfCommodityDTO.getShelfId());
        QYAssert.isTrue(null != warehouseShelf, "货位不存在，请重新选择");
        QYAssert.isTrue(!warehouseShelf.getStatus().equals(ShelfStatusEnum.DISABLE.getCode()) , "货位已经停用，请重新选择");
        QYAssert.isTrue(warehouseShelf.getType().equals(ShelfTypeEnum.PICK.getCode()) , "不是拣货位，请重新选择");

//        WarehouseShelfCommodity warehouseShelfCommodity = warehouseShelfCommodityMapper.queryShelfCommodityByShelfid(warehouseShelfCommodityDTO.getShelfId());
//        QYAssert.isTrue(null == warehouseShelfCommodity, "货位已经绑定，请选择其他货位");
    }

    /**
     * 检验库存，盘点，拣货
     */
    private void updateOrDeleteCheckData(WarehouseShelfCommodityDTO warehouseShelfCommodityDTO, Long warehouseId) {
        //是否在盘点中,盘点中不能更改
        boolean locked = stockInventoryOrderService.commodityWhetherLocked(warehouseShelfCommodityDTO.getCommodityId());
        QYAssert.isTrue(!locked, "在盘点中不能更改绑定");
        //是否在拣货中，拣货中不能更改 0 1
        int pickOrder = pickOrderMapper.pickOrderByCommodity(warehouseId, warehouseShelfCommodityDTO.getCommodityId());
        QYAssert.isTrue(pickOrder == 0, "拣货中不能更改绑定");
    }

    /**
     * 根据商品id修改绑定关系
     */
    @Transactional
    public Integer updateShelfidByCommodityid(WarehouseShelfCommodityDTO warehouseShelfCommodityDTO) {
        warehouseShelfCommodityDTO.checkData();
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        WarehouseShelfCommodity warehouseShelfCommodity = warehouseShelfCommodityMapper.queryShelfCommodityByCommodityid(warehouseShelfCommodityDTO.getCommodityId(), warehouseId);
        QYAssert.notNull(warehouseShelfCommodity, "该绑定关系不存在");

        //检验货位信息
        checkWarehouseShelf(warehouseShelfCommodityDTO);
        updateOrDeleteCheckData(warehouseShelfCommodityDTO, warehouseId);

        WarehouseShelfCommodity updateData = new WarehouseShelfCommodity();
        updateData.setId(warehouseShelfCommodity.getId());
        updateData.setShelfId(warehouseShelfCommodityDTO.getShelfId());
        return warehouseShelfCommodityMapper.updateById(updateData);
    }

    /**
     * 删除绑定关系(解绑)
     */
    @Transactional
    public Integer deleteByCommodityId(WarehouseShelfCommodityDTO dto) {
        dto.checkData();
        WarehouseShelfCommodity warehouseShelfCommodity = warehouseShelfCommodityMapper.queryByCommodityAndShelf(dto.getCommodityId(), dto.getShelfId());
        QYAssert.isTrue(null != warehouseShelfCommodity, "绑定关系不存在");

        long warehouseId = StockUtils.INSTANCE.warehouseId();
        StockItemDTO stockItemDTO = shopCommodityService.queryStock(warehouseId, dto.getCommodityId());
        QYAssert.isTrue(stockItemDTO.getStockNumber() <= 0, "有库存不能解除绑定");

        updateOrDeleteCheckData(dto, warehouseId);
        return warehouseShelfCommodityMapper.deleteById(warehouseShelfCommodity.getId());
    }

    /**
     * 根据货位信息，查询商品列表（分页）
     */
    @Transactional
    public MPage<QueryCommodityByShelfResult> queryCommodityByWarehouseShelf(QueryCommodityByShelfDTO dto){
        Long warehouseId = StockUtils.INSTANCE.warehouseId();
        dto.setWarehouseId(warehouseId);
        MPage<QueryCommodityByShelfResult> page =warehouseShelfCommodityMapper.queryCommodityByWarehouseShelf(dto);
        //商品条码
        Map<Long, List<String>> longListMap = new HashMap<>();
        if (!page.getList().isEmpty()) {
            List<Long> commodityIds = page.getList().stream().map(QueryCommodityByShelfResult::getCommodityId).collect(Collectors.toList());
            longListMap = commodityBarCodeService.queryCommodityBarCodeList(commodityIds);
        }

        //查询在盘点中的商品
        List<Long> longs = stockInventoryOrderItemMapper.lockedInventoryCommodityId(warehouseId);
        //查询正在拣货中的商品
        List<Long> longList = pickOrderMapper.pickOrderMidCommodityId(warehouseId);
        for (QueryCommodityByShelfResult e : page.getList()) {
            boolean isLock = longs.contains(e.getCommodityId());
            if (isLock) {
                e.setLocked(1);
            } else {
                e.setLocked(0);
            }
            boolean pickStatus = longList.contains(e.getCommodityId());
            if (pickStatus) {
                e.setPickStatus(1);
            } else {
                e.setPickStatus(0);
            }
            e.setBarCodeList(longListMap.get(e.getCommodityId()));
        }
        return page;
    }

    /**
     * 根据货位信息，查询商品列表（不分页）
     */
    public List<QueryCommodityByShelfResult> queryCommodityNotPage(QueryCommodityByShelfDTO dto) {
        dto.setWarehouseId(StockUtils.INSTANCE.warehouseId());
        dto.notLimit();
        return warehouseShelfCommodityMapper.queryCommodityByWarehouseShelf(dto).getList();
    }

    /**
     * 批量绑定商品和货位
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(ShelfCommodityBatchInsertDTO dto) {
        log.info("批量绑定货位入参={}",dto);
        dto.checkData();
        List<ShelfCommodityDTO> res = null;
        List<Long> shelfNos = dto.getDtoList().stream().map(ShelfCommodityDTO::getShelfId).distinct().collect(Collectors.toList());
        QYAssert.isTrue(shelfNos.size() == dto.getDtoList().size(), "货位不可以重复");
        List<Long> commodityIds = dto.getDtoList().stream().map(ShelfCommodityDTO::getCommodityId).distinct().collect(Collectors.toList());
        QYAssert.isTrue(commodityIds.size() == dto.getDtoList().size(), "存在重复商品");
        List<ShelfCommodityDTO> resDTOList = warehouseShelfCommodityMapper.queryBindCommodity(dto.getWarehouseId(), commodityIds, shelfNos);
        //拼装异常数据
        if (!CollectionUtils.isEmpty(resDTOList)) {
            res = new ArrayList<>();
            List<Long> commodityId = resDTOList.stream().map(ShelfCommodityDTO::getCommodityId).collect(Collectors.toList());
            List<Long> shelfId = resDTOList.stream().map(ShelfCommodityDTO::getShelfId).collect(Collectors.toList());
            for (ShelfCommodityDTO e : dto.getDtoList()) {
                if (commodityId.contains(e.getCommodityId()) || shelfId.contains(e.getShelfId())) {
                    res.add(e);
                }
            }
            //获取重复绑定的货位和商品的名称
            List<WarehouseShelf> shelfList = warehouseShelfMapper.selectBatchIds(res.stream().map(ShelfCommodityDTO::getShelfId).collect(Collectors.toList()));
            List<Commodity> commodityList = commodityMapper.selectBatchIds(res.stream().map(ShelfCommodityDTO::getCommodityId).collect(Collectors.toList()));
            Map<Long, String> shelfMap = shelfList.stream().collect(Collectors.toMap(WarehouseShelf::getId, WarehouseShelf::getShelfNo));
            Map<Long, String> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityName));
            StringBuilder builder = new StringBuilder();
            for (ShelfCommodityDTO e : res) {
                builder.append(commodityMap.get(e.getCommodityId()));
                builder.append("和");
                builder.append(shelfMap.get(e.getShelfId()));
                builder.append("绑定异常。");
            }
            log.info("异常提示={}",builder.toString());
            throw new BizLogicException(builder.toString());
        }
        WarehouseShelfCommodity warehouseShelfCommodity = null;
        for (ShelfCommodityDTO e : dto.getDtoList()) {
            warehouseShelfCommodity = new WarehouseShelfCommodity();
            warehouseShelfCommodity.setCommodityId(e.getCommodityId());
            warehouseShelfCommodity.setShelfId(e.getShelfId());
            warehouseShelfCommodityMapper.insert(warehouseShelfCommodity);
        }
    }

    /**
     * 根据货位id查询绑定商品
     * @param shelfId
     * @return
     */
    public Long queryCommodityIdByShelf(Long shelfId) {
        Long res = null;
        LambdaQueryWrapper query = new LambdaQueryWrapper<WarehouseShelfCommodity>()
                .eq(WarehouseShelfCommodity::getShelfId, shelfId);
        List<WarehouseShelfCommodity> list = warehouseShelfCommodityMapper.selectList(query);
        if (!CollectionUtils.isEmpty(list)) {
            res = list.get(0).getCommodityId();
        }
        return res;
    }

    public Map<String,String> queryShelfnoByCommodity(Long warehouseId, List<Long> commodityIds) {
        List<WarehouseShelfnoCommodityDTO> list = warehouseShelfCommodityMapper.queryShelfnoByCommodity(warehouseId, commodityIds);
        return list.stream().collect(Collectors.toMap(e->e.getWarehouseId().toString()+e.getCommodityId(),WarehouseShelfnoCommodityDTO::getShelfNo));
    }
}
