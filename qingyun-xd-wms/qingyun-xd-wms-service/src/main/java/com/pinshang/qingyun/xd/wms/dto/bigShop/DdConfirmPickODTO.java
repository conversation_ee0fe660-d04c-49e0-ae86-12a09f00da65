package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Set;

@Data
@ApiModel("DdConfirmPickODTO")
public class DdConfirmPickODTO implements Serializable {

    @ApiModelProperty("订单短号集合")
    private Set<String> orderNumList;

    @ApiModelProperty("订单短号集合字符串")
    private String orderNumListStr;

    public Set<String> getOrderNumList() {
        return SpringUtil.isEmpty(orderNumList) ? Collections.EMPTY_SET : orderNumList;
    }

    public String getOrderNumListStr() {

        if (SpringUtil.isNotEmpty(orderNumList)) {
            return String.join("、", orderNumList);
        }

        return "";
    }
}
