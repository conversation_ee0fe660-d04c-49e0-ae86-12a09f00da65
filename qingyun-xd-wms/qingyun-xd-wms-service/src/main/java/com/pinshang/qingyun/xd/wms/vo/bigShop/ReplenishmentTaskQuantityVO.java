package com.pinshang.qingyun.xd.wms.vo.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
补货数量
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReplenishmentTaskQuantityVO {

    @ApiModelProperty(value = "排面补货数量")
    private Long shelfAreaQuantity;

    @ApiModelProperty(value = "拣货位补货数量")
    private Long pickingAreaQuantity;
}
