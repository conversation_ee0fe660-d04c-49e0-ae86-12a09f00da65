package com.pinshang.qingyun.xd.wms.dto.pkg;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xd.wms.enums.DeliveryBatchEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/1/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderODTO {

    @ApiModelProperty(value = "门店编码")
    private String shopCode;
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    @ApiModelProperty(value = "云超订单编码")
    private String orderCode;
    @ApiModelProperty(value = "下单时间")
    private Date createTime;

    @ApiModelProperty(value = "送货日期")
    private Date orderTime;
    @ApiModelProperty(value = "配送批次 0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次(装箱用)")
    private Integer deliveryBatch;
    @ApiModelProperty(value = "预约到货时间")
    private String arriveTime;


    @ApiModelProperty(value = "收货人")
    private String receiveMan;
    @ApiModelProperty(value = "收货人手机")
    private String receiveMobile;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    private List<PackageCommodityODTO> itemList;



    public String getCreateTimeStr(){
        return DateUtil.getDateFormate(createTime,"yyyy-MM-dd HH:mm:ss");
    }
    public String getOrderTimeStr(){
        return DateUtil.getDateFormate(orderTime,"yyyy-MM-dd");
    }
    public String getDeliveryBatchName(){
        return DeliveryBatchEnum.getName(deliveryBatch);
    }
}
