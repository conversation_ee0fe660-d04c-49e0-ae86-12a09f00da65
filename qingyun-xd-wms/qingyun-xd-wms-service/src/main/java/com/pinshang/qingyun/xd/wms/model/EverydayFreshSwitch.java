package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: liu zhen
 * @DateTime: 2023/3/2 20:56
 * @Description
 */
@TableName("t_everyday_fresh_switch")
@Data
public class EverydayFreshSwitch {
    @TableId
    private Long id;
    private String todayTime;
    private String tomorrowTime;
    //鲜是
    private Integer xs;
    private Integer xd;
    private Integer thzt;
    private Long updateId;
    private Date updateTime;
}
