package com.pinshang.qingyun.xd.wms.service.stock.bigShop;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import com.pinshang.qingyun.xd.wms.service.stock.StockServiceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class StallQualityStockService {

    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    public Pair<Long, String> stockInOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        if (StockInOutEnum.IN.getCode() == typeEnum.getInOutType()) {
            stockQualityIn(idAndCode, typeEnum, commodityList, warehouseId, userId);
        } else if (StockInOutEnum.OUT.getCode() == typeEnum.getInOutType()) {
            stockQualityOut(idAndCode, typeEnum, commodityList, warehouseId, userId);
        }

        return null;
    }

    private void stockQualityOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {

        //处理库存-
        if (YesOrNoEnums.YES.getCode().equals(typeEnum.getCheckType())) {
            stallCommodityStockService.reduceQualityStock(commodityList, warehouseId);
        } else {
            stallCommodityStockService.reduceQualityStockUnCheck(commodityList, warehouseId);
        }
    }

    private void stockQualityIn(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        stallCommodityStockService.increaseQualityStock(commodityList, warehouseId);
    }
}
