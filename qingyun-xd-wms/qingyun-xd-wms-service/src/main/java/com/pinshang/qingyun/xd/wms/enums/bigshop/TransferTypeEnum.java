package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 移库类型枚举
 * 1-排面补货、2-拣货位补货、3-后仓上架、4-移库
 *
 * <AUTHOR>
 */
public enum TransferTypeEnum {
    SHELF(1, "排面补货"),
    PICKING(2, "拣货位补货"),
    BACKSTAGE_SHELF(3, "后仓上架"),
    TRANSFER(4, "移库"),
    ;
    private Integer code;
    private String name;

    TransferTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static TransferTypeEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferTypeEnum typeEnum : TransferTypeEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
