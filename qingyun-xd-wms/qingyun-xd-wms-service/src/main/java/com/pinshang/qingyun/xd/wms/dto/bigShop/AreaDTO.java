package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AreaDTO {

    private Long id;

    private Long shopId;

    private String areaCode;

    private String areaName;

    /**
     * 区域下的货位数量
     */
    private Long num = 0L;

    public void check() {
        QYAssert.isTrue(null != areaCode, "区域编码不能为空");
        QYAssert.isTrue(null != areaName, "区域名称不能为空");
    }
}
