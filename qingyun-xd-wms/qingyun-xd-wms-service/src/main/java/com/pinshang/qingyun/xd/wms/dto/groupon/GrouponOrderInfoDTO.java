package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderInfoDTO {

    private Long orderId;

    @ApiModelProperty("提货人")
    private String consignee;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("提货预约日期")
    private Date arrivalTime;

//    @ApiModelProperty("提货时间 或 取消时间")
//    private String pickTime;
//
    @ApiModelProperty("提示语")
    private String msg;

    @ApiModelProperty("订单状态,0=已取消, 1=待支付, 2=待拣货,，3=出库中, 4＝待配送，" +
            "5＝配送中，6＝配送成功，7＝配送失败, 8-订单锁定")
    private String status;

    @ApiModelProperty("0=普通订单 1=团购订单 2=云超订单")
    private Integer orderType;

    private Long shopId;
    private String shopCode;
    private String shopName;

    @ApiModelProperty("1已过期  2未到日期")
    private Integer timeStatus;

    @ApiModelProperty("云超专用 1= 门店未验证  4=待顾客提货  7＝顾客已提货")
    private Integer packageStatus;

//    @ApiModelProperty("商品单位")
//    private String commodityUnitName;

    @ApiModelProperty("是否少发 1 是，0否")
    private Integer ifLess;

    @ApiModelProperty("团购商品列表")
    private List<GrouponOrderCommodityListDTO> commodityList;

    @ApiModelProperty("云超包裹列表")
    private List<GrouponPackageDTO> packageList;
}
