package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.dto.QueryLog;
import com.pinshang.qingyun.xd.wms.dto.WarehouseEmployeeLogDTO;
import com.pinshang.qingyun.xd.wms.model.WarehouseEmployeeLog;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseEmployeeLogMapper extends BaseMapper<WarehouseEmployeeLog> {

    int batchInsert(@Param("list") List<WarehouseEmployeeLog> list);

    MPage<WarehouseEmployeeLogDTO> logList(@Param("e") QueryLog queryLog);
}
