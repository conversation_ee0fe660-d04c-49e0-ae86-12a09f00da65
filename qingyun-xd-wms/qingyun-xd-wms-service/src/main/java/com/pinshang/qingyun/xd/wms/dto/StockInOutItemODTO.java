package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 库存出入库明细DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockInOutItemODTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "出入库编号")
    private String orderCode;

    @ApiModelProperty(value = "业务类型")
    private Integer type;

    @ApiModelProperty(value = "相关单据号")
    private String referCode;

    @ApiModelProperty(value = "时间")
    private Date dateTime;

    @ApiModelProperty(value = "操作人")
    private String userName;
}