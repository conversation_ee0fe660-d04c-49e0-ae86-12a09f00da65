package com.pinshang.qingyun.xd.wms.dto.groupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName orderGrouponPageODTO
 * <AUTHOR>
 * @Date 2020/12/21 14:11
 * @Description orderGrouponPageODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrouponOrderPageODTO {
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品条码")
    private String barCode;
    @ApiModelProperty("数量")
    private String purchaseNumber;
    @ApiModelProperty("提货人手机号")
    private String receiveMobile;
    @ApiModelProperty("提货人")
    private String receiveMan;
    @ApiModelProperty("下单人手机号")
    private String userMobile;
    @ApiModelProperty("提货门店")
    private String shopName;
    @ApiModelProperty("门店到货日期")
    private Date arrivalTime;
    @ApiModelProperty("订单状态")
    private Integer orderStatus;
    @ApiModelProperty("提货描述")
    private String statusDesc;
    @ApiModelProperty("提货状态")
    private Integer status;
    @ApiModelProperty("完成提货时间")
    private Date orderCompleteDate;
    @ApiModelProperty("操作人")
    private String employeeName;
    @ApiModelProperty(value = "团购类型字典编码")
    private String typeCode;
    @ApiModelProperty(value = "团购类型字典值")
    private String typeName;
    @ApiModelProperty("订单编号")
    private String orderCode;
}
