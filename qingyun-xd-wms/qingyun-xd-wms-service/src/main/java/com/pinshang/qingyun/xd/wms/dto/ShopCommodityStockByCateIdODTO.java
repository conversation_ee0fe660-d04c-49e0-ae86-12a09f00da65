package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName ShopCommodityStockByCateIdODTO
 * <AUTHOR>
 * @Date 2022/10/18 17:59
 * @Description ShopCommodityStockByCateIdODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityStockByCateIdODTO {
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty("大类id")
    private Long firstCategoryId;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;
}
