package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询货位列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAllocationEnableListODTO {

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private Integer storageArea;

    @ApiModelProperty("库区 1排面区 2拣货区 3存储区")
    private String storageAreaName;

    @ApiModelProperty("货位信息")
    private List<GoodsAllocationItemListODTO> items;

    public String getStorageAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.storageArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }

}