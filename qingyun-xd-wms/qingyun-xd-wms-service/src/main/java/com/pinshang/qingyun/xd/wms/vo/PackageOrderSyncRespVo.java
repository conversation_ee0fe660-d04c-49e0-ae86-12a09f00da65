package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PackageOrderSyncRespVo {

    @ApiModelProperty("需要新增的数据")
    private List<Long> insertList;
    @ApiModelProperty("需要修改的数据")
    private List<Long> updateList;

    public PackageOrderSyncRespVo(List<Long> insertList,List<Long> updateList){
        this.insertList = insertList;
        this.updateList = updateList;
    }
}
