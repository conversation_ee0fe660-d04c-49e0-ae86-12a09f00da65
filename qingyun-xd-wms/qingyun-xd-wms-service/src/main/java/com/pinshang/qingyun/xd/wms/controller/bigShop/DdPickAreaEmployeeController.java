package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.xd.wms.dto.bigShop.*;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickAreaEmployeeService;
import com.pinshang.qingyun.xd.wms.service.bigShop.DdPickPartitionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: sk
 * @Date: 2024/11/5
 */
@Slf4j
@Api(tags = "分区拣货员", description = "分区拣货员")
@RestController
@RequestMapping("/pickAreaEmployee")
public class DdPickAreaEmployeeController {

    @Autowired
    private DdPickAreaEmployeeService ddPickAreaEmployeeService;
    @Autowired
    private DdPickPartitionOrderService ddPickPartitionOrderService;


    @PostMapping("/pageList")
    @MethodRender
    @ApiOperation(value = "分区拣货员列表", notes = "分区拣货员列表")
    public PageInfo<DdPickAreaEmployeeODTO> pickAreaEmployeePageList(@RequestBody DdPickAreaEmployeePageIDTO req) {
        return ddPickAreaEmployeeService.pickAreaEmployeePageList(req);
    }

    @PostMapping("/savePickAreaEmployee")
    @ApiOperation(value = "保存分区拣货员", notes = "保存分区拣货员")
    public Boolean savePickAreaEmployee(@RequestBody DdPickAreaEmployeeSaveIDTO req) {
        return ddPickAreaEmployeeService.savePickAreaEmployee(req);
    }

    @DeleteMapping("deleteByEmployeeId")
    @ApiOperation(value = "移除分区拣货员", notes = "移除分区拣货员")
    public Integer deleteByEmployeeId(@RequestParam(value = "employeeId",required = false) Long employeeId) {
        return ddPickAreaEmployeeService.deleteByEmployeeId(employeeId);
    }

    @GetMapping("/queryPickAreaEmployeeList")
    @ApiOperation(value = "查询已是当前门店拣货员的职员", notes = "查询已是当前门店拣货员的职员")
    public List<Long> queryPickAreaEmployeeList() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return ddPickAreaEmployeeService.queryPickAreaEmployeeList(tokenInfo.getShopId());
    }


    @PostMapping("/workOn")
    @ApiOperation(value = "接单", notes = "接单")
    public Integer workOn(@RequestBody DdPickAreaEmployeeSaveIDTO req) {
        return ddPickAreaEmployeeService.workOnOrOff(req.getIdList(), YesOrNoEnums.YES.getCode());
    }

    @PostMapping("/workOff")
    @ApiOperation(value = "停止接单", notes = "停止接单")
    public Integer workOff(@RequestBody DdPickAreaEmployeeSaveIDTO req) {
        return ddPickAreaEmployeeService.workOnOrOff(req.getIdList(), YesOrNoEnums.NO.getCode());
    }

    @PostMapping("/switchPartitionPickStatus")
    @ApiOperation(value = "启用/停用 合并拣货能力", notes = "启用/停用 合并拣货能力")
    public Boolean switchPartitionPickStatus(@RequestBody DdPickAreaEmployeePartitionPickSwitchIDTO req) {
        return ddPickAreaEmployeeService.switchPartitionPickStatus(req);
    }

    @PostMapping("refreshPickAreaEmployee")
    @ApiOperation(value = "刷新拣货职员", notes = "刷新拣货职员")
    public Boolean refreshPickEmployee(){

        ddPickAreaEmployeeService.refreshPickAreaEmployee();
        return Boolean.TRUE;
    }

    @PostMapping("selectPickAreaEmployeeList")
    @ApiOperation(value = "新增分区拣货员-选择职员", notes = "新增分区拣货员-选择职员")
    public List<DdPickEmployeeInfoODTO> selectPickAreaEmployeeList() {
        return ddPickAreaEmployeeService.selectPickAreaEmployeeList();
    }

    @PostMapping("testSendPickOrderMessage")
    @ApiOperation(value = "testSendPickOrderMessage", notes = "testSendPickOrderMessage")
    public Boolean testSendPickOrderMessage(@RequestParam(value = "employeeId",required = false) Long employeeId){
        Set<Long> employeeIdList = new HashSet<>();
        employeeIdList.add(employeeId);
        ddPickPartitionOrderService.sendPickOrderMessage(employeeIdList);
        return Boolean.TRUE;
    }
}
