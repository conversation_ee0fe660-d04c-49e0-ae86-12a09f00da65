package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 查询已上货位
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckGoodsAllocationODTO {

    @ApiModelProperty("所属门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("货位号")
    private String goodsAllocationCode;

}