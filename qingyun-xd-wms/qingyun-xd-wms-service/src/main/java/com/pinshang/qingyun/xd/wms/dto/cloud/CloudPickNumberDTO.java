package com.pinshang.qingyun.xd.wms.dto.cloud;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudPickNumberDTO {

    private Long subOrderItemId;

    private Long orderItemId;

    /**
     * 总的数量
     */
    private BigDecimal number;

    /**
     * 打包数量
     */
    private BigDecimal packageQuantity;

    /**
     * 打包份数
     */
    private Integer packageNumber;

    private Long commodityId;
}
