package com.pinshang.qingyun.xd.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pinshang.qingyun.xd.wms.bo.PrinterUserTypeQueryReqBO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseWorkDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityCount;
import com.pinshang.qingyun.xd.wms.model.WarehouseWork;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.vo.PrinterUserTypeQueryRspVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseWorkMapper extends BaseMapper<WarehouseWork> {

    /**
     * 更新加工点状态
     * @param warehouseWork
     * @return
     */
    Integer updateWarehouseWorkStatus(@Param("warehouseWork") WarehouseWork warehouseWork);

    /**
     * 根据id查询加工点
     * @param workId
     * @param warehouseId
     * @return
     */
    WarehouseWork selectById(@Param("workId") Long workId, @Param("warehouseId") Long warehouseId);

    /**
     * 根据状态查询加工点
     * @param status
     * @return
     */
    List<WarehouseWorkDTO> warehouseWorkByStatus(@Param("status") Integer status, @Param("warehouseId") Long warehouseId);

    /**
     * 根据条件查询加工点
     * @param data
     * @return
     */
    MPage<QueryWorkByConditionResult> queryWorByCondition(@Param("data") QueryWorkByConditionDTO data);

    /**
     * 查询加工点绑定的商品数量
     * @param warehouseId
     * @return
     */
    List<WorkCommodityCount> workCommodityCount(@Param("warehouseId") Long warehouseId);

    MPage<PrinterUserTypeQueryRspVO> pageByShopId(@Param("dto") PrinterUserTypeQueryReqBO bo);
}
