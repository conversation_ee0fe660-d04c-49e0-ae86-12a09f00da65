package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import lombok.Data;

import java.util.List;

/**
 * 批量解绑
 */
@Data
public class BatchCancelBindIDTO {

    private Long shopId;

    private Long stallId;

    private List<String> commodityCodes;

    public void check() {
        QYAssert.isTrue(null != shopId, "门店不能为空");
        QYAssert.isTrue(null != stallId, "档口不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityCodes), "商品清单不能为空");
    }
}
