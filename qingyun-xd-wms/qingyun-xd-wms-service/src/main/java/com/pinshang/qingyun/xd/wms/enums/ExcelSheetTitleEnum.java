package com.pinshang.qingyun.xd.wms.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/19.
 */
public enum ExcelSheetTitleEnum {
    WAREHOUSE_DELIVERY_DETAIL("Warehouse_delivery_detail","云超实发明细报表",new String[] { "门店编码","客户编码","门店","提货日期","发货日期","云超订单编号","一级品类","二级品类","三级品类","条形码","商品编码","商品名称","规格","计量单位","订单数量","结算价","实发数量","结算金额"}),
    STOCK_SNAPSHOT_DETAIL("stock_snapshot_detail","库存快照明细",new String[] { "商品编码","条形码","商品名称","规格","单位","系统库存数量","成本价","库存成本金额","大类","中类","小类","是否称重"}),
    STOCK_OUT_MONITOR ("stock_out_monitor","前置仓库存缺货监控",new String[] { "部门","客户编码","门店名称","商品编码","条形码","商品名称","前台名称","规格","计量单位","是否称重","库存数量","提取时间点"}),
    DD_REPLENISHMENT_TASK ("dd_replenishment_task","大店补货任务列表",new String[] { "档口","商品编码","商品名称","条形码","规格","计量单位","任务来源","任务类型","任务生成时间","建议补货数量","是否完成补货","实际补货数量","对应单据编号","补货人工号","补货姓名","补货时间","状态","flag"}),

    ;
    private String code;
    private String name;
    private String[] titles;
    /** 属性名 */
    private String[] props;

    private ExcelSheetTitleEnum(String code, String name, String[] titles) {
        this.code = code;
        this.name = name;
        this.titles = titles;
    }

    ExcelSheetTitleEnum(String code, String name, String[] titles, String[] props) {
        this.code = code;
        this.name = name;
        this.titles = titles;
        this.props = props;
    }


    /**
     * 动态设置title
     * @param code
     * @param titles
     */
    public static void setTitles(String code, String[] titles) {
        for (ExcelSheetTitleEnum est : ExcelSheetTitleEnum.values()) {
            if (code.equals(est.getCode())) {
                est.setTitles(titles);
                break;
            }
        }
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getTitles() {
        return titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }

    public String[] getProps() {
        return props;
    }

    public void setProps(String[] props) {
        this.props = props;
    }
}
