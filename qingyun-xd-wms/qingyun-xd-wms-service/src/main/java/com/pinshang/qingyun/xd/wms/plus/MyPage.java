package com.pinshang.qingyun.xd.wms.plus;

import lombok.Data;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/10/20
 */

@Data
public class MyPage<T> {
    private long pages;
    private String pageSize;
    private long currentPage;
    private boolean hasNextPage;
    private List<T> list;


    public static <T> MyPage<T> toMyPage(MPage<T> result) {
        MyPage<T> page = new MyPage<>();
        page.setPages(result.getPages());
        page.setPageSize(String.valueOf(result.getSize()));
        page.setCurrentPage(result.getCurrentPage());
        page.setHasNextPage(result.getHasNextPage());
        page.setList(result.getRecords());

        return page;
    }
}