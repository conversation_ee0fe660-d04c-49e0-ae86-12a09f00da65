package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.dto.OrderCommodityList;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分区拣货单明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PartitionOrderCommodityResult {

    @ApiModelProperty(value = "分区拣货单信息")
    private PickPartitionOrderListResult pickPartitionOrderListResult;

    @ApiModelProperty(value = "加工商品")
    private List<OrderCommodityList> processCommodity;

    @ApiModelProperty(value = "非加工商品")
    private List<OrderCommodityList> noProcessCommodity;
}
