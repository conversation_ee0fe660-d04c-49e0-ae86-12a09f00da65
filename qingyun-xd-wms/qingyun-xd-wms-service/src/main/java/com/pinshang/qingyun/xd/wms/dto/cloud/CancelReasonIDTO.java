package com.pinshang.qingyun.xd.wms.dto.cloud;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 订单取消原因
 */
@Data
public class CancelReasonIDTO {
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("取消订单原因选项id, 查字典接口-> optionCode: xd-order-cancel-reason, 并且只展示memo = 1的option")
    private Long reasonOptionId;
    @ApiModelProperty("补充原因")
    private String reason;
}
