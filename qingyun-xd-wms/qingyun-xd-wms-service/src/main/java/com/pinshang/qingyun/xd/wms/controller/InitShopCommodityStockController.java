package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.xd.wms.service.InitShopCommodityStockService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: hhf
 * @time: 2022/10/11/011 11:06
 */
@RestController
@RequestMapping("/initShopCommodityStock")
@Api(value = "期初门店商品库存相关字段", tags = "InitShopCommodityStockController")
public class InitShopCommodityStockController {

    @Autowired
    private InitShopCommodityStockService initShopCommodityStockService;

    /**
     * 期初字段shop_commodity
     */
    @GetMapping(value = "/initShopCommodityStockShopIdAndCommodityId")
    public void initShopCommodityStockShopIdAndCommodityId(){
        initShopCommodityStockService.initShopCommodityStockShopIdAndCommodityId();
    }
}
