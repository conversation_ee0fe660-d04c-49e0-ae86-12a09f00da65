package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/11/5
 */
@Data
public class DdPickAreaEmployeeLogDTO {

    /** 门店ID*/
    private Long shopId;

    /** 拣货分区id*/
    private Long pickAreaId;

    /** 拣货区域名称*/
    private String pickAreaName;

    /** 职员Id*/
    private Long employeeId;

    /** 职员编码*/
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeCode, keyName = "employeeId")
    private String employeeCode;

    /** 职员名称*/
    @FieldRender(fieldType = FieldTypeEnum.EMPLOYEE, fieldName = RenderFieldHelper.Employee.employeeName, keyName = "employeeId")
    private String employeeName;

    /** 操作类型 1=开始接单  2=停止接单 */
    private Integer operateType;

    private Long createId;
    private String createName;
    private String createTime;
}
