package com.pinshang.qingyun.xd.wms.enums.bigshop;

/**
 * 补货操作标志
 *
 * <AUTHOR>
 */
public enum ReplenishmentFlagEnum {
    NOT_COMPLETED(0, "未点击完成补货"),
    COMPLETED(1, "点击完成补货"),
    ;
    private Integer code;
    private String name;

    ReplenishmentFlagEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ReplenishmentFlagEnum getTypeEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReplenishmentFlagEnum typeEnum : ReplenishmentFlagEnum.values()) {
            if (typeEnum.code.compareTo(code) == 0) {
                return typeEnum;
            }
        }
        return null;
    }
}
