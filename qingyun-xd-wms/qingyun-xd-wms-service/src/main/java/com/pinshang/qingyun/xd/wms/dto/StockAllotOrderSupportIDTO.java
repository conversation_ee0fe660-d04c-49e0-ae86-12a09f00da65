package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class StockAllotOrderSupportIDTO  extends Pagination {
	private static final long serialVersionUID = 1L;

    /**
     * 调拨类型 1调拨入库申请 2调拨出库申请
     */
    private Integer allotType;

    /**
     * 开始时间
     */
    private String businessDateStart;

    /**
     * 结束时间
     */
    private String businessDateEnd;

}
