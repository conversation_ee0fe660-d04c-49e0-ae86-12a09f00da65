package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-05-28-23:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotPageODTO extends StockSnapshotListForStockAndPriceODTO{
    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty("大类")
    private String commodityFirstKindName;

    @ApiModelProperty("中类")
    private String commoditySecondKindName;

    @ApiModelProperty("小类")
    private String commodityThirdKindName;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

}
