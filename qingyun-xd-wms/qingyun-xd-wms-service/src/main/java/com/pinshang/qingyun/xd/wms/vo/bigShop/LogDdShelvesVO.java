package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 排面货架管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
public class LogDdShelvesVO implements Serializable {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("档口code")
    private String stallCode;

    @ApiModelProperty("档口名称")
    private String stallName;

    private Long shelveId;

    @ApiModelProperty("排面货架编码")
    private String shelveCode;

    @ApiModelProperty("排面货架名称")
    private String shelveName;

    private Integer operateType;

    private String operateUserName;

    private String operateUserCode;

    private Long operateUserId;

    private String operateTime;

}
