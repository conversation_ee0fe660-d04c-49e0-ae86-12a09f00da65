package com.pinshang.qingyun.xd.wms.mapper;

import com.pinshang.qingyun.xd.wms.dto.report.StockBreakageIDTO;
import com.pinshang.qingyun.xd.wms.dto.report.StockBreakageODTO;
import com.pinshang.qingyun.xd.wms.dto.report.StockShortIDTO;
import com.pinshang.qingyun.xd.wms.dto.report.StockShortODTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface StockReportMapper  {

    MPage<StockBreakageODTO> queryStockBreakagePage(@Param("e") StockBreakageIDTO idto);

    MPage<StockShortODTO> queryStockShortPage(@Param("e") StockShortIDTO idto);
}
