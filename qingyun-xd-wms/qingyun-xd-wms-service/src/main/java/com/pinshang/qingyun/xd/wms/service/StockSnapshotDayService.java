package com.pinshang.qingyun.xd.wms.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.store.StoreODTO;
import com.pinshang.qingyun.order.service.StoreClient2;
import com.pinshang.qingyun.shop.dto.*;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.org.OrgAndParentInfoODTO;
import com.pinshang.qingyun.smm.dto.org.SelectShopOrgInfoListIDTO;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.OrgClient;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotDayIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceODTO;
import com.pinshang.qingyun.xd.wms.enums.StockCalculateTypeEnum;
import com.pinshang.qingyun.xd.wms.mapper.ShopMapper;
import com.pinshang.qingyun.xd.wms.mapper.StockSnapshotDayMapper;
import com.pinshang.qingyun.xd.wms.model.Shop;
import com.pinshang.qingyun.xd.wms.model.StockSnapshotDay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockSnapshotDayService {

    @Autowired
    private StockLogSnapshotService stockLogSnapshotService;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private StockSnapshotDayMapper stockSnapshotDayMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private StoreClient2 storeClient2;
    /**
     * job 每天的库存快照 date是指跑某一天的日期
     */
    public void insertStockSnapshotDay(String date) {
        //删除当天的数据
        stockSnapshotDayMapper.deleteByStockDate(date);

        List<Shop> shopList = shopMapper.selectList(null);
        if (null != shopList && shopList.size() > 0) {

            // 构造一个线程池
            ThreadPoolExecutor threadPool = new ThreadPoolExecutor(4, 8, 3,
                    TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(shopList.size())
            );

            for (Shop shop : shopList) {
                threadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            getStockSnapshotDay(shop, date);
                        }catch (Exception e) {
                            log.error("门店={}日期={}库存快照出错:{}",shop.getId(),date,e);
                        }

                    }
                });
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void getStockSnapshotDay(Shop shop, String date) {

        StockSnapshotListForStockAndPriceIDTO dto = new StockSnapshotListForStockAndPriceIDTO();
        StockSnapshotDay stockSnapshotDay1 = new StockSnapshotDay();
        StockSnapshotDay stockSnapshotDay2 = new StockSnapshotDay();
        dto.setShopId(shop.getId());
        dto.setDate(date);
        List<StockSnapshotListForStockAndPriceODTO> list = stockLogSnapshotService.listForStockAndPrice(dto);
        stockSnapshotDay1.setShopId(shop.getId());
        stockSnapshotDay1.setCreateTime(new Date());
        stockSnapshotDay1.setStockDate(DateUtil.parseDate(date, "yyyy-MM-dd"));
        BeanUtils.copyProperties(stockSnapshotDay1, stockSnapshotDay2);
        if (null != list && list.size() > 0) {
            stockSnapshotDay1.setStockItem(list.size());
            //负库存原样计算
            BigDecimal stockWeightPrice = list.stream().filter(e -> e.getTotalPrice() != null).map(StockSnapshotListForStockAndPriceODTO::getTotalPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            stockSnapshotDay1.setStockWeightPrice(stockWeightPrice);
            stockSnapshotDay1.setStockStatus(StockCalculateTypeEnum.NORMAL.getCode());
            stockSnapshotDayMapper.insert(stockSnapshotDay1);
            //负库存为0计算
            stockSnapshotDay2.setStockItem(list.size());
            stockWeightPrice = list.stream().filter(e -> e.getTotalPrice() != null && e.getTotalPrice().compareTo(BigDecimal.ZERO) > 0)
                    .map(StockSnapshotListForStockAndPriceODTO::getTotalPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            stockSnapshotDay2.setStockWeightPrice(stockWeightPrice);
            stockSnapshotDay2.setStockStatus(StockCalculateTypeEnum.UN_NORMAL.getCode());
            stockSnapshotDayMapper.insert(stockSnapshotDay2);
        } else {
            stockSnapshotDay1.setStockItem(0);
            stockSnapshotDay1.setStockWeightPrice(BigDecimal.ZERO);
            stockSnapshotDay1.setStockStatus(StockCalculateTypeEnum.NORMAL.getCode());
            stockSnapshotDayMapper.insert(stockSnapshotDay1);
            stockSnapshotDay2.setStockItem(0);
            stockSnapshotDay2.setStockWeightPrice(BigDecimal.ZERO);
            stockSnapshotDay2.setStockStatus(StockCalculateTypeEnum.UN_NORMAL.getCode());
            stockSnapshotDayMapper.insert(stockSnapshotDay2);
        }
    }

    public TablePageInfo<StockSnapshotDayDTO> stockList(StockSnapshotDayIDTO dto) {
        // 查询日期往后一天
        Date date = DateUtil.parseDate(dto.getStockDate(), "yyyy-MM-dd");
        Date nextDate = DateUtil.addDay(date, 1);
        dto.setStockDate(DateUtil.get4yMd(nextDate));

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        //当用户没有门店权限时，不再查询数据
        if(SpringUtil.isEmpty( shopIdList )){
            return  new TablePageInfo<>();
        }
        if (dto.getShopId() == null && StringUtils.isNotBlank(dto.getDeptCode())){
            //根据部门查询门店
            List<ShopDropdownInfoODTO> shopDropdownInfoODTOS = selectUserDepartmentShopDropdownInfoList(dto.getDeptCode(), shopIdList);
            if (CollectionUtils.isEmpty(shopDropdownInfoODTOS)){
                return new TablePageInfo<>();
            }
            dto.setShopIdList(shopDropdownInfoODTOS.stream().map(ShopDropdownInfoODTO::getShopId).collect(Collectors.toList()));
        }else {
            dto.setShopIdList(shopIdList);
        }
        // 查询的日期比实际表中的数据早一天, 日期做+1处理后查询

        PageInfo<StockSnapshotDayDTO> pageInfo = PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            stockSnapshotDayMapper.stockList(dto);
        });

        if (pageInfo.getSize() > 0) {
            List<StockSnapshotDayDTO> list = pageInfo.getList();
            List<Long> resultShopIdList = list.stream().map(StockSnapshotDayDTO::getShopId).collect(Collectors.toList());
            Map<Long, OrgAndParentInfoODTO> map = getOrgMap(resultShopIdList);
            DecimalFormat format = new DecimalFormat("0.00");
            for (StockSnapshotDayDTO day : pageInfo.getList()) {
                if (map.containsKey(day.getShopId())) {
                    day.setDeptName(map.get(day.getShopId()).getParentOrgName());
                }

                day.setStockWeightPrice(format.format(day.getPrice()));

                // 返回数据中展示的日期往前一天
                day.setCreateTime(DateUtil.addDay(day.getCreateTime(), -1));
            }

            //根据客户id 查询 门店关联客户信息
            List<Long> storeIdList = list.stream().map(StockSnapshotDayDTO::getStoreId).collect(Collectors.toList());
            List<StoreODTO> storeList = storeClient2.findStoreListByStoreIdList(storeIdList);
            if(SpringUtil.isNotEmpty(storeIdList)){
                Map<Long, StoreODTO> storeMap = storeList.stream().collect(Collectors.toMap(StoreODTO::getId, store -> store));
                for(StockSnapshotDayDTO shopEntry : list){
                    if(null != shopEntry.getStoreId()){
                        StoreODTO storeODTO = storeMap.get(shopEntry.getStoreId());
                        if(null!= storeODTO){
                            shopEntry.setStoreCode(storeODTO.getStoreCode());
                        }
                    }
                }
            }
        }

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        StockSnapshotDayDTO header = totalPrice(dto);
        tablePageInfo.setHeader(header);

        return tablePageInfo;
    }

    public StockSnapshotDayDTO totalPrice(StockSnapshotDayIDTO dto) {
        List<StockSnapshotDayDTO> list = stockSnapshotDayMapper.stockList(dto);
        StockSnapshotDayDTO res = new StockSnapshotDayDTO();
        BigDecimal totalPrice = BigDecimal.ZERO;
        if (null != list && list.size() > 0) {
            if (null != dto.getStockStatus() && YesOrNoEnums.YES.getCode().equals(dto.getStockStatus())) {
                totalPrice = list.stream().filter(e -> e.getStockItem() > 0).map(StockSnapshotDayDTO::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            } else {
                totalPrice = list.stream().map(StockSnapshotDayDTO::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
        }
        DecimalFormat format = new DecimalFormat("0.00");
        res.setStockWeightPrice(format.format(totalPrice));
        return res;
    }

    /**
     * 根据shopIdList 获取上级组织
     * @param shopIdList
     * @return
     */
    public Map<Long, OrgAndParentInfoODTO> getOrgMap(List<Long> shopIdList){
        Map<Long,OrgAndParentInfoODTO> orgMap = new HashMap<>();
        SelectShopOrgInfoListIDTO selectShopOrgInfoListIDTO = new SelectShopOrgInfoListIDTO();
        selectShopOrgInfoListIDTO.setShopIdList(shopIdList);
        List<OrgAndParentInfoODTO> orgList = orgClient.selectShopOrgInfoList(selectShopOrgInfoListIDTO);
        if(CollectionUtils.isNotEmpty(orgList)){
            orgMap = orgList.stream().collect(Collectors.toMap(OrgAndParentInfoODTO::getRefObjId, e -> e));
        }
        return orgMap;
    }

    public List<ShopDropdownInfoODTO> selectUserDepartmentShopDropdownInfoList(String deptCode, List<Long> shopIdList) {
        SelectUserDepartmentShopDropdownInfoListIDTO idto = new SelectUserDepartmentShopDropdownInfoListIDTO();
        idto.setDepartmentCode(deptCode);
        idto.setUserShopIdList(shopIdList);
        idto.setLimitQuantity(9999);
        List<ShopDropdownInfoODTO> shopDropdownInfoODTOS = shopClient.selectUserDepartmentShopDropdownInfoList(idto);
        return shopDropdownInfoODTOS;
    }

}
