package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/29
 * @Version 1.0
 */
@Data
public class StallCommodityStockExport1ODTO {

    @ApiModelProperty("部门")
    @ExcelProperty("部门")
    private String deptName;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    private String storeCode;

    @ExcelProperty("档口")
    @ApiModelProperty("档口")
    private String stallName;

    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    @ExcelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("前台品名")
    @ExcelProperty("前台品名")
    private String commodityAppName;

    @ExcelProperty(value = "是否必售")
    @ApiModelProperty(value = "是否必售")
    private String saleStatusName;

    @ApiModelProperty("规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("包装规格")
    @ExcelProperty("包装规格")
    private String commodityPackageSpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("库存总数量")
    @ApiModelProperty("库存总数量=正常库数量+临时库数量")
    private BigDecimal totalQuantity;

    @ExcelProperty("排面区数量")
    @ApiModelProperty("排面区数量")
    private BigDecimal stockQuantity;

    @ExcelProperty("拣货区数量")
    @ApiModelProperty("拣货区数量")
    private BigDecimal pickingAreaStock;

    @ExcelProperty("存储区库存")
    @ApiModelProperty("存储区库存")
    private BigDecimal warehouseAreaStock;

    @ExcelProperty("临时库存")
    @ApiModelProperty("临时库存")
    private BigDecimal stockProvisional;

    @ExcelProperty("冻结库存")
    @ApiModelProperty("冻结库存")
    private Integer freezeNumber;

    /**
     * 上下架状态：0-上架，1-下架
     */
    @ApiModelProperty("上架状态")
    @ExcelIgnore
    private Integer appStatus;

    @ApiModelProperty("上架状态")
    @ExcelProperty("上架状态")
    private String appStatusStr;

    @ExcelProperty(value = "后台一级品类")
    @ApiModelProperty("后台一级品类")
    private String commodityFirstName;

    @ExcelProperty(value = "后台二级品类")
    @ApiModelProperty("后台二级品类")
    private String commoditySecondName;

    @ExcelProperty(value = "后台三级品类")
    @ApiModelProperty("后台三级品类")
    private String commodityThirdKindName;

    public BigDecimal getStockQuantity() {
        return null == stockQuantity? BigDecimal.ZERO : stockQuantity ;
    }

    public BigDecimal getPickingAreaStock() {
        return null == pickingAreaStock? BigDecimal.ZERO : pickingAreaStock;
    }

    public BigDecimal getWarehouseAreaStock() {
        return null == warehouseAreaStock? BigDecimal.ZERO : warehouseAreaStock;
    }

    public BigDecimal getStockProvisional() {
        return null == stockProvisional ? BigDecimal.ZERO : stockProvisional;
    }

    public Integer getFreezeNumber() {
        return null == freezeNumber ? 0 : freezeNumber;
    }
}
