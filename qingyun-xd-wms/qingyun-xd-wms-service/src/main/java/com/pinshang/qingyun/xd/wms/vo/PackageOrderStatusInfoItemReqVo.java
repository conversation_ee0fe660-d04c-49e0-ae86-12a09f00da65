package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageOrderStatusInfoItemReqVo {

    @ApiModelProperty("包裹单Id")
    private Long orderId;
    @ApiModelProperty("包裹单编号")
    private String orderCode;
    @ApiModelProperty("包裹单状态信息")
    private Integer packageStatus;
    @ApiModelProperty("包裹单明细Id")
    private Long orderItemId;
    @ApiModelProperty("包裹单明细信息")
    private String orderItemInfo;

}
