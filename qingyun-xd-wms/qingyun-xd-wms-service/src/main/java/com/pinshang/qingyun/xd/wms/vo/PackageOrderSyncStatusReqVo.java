package com.pinshang.qingyun.xd.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PackageOrderSyncStatusReqVo {

    @ApiModelProperty("订单Id")
    private Long orderId;
    @ApiModelProperty("包裹状态(门店使用)　0=已取消 10=待装筐，12=打包中, 15=已装箱,20=待揽收，30=待卸货，40=待收货，50=待门店拣货，60=已拣货( 一期时状态  1= 门店未验证  4=待顾客提货  7＝顾客已提货）")
    private Integer packageStatus;
    private List<PackageOrderSyncStatusItemReqVo> orderItemList;

}
