package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


@Data
@ToString
@ApiModel("DdShelvesSaveIDTO")
public class DdShelvesSaveIDTO {

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("排面货架编码")
    private String shelveCode;

    @ApiModelProperty("排面货架名称")
    private String shelveName;

}