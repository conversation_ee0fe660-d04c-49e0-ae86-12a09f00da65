package com.pinshang.qingyun.xd.wms.service.platform;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdDeliveryModeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.xd.order.dto.XdOrderODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.dto.PickOrderMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
public class PlatFormPickOrderService {

    @Autowired
    private XdOrderClient xdOrderClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    /**
     * 云超拣货完成处理
     * @param mqDTO
     */
    public Boolean cloudPickSuccess(PickOrderMqDTO mqDTO) {
        Boolean res = Boolean.FALSE;
        XdOrderODTO orderODTO = xdOrderClient.findByOrderId(mqDTO.getOrderId());
        if (null != orderODTO && XdDeliveryModeEnum.SF_DELIVERY.getCode() == orderODTO.getDeliveryMode()) {
            //云超 玖琅需要判断是否需要推送顺丰
            if (Objects.equals(OrderSourceTypeEnum.CLOUD_APP.getCode(), orderODTO.getSourceType().getCode()) ||
                    Objects.equals(OrderSourceTypeEnum.CLOUD_MINI.getCode(), orderODTO.getSourceType().getCode()) ||
                    Objects.equals(OrderSourceTypeEnum.JIULANG.getCode(), orderODTO.getSourceType().getCode()) ) {
                //判断是否需要提前推送顺丰
                DictionaryODTO dictionary = dictionaryClient.getDictionaryByCode("advance_push_time");
                Integer advanceTime = 60;
                if (null != dictionary) {
                    advanceTime = Integer.parseInt(dictionary.getOptionValue());
                }
                Date pushDate = DateUtil.addMinute(advanceTime);
                if (pushDate.before(orderODTO.getReceiveTimeBegin())) {
//                return;
                } else {
                    res = Boolean.TRUE;
                }
            } else if ( Objects.equals(OrderSourceTypeEnum.FARM_APP.getCode(), orderODTO.getSourceType().getCode()) ||
                    Objects.equals(OrderSourceTypeEnum.FARM_MINI.getCode(), orderODTO.getSourceType().getCode()) ) {
                //大店是顺丰配送模式
                if (XdDeliveryModeEnum.SF_DELIVERY.getCode() == orderODTO.getDeliveryMode()) {
                    res = Boolean.TRUE;
                }
            }

        }
        return res;
    }
}
