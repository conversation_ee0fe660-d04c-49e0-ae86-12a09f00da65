package com.pinshang.qingyun.xd.wms.bo;

import com.pinshang.qingyun.xd.wms.dto.StockAllotOrderApplyDTO;
import com.pinshang.qingyun.xd.wms.dto.StockAllotOrderItemApplyDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderApplyBO extends StockAllotOrderApplyDTO {

    /**
     * 是否是大店
     */
    private boolean isBigShop;

    /**
     * 是否店内调拨
     */
    private Integer isInstoreAllot;


}
