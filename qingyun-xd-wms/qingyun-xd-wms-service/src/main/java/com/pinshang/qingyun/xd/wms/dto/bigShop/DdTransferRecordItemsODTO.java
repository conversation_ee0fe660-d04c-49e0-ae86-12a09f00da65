package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 移库单 明细列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
@ApiModel("DdTransferRecordItemsODTO")
public class DdTransferRecordItemsODTO {

    @ApiModelProperty("移库单明细ID")
    private Long id;

    @ApiModelProperty("移库单表主键id")
    private Long transferRecordId;

    @ApiModelProperty("移出库区 1拣货区 2存储区 3排面区 4临时库")
    private Integer outStoreArea;

    @ApiModelProperty(value = "移库区域名称")
    private String outStoreAreaName;

    @ApiModelProperty("移出货位code")
    private String outGoodsAllocationCode;

    @ApiModelProperty("移入库区 1拣货区 2存储区 3排面区 4临时库")
    private Integer inStoreArea;

    @ApiModelProperty(value = "移入区域名称")
    private String inStoreAreaName;

    @ApiModelProperty("移入货位code")
    private String inGoodsAllocationCode;

    @ApiModelProperty("移库数量")
    private BigDecimal moveQuantity;

    private Long createId;

    private Date createTime;

    private Long updateId;

    private Date updateTime;

    public String getOutStoreAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.outStoreArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }

    public String getInStoreAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(this.inStoreArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }

}