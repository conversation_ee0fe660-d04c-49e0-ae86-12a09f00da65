package com.pinshang.qingyun.xd.wms.controller;


import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.enums.StockAllotOrderStatusEnums;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.xd.wms.dto.PickNumReportDTO;
import com.pinshang.qingyun.xd.wms.dto.PickNumReportListDTO;
import com.pinshang.qingyun.xd.wms.dto.report.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.ReportService;
import com.pinshang.qingyun.xd.wms.util.ExcelExportUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/4/22
 */
@RestController
@RequestMapping("/report")
@Api(value = "鲜到报表", tags = "ReportController")
public class ReportController {

    @Autowired
    private ReportService reportService;

    @Autowired
    private IRenderService renderService;

    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;

    @PostMapping(value = "/pickNumReportList")
    @ApiOperation(value = "拣货数量报表统计")
    public MPage<PickNumReportListDTO> pickNumReportList(@RequestBody PickNumReportDTO dto) {
        return reportService.pickNumReportList(dto);
    }

    @GetMapping(value = "/pickNumReportExport")
    @ApiOperation(value = "拣货数量报表导出")
    @FileCacheQuery(bizCode = "PICK_NUM_REPORT")
    public void pickNumReportExport(PickNumReportDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        MPage<PickNumReportListDTO> res = reportService.pickNumReportList(dto);

        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "拣货考评表" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("门店");
        tableHeader.add("经营模式");
        tableHeader.add("拣货员编码");
        tableHeader.add("拣货员");
        tableHeader.add("拣货完成单数");
        StringBuilder stringBuilder = new StringBuilder();
        List<List<String>> dataList = new ArrayList<>();

        if(res!=null && SpringUtil.isNotEmpty(res.getList())) {
            stringBuilder.setLength(0);
            List<String> one = new ArrayList<>();
            one.add("合计");
            one.add("");
            one.add("");
            one.add("");
            one.add(res.getList().get(0).getNum().toString());
            dataList.add(one);
            for (PickNumReportListDTO e : res.getList().subList(1,res.getList().size())) {
                stringBuilder.setLength(0);
                List<String> row = new ArrayList<>();
                row.add(e.getShopName());
                row.add(e.getManagementModeName());
                row.add(e.getEmployeeCode());
                row.add(e.getEmployeeName());
                row.add(e.getNum().toString());
                dataList.add(row);
            }
        }

        //覆盖文件名, 无需扩展名
        fileName = "拣货考评表" + sdf.format(new Date());
        ExcelUtil.setFileNameAndHead(response,  fileName);
        List<List<String>> excelHead = tableHeader.stream().map(Collections::singletonList).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet("数据").doWrite(dataList);

        /*已重构, 后续稳定后可删除
        XSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbook(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcel(response,fileName,xb);
        */
    }

    /**
     * 报损明细
     * @param stockBreakageIDTO
     * @return
     */
    @GetMapping("/stock/queryStockBreakagePage")
    @ApiOperation(value = "报损明细", notes = "报损明细")
    public MPage<StockBreakageODTO> queryStockBreakagePage(StockBreakageIDTO stockBreakageIDTO) {
        Long pageSize = stockBreakageIDTO.getPageSize();
        if(pageSize.equals(65536L)){
            stockBreakageIDTO.notLimit();
        }
        return reportService.queryStockBreakagePage(stockBreakageIDTO);
    }

    /**
     * 缺发少发明细
     * @param stockShortIDTO
     * @return
     */
    @GetMapping("/stock/queryStockShortPage")
    @ApiOperation(value = "缺发少发明细", notes = "缺发少发明细")
    public MPage<StockShortODTO> queryStockShortPage(StockShortIDTO stockShortIDTO) {
        Long pageSize = stockShortIDTO.getPageSize();
        if(pageSize.equals(65536L)){
            stockShortIDTO.notLimit();
        }
        return reportService.queryStockShortPage(stockShortIDTO);
    }

    @PostMapping("/stock/allotOutDetailList")
    @ApiOperation(value = "调拨出库明细", notes = "调拨出库明细")
    public MPage<StockAllotDetailListODTO> allotOutDetailList(@RequestBody StockAllotDetailListIDTO dto) {
        ddTokenShopIdService.processReadDdTokenShopId(dto.getOutShop(), dto.getOutStallId());
        MPage<StockAllotDetailListODTO> list = reportService.allotOutDetailList(dto);
        renderService.render(list.getList(), "/report/stock/allotOutDetailList");
        return list;
    }

    @PostMapping("/stock/allotInDetailList")
    @ApiOperation(value = "调拨入库明细", notes = "调拨入库明细")
    public MPage<StockAllotDetailListODTO> allotInDetailList(@RequestBody StockAllotDetailListIDTO dto) {
        ddTokenShopIdService.processReadDdTokenShopId(dto.getInShop(), dto.getInStallId());
        MPage<StockAllotDetailListODTO> list = reportService.allotInDetailList(dto);
        renderService.render(list.getList(), "/report/stock/allotInDetailList");
        return list;
    }

    @PostMapping("/stock/allotOutDetailExport")
    @ApiOperation(value = "调拨出库明细导出", notes = "调拨出库明细导出")
    public List<StockAllotDetailListODTO> allotOutDetailExport(@RequestBody StockAllotDetailListIDTO dto) {
        dto.notLimit();
        List<StockAllotDetailListODTO> list = reportService.allotOutDetailList(dto).getList();
        renderService.render(list, "/report/stock/allotOutDetailExport");
        return list;
    }

    @PostMapping("/stock/allotOutDetailExportExcel")
    @ApiOperation(value = "调拨出库明细导出Excel", notes = "调拨出库明细导出")
    @FileCacheQuery(bizCode = "ALLOT_OUT_DETAIL")
    public void allotOutDetailExportExcel(@RequestBody StockAllotDetailListIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        List<StockAllotDetailListODTO> list = reportService.allotOutDetailList(dto).getList();
        renderService.render(list, "/report/stock/allotOutDetailExportExcel");

        List<AllotOutDetailExcelDTO> excelList = list.parallelStream().map(data -> {
            AllotOutDetailExcelDTO excelDto = new AllotOutDetailExcelDTO();
            BeanUtils.copyProperties(data, excelDto);
            //sql中状态: 30,40; 40为完成; 推测逻辑: 从A仓库到B仓库, B仓库入库完成才算完成
            String statusName = "待入库";
            if (Objects.equals(data.getStatus(), StockAllotOrderStatusEnums.IN_SUCCESS.getCode())){
                statusName = "完成";
            }
            excelDto.setStatusName(statusName);
            return excelDto;
        }).collect(Collectors.toList());

        String filename = String.format("调拨出库明细表_%s", DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  filename);
        EasyExcel.write(response.getOutputStream(), AllotOutDetailExcelDTO.class).autoCloseStream(Boolean.FALSE)
                .sheet("数据").doWrite(excelList);
    }

    @PostMapping("/stock/allotInDetailExport")
    @ApiOperation(value = "调拨入库明细导出", notes = "调拨入库明细导出")
    public List<StockAllotDetailListODTO> allotInDetailExport(@RequestBody StockAllotDetailListIDTO dto) {
        dto.notLimit();
        List<StockAllotDetailListODTO> list = reportService.allotInDetailList(dto).getList();
        renderService.render(list, "/report/stock/allotInDetailExport");
        return list;
    }

    @PostMapping("/stock/allotInDetailExportExcel")
    @FileCacheQuery(bizCode = "ALLOT_IN_DETAIL")
    @ApiOperation(value = "调拨入库明细导出Excel", notes = "调拨入库明细导出")
    public void allotInDetailExportExcel(@RequestBody StockAllotDetailListIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        List<StockAllotDetailListODTO> list = reportService.allotInDetailList(dto).getList();
        renderService.render(list, "/report/stock/allotInDetailExportExcel");

        List<AllotInDetailExcelDTO> excelList = list.parallelStream().map(data -> {
            AllotInDetailExcelDTO excelDto = new AllotInDetailExcelDTO();
            BeanUtils.copyProperties(data, excelDto);
            return excelDto;
        }).collect(Collectors.toList());

        String filename = String.format("调拨入库明细表_%s", DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  filename);
        EasyExcel.write(response.getOutputStream(), AllotInDetailExcelDTO.class).autoCloseStream(Boolean.FALSE)
                .sheet("数据").doWrite(excelList);
    }

}
