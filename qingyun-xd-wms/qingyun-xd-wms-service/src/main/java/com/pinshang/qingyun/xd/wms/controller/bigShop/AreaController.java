package com.pinshang.qingyun.xd.wms.controller.bigShop;

import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.AreaIDTO;
import com.pinshang.qingyun.xd.wms.model.bigShop.Area;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.bigShop.AreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/area/")
@Api(value = "大店区域管理", tags = "大店区域管理")
public class AreaController {

    @Autowired
    private AreaService areaService;

    @PostMapping("add")
    @ApiOperation(value = "添加区域")
    public Boolean addArea(@RequestBody AreaDTO dto) {
        return areaService.addArea(dto);
    }

    @PostMapping("list")
    @ApiOperation(value = "区域列表")
    public MPage<AreaDTO> areaList(@RequestBody AreaIDTO dto) {
        return areaService.areaList(dto);
    }

    @PostMapping("update")
    @ApiOperation(value = "修改区域")
    public Boolean update(@RequestBody AreaDTO dto) {
        return areaService.update(dto);
    }

    @GetMapping("areaInfo")
    @ApiOperation(value = "查询区域信息")
    public Area areaInfo(@RequestParam Long areaId) {
        return areaService.areaInfo(areaId);
    }

    @GetMapping("delete")
    @ApiOperation(value = "删除区域  下面绑定货位数量为0时")
    public Boolean delete(@RequestParam Long areaId) {
        return areaService.delete(areaId);
    }

    @GetMapping("reallocation")
    @ApiOperation(value = "删除区域  下面绑定货位数量不为0时，重新分配")
    public Boolean reallocation(@RequestParam Long oldAreaId, @RequestParam Long newAreaId) {
        return areaService.reallocation(oldAreaId, newAreaId);
    }

    @GetMapping("getAreaListByKeyWord")
    @ApiOperation("根据编码或者名称搜索门店下的区域")
    public List<Area> getAreaListByKeyWord(@RequestParam Long shopId , @RequestParam String keyWord) {
        return areaService.getAreaListByKeyWord(shopId, keyWord);
    }

    @GetMapping("getAreaListByShopId")
    @ApiOperation("搜索门店下的区域")
    public List<Area> getAreaListByShopId(@RequestParam("shopId") Long shopId) {
        return areaService.getAreaListByKeyWord(shopId, null);
    }
}
