package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value="StockAllotOrderItem对象", description="调入调出申请详情表")
@TableName("t_stock_allot_order_item")
public class StockAllotOrderItem extends BaseEntity {

    private Long stockAllotId;

    private Long commodityId;

    private Integer applyNumber;

    private BigDecimal applyQuantity;

    private Integer outNumber;

    private BigDecimal outQuantity;

    private Integer inNumber;

    private BigDecimal inQuantity;

    private BigDecimal weightPrice;

    private BigDecimal commodityPrice;
}
