package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 拣货单配置信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickSettingDTO {
    /**
     * 拣货单预警开始时间
     */
    @ApiModelProperty(value = "拣货单预警开始时间")
    private Integer distributionBeginWarning;
    /**
     * 拣货单预警截止时间
     */
    @ApiModelProperty(value = "拣货单预警截止时间")
    private Integer distributionEndWarning;

    @ApiModelProperty(value = "是否显示接单，true：显示，false：不显示")
    private Boolean isShowOrderButton;

}