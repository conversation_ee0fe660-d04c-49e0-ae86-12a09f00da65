package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2020/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelledOrderItemDTO  {

    /** 商品id */
    private Long commodityId;

    /** 价格 */
    private BigDecimal price;

    /** 订货份数 */
    private Integer orderNumber;

    /** 配送份数 */
    private Integer deliverNumber;

}
