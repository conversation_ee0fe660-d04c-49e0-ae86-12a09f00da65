

package com.pinshang.qingyun.xd.wms.service.stock;

import com.pinshang.qingyun.base.enums.xd.StockInOutTypeEnums;
import com.pinshang.qingyun.xd.wms.dto.StockItemDTO;
import com.pinshang.qingyun.xd.wms.enums.StockInOutEnum;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 在途库存出入库
 * Created by chen<PERSON> on 2023/3/2.
 */
@Transactional
@Service
@Lazy
public class TransportStockService {

    @Autowired
    private ShopCommodityService shopCommodityService;

    /**
     * 质检库存出入库
     * @param idAndCode
     * @param typeEnum
     * @param commodityList
     * @param warehouseId
     * @param userId
     */
    public Pair<Long,String> stockInOut(Pair<Long, String> idAndCode, StockInOutTypeEnums typeEnum, List<StockItemDTO> commodityList, Long warehouseId, Long userId) {
        Pair<Long,String> pair = null;

        if (StockInOutEnum.IN.getCode() == typeEnum.getInOutType()){
            pair = stockTransportIn(idAndCode, commodityList, warehouseId);
        }else if (StockInOutEnum.OUT.getCode() == typeEnum.getInOutType()){
            pair = stockTransportOut(idAndCode, commodityList, warehouseId);
        }

        return pair;
    }

    /**
     * 在途库入库
     *
     * @param idAndCode
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockTransportIn(Pair<Long,String> idAndCode,
                                             List<StockItemDTO> commodityList, Long warehouseId) {

        //处理库存+
        shopCommodityService.increaseTransportStock(commodityList, warehouseId);

        return idAndCode;
    }

    /**
     * 在途库出库
     *
     * @param idAndCode
     * @param commodityList
     * @return
     */
    private Pair<Long,String> stockTransportOut( Pair<Long,String> idAndCode,
                                               List<StockItemDTO> commodityList, Long warehouseId) {

        //处理库存-
        shopCommodityService.reduceTransportStock(commodityList, warehouseId);

        return idAndCode;
    }
}