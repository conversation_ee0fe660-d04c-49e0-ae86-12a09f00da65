package com.pinshang.qingyun.xd.wms.service.pkg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.PackageStatusEnum;
import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.storage.dto.PackageOrderMakeUpIDTO;
import com.pinshang.qingyun.storage.dto.QueryShopPackageOrderInfoIDTO;
import com.pinshang.qingyun.storage.dto.QueryShopPackageOrderInfoODTO;
import com.pinshang.qingyun.storage.service.PackageOrderWebClient;
import com.pinshang.qingyun.xd.order.client.dto.OrderDetailItemODTO;
import com.pinshang.qingyun.xd.order.dto.XdOrderODTO;
import com.pinshang.qingyun.xd.order.service.XdOrderClient;
import com.pinshang.qingyun.xd.wms.client.StoragePackageOrderClient;
import com.pinshang.qingyun.xd.wms.client.dto.PackageIDTO;
import com.pinshang.qingyun.xd.wms.dto.pkg.*;
import com.pinshang.qingyun.xd.wms.mapper.DcShopPackageOrderMapper;
import com.pinshang.qingyun.xd.wms.mapper.PackageMapper;
import com.pinshang.qingyun.xd.wms.mapper.PickOrderItemMapper;
import com.pinshang.qingyun.xd.wms.model.DcShopPackageOrder;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/1/5
 */
@Service
public class PackageService {

    @Autowired
    private PackageMapper packageMapper;
    @Autowired
    private PickOrderItemMapper pickOrderItemMapper;
    @Autowired
    private IRenderService renderService;
    @Autowired
    private StoragePackageOrderClient storagePackageOrderClient;
    @Autowired
    private PackageOrderWebClient packageOrderWebClient;
    @Autowired
    private DcShopPackageOrderMapper dcShopPackageOrderMapper;

    @Autowired
    private XdOrderClient xdOrderClient;

    /**
     * 包裹追踪表分页
     * @param dto
     * @return
     */
    public MPage<PackageTrackODTO> packageTrackPage(PackageTrackIDTO dto) {

        // 根据箱码查询 t_dc_shop_package_order.id
        if(StringUtils.isNotBlank(dto.getBoxCode())){
            List<Long> packageOrderIdList = storagePackageOrderClient.getPackageOrderIdByBoxCode(dto.getBoxCode());
            if(CollectionUtils.isNotEmpty(packageOrderIdList)){
                dto.setPackageOrderIdList(packageOrderIdList);
            }else {
                return null;
            }
        }

        MPage<PackageTrackODTO> page = packageMapper.packageTrackPage(dto);
        renderService.render(page.getList(),"/package/packageTrackPage");
        if(page != null && SpringUtil.isNotEmpty(page.getList())){
            List<Long> packageOrderIdList = page.getList().stream().map(item -> item.getPackageOrderId()).collect(Collectors.toList());

            // 查询箱码
            PackageIDTO packageIDTO = new PackageIDTO();
            packageIDTO.setOrderTimeBegin(dto.getOrderTimeBegin());
            packageIDTO.setOrderTimeEnd(dto.getOrderTimeEnd());
            packageIDTO.setPackageOrderIdList(packageOrderIdList);
            Map<Long,List<String>> boxCodeMap = storagePackageOrderClient.getPackageOrderBoxCode(packageIDTO);
            for(PackageTrackODTO odto : page.getList()){
                List<String> boxCodeList = boxCodeMap.get(odto.getPackageOrderId());
                odto.setBoxCodeList(CollectionUtils.isNotEmpty(boxCodeList) ? StringUtils.strip(boxCodeList.toString(),"[]") : "");
            }

        }
        return page;
    }

    @NotNull
    private List<Long> getUserIdList(MPage<PackageTrackODTO> page) {
        List<Long> userIdList = new ArrayList<>();
        page.getList().stream().forEach(e->{
            if(e.getLoaderBoxId() != null){
                userIdList.add(e.getLoaderBoxId());
            }
            if(e.getTakerBoxId() != null){
                userIdList.add(e.getTakerBoxId());
            }
            if(e.getUnloadBoxId() != null){
                userIdList.add(e.getUnloadBoxId());
            }
            if(e.getReceiverBoxId() != null){
                userIdList.add(e.getReceiverBoxId());
            }
            if(e.getPickerBoxId() != null){
                userIdList.add(e.getPickerBoxId());
            }
        });
        return userIdList;
    }

    /**
     * 云超订单包裹详情
     * @param orderCode
     * @return
     */
    public PackageOrderODTO getOrderPackage(String orderCode) {
        QYAssert.notNull(orderCode,"云超订单号不能为空");
        PackageOrderODTO packageOrderODTO = new PackageOrderODTO();

        // 查询订单信息
        List<OrderDetailODTO> orderDetailList = packageMapper.listOrderInfo(orderCode);
        BeanUtils.copyProperties(orderDetailList.get(0),packageOrderODTO);

        // 查询包裹信息
        List<OrderDetailODTO> packageList = packageMapper.listPackageByOrderCode(orderDetailList.get(0).getOrderId());
        Map<Long, List<OrderDetailODTO>> packageMap = packageList.stream().collect(Collectors.groupingBy(OrderDetailODTO::getCommodityId));

        List<PackageCommodityODTO> itemList = new ArrayList<>();
        for(OrderDetailODTO odto : orderDetailList){
            PackageCommodityODTO item = new PackageCommodityODTO();
            BeanUtils.copyProperties(odto,item);

            List<String> packageCodeList = new ArrayList<>();
            List<String> packageStatusList = new ArrayList<>();
            List<OrderDetailODTO> pckList = packageMap.get(odto.getCommodityId());
            if(CollectionUtils.isNotEmpty(pckList)){
                for(OrderDetailODTO detailODTO : pckList){
                    if(!packageCodeList.contains(detailODTO.getPackageOrderCode())){
                        packageCodeList.add(detailODTO.getPackageOrderCode());
                        packageStatusList.add(PackageStatusEnum.getValue(detailODTO.getPackageStatus()));
                    }
                }
            }
            item.setPackageOrderCodeList(packageCodeList);
            item.setPackageStatusNameList(packageStatusList);
            itemList.add(item);
        }
        packageOrderODTO.setItemList(itemList);
        return packageOrderODTO;
    }

    /**
     * 包裹详情
     * @param packageOrderCode
     * @return
     */
    public PackageOrderODTO getPackageInfo(String packageOrderCode) {
        QYAssert.notNull(packageOrderCode,"包裹号不能为空");
        PackageOrderODTO packageOrderODTO = new PackageOrderODTO();

        // 根据包裹号查询云超订单号
        String orderCode = packageMapper.getOrderCode(packageOrderCode);

        // 查询订单信息
        List<OrderDetailODTO> orderDetailList = packageMapper.listOrderInfo(orderCode);
        BeanUtils.copyProperties(orderDetailList.get(0),packageOrderODTO);

        // 查询包裹信息
        List<OrderDetailODTO> packageList = packageMapper.listPackageByPackageOrderCode(packageOrderCode);
        List<PackageCommodityODTO> itemList = new ArrayList<>();
        for(OrderDetailODTO detailODTO : packageList){
            PackageCommodityODTO item = new PackageCommodityODTO();
            BeanUtils.copyProperties(detailODTO,item);
            itemList.add(item);
        }
        packageOrderODTO.setItemList(itemList);
        return packageOrderODTO;
    }

    public Integer packageOrderCompare(PackageOrderMakeUpReqVo vo) {
        Integer xdNumber = 0;
        if(vo.getPackageFlag() == 1){
            xdNumber = packageMapper.queryPackageOrderCount(vo);
        }else{
            xdNumber = pickOrderItemMapper.queryPackageOrderItemCount(vo);
        }
        return xdNumber;
    }

    public List<PackageOrderStatusInfoRespVo> queryPackageOrderStatusInfo(PackageOrderStatusInfoReqVo vo) {
        List<Long> orderIdList = new ArrayList<>();
        List<Long> orderItemList = new ArrayList<>();
        Map<String,PackageOrderStatusInfoItemReqVo> statusInfoMap = new HashMap<>();
        List<PackageOrderStatusInfoRespVo> resultList = new ArrayList<>();
        for (PackageOrderStatusInfoItemReqVo item : vo.getList()) {
            if(!orderIdList.contains(item.getOrderId())){
                orderIdList.add(item.getOrderId());
            }
            if(!orderItemList.contains(item.getOrderItemId())){
                orderItemList.add(item.getOrderItemId());
            }
            statusInfoMap.put(item.getOrderId()+"|"+item.getOrderItemId(),item);
        }
        List<PackageOrderStatusInfoRespVo> packageOrderStatusInfo = packageMapper.queryPackageOrderStatusInfo(orderIdList,orderItemList);
        List<Long> statusDiffList = new ArrayList<>();
        for (PackageOrderStatusInfoRespVo item : packageOrderStatusInfo) {
            PackageOrderStatusInfoItemReqVo compareData = statusInfoMap.get(item.getOrderId()+"|"+item.getOrderItemId());
            if(compareData != null &&
                    !item.getPackageStatus().equals(compareData.getPackageStatus()) &&
                    item.getPackageStatus() < compareData.getPackageStatus()){
                if(vo.getPackageFlag() == 3){
                    if(!statusDiffList.contains(item.getOrderId())){
                        resultList.add(item);
                        statusDiffList.add(item.getOrderId());
                    }
                }else {
                    if(!item.getOrderItemInfo().equals(compareData.getOrderItemInfo())){
                        resultList.add(item);
                    }
                }
            }
        }
        return resultList;
    }

    public List<String> queryDifferencePackageOrder(List<PackageOrderIdInfoReqVo> list){
        List<Long> orderIdList = new ArrayList<>();
        List<Long> orderItemList = new ArrayList<>();
        for (PackageOrderIdInfoReqVo item : list) {
            if(!orderIdList.contains(item.getOrderId())){
                orderIdList.add(item.getOrderId());
            }
            if(!orderItemList.contains(item.getOrderItemId())){
                orderItemList.add(item.getOrderItemId());
            }
        }
        List<PackageOrderIdInfoReqVo> packageOrderIdList = packageMapper.queryPackageOrderIdInfo(orderIdList,orderItemList);
        Map<String, PackageOrderIdInfoReqVo> map = new HashMap<>();
        if(SpringUtil.isNotEmpty(packageOrderIdList)){
            map = packageOrderIdList.stream().collect(Collectors.toMap(PackageOrderIdInfoReqVo::buildKey, Function.identity()));
        }
        List<String> dcLackOrderCodeList = new ArrayList<>();
        for (PackageOrderIdInfoReqVo item : list) {
            if(!map.containsKey(item.buildKey()) && !dcLackOrderCodeList.contains(item.getOrderCode())){
                dcLackOrderCodeList.add(item.getOrderCode());
            }
        }
        return dcLackOrderCodeList;
    }

    public PackageOrderSyncRespVo queryOrderSync(PackageOrderSyncReqVo vo) {
        if(SpringUtil.isNotEmpty(vo.getOrderList())){
            List<Long> insertList = new ArrayList<>();
            List<Long> updateList = new ArrayList<>();
            if(vo.getPackageFlag() == 1){
                List<Long> dcOrderIdList = vo.getOrderList().stream().map(PackageOrderSyncStatusReqVo::getOrderId).collect(Collectors.toList());
                List<PackageOrderStatusInfoRespVo> xdPackageOrderInfo = packageMapper.queryPackageOrderStatusInfo(dcOrderIdList,null);
                Map<Long,Integer> xdPackageOrderStatusMap = new HashMap<>();
                if(SpringUtil.isNotEmpty(xdPackageOrderInfo)){
                    for (PackageOrderStatusInfoRespVo item : xdPackageOrderInfo) {
                        if(!xdPackageOrderStatusMap.containsKey(item.getOrderId())){
                            xdPackageOrderStatusMap.put(item.getOrderId(),item.getPackageStatus());
                        }
                    }
                }
                for (PackageOrderSyncStatusReqVo dcPackageOrderInfo : vo.getOrderList()) {
                    Integer xdPackageOrderStatus = xdPackageOrderStatusMap.get(dcPackageOrderInfo.getOrderId());
                    if(xdPackageOrderStatus == null){
                        insertList.add(dcPackageOrderInfo.getOrderId());
                    } else {
                        if(!xdPackageOrderStatus.equals(dcPackageOrderInfo.getPackageStatus()) && xdPackageOrderStatus < dcPackageOrderInfo.getPackageStatus()){
                            updateList.add(dcPackageOrderInfo.getOrderId());
                        }
                    }
                }
            }else{
                List<Long> dcOrderItemIdList = new ArrayList<>();
                for (PackageOrderSyncStatusReqVo order : vo.getOrderList()) {
                    if(SpringUtil.isNotEmpty(order.getOrderItemList())){
                        for (PackageOrderSyncStatusItemReqVo orderItem : order.getOrderItemList()) {
                            dcOrderItemIdList.add(orderItem.getOrderItemId());
                        }
                    }
                }
                if(SpringUtil.isNotEmpty(dcOrderItemIdList)){
                    List<PackageOrderStatusInfoRespVo> xdPackageOrderInfo = packageMapper.queryPackageOrderStatusItemInfo(dcOrderItemIdList);
                    Map<Long,String> xdOrderItemMap = new HashMap<>();
                    if(SpringUtil.isNotEmpty(xdPackageOrderInfo)){
                        for (PackageOrderStatusInfoRespVo item : xdPackageOrderInfo) {
                            xdOrderItemMap.put(item.getOrderItemId(),item.getOrderItemInfo());
                        }
                    }
                    for (PackageOrderSyncStatusReqVo dcPackageOrderInfo : vo.getOrderList()) {
                        if(SpringUtil.isNotEmpty(dcPackageOrderInfo.getOrderItemList())){
                            for (PackageOrderSyncStatusItemReqVo dcOrderItem : dcPackageOrderInfo.getOrderItemList()) {
                                String xdOrderItemInfo = xdOrderItemMap.get(dcOrderItem.getOrderItemId());
                                if(StringUtils.isNotEmpty(xdOrderItemInfo)){
                                    if(!xdOrderItemInfo.equals(dcOrderItem.getPackageOrderItemInfo()) && !updateList.contains(dcOrderItem.getOrderItemId())){
                                        updateList.add(dcOrderItem.getOrderItemId());
                                    }
                                }else{
                                    if( !insertList.contains(dcOrderItem.getOrderItemId()) ){
                                        insertList.add(dcOrderItem.getOrderItemId());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return new PackageOrderSyncRespVo(insertList,updateList);
        }
        return new PackageOrderSyncRespVo();
    }

    public ApiResult savePackageOrderSyncData(SavePackageOrderSyncDataReqVo vo){
        if(vo.getPackageFlag() == 1){
            if(SpringUtil.isNotEmpty(vo.getInsertSyncOrderDataList())){
                packageMapper.savePackageOrderSyncData(vo.getInsertSyncOrderDataList());
            }
            if(SpringUtil.isNotEmpty(vo.getUpdateSyncOrderDataList())){
                packageMapper.updatePackageOrderSyncData(vo.getUpdateSyncOrderDataList());
            }
        }else{
            if(SpringUtil.isNotEmpty(vo.getInsertPackageOrderItemList())){
                pickOrderItemMapper.savePackageOrderSyncData(vo.getInsertPackageOrderItemList());
            }
            if(SpringUtil.isNotEmpty(vo.getUpdatePackageOrderItemList())){
                pickOrderItemMapper.updatePackageOrderSyncData(vo.getUpdatePackageOrderItemList());
            }
        }
        return ApiResult.ok();
    }

//    @Async
    public void packageOrderMakeUp(List<String> orderCodeList) {
        PackageOrderMakeUpIDTO idto = new PackageOrderMakeUpIDTO();
        idto.setOrderCodeList(orderCodeList);
        packageOrderWebClient.packageOrderMakeUp(idto);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long packageUnsynchronized(Long orderId) {
        LambdaQueryWrapper query = new LambdaQueryWrapper<DcShopPackageOrder>()
                .eq(DcShopPackageOrder::getReferOrderId, orderId);
        List<DcShopPackageOrder> list = dcShopPackageOrderMapper.selectList(query);
        long dcShopPackageOrders = list.stream().filter(e -> !e.getPackageStatus().equals(PackageStatusEnum.FINISH_PICK.getCode())).count();
        return dcShopPackageOrders;
    }

    public QueryShopPackageOrderInfoODTO queryShopPackageOrder(Long orderId) {
        QueryShopPackageOrderInfoIDTO dto = new QueryShopPackageOrderInfoIDTO();
        dto.setOrderId(orderId);
        return packageOrderWebClient.queryShopPackageOrderInfo(dto);
    }

    public void insertPackage(Long orderId) {
        XdOrderODTO dto = xdOrderClient.findByOrderId(orderId);
        DcShopPackageOrderReqVo vo = new DcShopPackageOrderReqVo();
        vo.setId(IdWorker.getId());
        vo.setOrderCode( String.valueOf(System.currentTimeMillis()) );
        vo.setPackageType(1);
        vo.setWarehouseId(dto.getShopId());
        vo.setOrderTime(dto.getToShopDate());
        vo.setShopId(dto.getShopId());
        vo.setStoreId(16L);
        vo.setSeedAreaId(12L);
        vo.setPackageStatus(60);
        vo.setIsBox(0);
        vo.setReferType(1);
        vo.setReferId(1L);
        vo.setReferCode("11");
        vo.setReferOrderId(dto.getId());
        vo.setReferOrderCode(dto.getOrderCode());
        vo.setCreateId(1L);
        vo.setCreateTime(new Date());
        vo.setUpdateId(1L);
        vo.setUpdateTime(new Date());
        packageMapper.savePackageOrderSyncData(Arrays.asList(vo));

        com.pinshang.qingyun.xd.order.client.dto.OrderDetailODTO detail = xdOrderClient.queryDetail(orderId);

        List<DcShopPackageOrderItemReqVo> vos = new ArrayList<>();
        for (OrderDetailItemODTO OrderDetailItemODTO : detail.getItems()) {
            DcShopPackageOrderItemReqVo item = new DcShopPackageOrderItemReqVo();
            item.setPackOrderId(vo.getId());
            item.setCommodityId(Long.parseLong(OrderDetailItemODTO.getCommodityId()));
            item.setQuantity( new BigDecimal(OrderDetailItemODTO.getNum()) );
            item.setNumber(OrderDetailItemODTO.getNum());
            item.setPackageNumber(OrderDetailItemODTO.getNum());
            item.setPackageQuantity( new BigDecimal(OrderDetailItemODTO.getNum()) );
            item.setPickQuantity( new BigDecimal(OrderDetailItemODTO.getNum()) );
            item.setPickNumber(OrderDetailItemODTO.getNum());
            vos.add(item);
        }

        pickOrderItemMapper.savePackageOrderSyncData(vos);
    }
}
