package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseWorkCommodityDTO extends BaseEntity {

    @ApiModelProperty(value = "加工点id")
    private Long workId;

    @ApiModelProperty(value = "商品Id")
    private Long commodityId;

    public void checkData() {
        QYAssert.isTrue(null != workId, "加工点不能为空");
        QYAssert.isTrue(null != commodityId, "商品不能为空");
    }

}
