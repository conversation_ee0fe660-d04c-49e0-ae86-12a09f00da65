package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 门店商品绑定陈列位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
public class LogDdDisplayPositionVO implements Serializable {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("货架id")
    private Long shelveId;

    @ApiModelProperty("档口code")
    private String stallCode;

    @ApiModelProperty("档口名称")
    private String stallName;

    @ApiModelProperty("货架编码")
    private String shelveCode;

    @ApiModelProperty("货架名称")
    private String shelveName;

    @ApiModelProperty("陈列位")
    private String displayPositionName;

    private Integer operateType;

    private String operateUserName;

    private Long operateUserId;

    private String operateTime;

    private String operateUserCode;
}
