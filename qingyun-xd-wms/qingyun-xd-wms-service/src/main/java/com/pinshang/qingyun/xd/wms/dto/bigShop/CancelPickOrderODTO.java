package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("CancelPickOrderODTO")
public class CancelPickOrderODTO implements Serializable {

    private static final long serialVersionUID = -3991376202923962492L;

    @ApiModelProperty(value = "分区拣货子单id")
    private Long pickPartitionOrderId;

    @ApiModelProperty("拣货子单编号")
    private String pickPartitionOrderCode;

    @ApiModelProperty(value = "订单短号")
    private String orderNum;

    @ApiModelProperty("打包口")
    private String packingPort;

    private String pickAreaId;

    @ApiModelProperty("分区")
    @FieldRender(fieldType = FieldTypeEnum.PICK_AREA, fieldName = RenderFieldHelper.PickArea.pickAreaName, keyName = "pickAreaId")
    private String pickAreaName;

    @ApiModelProperty("打包口id")
    private Long packingStationId;
}
