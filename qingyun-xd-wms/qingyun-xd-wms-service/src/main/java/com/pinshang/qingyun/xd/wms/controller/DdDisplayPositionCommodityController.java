package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.stream.plugin.common.ExcelResult;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdDisplayPositionCommoditySaveODTO;
import com.pinshang.qingyun.xd.wms.service.DdDisplayPositionCommoditySettingService;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.util.DdUtils;
import com.pinshang.qingyun.xd.wms.util.ReportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.joda.time.LocalDateTime;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 门店商品绑定陈列位  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Api(tags = "门店商品绑定陈列位", description = "门店商品绑定陈列位")
@RestController
@RequestMapping("/ddDisplayPositionCommodity")
@Slf4j
public class DdDisplayPositionCommodityController {

    @Autowired
    private DdDisplayPositionCommoditySettingService ddDisplayPositionCommoditySettingService;

    @Autowired
    private IRenderService renderService;

    /**
    * 门店商品绑定陈列位 列表
    * @param req
    * @return
    */
    @PostMapping("/page")
    @ApiOperation(value = "门店商品绑定陈列位 列表")
    @MethodRender
    public PageInfo<DdDisplayPositionCommodityODTO> page(@RequestBody DdDisplayPositionCommodityPageIDTO req) {
        return ddDisplayPositionCommoditySettingService.page(req);
    }

    /**
    * 保存 门店商品绑定陈列位
    * @param req
    */
    @PostMapping("/save")
    @ApiOperation(value = "保存 门店商品绑定陈列位 或者 更新最大最小数量")
    public Boolean save(@RequestBody DdDisplayPositionCommoditySaveIDTO req) {
        return ddDisplayPositionCommoditySettingService.save(req);
    }

    @PostMapping("/checkRepeat")
    @ApiOperation(value = "判断档口+商品是否已绑定陈列位")
    public DdDisplayPositionCommoditySaveODTO checkRepeat(@RequestBody DdDisplayPositionCommoditySaveIDTO req){
        return ddDisplayPositionCommoditySettingService.checkRepeat(req);
    }

    @PostMapping("/listCommodity")
    @ApiOperation(value = "pda 商品绑定陈列位 商品搜索")
    @MethodRender
    public List<DdDisplayPositionCommodityQueryODTO> listCommodity(@RequestBody DdDisplayPositionCommodityQueryIDTO req) {
        return ddDisplayPositionCommoditySettingService.listCommodity(req);
    }

    @PostMapping("/pda/saveBatch")
    @ApiOperation(value = "pda 商品绑定陈列位 批量绑定")
    public Boolean pdaBatchSave(@RequestBody DdDisplayPositionCommodityBatchSaveIDTO req) {
        ddDisplayPositionCommoditySettingService.pdaBatchSave(req);
        return Boolean.TRUE;
    }

    /**
    * 删除 门店商品绑定陈列位
    * @param req
    */
    @PostMapping("/delete")
    @ApiOperation(value = "删除 门店商品绑定陈列位")
    public DdDisplayPositionCommodityDeleteODTO delete(@RequestBody DdDisplayPositionCommodityDeleteIDTO req) {
        return ddDisplayPositionCommoditySettingService.delete(req);
    }

    /**
     * 根据id删除 门店商品绑定陈列位
     * @param req
     */
    @GetMapping("/deleteById")
    @ApiOperation(value = "删除 门店商品绑定陈列位")
    public Boolean deleteById(@RequestParam("id") Long id) {
        return ddDisplayPositionCommoditySettingService.deleteById(id);
    }

    @ApiOperation(value = "门店商品绑定陈列位导出", notes = "门店商品绑定陈列位导出")
    @GetMapping("/export")
    public void exportManualSort(DdDisplayPositionCommodityPageIDTO reqVO, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        reqVO.initExportPage();
        List<DdDisplayPositionCommodityODTO> list =  ddDisplayPositionCommoditySettingService.page(reqVO).getList();
        renderService.render(list, "/ddDisplayPositionCommodity/export");
        long qE = System.currentTimeMillis();
        log.info("门店商品绑定陈列位-导出--查询时间=" + ((qE - qS) / 1000));
        try {
            String fileName = "门店商品绑定陈列位_" + LocalDateTime.now().toString("yyyy-MM-dd");
            ExcelUtil.setFileNameAndHead(response, fileName);
            EasyExcel.write(response.getOutputStream(), DdDisplayPositionCommodityODTO.class).autoCloseStream(Boolean.FALSE).sheet("门店商品绑定陈列位")
                    .doWrite( list );
        }catch (Exception e){
            ExcelUtil.setExceptionResponse( response );
            log.error("",e);
        }
        long eE = System.currentTimeMillis();
        log.info("门店商品绑定陈列位-导出--excel处理时间="+ ( (eE -qE) /1000 )  );

    }


    @ApiOperation(value = "导入陈列位安全库存", notes = "导入陈列位安全库存", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/import/update", method = RequestMethod.POST)
    public ExcelResult importExcelUpdate(@ApiParam(value = "待上传的文件") @RequestParam(value = "file", required = true) MultipartFile file,
                                         @ApiParam(value = "档口") @RequestParam(value = "stallId", required = true) Long stallId) throws Exception {
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        Assert.notNull(wb, "工作簿不能为空");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "表单不能为空");
        List<DdDisplayPositionCommoditySaveIDTO> positionList = new ArrayList<>();
        QYAssert.isTrue(sheet.getLastRowNum() < 1001, "每次最多导入1000行");
        List<String> errorList = new ArrayList<>();
        sheet.forEach(row ->{
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "商品编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "陈列位最小数量"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 2, "陈列位最大数量"), "模板不正确");
            }
            // 第二行开始
            if (rowNum > 0) {
                DdDisplayPositionCommoditySaveIDTO  position = new DdDisplayPositionCommoditySaveIDTO();
                Cell commodityCell = row.getCell(0);
                if(null == commodityCell){
                    position.setCommodityCode("");
                }else{
                    position.setCommodityCode(commodityCell.getStringCellValue().trim());
                }
                Cell mixCell = row.getCell(1);
                if(null == mixCell || "".equals(mixCell.getStringCellValue())){
                    position.setMinStock(null);
                }else {
                    String mixCellValue = mixCell.getStringCellValue();
                    BigDecimal mixCellDecimalValue = new BigDecimal(mixCellValue).setScale(3, RoundingMode.HALF_UP);
                    position.setMinStock(mixCellDecimalValue);
                }
                Cell maxCell = row.getCell(2);
                if(null == maxCell || "".equals(maxCell.getStringCellValue())){
                    position.setMaxStock(null);
                }else {
                    String maxCellValue = maxCell.getStringCellValue();
                    BigDecimal maxCellDecimalValue = new BigDecimal(maxCellValue).setScale(3, RoundingMode.HALF_UP);
                    position.setMaxStock(maxCellDecimalValue);
                }
                positionList.add(position);
                if("".equals(position.getCommodityCode())){
                    errorList.add(rowNum + "行数据不能为空");
                }
                if ( null != position.getMinStock() && (position.getMinStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || position.getMinStock().compareTo(BigDecimal.ZERO) < 0) ) {
                    errorList.add(rowNum + "最小数量最大数量请输入0-99999之间的数字");
                }
                if ( null != position.getMaxStock() && (position.getMaxStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || position.getMaxStock().compareTo(BigDecimal.ZERO) < 0) ) {
                    errorList.add(rowNum + "最小数量最大数量请输入0-99999之间的数字");
                }
                if ( null != position.getMinStock() && null != position.getMaxStock() && position.getMinStock().compareTo(position.getMaxStock()) > 0 ){
                    errorList.add(rowNum + "行最小数量不可＞最大数量");
                }

            }
        });
        if(SpringUtil.isNotEmpty(errorList)){
            return new ExcelResult(errorList, null);
        }
        QYAssert.isTrue(SpringUtil.isNotEmpty(positionList), "导入数据不能为空");
        return ddDisplayPositionCommoditySettingService.importExcelUpdate(positionList, stallId);
    }

    @ApiOperation(value = "商品和陈列位绑定", notes = "导入陈列位", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public ExcelResult importExcel(@ApiParam(value = "待上传的文件") @RequestParam(value = "file", required = true) MultipartFile file,
                                               @ApiParam(value = "档口") @RequestParam(value = "stallId", required = true) Long stallId,
                                               @ApiParam(value = "门店") @RequestParam(value = "shopId", required = true) Long shopId) throws Exception {
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(in);
        Assert.notNull(wb, "工作簿不能为空");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "表单不能为空");
        List<DdDisplayPositionCommoditySaveIDTO> positionList = new ArrayList<>();
        QYAssert.isTrue(sheet.getLastRowNum() < 501, "每次最多导入500行");
        List<String> errorList = new ArrayList<>();
        sheet.forEach(row ->{
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 0, "商品编码*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 1, "陈列位*"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 2, "陈列位最小数量"), "模板不正确");
                QYAssert.isTrue(ReportUtil.checkRowNumZero(row, 3, "陈列位最大数量"), "模板不正确");
            }
            // 第二行开始
            if (rowNum > 0) {
                DdDisplayPositionCommoditySaveIDTO  position = new DdDisplayPositionCommoditySaveIDTO();
                Cell commodityCell = row.getCell(0);
                if(null == commodityCell){
                    position.setCommodityCode("");
                }else{
                    position.setCommodityCode(commodityCell.getStringCellValue().trim());
                }
                Cell positionCell = row.getCell(1);
                if(null == positionCell){
                    position.setDisplayPositionName("");
                }else{
                    position.setDisplayPositionName(positionCell.getStringCellValue().trim());
                }
                Cell mixCell = row.getCell(2);
                if(null == mixCell || "".equals(mixCell.getStringCellValue())){
                    position.setMinStock(null);
                }else {
                    String mixCellValue = mixCell.getStringCellValue();
                    BigDecimal mixCellDecimalValue = new BigDecimal(mixCellValue).setScale(3, RoundingMode.HALF_UP);
                    position.setMinStock(mixCellDecimalValue);
                }
                Cell maxCell = row.getCell(3);
                if(null == maxCell || "".equals(maxCell.getStringCellValue())){
                    position.setMaxStock(null);
                }else {
                    String maxCellValue = maxCell.getStringCellValue();
                    BigDecimal maxCellDecimalValue = new BigDecimal(maxCellValue).setScale(3, RoundingMode.HALF_UP);
                    position.setMaxStock(maxCellDecimalValue);
                }
                positionList.add(position);

                if("".equals(position.getCommodityCode()) || "".equals(position.getDisplayPositionName())){
                    errorList.add(rowNum + "行信息填写不完整");
                }

                if ( null != position.getMaxStock() && (position.getMaxStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || position.getMaxStock().compareTo(BigDecimal.ZERO) < 0) ) {
                    errorList.add(rowNum + "最小数量最大数量请输入0-99999之间的数字");
                }

                if( null != position.getMinStock() && (position.getMinStock().compareTo(DdUtils.STOCK_RESTRICT) > 0 || position.getMinStock().compareTo(BigDecimal.ZERO) < 0) ) {
                    errorList.add(rowNum + "最小数量最大数量请输入0-99999之间的数字");
                }

                if( null != position.getMaxStock() && null != position.getMinStock() && position.getMinStock().compareTo(position.getMaxStock()) > 0 ){
                    errorList.add(rowNum + "行最小数量不可＞最大数量");
                }

            }
        });
        if(SpringUtil.isNotEmpty(errorList)){
            return new ExcelResult(errorList, null);
        }
        QYAssert.isTrue(SpringUtil.isNotEmpty(positionList), "导入数据不能为空");
        return ddDisplayPositionCommoditySettingService.importExcel(positionList, stallId, shopId);
    }
}
