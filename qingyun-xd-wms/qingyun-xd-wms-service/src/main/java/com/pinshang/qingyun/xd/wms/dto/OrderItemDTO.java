package com.pinshang.qingyun.xd.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 订单明细消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemDTO {

    private Long orderId;

    /**
     * 订单itemID
     */
    private Long itemId;

    /**
     * 档口id
     */
    private Long stallId;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 商品数量
     */
    private BigDecimal quantity;

    /**
     * 商品份数
     */
    private Integer stockNumber;

    /**是否称重 0非称重 1称重*/
    private Integer isWeight;

    /**是否处理 0＝不处理，1＝处理*/
    private Integer isProcess;

    /**
     * 处理标签ID
     */
    private Long processId;
    /**
     * 处理名称
     */
    private String processName;

    private String originSubBizId;
}