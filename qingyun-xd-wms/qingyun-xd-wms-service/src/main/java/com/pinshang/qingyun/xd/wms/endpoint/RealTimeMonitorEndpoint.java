package com.pinshang.qingyun.xd.wms.endpoint;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 实时监控
 * Created by chenqi on 2019/12/31.
 */
@Slf4j
//@ServerEndpoint(value = "/ws/realTimeMonitor")
//@Component
public class RealTimeMonitorEndpoint {

    private static AtomicInteger onlineCount = new AtomicInteger(0);

    private static CopyOnWriteArraySet<RealTimeMonitorEndpoint> webSocketSet = new CopyOnWriteArraySet<RealTimeMonitorEndpoint>();

    private Session session;

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        setWarehouseId(session);

        this.session = session;
        webSocketSet.add(this);

        addOnlineCount();

        log.info("客户端 sessionId：{} 加入! 当前在线人数为: {}", session.getId(), getOnlineCount());
        try {
            sendMessage("hello webSocket");
        } catch (IOException e) {
            log.error("webSocket IO异常" + e);
        }
    }

    private void setWarehouseId(Session session) {
        Map<String, List<String>> requestParameterMap = session.getRequestParameterMap();
        List<String> list = requestParameterMap.get("warehouseId");
        QYAssert.isTrue(SpringUtil.isNotEmpty(list), "无效链接");
        String warehouseId = list.get(0);
        Map<String, Object> userProperties = session.getUserProperties();
        userProperties.put("warehouseId", warehouseId);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);

        subOnlineCount();
        log.info("客户端 sessionId：{} 关闭! 当前在线人数为: {}", session.getId(), getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("来自客户端的消息:" + message);
        //群发消息
        for (RealTimeMonitorEndpoint item : webSocketSet) {
            try {
                if(item.session.getUserProperties() != null && item.session.getUserProperties().get("warehouseId").equals(message)){
                    item.sendMessage(message);
                }
            } catch (IOException e) {
                log.error("接收客户端的消息异常:" + e);
            }
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("webSocket 发生错误");
        error.printStackTrace();
    }

    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
        //this.session.getAsyncRemote().sendText(message);
    }

    /**
     * 群发自定义消息
     */
    public static void sendInfo(String message) {
        for (RealTimeMonitorEndpoint item : webSocketSet) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                continue;
            }
        }
    }

    public static synchronized AtomicInteger getOnlineCount() {
        return onlineCount;
    }

    /**
     * 在线数加1
     */
    public static synchronized void addOnlineCount() {
        RealTimeMonitorEndpoint.onlineCount.incrementAndGet();
    }

    /**
     * 在线数减1
     */
    public static synchronized void subOnlineCount() {
        RealTimeMonitorEndpoint.onlineCount.decrementAndGet();
    }
}