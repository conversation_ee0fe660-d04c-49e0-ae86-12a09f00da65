package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName QueryOrderInfoForCloudTakeGoodODTO
 * <AUTHOR>
 * @Date 2023/7/31 18:06
 * @Description QueryOrderInfoForCloudTakeGoodODTO
 * @Version 1.0
 */
@Data
public class QueryOrderInfoForCloudTakeGoodODTO {
    private Long userId;

    @ApiModelProperty("提货人名称")
    private String userName;

    @ApiModelProperty("总包裹数")
    private Integer packageNum;

    @ApiModelProperty("联系电话")
    private String phone;

    private Long orderId;

    private String orderCode;

    @ApiModelProperty("短号")
    private Long orderNum;

    @ApiModelProperty("是否完成拣货, 1-是, 0-否")
    private Integer allPackaged;

    List<QueryOrderInfoForCloudTakeGoodItemODTO> itemList;

    @ApiModelProperty("2-还未拣货, 4-不属于当前门店, 5-核销码或者订单号不存在")
    private Integer status;
}
