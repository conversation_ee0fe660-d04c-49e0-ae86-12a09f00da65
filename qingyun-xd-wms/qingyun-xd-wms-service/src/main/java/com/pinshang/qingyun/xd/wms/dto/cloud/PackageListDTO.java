package com.pinshang.qingyun.xd.wms.dto.cloud;

import com.pinshang.qingyun.xd.wms.client.dto.QueryBoxCodeByPackageIdODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageListDTO {

    /**
     * 包裹单号
     */
    private String orderCode;

    @ApiModelProperty("箱码(装箱用)")
    private String boxCode;


    @ApiModelProperty("1:周转筐，2:冻品箱，3:整箱(装箱用)")
    private Integer boxType;

    /**
     * 包裹状态(门店使用)　0=已取消 10=待装筐，15=已装箱,20=待揽收，30=待卸货，40=待收货，50=待门店拣货，60=已拣货
     */
    @ApiModelProperty("0=已取消 10=待装筐，15=已装箱,20=待揽收，30=待卸货，40=待收货，50=待门店拣货，60=已拣货")
    private Integer packageStatus;

    /**
     * 包裹生成方式(0:手工；1:自动)
     */
    @ApiModelProperty("0:手工；1:自动")
    private Integer packageType;

    @ApiModelProperty("当包裹是自动打包时，获取周转箱的相关信息")
    private List<QueryBoxCodeByPackageIdODTO> autoPackage;
}
