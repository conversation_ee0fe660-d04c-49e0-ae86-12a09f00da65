package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.StorageAreaEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/29
 * @Version 1.0
 */
@Data
public class StallCommodityStockExport3ODTO {
    @ExcelIgnore
    private Long shopId;

    @ApiModelProperty("部门")
    @ExcelProperty("部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String deptName;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP, fieldName = RenderFieldHelper.Shop.shopName, keyName = "shopId")
    private String shopName;

    @ExcelIgnore
    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("客户编码")
    @ExcelProperty("客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode , keyName = "storeId")
    private String storeCode;

    @ExcelProperty("档口")
    @ApiModelProperty("档口")
    private String stallName;

    @ExcelProperty("库区")
    @ApiModelProperty("库区")
    private String storageAreaStr;

    @ApiModelProperty("货位")
    @ExcelProperty("货位")
    private String goodsAllocationName;

    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("条形码")
    @ExcelProperty("条形码")
    private String barCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("前台品名")
    @ExcelProperty("前台品名")
    private String commodityAppName;

    @ExcelProperty(value = "是否必售")
    @ApiModelProperty(value = "是否必售")
    private String saleStatusName;

    @ApiModelProperty("规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("包装规格")
    @ExcelProperty("包装规格")
    private String commodityPackageSpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("库存数量")
    @ApiModelProperty("库存总数量=正常库数量+临时库数量")
    private BigDecimal totalQuantity;

    /**
     * 上下架状态：0-上架，1-下架
     */
    @ApiModelProperty("上架状态")
    @ExcelIgnore
    private Integer appStatus;

    @ApiModelProperty("上架状态")
    @ExcelProperty("上架状态")
    private String appStatusStr;

    @ExcelProperty(value = "后台一级品类")
    @ApiModelProperty("后台一级品类")
    private String commodityFirstName;

    @ExcelProperty(value = "后台二级品类")
    @ApiModelProperty("后台二级品类")
    private String commoditySecondName;

    @ExcelProperty(value = "后台三级品类")
    @ApiModelProperty("后台三级品类")
    private String commodityThirdKindName;
}
