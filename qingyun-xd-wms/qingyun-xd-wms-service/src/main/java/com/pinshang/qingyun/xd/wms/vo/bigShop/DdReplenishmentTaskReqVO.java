package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 我的任务、全部任务查询
 * </p>
 *
 */
@Data
@ToString
@ApiModel("DdReplenishmentTaskReqVO")
public class DdReplenishmentTaskReqVO extends Pagination {

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("档口ID")
    private Long stallId;

    @ApiModelProperty("搜索关键词（品名或条码）")
    private String searchKey;

    @ApiModelProperty("补货任务查询类型 1-我的任务 2-全部任务")
    private Integer queryType;

    /**
     * @see com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskTypeEnum
     */
    @ApiModelProperty("补货任务类型 1-排面补货 2-拣货位补货")
    private Integer taskType;

}