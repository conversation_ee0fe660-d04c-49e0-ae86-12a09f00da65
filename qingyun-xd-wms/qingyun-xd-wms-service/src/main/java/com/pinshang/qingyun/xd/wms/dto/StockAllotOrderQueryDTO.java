package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOrderQueryDTO extends Pagination<StockAllotOrderListDTO> {

    @ApiModelProperty(value = "入库门店")
    private Long inShopId;

    @ApiModelProperty(value = "调入档口")
    private Long inStallId;

    @ApiModelProperty(value = "调拨单号")
    private String orderCode;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "调出门店")
    private Long outShopId;

    @ApiModelProperty(value = "调出档口")
    private Long outStallId;

    @ApiModelProperty(value = "入库人")
    private Long inPerson;

    @ApiModelProperty(value = "出库人")
    private Long outPerson;

    //总部
    @ApiModelProperty(value = "创建人")
    private Long createId;

    @ApiModelProperty(value = "审核人")
    private Long auditId;

    @ApiModelProperty(value = "当前登录人 后端用")
    private Long userId;

    public void check() {
        if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
            startTime = startTime.trim() + " 00:00:00";
            endTime = endTime.trim() + " 23:59:59";
        }
    }
}
