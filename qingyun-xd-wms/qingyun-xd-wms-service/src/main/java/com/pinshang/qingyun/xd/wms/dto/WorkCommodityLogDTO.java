package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkCommodityLogDTO extends Pagination<WorkCommodityLogListDTO> {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "操作人")
    private Long createId;

    @ApiModelProperty(value = "操作类型")
    private Integer type;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    private Long warehouseId;

}
