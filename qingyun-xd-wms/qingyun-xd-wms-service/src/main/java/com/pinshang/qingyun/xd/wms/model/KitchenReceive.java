package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.pinshang.qingyun.xd.wms.plus.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName KitchenReceive
 * <AUTHOR>
 * @Date 2021/10/20 17:10
 * @Description KitchenReceive
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_xd_kitchen_receive")
public class KitchenReceive extends BaseEntity {
    @ApiModelProperty("领用出库单")
    private String receiveCode;
    @ApiModelProperty("移动平均价")
    private BigDecimal weightPrice;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("份数")
    private Integer number;
}
