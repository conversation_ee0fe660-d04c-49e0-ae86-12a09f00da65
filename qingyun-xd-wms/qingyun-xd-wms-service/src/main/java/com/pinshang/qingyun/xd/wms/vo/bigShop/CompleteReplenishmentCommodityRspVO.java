package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel("CompleteReplenishmentCommodityRspVO")
public class CompleteReplenishmentCommodityRspVO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("条形码")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.barCode,keyName = "commodityId")
    private String barCode;

    @ApiModelProperty("商品名称")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("商品规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityUnit,keyName = "commodityId")
    private String commodityUnitName;

    @ApiModelProperty("是否称重")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.isWeight,keyName = "commodityId")
    private String isWeightName;

    @ApiModelProperty("实际补货数量")
    private BigDecimal realQuantity;

    @ApiModelProperty("陈列位")
    private String displayPosition;

    @ApiModelProperty("拣货位")
    private String pickingPosition;

}