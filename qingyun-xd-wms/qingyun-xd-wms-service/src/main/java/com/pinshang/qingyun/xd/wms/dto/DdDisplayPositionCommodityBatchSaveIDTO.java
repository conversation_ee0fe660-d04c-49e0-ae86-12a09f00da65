package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@ApiModel("DdDisplayPositionCommodityBatchSaveIDTO")
public class DdDisplayPositionCommodityBatchSaveIDTO {

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    @ApiModelProperty("商品id列表")
    private List<DdDisplayPositionCommodityIDTO> displayPositionCommodityList;

    public void checkData() {
        QYAssert.notNull(shopId, "门店不能为空");
        QYAssert.notNull(displayPositionId, "陈列位不能为空");
        QYAssert.notEmpty(displayPositionCommodityList, "商品不能为空");
        QYAssert.isTrue(displayPositionCommodityList.size() <= 15, "每次最多添加15个商品");
        Set<String> commodityIdSet = displayPositionCommodityList.stream()
                .map(DdDisplayPositionCommodityIDTO::getCommodityId)
                .collect(Collectors.toSet());
        QYAssert.isTrue(commodityIdSet.size() == displayPositionCommodityList.size(), "商品不能重复");

        for (DdDisplayPositionCommodityIDTO ddDisplayPositionCommodityIDTO : displayPositionCommodityList) {

            BigDecimal minStock = ddDisplayPositionCommodityIDTO.getMinStock();
            BigDecimal maxStock = ddDisplayPositionCommodityIDTO.getMaxStock();
            if (minStock != null && maxStock != null && maxStock.compareTo(minStock) < 0) {
                QYAssert.isFalse("最小数量不可大于最大数量");
            }
        }
    }

    @Data
    public static class DdDisplayPositionCommodityIDTO {

        @ApiModelProperty("商品id")
        private String commodityId;

        @ApiModelProperty("最小数量")
        private BigDecimal minStock;

        @ApiModelProperty("最大数量")
        private BigDecimal maxStock;
    }
}
