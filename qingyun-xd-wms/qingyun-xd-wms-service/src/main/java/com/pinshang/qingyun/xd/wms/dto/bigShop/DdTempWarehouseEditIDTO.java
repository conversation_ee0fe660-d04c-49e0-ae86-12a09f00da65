package com.pinshang.qingyun.xd.wms.dto.bigShop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * @ClassName DdTempWarehouseUpdateIDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 14:34
 * @Version 1.0
 */
@Data
@ToString
@ApiModel("DdTempWarehouseUpdateIDTO")
public class DdTempWarehouseEditIDTO {
    @ApiModelProperty(value = "档口Id")
    private Long stallId;
    @ApiModelProperty(value = "货位号")
    private String goodsAllocationCode;
    @ApiModelProperty(value = "操作类型 0-POS退货入库、1-APP退货入库、2-折扣特价码入库")
    private Integer tempOperationType;
    @ApiModelProperty(value = "货位Id")
    private Long goodsAllocationId;
}
