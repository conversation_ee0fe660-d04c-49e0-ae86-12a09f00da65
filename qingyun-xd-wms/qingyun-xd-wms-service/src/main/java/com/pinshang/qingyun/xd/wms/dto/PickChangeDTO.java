package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 改派拣货DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickChangeDTO {
    /**
     * 拣货单id
     */
    private Long pickOrderId;

    /**
     * 拣货人
     */
    private Long pickId;

    public void checkData() {
        QYAssert.isTrue(pickOrderId != null, "拣货单id不能为空");
        QYAssert.isTrue(pickId != null, "拣货人不能为空");
    }
}