package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSnapshotDayDTO {

    @ApiModelProperty("库存快照日期")
    private Date createTime;

    private Long shopId;

    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("门店状态")
    private Integer shopStatus;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("库存品项")
    private Integer stockItem;

    @ApiModelProperty("库存成本金额")
    private String stockWeightPrice;

    @ApiModelProperty("库存成本金额字符串")
    private BigDecimal price;

    @ApiModelProperty("部门id")
    private Long storeId;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("经营模式")
    private Integer managementMode;

    @ApiModelProperty("经营模式-名称")
    private String managementModeName;
}
