package com.pinshang.qingyun.xd.wms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseShelfListDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "货位编号")
    private String shelfNo;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "货位属性:1-拣货位,2-配送取货位,3-加工点取货位")
    private Integer type;

    @ApiModelProperty(value = "货位状态:0-停用(没有绑定货可停用),1-启用")
    private Integer status;
}
