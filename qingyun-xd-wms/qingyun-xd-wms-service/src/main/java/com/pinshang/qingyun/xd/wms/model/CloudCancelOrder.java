package com.pinshang.qingyun.xd.wms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="记录云超已经走了取消单入库的订单 包括云超普通订单和团购订单")
@TableName("t_xd_cloud_cancel_order")
public class CloudCancelOrder {

    private Long id;

    private Long shopId;

    private Long orderId;

    private Integer orderType;

    private Long createId;

    private String createName;

    private Date createTime;
}
