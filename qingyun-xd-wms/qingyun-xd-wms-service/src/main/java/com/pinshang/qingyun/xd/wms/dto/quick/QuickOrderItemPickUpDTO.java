package com.pinshang.qingyun.xd.wms.dto.quick;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuickOrderItemPickUpDTO {

    private Long orderItemId;

    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品编码")
    private String barCode;

    private List<String> barCodeList;

    @ApiModelProperty("商品单价")
    private BigDecimal price;

    @ApiModelProperty("商品优惠后总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("下单份数")
    private Integer number;

    @ApiModelProperty("实发份数")
    private Integer realDiffNumber;

    @ApiModelProperty("是否称重")
    private Integer isWeight;

    @ApiModelProperty("计量单位")
    private String commodityUnitName;

}
