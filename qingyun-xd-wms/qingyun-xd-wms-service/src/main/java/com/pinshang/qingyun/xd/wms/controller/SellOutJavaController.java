package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xd.wms.dto.SellOutExcelDTO;
import com.pinshang.qingyun.xd.wms.dto.SellOutIDTO;
import com.pinshang.qingyun.xd.wms.dto.report.AllotInDetailExcelDTO;
import com.pinshang.qingyun.xd.wms.model.SellOut;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.SellOutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/sellout")
@RequiredArgsConstructor
public class SellOutJavaController {
    private final SellOutService sellOutService;

    @PostMapping("/export/page/excel")
    @ApiOperation("缺货分析报表导出excel")
    @FileCacheQuery(bizCode = "ALLOT_OUT_OF_STOCK")
    public void sellOutExportExcel(@RequestBody SellOutIDTO sellOutIDTO, HttpServletResponse response) throws IOException {
        sellOutIDTO.notLimit();
        MPage<SellOut> page = sellOutService.page(sellOutIDTO);

        List<SellOutExcelDTO> excelList = page.getList().parallelStream().map(data -> {
            SellOutExcelDTO excelDTO = new SellOutExcelDTO();
            BeanUtils.copyProperties(data, excelDTO);
            return excelDTO;
        }).collect(Collectors.toList());

        String filename = String.format("缺货分析报表_%s", DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        ExcelUtil.setFileNameAndHead(response,  filename);
        EasyExcel.write(response.getOutputStream(), SellOutExcelDTO.class).autoCloseStream(Boolean.FALSE)
                .sheet("数据").doWrite(excelList);
    }

}
