package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调拨出库DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockAllotOutDTO {
    /**
     * 调拨单id
     */
    @ApiModelProperty(value = "调拨单id")
    private Long stockAllotId;

    /**
     * 调拨单明细
     */
    @ApiModelProperty(value = "调拨单明细")
    private List<StockAllotOutItemDTO> items;

    public void checkData() {
        QYAssert.isTrue(stockAllotId != null, "调拨单id不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(items), "调拨单明细不能为空");
        List<StockAllotOutItemDTO> checkItems = items.parallelStream().filter(it -> it != null
                                                 && it.getOutQuantity().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        QYAssert.isTrue(SpringUtil.isNotEmpty(checkItems), "调拨单出库数量不能全部为0");
    }

    public List<StockItemDTO> toStockItemList(){
        List<StockAllotOutItemDTO> items = this.getItems();
        List<StockItemDTO> stockItems = new ArrayList<>();
        for (StockAllotOutItemDTO item : items) {
            StockItemDTO dto = new StockItemDTO();
            dto.setCommodityId(item.getCommodityId());
//            dto.setStockNumber(item.getOutNumber());
            dto.setQuantity(item.getOutQuantity());
            stockItems.add(dto);
        }
        return stockItems;
    }
}