package com.pinshang.qingyun.xd.wms.dto.bigShop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentFlagEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskSourceEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.bigshop.ReplenishmentTaskTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 大店补货任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@ToString
@ApiModel("DdReplenishmentTaskODTO")
public class DdReplenishmentTaskQueryODTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("任务类型，1-排面补货，2-拣货区补货")
    private Integer taskType;

}