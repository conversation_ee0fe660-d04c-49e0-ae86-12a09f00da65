package com.pinshang.qingyun.xd.wms.vo.bigShop;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 门店商品绑定陈列位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@ToString
public class LogDdDisplayPositionCommodityVO implements Serializable {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("档口id")
    private Long stallId;

    @ApiModelProperty("档口code")
    private String stallCode;

    @ApiModelProperty("档口名称")
    private String stallName;

    @ApiModelProperty("陈列位")
    private String displayPositionName;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("陈列位最小数量")
    @ApiModelProperty("陈列位最小数量")
    private BigDecimal minStock;

    @ExcelProperty("陈列位最大数量")
    @ApiModelProperty("陈列位最大数量")
    private BigDecimal maxStock;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("陈列位id")
    private Long displayPositionId;

    private Integer operateType;

    private String operateUserName;

    private Long operateUserId;

    private String operateTime;

    private String operateUserCode;

}
