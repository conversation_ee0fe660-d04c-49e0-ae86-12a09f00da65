package com.pinshang.qingyun.xd.wms.dto.report;

//@Data
//public class StockAllotDetailListOfHeadIDTO extends Pagination<StockAllotDetailListOfHeadODTO> {
//
//    @ApiModelProperty(value = "调入门店")
//    private Long inShopId;
//    @ApiModelProperty(value = "调出门店")
//    private Long outShopId;
//    @ApiModelProperty(value = "调拨单号")
//    private String orderCode;
//    @ApiModelProperty(value = "开始时间")
//    private String startTime;
//    @ApiModelProperty(value = "结束时间")
//    private String endTime;
//    @ApiModelProperty(value = "申请类型 1调拨入库 2调拨出库")
//    private Integer allotType;
//    @ApiModelProperty(value = "商品id")
//    private Long commodityId;
//
//}
