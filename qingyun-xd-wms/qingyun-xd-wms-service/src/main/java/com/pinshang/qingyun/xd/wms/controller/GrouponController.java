package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.*;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.groupon.AbstractGroupOrder;
import com.pinshang.qingyun.xd.wms.service.groupon.GroupPurchaseService;
import com.pinshang.qingyun.xd.wms.service.groupon.GrouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/12/18
 */
@RestController
@RequestMapping("/groupon")
@Api(value = "社区团购", tags = "GrouponController")
public class GrouponController {

    @Autowired
    private GrouponService grouponService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private GroupPurchaseService groupPurchaseService;



    /**
     * 查询社区团购商品信息
     * @param ito
     * @return
     */
    @PostMapping("/queryGrouponCommodityList")
    @ApiOperation(value = "查询社区团购商品信息", notes = "查询社区团购商品信息")
    public List<GrouponCommodityODTO> queryGrouponCommodityList(@RequestBody GrouponCommodityIDTO ito) {
        return grouponService.queryGrouponCommodityList(ito);
    }

    @PostMapping("/grouponOrderByMobileList")
    @ApiOperation(value = "根据手机号查询提货列表")
    public GrouponOrderCommodityDTO grouponOrderByMobileList(@RequestParam(value = "receiveMobile",required = false) String receiveMobile) {
        return grouponService.grouponOrderByMobileList(receiveMobile);
    }

    @PostMapping("/grouponOrderListByPickupCode")
    @ApiOperation(value = "根据提货码查询提货列表")
    public GrouponOrderCommodityDTO grouponOrderListByPickupCode(@RequestParam(value = "pickupCode",required = false) String pickupCode) {
        return grouponService.grouponOrderListByPickupCode(pickupCode);
    }

    @PostMapping("/getGrouponOrderByPickupCodeNew")
    @ApiOperation(value = "团购，云超提货 新版")
    public GrouponOrderInfoDTO getGrouponOrderByPickupCodeNew(@RequestParam(value = "pickupCode",required = false) String pickupCode) {
        AbstractGroupOrder abstractGroupOrder = grouponService.grouponOrder(pickupCode);
        return abstractGroupOrder.groupOrderInfo(pickupCode);
    }

    @PostMapping("/grouponOrderCancel")
    @ApiOperation(value = "团购，云超提货核销  新版")
    public Boolean grouponOrderCancel(@RequestParam(value = "pickupCode",required = false) String pickupCode,
                                                  @RequestParam(value = "list",required = false) List<String> list) {
        AbstractGroupOrder abstractGroupOrder = grouponService.grouponOrder(pickupCode);
        return abstractGroupOrder.grouponOrderCancel(pickupCode, list);
    }

    /**
     * 未发货的称重商品，分行展示
     * @param orderId
     * @return
     */
    @PostMapping("/getUnShipped")
    @ApiOperation(value = "云超获取未发货商品")
    public List<ShopPackageDetailItemODTO> getUnShipped(@RequestParam(value = "orderId",required = false) Long orderId){
        return grouponService.getUnShipped(orderId);
    }



    @PostMapping("/getGrouponOrderByPickupCode")
    @ApiOperation(value = "团购提货(每次只提一单)   老版")
    public GrouponOrderReturnODTO getGrouponOrderByPickupCode(@RequestParam(value = "pickupCode",required = false) String pickupCode) {
        return grouponService.getGrouponOrderByPickupCode(pickupCode);
    }

    @PostMapping("/completeTakeGood")
    @ApiOperation(value = "团购提货,完成提货(每次只提一单)  老版")
    public GrouponOrderReturnODTO completeTakeGood(@RequestParam(value = "pickupCode",required = false) String pickupCode,@RequestParam(value = "barCode",required = false) String barCode) {
        return grouponService.completeTakeGood(pickupCode,barCode);
    }

    /**
     * 团购退款 (根据提货码查询提货列表)
     * @param pickupCode
     * @return
     */
    @PostMapping("/getReturnGrouponOrder")
    @ApiOperation(value = "团购退款 (根据提货码查询提货列表)")
    public GrouponOrderReturnODTO getReturnGrouponOrder(@RequestParam(value = "pickupCode",required = false) String pickupCode) {
        return grouponService.getReturnGrouponOrder(pickupCode);
    }

    /**
     *  团购退款 (提交团购退货)
     * @param pickupCode
     * @return
     */
    @PostMapping("/returnGrouponOrder")
    @ApiOperation(value = "团购退款 (提交团购退货)")
    public Boolean returnGrouponOrder(@RequestParam(value = "pickupCode",required = false) String pickupCode) {
        return grouponService.returnGrouponOrder(pickupCode);
    }

    @PostMapping("/groupStockReceipt")
    @ApiOperation(value = "团购收货", notes = "团购收货")
    public Boolean groupStockReceipt(@RequestBody StockReceiptIDTO stockReceiptIDTO) {
        //检验数据
        stockReceiptIDTO.checkData();
        grouponService.groupStockReceipt(stockReceiptIDTO);

        return Boolean.TRUE;
    }

    /**
     * 团购提货页面
     * @param idto
     * @return
     */
    @PostMapping("/grouponOrderPage")
    @ApiOperation(value = "团购提货列表页查询", notes = "团购提货列表页查询")
    public TablePageInfo<GrouponOrderPageODTO> grouponOrderPage(@RequestBody GrouponOrderPageIDTO idto){
        return grouponService.grouponOrderPage(idto);
    }

    @PostMapping("/completeDelivery")
    public Boolean completeDelivery(@RequestBody CompleteDeliveryDTO completeDeliveryDTO) {
        return grouponService.completeDelivery(completeDeliveryDTO);
    }

    /**
     * 团购单生成取货码
     * @param orderId
     * @return
     */
    @PostMapping("/generatePickupCode/{orderId}")
    public Boolean generatePickupCode(@PathVariable("orderId") Long orderId) {
        return grouponService.generatePickupCode(orderId);
    }

    /**
     * 云超发货查询
     * @param dto
     * @return
     */
    @PostMapping("/cloudDeliverList")
    @ApiOperation(value = "云超发货查询")
    public MPage<CloudDeliverListDTO> cloudDeliverList(@RequestBody CloudDeliverDTO dto) {
        return grouponService.cloudDeliverList(dto);
    }

    @GetMapping("/cloudOrderDetail")
    @ApiOperation(value = "云超发货详情")
    public CloudCommodityDetailODTO cloudOrderDetail(@RequestParam(value = "orderId",required = false) Long orderId) {
        return grouponService.cloudOrderDetail(orderId);
    }

    @GetMapping("/grouponReturnOrderInfo")
    @ApiOperation(value = "小程序团购，查询退货信息,pda专用")
    public GrouponReturnOrderInfoDTO grouponReturnOrderInfo(@RequestParam(value = "itemId",required = false) String itemId) {
        return groupPurchaseService.grouponReturnOrderInfo(itemId);
    }

}
