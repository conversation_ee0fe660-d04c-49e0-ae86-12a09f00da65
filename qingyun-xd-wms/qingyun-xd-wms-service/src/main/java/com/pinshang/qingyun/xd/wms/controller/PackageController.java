package com.pinshang.qingyun.xd.wms.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.PackageStatusEnum;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xd.wms.dto.pkg.PackageOrderODTO;
import com.pinshang.qingyun.xd.wms.dto.pkg.PackageTrackIDTO;
import com.pinshang.qingyun.xd.wms.dto.pkg.PackageTrackODTO;
import com.pinshang.qingyun.xd.wms.enums.DeliveryBatchEnum;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.pkg.PackageService;
import com.pinshang.qingyun.xd.wms.util.ExcelExportUtils;
import com.pinshang.qingyun.xd.wms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/1/5
 */

@RestController
@RequestMapping("/package")
@Api(value = "包裹", tags = "PackageController")
@Slf4j
public class PackageController {

    @Autowired
    private PackageService packageService;


    @PostMapping(value = "/packageTrackPage")
    @ApiOperation(value = "包裹追踪表分页")
    public MPage<PackageTrackODTO> packageTrackPage(@RequestBody PackageTrackIDTO dto) {
        return packageService.packageTrackPage(dto);
    }


    @GetMapping("/getOrderPackage")
    @ApiOperation(value = "云超订单包裹详情")
    public PackageOrderODTO getOrderPackage(@RequestParam(value = "orderCode",required = false) String orderCode) {
        return packageService.getOrderPackage(orderCode);
    }

    @GetMapping("/getPackageInfo")
    @ApiOperation(value = "包裹详情")
    public PackageOrderODTO getPackageInfo(@RequestParam(value = "packageOrderCode",required = false) String packageOrderCode) {
        return packageService.getPackageInfo(packageOrderCode);
    }

    @ApiOperation(value = "查询包裹状态")
    @GetMapping("/getPackageStatusList")
    public List getPackageStatusList() {
        return PackageStatusEnum.toList();
    }

    @ApiOperation(value = "查询配送批次")
    @GetMapping("/getDeliveryBatchList")
    public List getDeliveryBatchList() {
        return DeliveryBatchEnum.toList();
    }


    @GetMapping("/workCommodityListExport")
    @ApiOperation(value = "包裹追踪表导出", notes = "包裹追踪表导出")
    @FileCacheQuery(bizCode = "PACKAGE_TRACKING")
    public void workCommodityListExport(PackageTrackIDTO dto, HttpServletResponse response) throws IOException {
        dto.notLimit();
        MPage<PackageTrackODTO> res = packageService.packageTrackPage(dto);
        //Excel数据组装
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "包裹追踪表" + sdf.format(new Date()) + ".xlsx";
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("门店");
        tableHeader.add("云超订单号");
        tableHeader.add("订单状态");
        tableHeader.add("送货日期");
        tableHeader.add("配送批次");
        tableHeader.add("预约到货时间");
        tableHeader.add("包裹号");
        tableHeader.add("包裹状态");
        tableHeader.add("箱码");
        tableHeader.add("装箱时间");
        tableHeader.add("装箱人");
        tableHeader.add("出库时间");
        tableHeader.add("出库人");
        tableHeader.add("揽收时间");
        tableHeader.add("揽收人");
        tableHeader.add("卸货时间");
        tableHeader.add("卸货人");
        tableHeader.add("收货时间");
        tableHeader.add("收货人");
        tableHeader.add("拣货时间");
        tableHeader.add("拣货人");
        tableHeader.add("取消人");
        tableHeader.add("取消时间");
        List<List<String>> dataList = new ArrayList<>();
        if(res!=null && SpringUtil.isNotEmpty(res.getList())) {
            for (PackageTrackODTO e : res.getList()) {
                List<String> row = new ArrayList<>();
                row.add(e.getShopName());
                row.add(e.getOrderCode());
                row.add(e.getOrderStatusName());
                row.add(e.getOrderTimeStr());
                row.add(e.getDeliveryBatchName());
                row.add(e.getArriveTime());
                row.add(e.getPackageOrderCode());
                row.add(e.getPackageStatusName() + "");
                row.add(e.getBoxCodeList());
                row.add(e.getLoadBoxTimeStr());
                row.add(e.getLoaderBoxUserName());
                row.add(e.getOutTimeStr());
                row.add(e.getOutUserName());
                row.add(e.getTakeBoxTimeStr());
                row.add(e.getTakerBoxUserName());
                row.add(e.getUnloadBoxTimeStr());
                row.add(e.getUnloadBoxUserName());
                row.add(e.getReceiverBoxTimeStr());
                row.add(e.getReceiverBoxUserName());
                row.add(e.getPickerBoxTimeStr());
                row.add(e.getPickerBoxUserName());

                row.add(e.getCancelUseName());
                row.add(e.getCancelTimeStr());
                dataList.add(row);
            }
        }

        //覆盖文件名, 无需扩展名
        fileName = "包裹追踪表" + sdf.format(new Date());
        ExcelUtil.setFileNameAndHead(response,  fileName);
        List<List<String>> excelHead = tableHeader.stream().map(Collections::singletonList).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).head(excelHead).sheet("数据").doWrite(dataList);

        /* 已重构, 后续稳定后删除
        XSSFWorkbook xb = ExcelExportUtils.getXSSFWorkbook(fileName, tableHeader, dataList, null);
        ExcelExportUtils.exportExcel(response,fileName,xb);
        */
    }

    @ApiOperation(value = "获取包裹数量", notes = "获取包裹数量")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "PackageOrderMakeUpReqVo")
    @RequestMapping(value = "/queryPackageOrderCount", method = RequestMethod.POST)
    public Integer queryPackageOrderCount(@RequestBody PackageOrderMakeUpReqVo vo) {
        QYAssert.notNull(vo,"参数不能为空");
        QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getOrderCodeList()) || (StringUtils.isNotEmpty(vo.getStartTime()) && StringUtils.isNotEmpty(vo.getEndTime())),"至少某个条件有值的情况下才可以操作");
        QYAssert.isTrue(vo.getPackageFlag() != null && (vo.getPackageFlag() == 1 || vo.getPackageFlag() == 2), "包裹类型异常，请查验");
        return packageService.packageOrderCompare(vo);
    }

    @ApiOperation(value = "获取包裹状态不一致的信息", notes = "获取包裹状态不一致的信息")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "PackageOrderStatusInfoReqVo")
    @RequestMapping(value = "/queryPackageOrderStatusInfo", method = RequestMethod.POST)
    public List<PackageOrderStatusInfoRespVo> queryPackageOrderStatusInfo(@RequestBody PackageOrderStatusInfoReqVo vo) {
        QYAssert.notNull(vo,"参数不能为空");
        QYAssert.isTrue(vo.getPackageFlag() != null && (vo.getPackageFlag() == 3 || vo.getPackageFlag() == 4), "需要比较的包裹类型异常，请查验");
        QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getList()),"需要比对的数据不能为空");
        return packageService.queryPackageOrderStatusInfo(vo);
    }

    @ApiOperation(value = "获取XD库缺少的包裹", notes = "获取XD库缺少的包裹")
    @ApiImplicitParam(name = "list", value = "", required = true, paramType = "body", dataType = "PackageOrderIdInfoReqVo")
    @RequestMapping(value = "/queryDifferencePackageOrder", method = RequestMethod.POST)
    public List<String> queryDifferencePackageOrder(@RequestBody List<PackageOrderIdInfoReqVo> list) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(list),"DC库的包裹信息不能为空");
        return packageService.queryDifferencePackageOrder(list);
    }

    @ApiOperation(value = "获取DC缺少或状态不一致的订单Id", notes = "获取DC缺少或状态不一致的订单Id", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/queryOrderSync")
    public PackageOrderSyncRespVo queryOrderSync(@RequestBody PackageOrderSyncReqVo vo) {
        return packageService.queryOrderSync(vo);
    }

    @ApiOperation(value = "添加缺少的包裹单", notes = "添加缺少的包裹单", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/savePackageOrderSyncData")
    public ApiResult savePackageOrderSyncData(@RequestBody SavePackageOrderSyncDataReqVo vo) {
        log.error("接收到的包裹同步数据："+ JsonUtil.java2json(JsonUtil.java2json(vo)));
        QYAssert.isTrue(vo.getPackageFlag() != null && (vo.getPackageFlag() == 1 || vo.getPackageFlag() == 2), "包裹类型异常，请查验");
        if(vo.getPackageFlag() == 1){
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getInsertSyncOrderDataList()) || SpringUtil.isNotEmpty(vo.getUpdateSyncOrderDataList()), "包裹数据不能为空");
        }else{
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getInsertPackageOrderItemList()) || SpringUtil.isNotEmpty(vo.getUpdatePackageOrderItemList()), "包裹数据不能为空");
        }
        return packageService.savePackageOrderSyncData(vo);
    }
}
