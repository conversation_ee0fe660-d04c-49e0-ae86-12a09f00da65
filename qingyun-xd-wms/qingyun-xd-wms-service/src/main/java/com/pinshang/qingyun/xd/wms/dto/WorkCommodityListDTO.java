package com.pinshang.qingyun.xd.wms.dto;

import com.pinshang.qingyun.xd.wms.plus.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkCommodityListDTO extends Pagination<WorkCommodityListResult> {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "是否有加工点  1-有加工点 0没有加工点")
    private Integer isWork;

    @ApiModelProperty(value = "加工点id")
    private Long warehouseWorkId;

    @ApiModelProperty(value = "是否有前台加工方式  1-有 0没有")
    private Integer isProcess;

    @ApiModelProperty(value = "仓库Id")
    private Long warehouseId;
}
