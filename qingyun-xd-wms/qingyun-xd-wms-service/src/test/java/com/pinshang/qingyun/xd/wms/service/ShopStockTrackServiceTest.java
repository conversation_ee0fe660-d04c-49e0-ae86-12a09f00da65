package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.service.pkg.PackageService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @ClassName ShopStockTrackServiceTest
 * <AUTHOR>
 * @Date 2022/11/10 18:19
 * @Description ShopStockTrackServiceTest
 * @Version 1.0
 */
public class ShopStockTrackServiceTest extends AbstractJunitBase {
    @Autowired
    private ShopStockTrackService service;

    @Autowired
    private PackageService packageService;
    @Test
    @Rollback(value = false)
    public void insertStockListTest(){
        service.insertStockList();
    }

    @Test
    @Rollback(value = false)
    public void insertPackage() {
        packageService.insertPackage(1928341498320117762L);
    }


}
