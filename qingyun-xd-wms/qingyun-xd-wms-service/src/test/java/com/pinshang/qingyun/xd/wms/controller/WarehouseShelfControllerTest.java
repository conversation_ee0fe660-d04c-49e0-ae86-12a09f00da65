package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.QueryWarehouseShelfDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryWarehouseShelfResult;
import com.pinshang.qingyun.xd.wms.dto.ShelfFreeDTO;
import com.pinshang.qingyun.xd.wms.dto.WarehouseShelfDTO;
import com.pinshang.qingyun.xd.wms.enums.ShelfStatusEnum;
import com.pinshang.qingyun.xd.wms.enums.ShelfTypeEnum;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class WarehouseShelfControllerTest extends AbstractJunitBase {

    @Autowired
    private WarehouseShelfService warehouseShelfService;

    @Test
    public void batchInsertWarehouseShelf() {
        List<WarehouseShelfDTO> list = new ArrayList<>();
        WarehouseShelfDTO warehouseShelfDTO = new WarehouseShelfDTO();
        warehouseShelfDTO.setShelfNo("aa");
        warehouseShelfDTO.setStatus(ShelfStatusEnum.ENABLE.getCode());
        warehouseShelfDTO.setType(ShelfTypeEnum.PICK.getCode());
//        warehouseShelfDTO.setCreateId(1L);
//        warehouseShelfDTO.setCreateTime(new Date());
//        warehouseShelfDTO.setUpdateTime(new Date());
//        warehouseShelfDTO.setUpdateId(1l);
        list.add(warehouseShelfDTO);
        warehouseShelfService.batchInsertWarehouseShelf(list);
    }

    @Test
    public void updateWarehouseShelfStatus() {
        WarehouseShelfDTO dto = new WarehouseShelfDTO();
        dto.setId(24L);
        dto.setStatus(0);
        warehouseShelfService.updateWarehouseShelfStatus(dto);
    }

    @Test
    public void queryWarehouseShelf() {
        QueryWarehouseShelfDTO queryWarehouseShelfDTO = new QueryWarehouseShelfDTO();
//        queryWarehouseShelfDTO.setShelfNo("a");
//        queryWarehouseShelfDTO.setStatus(ShelfStatusEnum.DISABLE);
//        queryWarehouseShelfDTO.setOccupy(2);
        queryWarehouseShelfDTO.setType(ShelfTypeEnum.PICK.getCode());
        MPage<QueryWarehouseShelfResult> iPage = warehouseShelfService.queryWarehouseShelf(queryWarehouseShelfDTO);
        log.info("sss={}", iPage);
    }

    @Test
    public void queryShelfNotBind() {
        QueryWarehouseShelfDTO queryWarehouseShelfDTO = new QueryWarehouseShelfDTO();
        //queryWarehouseShelfDTO.setType(ShelfTypeEnum.PICK);
        //queryWarehouseShelfDTO.setOccupy(2);
        List<QueryWarehouseShelfResult> list = warehouseShelfService.queryShelfNotPage(queryWarehouseShelfDTO);
        log.info("sss={}",list);
    }

    @Test
    public void queryShelfFree() {
        ShelfFreeDTO dto = new ShelfFreeDTO();
        dto.setType(2);
        dto.setPageSize(2);
        warehouseShelfService.queryShelfFree(dto);
    }

}
