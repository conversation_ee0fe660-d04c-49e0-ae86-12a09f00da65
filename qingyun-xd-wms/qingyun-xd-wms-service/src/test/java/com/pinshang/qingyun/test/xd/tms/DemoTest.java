package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.model.DemoWms;
import com.pinshang.qingyun.xd.wms.service.DemoWmsService;
import org.junit.Test;
import org.redisson.api.RDeque;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName DemoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/14 11:16
 **/
public class DemoTest extends AbstractJunitBase {
    @Autowired
    private DemoWmsService demoWmsService;

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testPick(){
        RDeque<Long> testQue = redissonClient.getDeque("testQueue");
        testQue.clear();
        testQue.addLast(1L);
        testQue.addLast(2L);
        testQue.addLast(3L);
        testQue.addLast(1L);
        testQue.addLast(2L);
        testQue.addLast(3L);
        testQue.remove(2L);

        Long first = testQue.removeFirst();
        testQue.addLast(first);

        System.out.println(testQue);


        System.out.println(testQue.contains(first));
    }

    @Test
    public void testPickEmployee(){
        Long shopId = 3321L;
        RDeque<Long> pickers = redissonClient.getDeque("WMS:PICK_EMPLOYEE:" + shopId);
        pickers.clear();
    }

    @Test
    public void testShelf(){
        RList<String> testList = redissonClient.getList("testList");

        testList.clear();
        testList.add("L1");
        testList.add("L2");
        testList.add("L3");
        testList.add("L4");
        testList.add("L1");
        testList.add("L2");
        testList.add("L3");
        testList.add("L4");

//        String shelf = testList.remove(0);
//        testList.add(shelf);

        testList.remove("L2");
        testList.remove("L2");
        testList.remove("L2");
        testList.remove("L4");
        testList.add("L5");


        System.out.println(testList);
    }

    @Test
    public void getId(){
        DemoWms country =  demoWmsService.getById();
        System.out.println(country.getName());
    }
}
