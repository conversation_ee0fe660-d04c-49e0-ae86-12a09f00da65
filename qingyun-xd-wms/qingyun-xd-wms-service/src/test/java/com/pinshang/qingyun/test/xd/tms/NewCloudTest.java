package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.service.cloud.NewCloudService;
import org.junit.Test;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 云超
 * Created by chenqi on 2023/12/03.
 */
@Rollback(false)
public class NewCloudTest extends AbstractJunitBase {

    @Autowired
    private NewCloudService newCloudService;

    @Autowired
    private RedissonClient redissonClient;

    @Test
    @Rollback(false)
    public void completeOrderInfoForCloudTakeGoodTest(){
        Long[] data = {1729093661215326210L,1726910834632912897L};

        List<Long> idList = Arrays.asList(data);

        for (Long orderId : idList) {
            try {
                newCloudService.completeOrderInfoForCloudTakeGood(orderId);
//                cachedThreadPool.execute(()->orderBizService.cancelOrder(orderId, false, false));

                Thread.sleep(100L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
