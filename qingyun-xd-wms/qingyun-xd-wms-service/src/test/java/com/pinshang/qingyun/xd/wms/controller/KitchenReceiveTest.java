package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceiveAddIDTO;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageIDTO;
import com.pinshang.qingyun.xd.wms.dto.KitchenReceivePageODTO;
import com.pinshang.qingyun.xd.wms.service.KitchenReceiveService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;

/**
 * @ClassName KitchenReceiveTest
 * <AUTHOR>
 * @Date 2021/10/21 17:10
 * @Description KitchenReceiveTest
 * @Version 1.0
 */
@Rollback(value = false)
public class KitchenReceiveTest extends AbstractJunitBase {
    @Autowired
    private KitchenReceiveService kitchenReceiveService;
    @Test
    public void addTest(){
        preHandle();

        KitchenReceiveAddIDTO idto = new KitchenReceiveAddIDTO();
        idto.setCommodityId(25154812947092400L);
        idto.setNumber(2);
        idto.setQuantity(new BigDecimal("13.25"));
        kitchenReceiveService.add(idto);
    }

    @Test
    public void pageTest(){
        KitchenReceivePageIDTO idto = new KitchenReceivePageIDTO();
        idto.setPageNo(1);
        idto.setPageSize(20);
        PageInfo<KitchenReceivePageODTO> result = kitchenReceiveService.page(idto);
        System.out.println(result.toString());
    }

    public static void preHandle(){
        TokenInfo qy = new TokenInfo();
        qy.setShopId(2414L);
        qy.setUserId(23805L);
        FastThreadLocalUtil.setQY(qy);
    }
}
