package com.pinshang.qingyun.xd.wms.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.service.bigShop.StallCommodityStockService;
import com.pinshang.qingyun.xd.wms.vo.bigShop.BigShopCommodityUpDownKafkaVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;


public class StallCommodityStockServiceTest extends AbstractJunitBase {
    @Autowired
    private StallCommodityStockService stallCommodityStockService;

    @Test
    @Rollback(value = false)
    public void dealBigShopStallCommodityStockTest() {
        String msg = "[{\"appStatus\":1,\"shopId\":3,\"commodityIdList\":[2370917302917800]}]";

        List<BigShopCommodityUpDownKafkaVo> appStatusList = JSON.parseArray(msg, BigShopCommodityUpDownKafkaVo.class);

        stallCommodityStockService.dealBigShopStallCommodityStock(appStatusList);
    }
}
