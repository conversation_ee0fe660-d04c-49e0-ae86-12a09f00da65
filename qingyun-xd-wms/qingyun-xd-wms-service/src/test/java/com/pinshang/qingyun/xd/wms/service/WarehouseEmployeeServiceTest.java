package com.pinshang.qingyun.xd.wms.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import org.junit.Test;
import org.redisson.api.RDeque;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName WarehouseEmployeeServiceTest
 * <AUTHOR>
 * @Date 2023/5/19 19:16
 * @Description WarehouseEmployeeServiceTest
 * @Version 1.0
 */
public class WarehouseEmployeeServiceTest extends AbstractJunitBase {

    private static final String PICK_EMPLOYEE = "WMS:PICK_EMPLOYEE:";

    @Autowired
    private RedissonClient redissonClient;


    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Test
    public void test(){
      /*  RDeque<Long> pickers = redissonClient.getDeque(PICK_EMPLOYEE + 16);
        //pickers.addLast(11111L);
        pickers.clear();*/

        this.sendClosePxAccountKafkaMsg(Collections.singletonList(999771252982050041L));
    }

    public void sendClosePxAccountKafkaMsg(List<Long> employeeIdList) {
        if (SpringUtil.isNotEmpty(employeeIdList)) {
            try {
                mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.CLOSE_PX_ACCOUNT_TOPIC.getTopic(),
                        employeeIdList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.CLOSE_PX_ACCOUNT.name(),
                        KafkaMessageOperationTypeEnum.DELETE.name());
            } catch (Exception e) {
                System.out.println("【SMM发送  离职/关闭账号  消息】异常：\n error=" + e + "\n userIdList=" + employeeIdList);
            }
        } else {
            System.out.println("【SMM发送  离职/关闭账号  消息】：userIdList不能为空！");
        }
    }

}
