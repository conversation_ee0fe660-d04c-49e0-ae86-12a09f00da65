package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryCommodityByShelfResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseShelfCommodityDTO;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseShelfCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class WarehouseShelfCommodityControllerTest extends AbstractJunitBase {

    @Autowired
    private WarehouseShelfCommodityService warehouseShelfCommodityService;

    @Test
    public void insertWarehouseShelfCommodity() {
        WarehouseShelfCommodityDTO e = new WarehouseShelfCommodityDTO();
        e.setCommodityId(2540934434499500l);
        e.setShelfId(5l);
        warehouseShelfCommodityService.insertWarehouseShelfCommodity(e);
    }

    @Test
    public void updateShelfCommodityByCommodityid() {
        WarehouseShelfCommodityDTO e = new WarehouseShelfCommodityDTO();
        e.setCommodityId(2580787591198700l);
        e.setShelfId(14l);
        warehouseShelfCommodityService.updateShelfidByCommodityid(e);
    }

    @Test
    public void deleteBycommodityId() {
        WarehouseShelfCommodityDTO dto = new WarehouseShelfCommodityDTO();
        warehouseShelfCommodityService.deleteByCommodityId(dto);
    }

    @Test
    public void queryCommodityByWarehouseShelf() {
        QueryCommodityByShelfDTO dto = new QueryCommodityByShelfDTO();
        //dto.setIsShelf(0);
        //dto.setCommodityId(2540934434499500l);
        MPage<QueryCommodityByShelfResult> pageInfo = warehouseShelfCommodityService.queryCommodityByWarehouseShelf(dto);
        log.info("sss={}", pageInfo);
    }

//    @Test
//    public void deleteBycommodityId() {
//        warehouseShelfCommodityService.deleteBycommodityId(444l);
//    }
}
