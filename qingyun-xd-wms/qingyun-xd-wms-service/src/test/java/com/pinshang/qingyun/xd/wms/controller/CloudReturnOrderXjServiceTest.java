package com.pinshang.qingyun.xd.wms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.xd.XdOrderTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdPickOrderStatusEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.ClouodCancelOrderListIDTO;
import com.pinshang.qingyun.xd.wms.dto.ClouodCancelOrderListODTO;
import com.pinshang.qingyun.xd.wms.dto.PickOrderMqDTO;
import com.pinshang.qingyun.xd.wms.service.groupon.CloudReturnOrderXjService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName CloudReturnOrderXjServiceTest
 * <AUTHOR>
 * @Date 2022/1/6 17:58
 * @Description CloudReturnOrderXjServiceTest
 * @Version 1.0
 */
public class CloudReturnOrderXjServiceTest extends AbstractJunitBase {
    @Autowired
    private CloudReturnOrderXjService cloudReturnOrderXjService;

    @Test
    public void distributionWarnForPDA(){
        cloudReturnOrderXjService.distributionWarnForPDA();
    }

    @Test
    public void distributionWarnByShopId(){
        TokenInfo qy = FastThreadLocalUtil.getQY();
        qy.setUserId(16L);
        cloudReturnOrderXjService.distributionWarnByShopId(16L);
    }

    @Test
    public void cancelOrderListTest(){
        ClouodCancelOrderListIDTO dto = new ClouodCancelOrderListIDTO();
        dto.setOrderBeginTime("2022-02-08");
        dto.setOrderEndTime("2022-02-09");
        dto.setArriveBeginTime("2022-02-08");
        dto.setArriveEndTime("2022-02-09");
        dto.setOrderMobile("18017612152");
        PageInfo<ClouodCancelOrderListODTO> result = cloudReturnOrderXjService.cancelOrderList(dto);
        System.out.println(result.getList().toString());
    }

    @Test
    public void cancelOrderTest(){
        PickOrderMqDTO mqDTO = new PickOrderMqDTO();
        mqDTO.setOrderId(1L);
        mqDTO.setOrderCode("501578500377590294");
        mqDTO.setPickStatus(XdPickOrderStatusEnum.CANCEL.getCode());
        mqDTO.setOrderType(XdOrderTypeEnum.CLOUDXJ.getCode());
    }
}
