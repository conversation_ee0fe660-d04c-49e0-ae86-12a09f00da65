package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListDTO;
import com.pinshang.qingyun.xd.wms.dto.WorkCommodityListResult;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseWorkCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class WarehouseWorkCommodityControllerTest extends AbstractJunitBase {

    @Autowired
    private WarehouseWorkCommodityService warehouseWorkCommodityService;

    @Test
    public void workCommodityList() {
        WorkCommodityListDTO dto = new WorkCommodityListDTO();
        //dto.setCommodityId(1L);
        MPage<WorkCommodityListResult> pageInfo = warehouseWorkCommodityService.workCommodityList(dto);
        log.info("ddd={}",pageInfo);
    }
}
