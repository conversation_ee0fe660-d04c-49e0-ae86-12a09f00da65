package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionDTO;
import com.pinshang.qingyun.xd.wms.dto.QueryWorkByConditionResult;
import com.pinshang.qingyun.xd.wms.dto.WarehouseWorkDTO;
import com.pinshang.qingyun.xd.wms.enums.WorkStatusEnum;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.WarehouseWorkService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class WarehouseWorkControllerTest extends AbstractJunitBase {

    @Autowired
    private WarehouseWorkService warehouseWorkService;

    @Test
    public void insertWarehouseWork() {
        WarehouseWorkDTO data = new WarehouseWorkDTO();
        data.setWorkName("uu");
        warehouseWorkService.insertWarehouseWork(data);
    }

    @Test
    public void updateWarehouseWork() {
        WarehouseWorkDTO data = new WarehouseWorkDTO();
        data.setId(1l);
        data.setWorkName("ddd");
        warehouseWorkService.updateWarehouseWork(data);
    }

    @Test
    public void updateWarehouseWorkStatus() {
        WarehouseWorkDTO data = new WarehouseWorkDTO();
        data.setId(5l);
        data.setStatus(WorkStatusEnum.DISABLE.getCode());
        warehouseWorkService.updateWarehouseWorkStatus(data);
    }

    @Test
    public void warehouseWorkByStatus() {
        List<WarehouseWorkDTO> list = warehouseWorkService.warehouseWorkByStatus(1);
        log.info("sss={}",list);
    }

    @Test
    public void queryWorByCondition() {
        QueryWorkByConditionDTO data = new QueryWorkByConditionDTO();
        MPage<QueryWorkByConditionResult> pageData = warehouseWorkService.queryWorByCondition(data);
        log.info("sss={}",pageData);
    }
}
