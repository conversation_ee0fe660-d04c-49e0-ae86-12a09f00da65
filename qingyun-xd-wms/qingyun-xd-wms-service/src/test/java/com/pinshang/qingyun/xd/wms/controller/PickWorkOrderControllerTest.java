package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderDTO;
import com.pinshang.qingyun.xd.wms.dto.PickWorkOrderResult;
import com.pinshang.qingyun.xd.wms.dto.PrintWorkOrderrResult;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.PickWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class PickWorkOrderControllerTest extends AbstractJunitBase {

    @Autowired
    private PickWorkOrderService pickWorkOrderService;

    @Test
    public void pickWorkOrderList() {
        PickWorkOrderDTO dto = new PickWorkOrderDTO();
   //     dto.setWorkStatus(1);
        MPage<PickWorkOrderResult> res =  pickWorkOrderService.pickWorkOrderList(dto);
        log.info("res={}", res);
    }

    @Test
    public void printWorkOrder() {
        PrintWorkOrderrResult one = pickWorkOrderService.printWorkOrder(1l);
        log.info("one={}",one);
    }

    @Test
    public void workComplete() {
        pickWorkOrderService.workComplete(1201783252749422593l);
    }
}
