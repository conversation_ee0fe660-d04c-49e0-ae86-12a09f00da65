package com.pinshang.qingyun.xd.wms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.shop.dto.ShopCommodityStockODTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptIDTO;
import com.pinshang.qingyun.shop.dto.stock.ShopStockReceiptItemIDTO;
import com.pinshang.qingyun.shop.service.ShopCommodityClient;
import com.pinshang.qingyun.shop.service.ShopStockClient;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.order.dto.XdMultipleCommodityApplyReturnIDTO;
import com.pinshang.qingyun.xd.wms.dto.ShopStockDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.CloudApplyReturnOrderIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.CloudCancelIniIDTO;
import com.pinshang.qingyun.xd.wms.mapper.XdReturnOrderItemMapper;
import com.pinshang.qingyun.xd.wms.model.XdReturnOrderItemEntity;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import com.pinshang.qingyun.xd.wms.service.groupon.CloudReturnOrderXjService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * @ClassName CloudXjReturnOrderController
 * <AUTHOR>
 * @Date 2021/6/22 16:23
 * @Description CloudXjReturnOrderController
 * @Version 1.0
 */
public class CloudXjReturnOrderControllerTest extends AbstractJunitBase {
    @Autowired
    CloudXjReturnOrderController cloudXjReturnOrderController;
    @Autowired
    private ShopCommodityClient shopCommodityClient;
    @Autowired
    private XdReturnOrderItemMapper xdReturnOrderItemMapper;
    @Autowired
    private ShopCommodityService shopCommodityService;
    @Autowired
    private ShopStockClient shopStockClient;

    @Autowired
    private CloudReturnOrderXjService cloudReturnOrderXjService;
    @Test
    public void returnOrderTest(){
        setFastUtil();
        CloudApplyReturnOrderIDTO idto = new CloudApplyReturnOrderIDTO();
        idto.setOrderId(1420565761461686273L);
        List<XdMultipleCommodityApplyReturnIDTO> itemList = new ArrayList<>();
        XdMultipleCommodityApplyReturnIDTO item = new XdMultipleCommodityApplyReturnIDTO();
        item.setOrderItemId(1419927849871114459L);
        item.setReturnNumber(1);
        itemList.add(item);
        idto.setMultipleCommodity(itemList);
        cloudXjReturnOrderController.applyReturnOrder(idto);
    }

    @Test
    public void queryOrderInfoForReturn(){
        cloudXjReturnOrderController.queryOrderInfoForReturn("501621518683996541");
    }


    @Test
    public void testStock(){
        Long returnOrderId = 1417071445619580929L;
        Long shopId = 66L;
        Long storeId = 999872035591511727L;
        List<XdReturnOrderItemEntity> returnOrderItemList = xdReturnOrderItemMapper.selectList(new LambdaQueryWrapper<XdReturnOrderItemEntity>()
                .eq(XdReturnOrderItemEntity::getReturnOrderId, returnOrderId));
        List<Long> commodityIdList = returnOrderItemList.stream().map(XdReturnOrderItemEntity::getCommodityId).collect(Collectors.toList());
        //获取成本价位0的记录
        Map<Long, ShopCommodityStockODTO> shopCommodityValidMap = shopCommodityClient.queryShopCommodityValidStock(shopId, commodityIdList);
        List<ShopStockDTO> finishedStockList = shopCommodityService.queryShopStock(shopId, commodityIdList);
        Map<Long, Integer> stockCollect = finishedStockList.stream().collect(toMap(ShopStockDTO::getCommodityId, ShopStockDTO::getStockNumber));

        List<ShopStockReceiptItemIDTO> commodityList = new ArrayList<>(returnOrderItemList.size());
        ShopStockReceiptIDTO shopStockReceipt = new ShopStockReceiptIDTO();
        shopStockReceipt.setShopId(shopId);

        Map<String, BigDecimal> stockCommodityPrice = cloudReturnOrderXjService.getStockInPrice(storeId, commodityIdList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(stockCommodityPrice), "门店进货价为空");

        for(XdReturnOrderItemEntity item: returnOrderItemList){
            if(null != shopCommodityValidMap.get(item.getCommodityId()) && (BigDecimal.ZERO.compareTo(shopCommodityValidMap.get(item.getCommodityId()).getWeightPrice())==0)){
                ShopStockReceiptItemIDTO stockReceiptItem = new ShopStockReceiptItemIDTO();
                stockReceiptItem.setCommodityId(item.getCommodityId());
                stockReceiptItem.setQuantity(item.getQuantity());
                BigDecimal price = stockCommodityPrice.get(item.getCommodityId()+"");
                stockReceiptItem.setPrice(price);
                stockReceiptItem.setTotalPrice(price.multiply(item.getQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP));
                stockReceiptItem.setExistStockNumber(stockCollect.get(item.getCommodityId()));
                commodityList.add(stockReceiptItem);
            }
        }
        shopStockReceipt.setCommodityList(commodityList);

        if(SpringUtil.isNotEmpty(shopStockReceipt.getCommodityList())) {
            shopStockClient.stockReceipt(shopStockReceipt);
        }
    }

    public static void setFastUtil(){
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserId(1L);
        tokenInfo.setShopId(16L);
        tokenInfo.setStoreId(999872035591509085L);
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(16L);
        shopIdList.add(97L);
        tokenInfo.setShopIdList(shopIdList);
        FastThreadLocalUtil.setQY(tokenInfo);
    }

    @Test
    public void cloudCancelIniTest(){
        List<Long> orderIds = new ArrayList<>();
        orderIds.add(1419921339447300098L);
        CloudCancelIniIDTO idto = new CloudCancelIniIDTO();
        idto.setOrderIdList(orderIds);
        idto.setUserId(1L);
        cloudReturnOrderXjService.cloudCancelIni(idto);
    }

    @Test
    public void checkForInventoryInitialTest(){
        List<String> orderCodes = new ArrayList<>();
        orderCodes.add("501627375129237477");
        cloudReturnOrderXjService.checkForInventoryInitial(orderCodes);
    }
}
