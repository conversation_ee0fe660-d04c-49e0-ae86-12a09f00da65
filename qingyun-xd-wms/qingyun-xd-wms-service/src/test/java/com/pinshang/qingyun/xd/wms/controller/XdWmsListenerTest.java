package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.listener.XdWmsListener;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @ClassName XdWmsListener
 * <AUTHOR>
 * @Date 2022/2/12 10:56
 * @Description XdWmsListener
 * @Version 1.0
 */
public class XdWmsListenerTest extends AbstractJunitBase {
    @Autowired
    private XdWmsListener xdWmsListener;

    @Rollback(value = false)
    @Test
    public void deliveryOrderChangeBroadcastTest(){
        String message = "{\"wrapper\":\"kafka\",\"type\":\"XD_DELIVERY_CHANGE_TYPE\",\"data\":{\"orderId\":1569926503553114114,\"orderCode\":\"601663134620928972\",\"orderType\":1,\"deliveryStatus\":4,\"deliveryType\":2,\"sourceType\":3,\"ifAutomaticReturns\":1,\"ifOvertime\":0,\"warehouseId\":16},\"optionType\":\"UPDATE\",\"uuid\":\"28cc5bca-cbec-41f1-8586-22c7faad433b\",\"keyId\":\"0\"}";
        xdWmsListener.deliveryOrderChangeBroadcast(message);
    }
}
