package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.StockAllotOutDTO;
import com.pinshang.qingyun.xd.wms.dto.StockAllotOutItemDTO;
import com.pinshang.qingyun.xd.wms.service.StockAllotOrderService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 拣货
 * Created by chenqi on 2019/12/03.
 */
@Rollback(false)
public class AllotOrderTest extends AbstractJunitBase {

    @Autowired
    private StockAllotOrderService stockAllotOrderService;

    @Test
    public void testAuditRejected(){
        stockAllotOrderService.auditRejected(1L);

        QYAssert.isTrue(true, "");
    }

    @Test
    public void testAudit(){
        stockAllotOrderService.audit(1L);

        QYAssert.isTrue(true, "");
    }

    @Test
    public void testAllotOut(){
        StockAllotOutDTO stockAllotOutDTO = new StockAllotOutDTO();
        stockAllotOutDTO.setStockAllotId(1L);
        List<StockAllotOutItemDTO> items = new ArrayList<>();
        StockAllotOutItemDTO dto = new StockAllotOutItemDTO();
        dto.setCommodityId(261830719339504928L);
        dto.setOutQuantity(BigDecimal.valueOf(5));
        items.add(dto);

        StockAllotOutItemDTO dto1 = new StockAllotOutItemDTO();
        dto1.setCommodityId(262192507435959136L);
        dto1.setOutQuantity(BigDecimal.valueOf(6));
        items.add(dto1);
        stockAllotOutDTO.setItems(items);
        stockAllotOrderService.allotOut(stockAllotOutDTO);

        QYAssert.isTrue(true, "");
    }

    @Test
    public void testAllotIn(){
        stockAllotOrderService.allotIn(1L);

        QYAssert.isTrue(true, "");
    }


}
