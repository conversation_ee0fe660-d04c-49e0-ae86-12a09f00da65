package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponOrderPageIDTO;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponOrderPageODTO;
import com.pinshang.qingyun.xd.wms.service.groupon.GrouponService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName GrouponControllerTest
 * <AUTHOR>
 * @Date 2020/12/21 15:52
 * @Description GrouponControllerTest
 * @Version 1.0
 */

@Slf4j
public class GrouponControllerTest extends AbstractJunitBase {
    @Autowired
    GrouponService grouponService;

    @Test
    public void grouponPageTest(){
        GrouponOrderPageIDTO idto = new GrouponOrderPageIDTO();
        idto.setPageNo(1);
        idto.setPageSize(10);
        idto.setArrivalBeginTime("2021-03-10 00:00:00");
        idto.setArrivalEndTime("2021-03-25 14:39:58");
        //idto.setCommodityId(37027L);
        //idto.setReceiveMobile("12");
        idto.setSuccReceiveBeginTime("2020-12-30 12:08:06");
        idto.setSuccReceiveEndTime("2020-12-30 13:08:06");
        idto.setPickupCode("828985");
        TablePageInfo<GrouponOrderPageODTO> odto = grouponService.grouponOrderPage(idto);
        System.out.println(odto.toString());
    }
}
