package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.service.PickOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class PickOrderItemControllerTest extends AbstractJunitBase {

    @Autowired
    private PickOrderItemService pickOrderItemService;

    @Test
    public void orderCommodityDetail() {
        pickOrderItemService.orderCommodityDetail(1201783252480987137l);
    }

    @Test
    public void orderCommodityList() {
        pickOrderItemService.orderCommodityList(120178325248098713l);
    }

}
