package com.pinshang.qingyun.xd.wms.controller;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.PickOrderDTO;
import com.pinshang.qingyun.xd.wms.dto.PickOrderListResult;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

@Slf4j
public class PickOrderControllerTest extends AbstractJunitBase {

    @Autowired
    private PickOrderService pickOrderService;

    @Test
    public void pickOrderList() {
        PickOrderDTO dto = new PickOrderDTO();
        dto.setEmployeeName("欢");
        MPage<PickOrderListResult> page =  pickOrderService.pickOrderList(dto);
        log.info("ddd={}",page);
    }

    @Test
    public void pickOrderById() {
        PickOrderListResult res = pickOrderService.pickOrderById(1201783252480987137l);
        log.info("res={}", res);
    }

    @Test
    public void distributePickOrderSingle(){
        pickOrderService.distributePickOrderSingle(1539182971389603841L, 999771252982024973L);
    }

    @Test
    @Rollback(value = false)
    public void beginPickOrderTest(){
        pickOrderService.beginPickOrder(1595319197959843842L);
    }
}
