package com.pinshang.qingyun.test.xd.wms;

import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.controller.CloudController;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotListForStockAndPriceODTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockSnapshotPageODTO;
import com.pinshang.qingyun.xd.wms.service.StockLogSnapshotService;
import com.pinshang.qingyun.xd.wms.service.StockSnapshotDayService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-05-12-14:54
 */
public class StockLogSnapshotServiceTest extends AbstractJunitBase {
    @Autowired
    private StockLogSnapshotService stockLogSnapshotService;
    @Autowired
    private StockSnapshotDayService stockSnapshotDayService;
    @Autowired
    private CloudController cloudController;

    @Rollback(value = false)
    @Test
    public void generateTotalScheduleTest(){
        stockLogSnapshotService.generateTotalSchedule();
    }


    @Rollback(value = false)
    @Test
    public void generateSnapshotWeeklyTest(){
        stockLogSnapshotService.generateSnapshotWeekly();
    }

    @Test
    public void listForStockAndPriceTest(){
        StockSnapshotListForStockAndPriceIDTO idto = new StockSnapshotListForStockAndPriceIDTO();
        idto.setShopId(4404L);
        idto.setDate("2023-01-05");
        List<StockSnapshotListForStockAndPriceODTO> result = stockLogSnapshotService.listForStockAndPrice(idto);
        System.out.println(result.toString());
    }

    @Test
    @Rollback(value = false)
    public void insertHistoryWeeklyRecord(){
        stockLogSnapshotService.insertHistoryWeeklyRecord("2022-05-26 00:00:00");
    }

    @Test
    public void page(){
        StockSnapshotPageIDTO idto = new StockSnapshotPageIDTO();
        idto.setPageNo(381);
        idto.setPageSize(20);
        idto.setDate("2022-11-01");
        idto.setShopId(6036L);
        idto.setStockStatus(0);
        TablePageInfo<StockSnapshotPageODTO> result = stockLogSnapshotService.page(idto);
        System.out.println(result.getList().toString());
        List<StockSnapshotPageODTO> collect = result.getList().stream().filter(it -> it.getCommodityId().equals(999965182752840247L)).collect(Collectors.toList());
        System.out.println(collect);
    }

    @Rollback(value = false)
    @Test
    public void insertStockSnapshotDay() {
        stockSnapshotDayService.insertStockSnapshotDay("2023-01-03");
    }

    @Rollback(value = false)
    @Test
    public void test() {
        cloudController.pickPackage(1693861060787257346L,"0058230822000013");
    }
}
