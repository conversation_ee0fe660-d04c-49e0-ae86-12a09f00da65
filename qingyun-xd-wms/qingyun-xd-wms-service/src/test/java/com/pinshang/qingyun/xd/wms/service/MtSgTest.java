//package com.pinshang.qingyun.xd.wms.service;
//
//import com.alibaba.fastjson.JSONArray;
//import com.pinshang.qingyun.test.xd.AbstractJunitBase;
//import com.pinshang.qingyun.xd.wms.dto.meiTuan.RetailSkuStockDTO;
//import com.pinshang.qingyun.xd.wms.dto.meiTuan.RetailSkuStockSkuDTO;
//import com.pinshang.qingyun.xd.wms.util.MeiTuanClient;
//import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
//import com.sankuai.meituan.shangou.open.sdk.request.RetailSkuStockRequest;
//import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
///**
// * @ClassName MtSgTest
// * <AUTHOR>
// * @Date 2023/7/21 10:33
// * @Description MtSgTest
// * @Version 1.0
// */
//public class MtSgTest extends AbstractJunitBase {
//
//    @Autowired
//    private MeiTuanClient meiTuanClient;
//
//    @Test
//    public void shopStockSyncTest() throws SgOpenException, IOException {
//        RetailSkuStockRequest request = new RetailSkuStockRequest(meiTuanClient.getSystemParam());
//        request.setApp_poi_code("51");
//        RetailSkuStockSkuDTO skuStockSkuDTO1 = new RetailSkuStockSkuDTO("0019253", "350");
//        RetailSkuStockSkuDTO skuStockSkuDTO2 = new RetailSkuStockSkuDTO("000002", "450");
//        RetailSkuStockDTO skuStockDTO1 = new RetailSkuStockDTO("0019253", Collections.singletonList(skuStockSkuDTO1));
//        RetailSkuStockDTO skuStockDTO2 = new RetailSkuStockDTO("000002", Collections.singletonList(skuStockSkuDTO2));
//        List<RetailSkuStockDTO> stockDTOList = new ArrayList<>();
//        stockDTOList.add(skuStockDTO1);
//        stockDTOList.add(skuStockDTO2);
//        String foodData = JSONArray.toJSONString(stockDTOList);
//        // String foodData = "[{\"app_spu_code\":\"0019253\",\"skus\":[{\"sku_id\":\"0019253\",\"stock\":\"300\"}]},{\"app_spu_code\":\"000002\",\"skus\":[{\"sku_id\":\"000002\",\"stock\":\"400\"}]}]";
//        request.setFood_data(foodData);
//        SgOpenResponse result = request.doRequest();
//
//    }
//
//}
