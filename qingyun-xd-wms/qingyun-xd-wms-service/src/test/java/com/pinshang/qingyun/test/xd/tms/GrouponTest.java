package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.groupon.GrouponOrderCommodityDTO;
import com.pinshang.qingyun.xd.wms.service.groupon.GrouponService;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.util.Assert;

/**
 * 拣货
 * Created by chenqi on 2021/01/14.
 */
@Rollback(false)
public class GrouponTest extends AbstractJunitBase {

    @Autowired
    private GrouponService grouponService;

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testCodeTest(){
        for (int i = 0; i < 100; i++) {
            String pickupCode = grouponService.generateRandomCode();
            System.out.println(pickupCode);
        }
        Assert.isTrue(1 == 1);
    }

    @Test
    public void testGenerateCodeTest(){
        GrouponOrderCommodityDTO grouponOrderCommodityDTO = grouponService.grouponOrderListByPickupCode("599696");
        Assert.isTrue(grouponOrderCommodityDTO != null);
    }

    @Test
    public void testGrouponOrderListByPickupCode(){
        Long orderId = 1348537209015279618L;
        boolean b = grouponService.generatePickupCode(orderId);
        Assert.isTrue(b == true);
    }
}
