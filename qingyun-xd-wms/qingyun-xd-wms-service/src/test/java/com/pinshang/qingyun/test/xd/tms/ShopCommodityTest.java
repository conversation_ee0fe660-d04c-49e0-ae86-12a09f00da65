package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.mapper.CommodityStockMapper;
import com.pinshang.qingyun.xd.wms.plus.MPage;
import com.pinshang.qingyun.xd.wms.service.ShopCommodityService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存
 * Created by chenqi on 2019/11/25.
 */
@Rollback(false)
public class ShopCommodityTest extends AbstractJunitBase {
    @Autowired
    private CommodityStockMapper commodityStockMapper;

    @Autowired
    private ShopCommodityService shopCommodityService;

    @Autowired
    private KafkaTemplate<String,String> kafkaTemplate;

    @Test
    public void testStockMessage(){

        CommodityStockIDTO stockIDTO = new CommodityStockIDTO();
        List<CommodityStockItemDTO> commodityList = new ArrayList<>();
        CommodityStockItemDTO item = null;

        commodityList.add(item);

        stockIDTO.setShopId(4258L);
        stockIDTO.setReferId(53300400833610065L);
        stockIDTO.setReferCode("905330120072300002");
        stockIDTO.setSaleType(1);
        stockIDTO.setCommodityList(commodityList);


        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.SHOP_COMMODITY_STOCK
                , stockIDTO, KafkaMessageOperationTypeEnum.UPDATE);
        kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.SHOP_COMMODITY_STOCK_TOPIC, JsonUtil.java2json(message));
    }

    @Test
    public void testProcessStock(){
        Long warehouseId = 99999L;
        List<StockItemDTO> commodityList = new ArrayList<>();
        StockItemDTO dto = new StockItemDTO();
        dto.setCommodityId(2029110267185300L);
        dto.setStockNumber(-1003);
        dto.setQuantity(BigDecimal.TEN);
        commodityList.add(dto);

        StockItemDTO dto1 = new StockItemDTO();
        dto1.setCommodityId(2580787591198700L);
        dto1.setStockNumber(5);
        dto1.setQuantity(BigDecimal.TEN);
        commodityList.add(dto1);
        Integer result = commodityStockMapper.processStock(warehouseId, dto1);
        QYAssert.isTrue(result > 0);
    }

    @Test
    public void testProcessQualityStock(){
        Long warehouseId = 99999L;
        List<StockItemDTO> commodityList = new ArrayList<>();
        StockItemDTO dto = new StockItemDTO();
        dto.setCommodityId(2029110267185300L);
        dto.setStockNumber(3);
        dto.setQuantity(BigDecimal.TEN);
        commodityList.add(dto);

        Integer result = commodityStockMapper.processQualityStock(warehouseId, dto);
        QYAssert.isTrue(result > 0);
    }

    @Test
    public void testQueryStockPage(){
        StockQueryPageIDTO idto = new StockQueryPageIDTO();
        idto.setPageNo(1);
        idto.setPageSize(20);
        idto.setWarehouseId(16L);
        idto.setConsignmentId(1L);
        MPage<StockItemPageODTO> stockPage = shopCommodityService.queryStockPage(idto);
        List<StockItemPageODTO> records = stockPage.getList();
        QYAssert.isTrue(records.size() > 0);
    }

    @Test
    public void testQueryStockInOutPage(){
        StockInOutPageIDTO idto = new StockInOutPageIDTO();
        idto.setPageNo(1);
        idto.setPageSize(20);
        idto.setWarehouseId(1L);
        idto.setWarehouseType(2);
        idto.setInOutType(2);
        idto.setBeginTime("2019-12-01");
        idto.setEndTime("2019-12-10");
        MPage<StockInOutItemODTO> stockPage = shopCommodityService.queryStockInOutPage(idto);
        List<StockInOutItemODTO> records = stockPage.getList();
        QYAssert.isTrue(records.size() > 0);
    }

    @Test
    public void testQueryStockInOutDetail(){
        StockInOutDetailIDTO idto = new StockInOutDetailIDTO();
        idto.setWarehouseType(1);
        idto.setInOutType(1);
        idto.setOrderCode("1001");
        StockInOutDetailODTO stockInOutDetailODTO = shopCommodityService.queryStockInOutDetail(idto);
        List<StockInOutDetailListDTO> records = stockInOutDetailODTO.getItemList();
        QYAssert.isTrue(records.size() > 0);
    }

    @Test
    public void testStockFreeze(){
        Long warehouseId = 4258L;
        List<StockItemDTO> list = new ArrayList<>();
        StockItemDTO dto1 = new StockItemDTO();
        dto1.setCommodityId(9820665500513400L);
        dto1.setStockNumber(1);

        StockItemDTO dto2 = new StockItemDTO();
        dto2.setCommodityId(24684610518094804L);
        dto2.setStockNumber(1);
        list.add(dto1);
        list.add(dto2);

//       int i = shopCommodityService.stockFreeze(1334811029657481218L,"501607078723717712",warehouseId, list, 1L);
//      QYAssert.isTrue(i > 0);
    }

    @Test
    public void testUnStockFreeze(){
        Long warehouseId = 2414L;
        int i = shopCommodityService.stockUnFreeze(1331539333536624642L,"501606298690576449",warehouseId);
        QYAssert.isTrue(i > 0);
    }

    @Test
    public void testReduceStock(){
        List<StockItemDTO> list = new ArrayList<>();
        StockItemDTO dto1 = new StockItemDTO();
        dto1.setCommodityId(1559472219683300L);
        dto1.setStockNumber(111);
        dto1.setQuantity(BigDecimal.TEN);

        StockItemDTO dto2 = new StockItemDTO();
        dto2.setCommodityId(2020608223169900L);
        dto2.setStockNumber(111);
        dto2.setQuantity(BigDecimal.TEN);
        list.add(dto1);
        list.add(dto2);

        shopCommodityService.reduceStock(list, 1);
        QYAssert.isTrue(true);
    }

    @Test
    public void testIncreaseStock(){
        List<StockItemDTO> list = new ArrayList<>();
        StockItemDTO dto1 = new StockItemDTO();
        dto1.setCommodityId(1559472219683300L);
        dto1.setStockNumber(111);
        dto1.setQuantity(BigDecimal.TEN);

        StockItemDTO dto2 = new StockItemDTO();
        dto2.setCommodityId(2020608223169900L);
        dto2.setStockNumber(111);
        dto2.setQuantity(BigDecimal.TEN);
        list.add(dto1);
        list.add(dto2);

        shopCommodityService.increaseStock(list, 1);
        QYAssert.isTrue(true);
    }
}
