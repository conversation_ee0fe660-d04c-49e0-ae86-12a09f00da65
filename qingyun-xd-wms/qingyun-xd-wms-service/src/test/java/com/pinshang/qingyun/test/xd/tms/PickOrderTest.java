package com.pinshang.qingyun.test.xd.tms;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.XdOrderStatusEnum;
import com.pinshang.qingyun.test.xd.AbstractJunitBase;
import com.pinshang.qingyun.xd.wms.dto.*;
import com.pinshang.qingyun.xd.wms.service.PickOrderService;
import org.junit.Test;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 拣货
 * Created by chen<PERSON> on 2019/12/03.
 */
@Rollback(false)
public class PickOrderTest extends AbstractJunitBase {

    @Autowired
    private PickOrderService pickOrderService;

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testTest(){
        String key = "{xd:real_time_sales_monitoring}:order:daily:group:gross_profit:21-01-05";

        RScoredSortedSet<Long> grossProfitOnlineSet = redissonClient.getScoredSortedSet(key);
        System.out.println(grossProfitOnlineSet);
        Double profit = grossProfitOnlineSet.getScore(3321L);
        System.out.println(profit);
        Assert.isTrue(1 == 1);
    }

    @Test
    public void testStock(){
        String SHOP_COMMODITY_STOCK_SYNC = "WMS:SHOP_COMMODITY_STOCK_SYNC:";
        Long[] warehouseIds = {47L};
        Long[] data = {999965182752700120L};

        List<Long> idList = Arrays.asList(data);
        for (Long warehouseId : warehouseIds) {
            RSet<Long> dealCacheSet = redissonClient.getSet(SHOP_COMMODITY_STOCK_SYNC + warehouseId);

            System.out.println("11");
            dealCacheSet.addAll(idList);
        }
    }

    @Test
    public void testCancelPickOrder(){
        pickOrderService.cancelPickOrder(6L, null);

        QYAssert.isTrue(true, "");
    }

    @Test
    public void testDistributePickOrderList(){
        pickOrderService.distributePickOrderList();

        QYAssert.isTrue(true, "");
    }

    @Test
    public void testCreatePickOrder(){
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(212L);
        orderDTO.setOrderCode("501577422011874603");
        orderDTO.setShopId(99999L);
        orderDTO.setStatus(XdOrderStatusEnum.WAITING_PICK);
        orderDTO.setDeliveryEndTime("2019-12-27 13:00:00");

        List<OrderItemDTO> orderItems = new ArrayList<>();
        OrderItemDTO item1 = new OrderItemDTO();
        item1.setItemId(3221L);
        item1.setCommodityId(10758910621399202L);
        item1.setIsProcess(0);
        item1.setIsWeight(0);
        item1.setStockNumber(3);
        item1.setQuantity(new BigDecimal(90));
        orderItems.add(item1);
        OrderItemDTO item2 = new OrderItemDTO();
        item2.setItemId(3230L);
        item2.setCommodityId(29636284212904200L);
        item2.setIsProcess(0);
        item2.setIsWeight(0);
        item2.setStockNumber(4);
        item2.setQuantity(new BigDecimal(4));
        orderItems.add(item2);
        OrderItemDTO item3 = new OrderItemDTO();
        item3.setItemId(3231L);
        item3.setCommodityId(31420677825407000L);
        item3.setIsProcess(0);
        item3.setIsWeight(0);
        item3.setStockNumber(5);
        item3.setQuantity(new BigDecimal(5));
        orderItems.add(item3);
        OrderItemDTO item4 = new OrderItemDTO();
        item4.setItemId(3232L);
        item4.setCommodityId(32289764792662004L);
        item4.setIsProcess(0);
        item4.setIsWeight(1);
        item4.setStockNumber(1);
        item4.setQuantity(new BigDecimal(2));
        orderItems.add(item4);
        OrderItemDTO item5 = new OrderItemDTO();
        item5.setItemId(3236L);
        item5.setCommodityId(58024722336020704L);
        item5.setIsProcess(0);
        item5.setIsWeight(0);
        item5.setStockNumber(1);
        item5.setQuantity(new BigDecimal(1));
        orderItems.add(item5);
        OrderItemDTO item6 = new OrderItemDTO();
        item6.setItemId(3237L);
        item6.setCommodityId(68668066998631112L);
        item6.setIsProcess(0);
        item6.setIsWeight(0);
        item6.setStockNumber(1);
        item6.setQuantity(new BigDecimal(1));
        orderItems.add(item6);
        OrderItemDTO item7 = new OrderItemDTO();
        item7.setItemId(3238L);
        item7.setCommodityId(72855095036517296L);
        item7.setIsProcess(0);
        item7.setIsWeight(1);
        item7.setStockNumber(1);
        item7.setQuantity(new BigDecimal(1));
        orderItems.add(item7);
        OrderItemDTO item8 = new OrderItemDTO();
        item8.setItemId(3222L);
        item8.setCommodityId(140634616944811600L);
        item8.setIsProcess(0);
        item8.setIsWeight(1);
        item8.setStockNumber(1);
        item8.setQuantity(new BigDecimal(1));
        orderItems.add(item8);
        OrderItemDTO item9 = new OrderItemDTO();
        item9.setItemId(3223L);
        item9.setCommodityId(152116718191562816L);
        item9.setIsProcess(0);
        item9.setIsWeight(1);
        item9.setStockNumber(16);
        item9.setQuantity(new BigDecimal(16));
        orderItems.add(item9);
        OrderItemDTO item10 = new OrderItemDTO();
        item10.setItemId(3224L);
        item10.setCommodityId(154243637820845312L);
        item10.setIsProcess(0);
        item10.setIsWeight(1);
        item10.setStockNumber(1);
        item10.setQuantity(new BigDecimal(1));
        orderItems.add(item10);
        OrderItemDTO item11 = new OrderItemDTO();
        item11.setItemId(3225L);
        item11.setCommodityId(279125955578871712L);
        item11.setIsProcess(1);
        item11.setIsWeight(1);
        item11.setStockNumber(1);
        item11.setQuantity(new BigDecimal(4));
        item11.setProcessId(4L);
        item11.setProcessName("他好");
        orderItems.add(item11);
        OrderItemDTO item12 = new OrderItemDTO();
        item12.setItemId(3226L);
        item12.setCommodityId(279125955578871712L);
        item12.setIsProcess(1);
        item12.setIsWeight(1);
        item12.setStockNumber(1);
        item12.setQuantity(new BigDecimal(4));
        item12.setProcessId(4L);
        item12.setProcessName("他好");
        orderItems.add(item12);
        OrderItemDTO item13 = new OrderItemDTO();
        item13.setItemId(3227L);
        item13.setCommodityId(279125955578871712L);
        item13.setIsProcess(1);
        item13.setIsWeight(1);
        item13.setStockNumber(1);
        item13.setQuantity(new BigDecimal(4));
        item13.setProcessId(4L);
        item13.setProcessName("他好");
        orderItems.add(item13);
        OrderItemDTO item14 = new OrderItemDTO();
        item14.setItemId(3228L);
        item14.setCommodityId(279125955578871712L);
        item14.setIsProcess(1);
        item14.setIsWeight(1);
        item14.setStockNumber(1);
        item14.setQuantity(new BigDecimal(4));
        item14.setProcessId(4L);
        item14.setProcessName("他好");
        orderItems.add(item14);
        OrderItemDTO item15 = new OrderItemDTO();
        item15.setItemId(3228L);
        item15.setCommodityId(279125955578871712L);
        item15.setIsProcess(1);
        item15.setIsWeight(1);
        item15.setStockNumber(1);
        item15.setQuantity(new BigDecimal(4));
        item15.setProcessId(5L);
        item15.setProcessName("都好");
        orderItems.add(item15);

        orderDTO.setItems(orderItems);
        pickOrderService.createPickOrder(orderDTO);
    }

    @Test
    public void testSelectPickOrderByOrderId(){
        PickOrderMqDTO mqDTO = pickOrderService.pickOrderByOrderId(2L);

        QYAssert.isTrue(mqDTO != null, "");
    }

    /**
     * 完成拣货
     */
    @Test
    @Rollback(value = false)
    public void testCompletePickOrder(){
        PickCompleteDTO pickCompleteDTO = new PickCompleteDTO();
        pickCompleteDTO.setPickOrderId(1693578111582531586L);
        List<PickCompleteItemDTO> itemDTOList = new ArrayList<>();
        PickCompleteItemDTO itemDTO = new PickCompleteItemDTO();
        itemDTO.setCommodityId(999965182752807491L);
        itemDTO.setIsWeight(0);
        itemDTO.setPickOrderId(1693578111582531586L);
        itemDTO.setPickOrderItemId(1693578111779799041L);
        itemDTO.setPickQuantity(new BigDecimal("1.000"));
        itemDTOList.add(itemDTO);
        PickCompleteItemDTO itemDTO2 = new PickCompleteItemDTO();
        itemDTO2.setCommodityId(999965182752815531L);
        itemDTO2.setIsWeight(1);
        itemDTO2.setPickOrderId(1693578111582531586L);
        itemDTO2.setPickOrderItemId(1693578111825936385L);
        itemDTO2.setPickQuantity(new BigDecimal("0.600"));
        itemDTOList.add(itemDTO2);
        pickCompleteDTO.setItems(itemDTOList);
        pickOrderService.completePickOrder(pickCompleteDTO);
    }
}
