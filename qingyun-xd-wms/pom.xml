<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
   <parent>
		<groupId>com.pinshang.qingyun</groupId>
		<artifactId>qingyun-xd-parent</artifactId>
       <version>3.7.3-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xd-parent/pom.xml</relativePath>
	</parent>
    <artifactId>qingyun-xd-wms</artifactId>
	<packaging>pom</packaging>
    <modules>
        <module>qingyun-xd-wms-client</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>

    <dependencies>
        <!--使用 lombok 简化 Java 代码-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-basic</artifactId>
                <version>${qingyun.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-renderer</artifactId>
                <version>${qingyun.renderer.version}</version>
            </dependency>
            <!--  project version start   -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
                <!-- <version>4.1.1</version>-->
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
                <!--<version>4.1.1</version>-->
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-tiot-platform-client</artifactId>
                <version>${qingyun.tiot.platform.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-upload-client</artifactId>
                <version>${qingyun.upload.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-import-plugin</artifactId>
                <version>${qingyun.import.plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-admin-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-cache</artifactId>
                <version>${qingyun.cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-common-client</artifactId>
                <version>${qingyun.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-order-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-cms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-smm-client</artifactId>
                <version>${qingyun.smm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xd-price-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-integration-client</artifactId>
                <version>${qingyun.integration.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-msg-client</artifactId>
                <version>${qingyun.msg.version}</version>
            </dependency>
            <!--  project version end   -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${org.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${org.boot.version}</version>
            </dependency>

            <!-- core -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${org.spring.version}</version>
            </dependency>

            <!--Swagger2 - RESTful API文档-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>

            <!--spring web相关包-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.6</version>
            </dependency>

            <!--mybatis依赖包-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.1</version>
            </dependency>
            <!--tk.mybatis-->
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>4.0.1</version>
            </dependency>
            <!-- pagehelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>

            <!--开发相关-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
                <version>${org.boot.version}</version>
            </dependency>

            <!--数据库-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.41</version>
            </dependency>
            <!-- druid redisson 升级 先处理wms 没问题把版本删了 扩展到其余项目 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-storage-client</artifactId>
                <version>${qingyun.storage.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-weixin-client</artifactId>
                <version>${qingyun.weixin.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>